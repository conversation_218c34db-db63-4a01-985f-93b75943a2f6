package user.controller;

import system.controller.BaseController;
import core.common.ApiResponse;
import domain.entity.PaymentTransaction;
import common.service.payment.PaymentService;
import core.util.ReactiveExceptionHandler;
import core.util.ReactiveOperationUtils;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import reactor.core.publisher.Mono;

/**
 * 支付交易管理控制器
 */
@RestController
@RequestMapping("/api/payment/transactions")
@PreAuthorize("hasRole('ADMIN')")
public class PaymentTransactionController extends BaseController {

    @Autowired
    private PaymentService paymentService;

    /**
     * 获取交易列表（分页）
     */
    @GetMapping
    public Mono<ResponseEntity<ApiResponse<Object>>> getTransactionList(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int pageSize,
            @RequestParam(required = false) String userId,
            @RequestParam(required = false) String cardId,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate,
            @RequestParam(required = false) Double minAmount,
            @RequestParam(required = false) Double maxAmount) {

        return Mono.zip(
                paymentService.getTransactionsPaged(page - 1, pageSize),
                paymentService.getAllTransactions()
            )
            .map(tuple -> {
                List<PaymentTransaction> transactions = tuple.getT1();
                List<PaymentTransaction> allTransactions = tuple.getT2();

                // 计算总页数
                int totalElements = allTransactions.size();
                int totalPages = (int) Math.ceil((double) totalElements / pageSize);

                // 使用统一的分页响应格式
                Map<String, Object> response = new HashMap<>();
                response.put("content", transactions);
                response.put("totalElements", totalElements);
                response.put("totalPages", (int) Math.ceil((double) totalElements / pageSize));
                response.put("size", pageSize);
                response.put("number", page);
                response.put("first", page == 0);
                response.put("last", (page + 1) * pageSize >= totalElements);

                logger.info("获取交易列表成功，页码: {}, 大小: {}, 总数: {}",
                    page, pageSize, totalElements);

                return success((Object) response);
            })
            .onErrorResume(ReactiveExceptionHandler.handleError("获取交易列表"));
    }

    /**
     * 获取交易详情
     */
    @GetMapping("/{transactionId}")
    public Mono<ResponseEntity<ApiResponse<Object>>> getTransactionDetail(@PathVariable String transactionId) {
        return paymentService.getTransaction(transactionId)
            .map(transaction -> success((Object) transaction))
            .onErrorResume(e -> {
                logger.error("获取交易详情失败: {}", transactionId, e);
                if (e.getMessage().contains("not found")) {
                    return Mono.just(error(404, "交易不存在"));
                }
                return Mono.just(handleException((Exception) e, "获取交易详情"));
            });
    }

    /**
     * 处理交易
     */
    @PostMapping("/{transactionId}/process")
    public Mono<ResponseEntity<ApiResponse<Object>>> processTransaction(@PathVariable String transactionId) {
        return paymentService.processPayment(transactionId)
            .map(transaction -> success((Object) transaction, "交易处理成功"))
            .onErrorResume(e -> {
                logger.error("处理交易失败: {}", transactionId, e);
                return Mono.just(handleException((Exception) e, "处理交易"));
            });
    }

    /**
     * 获取交易验证状态
     */
    @GetMapping("/{transactionId}/verification-status")
    public Mono<ResponseEntity<ApiResponse<Object>>> getVerificationStatus(@PathVariable String transactionId) {
        return paymentService.getTransaction(transactionId)
            .map(transaction -> {
                Map<String, Object> status = new HashMap<>();
                status.put("transactionId", transaction.getTransactionId());
                status.put("status", transaction.getStatus().toString().toLowerCase());
                status.put("verificationType", transaction.getVerificationType());
                status.put("lastUpdate", transaction.getUpdatedAt());

                return success((Object) status, "获取验证状态成功");
            })
            .onErrorResume(e -> {
                logger.error("获取验证状态失败: {}", transactionId, e);
                if (e.getMessage().contains("not found")) {
                    return Mono.just(error(404, "交易不存在"));
                }
                return Mono.just(handleException((Exception) e, "获取验证状态"));
            });
    }

    /**
     * 获取交易统计信息
     */
    @GetMapping("/statistics")
    public Mono<ResponseEntity<ApiResponse<Object>>> getTransactionStatistics() {
        return paymentService.getAllTransactions()
            .map(allTransactions -> {
                if (allTransactions == null) {
                    allTransactions = List.of(); // 避免null异常
                }

                LocalDateTime todayStart = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0).withNano(0);

                Map<String, Object> stats = new HashMap<>();
                stats.put("totalTransactions", allTransactions.size());

                // 安全地计算各状态交易数量
                long successfulCount = allTransactions.stream()
                    .filter(t -> t != null && t.getStatus() != null && "SUCCEEDED".equals(t.getStatus().toString()))
                    .count();
                long failedCount = allTransactions.stream()
                    .filter(t -> t != null && t.getStatus() != null && "FAILED".equals(t.getStatus().toString()))
                    .count();
                long pendingCount = allTransactions.stream()
                    .filter(t -> t != null && t.getStatus() != null && "PENDING".equals(t.getStatus().toString()))
                    .count();

                stats.put("successfulTransactions", successfulCount);
                stats.put("failedTransactions", failedCount);
                stats.put("pendingTransactions", pendingCount);



                // 今日交易统计
                List<PaymentTransaction> todayTransactionsList = allTransactions.stream()
                    .filter(t -> t != null && t.getCreatedAt() != null && t.getCreatedAt().isAfter(todayStart))
                    .collect(Collectors.toList());

                long todayTransactions = todayTransactionsList.size();

                stats.put("todayTransactions", todayTransactions);

                // 成功率计算
                double successRate = allTransactions.size() > 0 ?
                    (double) successfulCount / allTransactions.size() * 100 : 0;
                stats.put("successRate", Math.round(successRate * 100.0) / 100.0);

                return success((Object) stats);
            })
            .onErrorResume(e -> {
                logger.error("获取交易统计失败", e);
                return Mono.just(handleException((Exception) e, "获取交易统计"));
            });
    }

    /**
     * 获取交易状态分布
     */
    @GetMapping("/status-distribution")
    public Mono<ResponseEntity<ApiResponse<Object>>> getTransactionStatusDistribution() {
        return paymentService.getAllTransactions()
            .map(allTransactions -> {
                if (allTransactions == null) {
                    allTransactions = List.of(); // 避免null异常
                }

                // 创建final变量供lambda使用
                final List<PaymentTransaction> finalAllTransactions = allTransactions;

                // 安全地按状态分组
                Map<String, List<PaymentTransaction>> statusGroups = finalAllTransactions.stream()
                    .filter(t -> t != null) // 过滤null交易
                    .collect(Collectors.groupingBy(
                        transaction -> transaction.getStatus() != null ? transaction.getStatus().toString() : "UNKNOWN"
                    ));

                List<Map<String, Object>> distribution = statusGroups.entrySet().stream()
                    .map(entry -> {
                        Map<String, Object> item = new HashMap<>();
                        String status = entry.getKey();
                        List<PaymentTransaction> transactions = entry.getValue();
                        final long count = transactions.size();

                        item.put("status", status);
                        item.put("count", count);

                        // 计算百分比
                        double percentage = finalAllTransactions.size() > 0 ?
                            Math.round((double) count / finalAllTransactions.size() * 100 * 100.0) / 100.0 : 0;
                        item.put("percentage", percentage);

                        return item;
                    })
                    .collect(Collectors.toList());

                return success((Object) distribution);
            })
            .onErrorResume(e -> {
                logger.error("获取交易状态分布失败", e);
                return Mono.just(handleException((Exception) e, "获取交易状态分布"));
            });
    }
}
