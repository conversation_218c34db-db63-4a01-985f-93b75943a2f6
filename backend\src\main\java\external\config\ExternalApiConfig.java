package external.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 外部API配置类
 * 统一管理所有外部API的配置信息
 */
@Configuration
@ConfigurationProperties(prefix = "external.api")
public class ExternalApiConfig {

    private BinLookupConfig binLookup = new BinLookupConfig();
    private GeoLocationConfig geoLocation = new GeoLocationConfig();
    private FraudDetectionConfig fraudDetection = new FraudDetectionConfig();
    private IPRegistryConfig ipRegistry = new IPRegistryConfig();

    // Getters and Setters
    public BinLookupConfig getBinLookup() {
        return binLookup;
    }

    public void setBinLookup(BinLookupConfig binLookup) {
        this.binLookup = binLookup;
    }



    public GeoLocationConfig getGeoLocation() {
        return geoLocation;
    }

    public void setGeoLocation(GeoLocationConfig geoLocation) {
        this.geoLocation = geoLocation;
    }

    public FraudDetectionConfig getFraudDetection() {
        return fraudDetection;
    }

    public void setFraudDetection(FraudDetectionConfig fraudDetection) {
        this.fraudDetection = fraudDetection;
    }

    public IPRegistryConfig getIpRegistry() {
        return ipRegistry;
    }

    public void setIpRegistry(IPRegistryConfig ipRegistry) {
        this.ipRegistry = ipRegistry;
    }

    /**
     * BIN码查询配置
     */
    public static class BinLookupConfig {
        private boolean enabled = true;
        private String provider = "binlist"; // binlist, bindb
        private String baseUrl = "https://lookup.binlist.net";
        private int timeout = 5000; // 毫秒
        private int retryCount = 3;
        private boolean cacheEnabled = true;
        private int cacheTtl = 3600; // 秒

        // Getters and Setters
        public boolean isEnabled() {
            return enabled;
        }

        public void setEnabled(boolean enabled) {
            this.enabled = enabled;
        }

        public String getProvider() {
            return provider;
        }

        public void setProvider(String provider) {
            this.provider = provider;
        }




        public String getBaseUrl() {
            return baseUrl;
        }

        public void setBaseUrl(String baseUrl) {
            this.baseUrl = baseUrl;
        }

        public int getTimeout() {
            return timeout;
        }

        public void setTimeout(int timeout) {
            this.timeout = timeout;
        }

        public int getRetryCount() {
            return retryCount;
        }

        public void setRetryCount(int retryCount) {
            this.retryCount = retryCount;
        }

        public boolean isCacheEnabled() {
            return cacheEnabled;
        }

        public void setCacheEnabled(boolean cacheEnabled) {
            this.cacheEnabled = cacheEnabled;
        }

        public int getCacheTtl() {
            return cacheTtl;
        }

        public void setCacheTtl(int cacheTtl) {
            this.cacheTtl = cacheTtl;
        }
    }



    /**
     * 地理位置查询配置
     */
    public static class GeoLocationConfig {
        private boolean enabled = true;
        private String provider = "ip-api.io"; // ip-api.io, ip.sb
        private String baseUrl = "https://ip-api.io/api/v1";
        private int timeout = 4000;
        private int retryCount = 2;
        private boolean cacheEnabled = true;
        private int cacheTtl = 7200; // 2小时

        // Getters and Setters
        public boolean isEnabled() {
            return enabled;
        }

        public void setEnabled(boolean enabled) {
            this.enabled = enabled;
        }

        public String getProvider() {
            return provider;
        }

        public void setProvider(String provider) {
            this.provider = provider;
        }



        public String getBaseUrl() {
            return baseUrl;
        }

        public void setBaseUrl(String baseUrl) {
            this.baseUrl = baseUrl;
        }

        public int getTimeout() {
            return timeout;
        }

        public void setTimeout(int timeout) {
            this.timeout = timeout;
        }

        public int getRetryCount() {
            return retryCount;
        }

        public void setRetryCount(int retryCount) {
            this.retryCount = retryCount;
        }

        public boolean isCacheEnabled() {
            return cacheEnabled;
        }

        public void setCacheEnabled(boolean cacheEnabled) {
            this.cacheEnabled = cacheEnabled;
        }

        public int getCacheTtl() {
            return cacheTtl;
        }

        public void setCacheTtl(int cacheTtl) {
            this.cacheTtl = cacheTtl;
        }
    }

    /**
     * 风控检测配置
     */
    public static class FraudDetectionConfig {
        private boolean enabled = true;
        private String provider = "internal"; // internal, external
        private String baseUrl;
        private int timeout = 2000;
        private int retryCount = 1;
        private double riskThreshold = 0.7; // 风险阈值

        // Getters and Setters
        public boolean isEnabled() {
            return enabled;
        }

        public void setEnabled(boolean enabled) {
            this.enabled = enabled;
        }

        public String getProvider() {
            return provider;
        }

        public void setProvider(String provider) {
            this.provider = provider;
        }



        public String getBaseUrl() {
            return baseUrl;
        }

        public void setBaseUrl(String baseUrl) {
            this.baseUrl = baseUrl;
        }

        public int getTimeout() {
            return timeout;
        }

        public void setTimeout(int timeout) {
            this.timeout = timeout;
        }

        public int getRetryCount() {
            return retryCount;
        }

        public void setRetryCount(int retryCount) {
            this.retryCount = retryCount;
        }

        public double getRiskThreshold() {
            return riskThreshold;
        }

        public void setRiskThreshold(double riskThreshold) {
            this.riskThreshold = riskThreshold;
        }
    }

    /**
     * IPRegistry配置
     */
    public static class IPRegistryConfig {
        private boolean enabled = false;
        private String apiKey;
        private String baseUrl = "https://api.ipregistry.co";
        private int timeout = 5000;
        private int retryCount = 2;
        private boolean cacheEnabled = true;
        private int cacheTtl = 3600; // 1小时
        private String redirectUrl = "https://example.com";

        // Getters and Setters
        public boolean isEnabled() {
            return enabled;
        }

        public void setEnabled(boolean enabled) {
            this.enabled = enabled;
        }

        public String getApiKey() {
            return apiKey;
        }

        public void setApiKey(String apiKey) {
            this.apiKey = apiKey;
        }

        public String getBaseUrl() {
            return baseUrl;
        }

        public void setBaseUrl(String baseUrl) {
            this.baseUrl = baseUrl;
        }

        public int getTimeout() {
            return timeout;
        }

        public void setTimeout(int timeout) {
            this.timeout = timeout;
        }

        public int getRetryCount() {
            return retryCount;
        }

        public void setRetryCount(int retryCount) {
            this.retryCount = retryCount;
        }

        public boolean isCacheEnabled() {
            return cacheEnabled;
        }

        public void setCacheEnabled(boolean cacheEnabled) {
            this.cacheEnabled = cacheEnabled;
        }

        public int getCacheTtl() {
            return cacheTtl;
        }

        public void setCacheTtl(int cacheTtl) {
            this.cacheTtl = cacheTtl;
        }

        public String getRedirectUrl() {
            return redirectUrl;
        }

        public void setRedirectUrl(String redirectUrl) {
            this.redirectUrl = redirectUrl;
        }
    }
}
