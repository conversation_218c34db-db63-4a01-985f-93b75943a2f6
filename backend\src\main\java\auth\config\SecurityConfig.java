package auth.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpMethod;
import org.springframework.security.config.annotation.web.reactive.EnableWebFluxSecurity;
import org.springframework.security.config.web.server.ServerHttpSecurity;
import org.springframework.security.web.server.SecurityWebFilterChain;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.reactive.CorsConfigurationSource;
import org.springframework.web.cors.reactive.UrlBasedCorsConfigurationSource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.web.server.authentication.AuthenticationWebFilter;
import org.springframework.security.web.server.context.NoOpServerSecurityContextRepository;
import org.springframework.security.web.server.ServerAuthenticationEntryPoint;
import org.springframework.security.web.server.authentication.HttpStatusServerEntryPoint;
import org.springframework.http.HttpStatus;
import security.service.DomainService;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Configuration
@EnableWebFluxSecurity
public class SecurityConfig {

    @Autowired
    private ReactiveJwtAuthenticationFilter jwtAuthenticationFilter;

    @Autowired
    private DynamicCorsConfigurationSource dynamicCorsConfigurationSource;

    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }

    @Bean
    public CorsConfigurationSource corsConfigurationSource() {
        // 使用动态CORS配置源
        return dynamicCorsConfigurationSource;
    }

    @Bean
    public SecurityWebFilterChain securityWebFilterChain(ServerHttpSecurity http) {
        return http
            .csrf(csrf -> csrf.disable())
            .cors(cors -> cors.configurationSource(corsConfigurationSource()))
            .securityContextRepository(NoOpServerSecurityContextRepository.getInstance())

            .authorizeExchange(exchanges -> exchanges
                // 公开端点 - 无需认证
                .pathMatchers(HttpMethod.OPTIONS, "/**").permitAll()
                .pathMatchers("/api/auth/login").permitAll()
                .pathMatchers("/api/payment/process").permitAll()
                .pathMatchers("/api/payment/3d-secure/**").permitAll()
                .pathMatchers("/actuator/health", "/actuator/info").permitAll()
                .pathMatchers("/favicon.ico").permitAll()

                // WebSocket连接 - 在处理器中进行token验证
                .pathMatchers("/ws/**").permitAll()

                // 3D安全模板API - 需要ADMIN或OPERATOR角色
                .pathMatchers("/api/3d-secure/templates/**").hasAnyRole("OPERATOR", "ADMIN")

                // 管理员API - 需要ADMIN角色
                .pathMatchers("/api/admin/**").hasRole("ADMIN")
                .pathMatchers("/api/system/**").hasRole("ADMIN")
                .pathMatchers("/api/config/**").hasRole("ADMIN")
                .pathMatchers("/api/domains/**").hasRole("ADMIN")
                .pathMatchers("/api/users/**").hasRole("ADMIN")

                // 操作员API - 需要OPERATOR或ADMIN角色
                .pathMatchers("/api/payment-cards/**").hasAnyRole("OPERATOR", "ADMIN")
                .pathMatchers("/api/otp/**").hasAnyRole("OPERATOR", "ADMIN")
                .pathMatchers("/api/verification/**").hasAnyRole("OPERATOR", "ADMIN")

                // 监控端点 - 需要ADMIN角色
                .pathMatchers("/actuator/**").hasRole("ADMIN")

                // 其他API端点 - 需要认证
                .pathMatchers("/api/**").authenticated()

                // 拒绝所有其他请求
                .anyExchange().denyAll()
            )
            .formLogin(formLogin -> formLogin.disable())
            .httpBasic(httpBasic -> httpBasic.disable())
            .exceptionHandling(exceptions -> exceptions
                .authenticationEntryPoint(new HttpStatusServerEntryPoint(HttpStatus.UNAUTHORIZED))
            )
            .build();
    }

}