package external.service;

import core.common.ApiResponse;
import core.constants.AppConstants;
import external.client.BinLookupApiClient;
import external.dto.BinLookupResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;
import reactor.core.publisher.Flux;

import java.util.concurrent.CompletableFuture;
import java.util.Map;
import java.util.HashMap;
import java.util.List;
import java.util.Arrays;

/**
 * 外部API服务管理类
 */
@Service
public class ExternalApiService {

    private static final Logger logger = LoggerFactory.getLogger(ExternalApiService.class);

    @Autowired
    private BinLookupApiClient binLookupApiClient;

    @Cacheable(value = AppConstants.Cache.BIN_LOOKUP, key = "#bin", unless = "#result == null || !#result.success")
    public Mono<ApiResponse<BinLookupResult>> lookupBin(String bin) {
        return binLookupApiClient.lookupBin(bin);
    }

    /**
     * 异步查询BIN码信息
     *
     * @param bin BIN码
     * @return 异步结果
     */
    public CompletableFuture<ApiResponse<BinLookupResult>> lookupBinAsync(String bin) {
        return lookupBin(bin).toFuture();
    }



    /**
     * 验证BIN码格式
     * 委托给BinLookupApiClient处理
     *
     * @param bin BIN码
     * @return 验证结果
     */
    public boolean isValidBin(String bin) {
        if (bin == null || bin.trim().isEmpty()) {
            return false;
        }

        // 清理BIN码，只保留数字
        String cleanBin = bin.replaceAll("[^0-9]", "");

        // BIN码通常是6-8位数字
        return cleanBin.length() >= 6 && cleanBin.length() <= 8;
    }

    /**
     * 批量查询BIN码信息
     */
    public Mono<Map<String, BinLookupResult>> batchLookupBin(String[] bins) {
        logger.debug("批量查询BIN码信息: {} 个", bins.length);

        return Flux.fromArray(bins)
                .flatMap(bin -> lookupBin(bin)
                        .map(response -> Map.entry(bin, response.getData()))
                        .onErrorReturn(Map.entry(bin, null)))
                .collectMap(Map.Entry::getKey, Map.Entry::getValue)
                .doOnSuccess(result -> logger.debug("批量BIN查询完成: {} 个结果", result.size()))
                .doOnError(error -> logger.error("批量BIN查询失败", error));
    }

    /**
     * 获取所有外部API状态
     */
    public Mono<Map<String, Object>> getAllExternalApiStatus() {
        logger.debug("获取所有外部API状态");

        Map<String, Object> status = new HashMap<>();
        status.put("binLookup", getBinLookupServiceStatus());
        status.put("timestamp", System.currentTimeMillis());
        status.put("healthy", isHealthy());

        return Mono.just(status);
    }

    /**
     * 获取BIN查询服务状态
     */
    public Map<String, Object> getBinLookupServiceStatus() {
        Map<String, Object> status = new HashMap<>();
        status.put("service", "BinLookup");
        status.put("status", "active");
        status.put("lastCheck", System.currentTimeMillis());
        status.put("healthy", true);
        return status;
    }

    /**
     * 检查服务是否健康
     */
    public boolean isHealthy() {
        // 简单的健康检查
        return binLookupApiClient != null;
    }

    /**
     * 获取缓存统计信息
     */
    public Mono<Map<String, Object>> getCacheStatistics() {
        logger.debug("获取缓存统计信息");

        Map<String, Object> stats = new HashMap<>();
        stats.put("cacheSize", 0);
        stats.put("hitRate", 0.0);
        stats.put("missCount", 0);
        stats.put("evictionCount", 0);

        return Mono.just(stats);
    }

}
