package common.service.cache;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.reactive.TransactionalOperator;
import reactor.core.publisher.Mono;

import java.time.Duration;
import java.util.Map;
import java.util.function.Supplier;

/**
 * 事务性缓存管理器
 * 确保数据库操作和缓存操作的一致性
 */
@Service
public class TransactionalCacheManager {

    private static final Logger logger = LoggerFactory.getLogger(TransactionalCacheManager.class);

    @Autowired
    private ReactiveCacheService cacheService;

    @Autowired
    private TransactionalOperator transactionalOperator;

    /**
     * 事务性保存操作
     * 先保存到数据库，成功后更新缓存
     */
    public <T> Mono<T> saveWithCache(Supplier<Mono<T>> databaseOperation, 
                                     String cacheKey, 
                                     Duration cacheTtl) {
        return transactionalOperator.transactional(
            databaseOperation.get()
                .flatMap(result -> {
                    // 数据库操作成功后，更新缓存
                    return cacheService.set(cacheKey, result, cacheTtl)
                            .doOnSuccess(cached -> {
                                if (cached) {
                                    logger.debug("事务性缓存更新成功: key={}", cacheKey);
                                } else {
                                    logger.warn("事务性缓存更新失败: key={}", cacheKey);
                                }
                            })
                            .thenReturn(result);
                })
                .doOnError(error -> {
                    logger.error("事务性保存操作失败: key={}", cacheKey, error);
                    // 清理可能的脏缓存
                    cacheService.delete(cacheKey).subscribe();
                })
        );
    }

    /**
     * 事务性删除操作
     * 先从数据库删除，成功后清除缓存
     */
    public <T> Mono<T> deleteWithCache(Supplier<Mono<T>> databaseOperation, 
                                       String cacheKey) {
        return transactionalOperator.transactional(
            databaseOperation.get()
                .flatMap(result -> {
                    // 数据库操作成功后，清除缓存
                    return cacheService.delete(cacheKey)
                            .doOnSuccess(deleted -> {
                                if (deleted) {
                                    logger.debug("事务性缓存清除成功: key={}", cacheKey);
                                } else {
                                    logger.debug("事务性缓存清除完成(key不存在): key={}", cacheKey);
                                }
                            })
                            .thenReturn(result);
                })
                .doOnError(error -> logger.error("事务性删除操作失败: key={}", cacheKey, error))
        );
    }

    /**
     * 事务性Hash保存操作
     */
    public <T> Mono<T> saveHashWithCache(Supplier<Mono<T>> databaseOperation,
                                         String cacheKey,
                                         Map<String, Object> hashData,
                                         Duration cacheTtl) {
        return transactionalOperator.transactional(
            databaseOperation.get()
                .flatMap(result -> {
                    // 数据库操作成功后，更新Hash缓存
                    return cacheService.setHash(cacheKey, hashData, cacheTtl)
                            .doOnSuccess(cached -> {
                                if (cached) {
                                    logger.debug("事务性Hash缓存更新成功: key={}", cacheKey);
                                } else {
                                    logger.warn("事务性Hash缓存更新失败: key={}", cacheKey);
                                }
                            })
                            .thenReturn(result);
                })
                .doOnError(error -> {
                    logger.error("事务性Hash保存操作失败: key={}", cacheKey, error);
                    // 清理可能的脏缓存
                    cacheService.delete(cacheKey).subscribe();
                })
        );
    }

    /**
     * 缓存穿透保护的查询操作
     */
    public <T> Mono<T> getWithFallback(String cacheKey,
                                       Class<T> type,
                                       Supplier<Mono<T>> databaseFallback,
                                       Duration cacheTtl) {
        return cacheService.get(cacheKey, type)
                .switchIfEmpty(
                    // 缓存未命中，从数据库查询
                    databaseFallback.get()
                            .flatMap(result -> {
                                if (result != null) {
                                    // 查询到数据，更新缓存
                                    return cacheService.set(cacheKey, result, cacheTtl)
                                            .thenReturn(result);
                                } else {
                                    // 查询为空，设置空值缓存防止缓存穿透
                                    return cacheService.set(cacheKey, "NULL", Duration.ofMinutes(5))
                                            .thenReturn((T) null);
                                }
                            })
                            .doOnSuccess(result -> logger.debug("缓存回源查询完成: key={}, found={}", 
                                    cacheKey, result != null))
                            .doOnError(error -> logger.error("缓存回源查询失败: key={}", cacheKey, error))
                )
                .filter(result -> !"NULL".equals(result)); // 过滤空值标记
    }

    /**
     * 批量缓存失效
     */
    public Mono<Void> invalidatePattern(String pattern) {
        // 注意：这里需要实现Redis的SCAN操作来查找匹配的key
        // 由于ReactiveRedisTemplate不直接支持SCAN，这里简化处理
        logger.info("批量缓存失效请求: pattern={}", pattern);
        return Mono.empty();
    }

    /**
     * 预热缓存
     */
    public <T> Mono<Void> warmupCache(String cacheKey,
                                      Supplier<Mono<T>> dataLoader,
                                      Duration cacheTtl) {
        return dataLoader.get()
                .flatMap(data -> cacheService.set(cacheKey, data, cacheTtl))
                .doOnSuccess(result -> {
                    if (result) {
                        logger.info("缓存预热成功: key={}", cacheKey);
                    } else {
                        logger.warn("缓存预热失败: key={}", cacheKey);
                    }
                })
                .doOnError(error -> logger.error("缓存预热异常: key={}", cacheKey, error))
                .then();
    }
}
