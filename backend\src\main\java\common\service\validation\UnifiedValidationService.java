package common.service.validation;

import common.service.payment.detection.CardTypeDetectionService;
import common.constants.CardConstants;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.util.HashMap;
import java.util.Map;
import java.util.regex.Pattern;

/**
 * 统一验证服务
 * 整合所有验证逻辑，避免重复代码
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UnifiedValidationService {

    private final CardTypeDetectionService cardTypeDetectionService;

    // 常用正则表达式
    private static final Pattern EMAIL_PATTERN = Pattern.compile(
        "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$"
    );

    private static final Pattern PHONE_PATTERN = Pattern.compile(
        "^[+]?[1-9]\\d{1,14}$"
    );

    private static final Pattern BIN_PATTERN = Pattern.compile("^[0-9]{6,8}$");

    /**
     * 验证卡号（使用专业服务）
     */
    public Mono<Map<String, Object>> validateCardNumber(String cardNumber) {
        log.debug("验证卡号: {}", cardNumber);
        
        return cardTypeDetectionService.validateCardNumber(cardNumber)
                .flatMap(isValid -> {
                    Map<String, Object> result = new HashMap<>();
                    result.put("valid", isValid);
                    
                    if (isValid) {
                        return cardTypeDetectionService.getCardDetails(cardNumber)
                                .map(details -> {
                                    result.putAll(details);
                                    return result;
                                });
                    } else {
                        result.put("error", "无效的卡号格式");
                        return Mono.just(result);
                    }
                })
                .doOnSuccess(result -> log.debug("卡号验证结果: {}", result))
                .doOnError(error -> log.error("卡号验证失败", error));
    }

    /**
     * 验证CVV
     */
    public Mono<Map<String, Object>> validateCvv(String cvv, String cardNumber) {
        log.debug("验证CVV");
        
        return Mono.fromCallable(() -> {
            Map<String, Object> result = new HashMap<>();
            
            if (cvv == null || cvv.trim().isEmpty()) {
                result.put("valid", false);
                result.put("error", "CVV不能为空");
                return result;
            }
            
            String cleanCvv = cvv.trim();
            
            // 如果有卡号，根据卡片类型验证CVV长度
            if (cardNumber != null && !cardNumber.trim().isEmpty()) {
                String cardType = CardConstants.determineCardType(cardNumber);
                int expectedLength = CardConstants.getCvvLength(cardType);
                
                if (cleanCvv.length() != expectedLength) {
                    result.put("valid", false);
                    result.put("error", String.format("CVV长度应为%d位", expectedLength));
                    return result;
                }
            } else {
                // 没有卡号时，验证通用CVV格式
                if (!cleanCvv.matches("^[0-9]{3,4}$")) {
                    result.put("valid", false);
                    result.put("error", "CVV格式无效");
                    return result;
                }
            }
            
            result.put("valid", true);
            return result;
        })
        .doOnSuccess(result -> log.debug("CVV验证结果: {}", result))
        .doOnError(error -> log.error("CVV验证失败", error));
    }

    /**
     * 验证有效期
     */
    public Mono<Map<String, Object>> validateExpiryDate(String expiryMonth, String expiryYear) {
        log.debug("验证有效期: {}/{}", expiryMonth, expiryYear);
        
        return Mono.fromCallable(() -> {
            Map<String, Object> result = new HashMap<>();
            
            try {
                int month = Integer.parseInt(expiryMonth);
                int year = Integer.parseInt(expiryYear);
                
                // 验证月份范围
                if (month < 1 || month > 12) {
                    result.put("valid", false);
                    result.put("error", "月份必须在1-12之间");
                    return result;
                }
                
                // 验证年份范围（当前年份到未来20年）
                int currentYear = java.time.Year.now().getValue();
                if (year < currentYear || year > currentYear + 20) {
                    result.put("valid", false);
                    result.put("error", "年份无效");
                    return result;
                }
                
                // 验证是否已过期
                java.time.LocalDate now = java.time.LocalDate.now();
                java.time.LocalDate expiry = java.time.LocalDate.of(year, month, 1)
                        .plusMonths(1).minusDays(1); // 月末
                
                if (expiry.isBefore(now)) {
                    result.put("valid", false);
                    result.put("error", "卡片已过期");
                    return result;
                }
                
                result.put("valid", true);
                return result;
                
            } catch (NumberFormatException e) {
                result.put("valid", false);
                result.put("error", "日期格式无效");
                return result;
            }
        })
        .doOnSuccess(result -> log.debug("有效期验证结果: {}", result))
        .doOnError(error -> log.error("有效期验证失败", error));
    }

    /**
     * 验证邮箱
     */
    public Mono<Map<String, Object>> validateEmail(String email) {
        log.debug("验证邮箱: {}", email);
        
        return Mono.fromCallable(() -> {
            Map<String, Object> result = new HashMap<>();
            
            if (email == null || email.trim().isEmpty()) {
                result.put("valid", false);
                result.put("error", "邮箱不能为空");
                return result;
            }
            
            boolean isValid = EMAIL_PATTERN.matcher(email.trim()).matches();
            result.put("valid", isValid);
            
            if (!isValid) {
                result.put("error", "邮箱格式无效");
            }
            
            return result;
        })
        .doOnSuccess(result -> log.debug("邮箱验证结果: {}", result))
        .doOnError(error -> log.error("邮箱验证失败", error));
    }

    /**
     * 验证手机号
     */
    public Mono<Map<String, Object>> validatePhone(String phone) {
        log.debug("验证手机号: {}", phone);
        
        return Mono.fromCallable(() -> {
            Map<String, Object> result = new HashMap<>();
            
            if (phone == null || phone.trim().isEmpty()) {
                result.put("valid", false);
                result.put("error", "手机号不能为空");
                return result;
            }
            
            String cleanPhone = phone.replaceAll("[\\s-()]", "");
            boolean isValid = PHONE_PATTERN.matcher(cleanPhone).matches();
            result.put("valid", isValid);
            
            if (!isValid) {
                result.put("error", "手机号格式无效");
            }
            
            return result;
        })
        .doOnSuccess(result -> log.debug("手机号验证结果: {}", result))
        .doOnError(error -> log.error("手机号验证失败", error));
    }

    /**
     * 验证BIN码
     */
    public Mono<Map<String, Object>> validateBin(String bin) {
        log.debug("验证BIN码: {}", bin);
        
        return Mono.fromCallable(() -> {
            Map<String, Object> result = new HashMap<>();
            
            if (bin == null || bin.trim().isEmpty()) {
                result.put("valid", false);
                result.put("error", "BIN码不能为空");
                return result;
            }
            
            boolean isValid = BIN_PATTERN.matcher(bin.trim()).matches();
            result.put("valid", isValid);
            
            if (!isValid) {
                result.put("error", "BIN码格式无效，必须是6-8位数字");
            }
            
            return result;
        })
        .doOnSuccess(result -> log.debug("BIN码验证结果: {}", result))
        .doOnError(error -> log.error("BIN码验证失败", error));
    }

    /**
     * 批量验证支付卡信息
     */
    public Mono<Map<String, Object>> validatePaymentCard(String cardNumber, String holderName, 
                                                        String expiryMonth, String expiryYear, String cvv) {
        log.debug("批量验证支付卡信息");
        
        return Mono.zip(
                validateCardNumber(cardNumber),
                validateCvv(cvv, cardNumber),
                validateExpiryDate(expiryMonth, expiryYear)
        )
        .map(tuple -> {
            Map<String, Object> cardResult = tuple.getT1();
            Map<String, Object> cvvResult = tuple.getT2();
            Map<String, Object> dateResult = tuple.getT3();
            
            Map<String, Object> result = new HashMap<>();
            result.put("cardNumber", cardResult);
            result.put("cvv", cvvResult);
            result.put("expiryDate", dateResult);
            
            boolean allValid = (Boolean) cardResult.get("valid") && 
                             (Boolean) cvvResult.get("valid") && 
                             (Boolean) dateResult.get("valid");
            result.put("valid", allValid);
            
            return result;
        })
        .doOnSuccess(result -> log.debug("支付卡批量验证结果: {}", result))
        .doOnError(error -> log.error("支付卡批量验证失败", error));
    }
}
