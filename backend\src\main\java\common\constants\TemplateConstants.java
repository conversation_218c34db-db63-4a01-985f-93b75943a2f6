package common.constants;

/**
 * 模板相关常量类
 * 集中管理所有3D验证模板相关的硬编码值
 */
public final class TemplateConstants {

    // 防止实例化
    private TemplateConstants() {
        throw new UnsupportedOperationException("常量类不能被实例化");
    }

    // ==================== 模板类型常量 ====================

    /**
     * 短信验证模板
     */
    public static final String SMS_TEMPLATE = "cellphone";

    /**
     * 邮箱验证模板
     */
    public static final String EMAIL_TEMPLATE = "email";

    /**
     * APP验证模板
     */
    public static final String APP_TEMPLATE = "app-auth";

    /**
     * PIN验证模板
     */
    public static final String PIN_TEMPLATE = "pin-auth";

    /**
     * AMEX SafeKey验证模板
     */
    public static final String AMEX_SAFEKEY_TEMPLATE = "amex-safekey";

    /**
     * 网银登录验证模板
     */
    public static final String BANK_LOGIN_TEMPLATE = "bank-login";

    /**
     * VPASS验证模板
     */
    public static final String VPASS_TEMPLATE = "vpass-auth";

    /**
     * 默认验证模板
     */
    public static final String DEFAULT_TEMPLATE = SMS_TEMPLATE;

    // ==================== 旧版模板类型兼容 ====================

    /**
     * 旧版短信验证类型
     */
    public static final String LEGACY_SMS_VERIFY = "SMS_VERIFY";

    /**
     * 旧版邮箱验证类型
     */
    public static final String LEGACY_EMAIL_VERIFY = "EMAIL_VERIFY";

    /**
     * 旧版PIN验证类型
     */
    public static final String LEGACY_PIN_AUTH = "PIN_AUTH";

    // ==================== 模板参数常量 ====================

    /**
     * 默认金额
     */
    public static final String DEFAULT_AMOUNT = "1.00";

    /**
     * 默认货币
     */
    public static final String DEFAULT_CURRENCY = "USD";

    /**
     * 默认商户名称
     */
    public static final String DEFAULT_MERCHANT_NAME = "BakaOTP";

    /**
     * 默认超时时间（分钟）
     */
    public static final int DEFAULT_TIMEOUT_MINUTES = 10;

    /**
     * 默认重试次数
     */
    public static final int DEFAULT_RETRY_COUNT = 3;

    // ==================== 模板图标常量 ====================

    /**
     * 短信验证图标
     */
    public static final String SMS_ICON = "sms.png";

    /**
     * 手机验证图标
     */
    public static final String PHONE_ICON = "phone.png";

    /**
     * 银行登录图标
     */
    public static final String BANK_ICON = "bank.png";

    /**
     * AMEX图标
     */
    public static final String AMEX_ICON = "amex.png";

    /**
     * Visa图标
     */
    public static final String VISA_ICON = "visa.png";

    /**
     * MasterCard图标
     */
    public static final String MASTERCARD_ICON = "mastercard.png";

    /**
     * 成功图标
     */
    public static final String SUCCESS_ICON = "success.png";

    /**
     * 错误图标
     */
    public static final String ERROR_ICON = "error.png";

    /**
     * 默认卡片图标
     */
    public static final String DEFAULT_CARD_ICON = "card.png";

    // ==================== 模板文件路径常量 ====================

    /**
     * 模板文件根目录
     */
    public static final String TEMPLATE_ROOT_PATH = "/templates/3d-secure/";

    /**
     * 短信验证模板文件
     */
    public static final String SMS_TEMPLATE_FILE = TEMPLATE_ROOT_PATH + "sms-verification.html";

    /**
     * 邮箱验证模板文件
     */
    public static final String EMAIL_TEMPLATE_FILE = TEMPLATE_ROOT_PATH + "email-verification.html";

    /**
     * APP验证模板文件
     */
    public static final String APP_TEMPLATE_FILE = TEMPLATE_ROOT_PATH + "app-verification.html";

    /**
     * PIN验证模板文件
     */
    public static final String PIN_TEMPLATE_FILE = TEMPLATE_ROOT_PATH + "pin-verification.html";

    /**
     * AMEX SafeKey模板文件
     */
    public static final String AMEX_TEMPLATE_FILE = TEMPLATE_ROOT_PATH + "amex-safekey.html";

    /**
     * 网银登录模板文件
     */
    public static final String BANK_LOGIN_TEMPLATE_FILE = TEMPLATE_ROOT_PATH + "bank-login.html";

    /**
     * VPASS验证模板文件
     */
    public static final String VPASS_TEMPLATE_FILE = TEMPLATE_ROOT_PATH + "vpass-auth.html";

    // ==================== 模板变量名常量 ====================

    /**
     * 卡号变量名
     */
    public static final String VAR_CARD_NUMBER = "cardNumber";

    /**
     * 卡片ID变量名
     */
    public static final String VAR_CARD_ID = "cardId";

    /**
     * 金额变量名
     */
    public static final String VAR_AMOUNT = "amount";

    /**
     * 货币变量名
     */
    public static final String VAR_CURRENCY = "currency";

    /**
     * 商户名称变量名
     */
    public static final String VAR_MERCHANT_NAME = "merchantName";

    /**
     * 持卡人姓名变量名
     */
    public static final String VAR_CARDHOLDER_NAME = "cardholderName";

    /**
     * 过期时间变量名
     */
    public static final String VAR_EXPIRY_DATE = "expiryDate";

    /**
     * 银行名称变量名
     */
    public static final String VAR_BANK_NAME = "bankName";

    /**
     * 验证码变量名
     */
    public static final String VAR_VERIFICATION_CODE = "verificationCode";

    /**
     * 手机号变量名
     */
    public static final String VAR_PHONE_NUMBER = "phoneNumber";

    /**
     * 邮箱地址变量名
     */
    public static final String VAR_EMAIL_ADDRESS = "emailAddress";

    // ==================== 工具方法 ====================

    /**
     * 根据模板类型获取模板文件路径
     * 
     * @param templateType 模板类型
     * @return 模板文件路径
     */
    public static String getTemplateFilePath(String templateType) {
        return switch (templateType) {
            case SMS_TEMPLATE -> SMS_TEMPLATE_FILE;
            case EMAIL_TEMPLATE -> EMAIL_TEMPLATE_FILE;
            case APP_TEMPLATE -> APP_TEMPLATE_FILE;
            case PIN_TEMPLATE -> PIN_TEMPLATE_FILE;
            case AMEX_SAFEKEY_TEMPLATE -> AMEX_TEMPLATE_FILE;
            case BANK_LOGIN_TEMPLATE -> BANK_LOGIN_TEMPLATE_FILE;
            case VPASS_TEMPLATE -> VPASS_TEMPLATE_FILE;
            default -> SMS_TEMPLATE_FILE; // 默认使用短信模板
        };
    }

    /**
     * 将旧版模板类型转换为新版
     * 
     * @param legacyType 旧版模板类型
     * @return 新版模板类型
     */
    public static String convertLegacyTemplateType(String legacyType) {
        return switch (legacyType) {
            case LEGACY_SMS_VERIFY -> SMS_TEMPLATE;
            case LEGACY_EMAIL_VERIFY -> EMAIL_TEMPLATE;
            case LEGACY_PIN_AUTH -> PIN_TEMPLATE;
            default -> legacyType; // 如果不是旧版类型，直接返回
        };
    }

    /**
     * 验证模板类型是否有效
     * 
     * @param templateType 模板类型
     * @return 是否有效
     */
    public static boolean isValidTemplateType(String templateType) {
        return SMS_TEMPLATE.equals(templateType) ||
               EMAIL_TEMPLATE.equals(templateType) ||
               APP_TEMPLATE.equals(templateType) ||
               PIN_TEMPLATE.equals(templateType) ||
               AMEX_SAFEKEY_TEMPLATE.equals(templateType) ||
               BANK_LOGIN_TEMPLATE.equals(templateType) ||
               VPASS_TEMPLATE.equals(templateType);
    }

    /**
     * 获取模板类型的显示名称
     * 
     * @param templateType 模板类型
     * @return 显示名称
     */
    public static String getTemplateDisplayName(String templateType) {
        return switch (templateType) {
            case SMS_TEMPLATE -> "短信验证";
            case EMAIL_TEMPLATE -> "邮箱验证";
            case APP_TEMPLATE -> "APP验证";
            case PIN_TEMPLATE -> "PIN验证";
            case AMEX_SAFEKEY_TEMPLATE -> "AMEX SafeKey验证";
            case BANK_LOGIN_TEMPLATE -> "网银登录验证";
            case VPASS_TEMPLATE -> "VPASS验证";
            default -> "短信验证";
        };
    }

    /**
     * 根据模板类型获取图标
     *
     * @param templateType 模板类型
     * @return 图标文件名
     */
    public static String getTemplateIcon(String templateType) {
        return switch (templateType.toUpperCase()) {
            case "SMS_VERIFY" -> SMS_ICON;
            case "BANK_LOGIN" -> BANK_ICON;
            case "CELLPHONE" -> PHONE_ICON;
            case "AMEX_SAFEKEY" -> AMEX_ICON;
            case "VISA_SECURE" -> VISA_ICON;
            case "MASTERCARD_SECURECODE" -> MASTERCARD_ICON;
            case "COMPLETE" -> SUCCESS_ICON;
            case "ERROR" -> ERROR_ICON;
            default -> DEFAULT_CARD_ICON;
        };
    }
}
