package admin.service;

import domain.entity.Order;
import domain.entity.PaymentTransaction;
import reactor.core.publisher.Mono;
import java.util.Map;
import java.util.List;

public interface DashboardService {
    Mono<Map<String, Object>> getDashboardStats();
    List<Order> getOrders(Order.OrderStatus status, int page, int size);
    List<PaymentTransaction> getTransactions(PaymentTransaction.TransactionStatus status, int page, int size);
    Mono<Map<String, Object>> addCardToBlacklist(String cardNumber, String reason);
    void removeCardFromBlacklist(String id);
    Mono<List<Map<String, Object>>> getBlacklistedCards(int page, int size);
    Map<String, Object> getOrderStats();
}