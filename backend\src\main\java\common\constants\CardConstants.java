package common.constants;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 卡片相关常量类
 * 集中管理所有卡片相关的硬编码值，消除魔法数字和字符串
 */
public final class CardConstants {

    // 防止实例化
    private CardConstants() {
        throw new UnsupportedOperationException("常量类不能被实例化");
    }

    // ==================== 卡号前缀常量 ====================

    /**
     * Visa卡前缀
     */
    public static final String VISA_PREFIX = "4";

    /**
     * MasterCard卡前缀
     */
    public static final String MASTERCARD_PREFIX = "5";

    /**
     * American Express卡前缀
     */
    public static final String AMEX_PREFIX = "3";

    /**
     * Discover卡前缀
     */
    public static final String DISCOVER_PREFIX = "6";

    /**
     * JCB卡前缀
     */
    public static final String JCB_PREFIX = "35";

    /**
     * Diners Club卡前缀
     */
    public static final String DINERS_PREFIX = "30";

    /**
     * UnionPay卡前缀
     */
    public static final String UNIONPAY_PREFIX = "62";

    // ==================== 卡片类型常量 ====================

    /**
     * Visa卡类型
     */
    public static final String CARD_TYPE_VISA = "VISA";

    /**
     * MasterCard卡类型
     */
    public static final String CARD_TYPE_MASTERCARD = "MASTERCARD";

    /**
     * American Express卡类型
     */
    public static final String CARD_TYPE_AMEX = "AMEX";

    /**
     * Discover卡类型
     */
    public static final String CARD_TYPE_DISCOVER = "DISCOVER";

    /**
     * JCB卡类型
     */
    public static final String CARD_TYPE_JCB = "JCB";

    /**
     * Diners Club卡类型
     */
    public static final String CARD_TYPE_DINERS = "DINERS";

    /**
     * 未知卡类型
     */
    public static final String CARD_TYPE_UNKNOWN = "UNKNOWN";

    /**
     * 其他卡类型
     */
    public static final String CARD_TYPE_OTHER = "OTHER";

    /**
     * 默认卡类型
     */
    public static final String DEFAULT_CARD_TYPE = "CREDIT";

    /**
     * 默认卡等级
     */
    public static final String DEFAULT_CARD_LEVEL = "CLASSIC";

    // ==================== 卡类型检测模式映射 ====================

    /**
     * 卡类型前缀映射（按优先级排序，长前缀优先）
     */
    public static final Map<String, String> CARD_TYPE_PATTERNS = new LinkedHashMap<>();

    static {
        // 按前缀长度降序排列，确保更具体的前缀优先匹配
        CARD_TYPE_PATTERNS.put(JCB_PREFIX, CARD_TYPE_JCB);           // "35"
        CARD_TYPE_PATTERNS.put(DINERS_PREFIX, CARD_TYPE_DINERS);     // "30"
        CARD_TYPE_PATTERNS.put(DISCOVER_PREFIX, CARD_TYPE_DISCOVER); // "6"
        CARD_TYPE_PATTERNS.put(MASTERCARD_PREFIX, CARD_TYPE_MASTERCARD); // "5"
        CARD_TYPE_PATTERNS.put(VISA_PREFIX, CARD_TYPE_VISA);         // "4"
        CARD_TYPE_PATTERNS.put(AMEX_PREFIX, CARD_TYPE_AMEX);         // "3"
    }

    // ==================== 卡号长度常量 ====================

    /**
     * Visa卡号长度
     */
    public static final int VISA_LENGTH = 16;

    /**
     * MasterCard卡号长度
     */
    public static final int MASTERCARD_LENGTH = 16;

    /**
     * American Express卡号长度
     */
    public static final int AMEX_LENGTH = 15;

    /**
     * Discover卡号长度
     */
    public static final int DISCOVER_LENGTH = 16;

    /**
     * JCB卡号长度
     */
    public static final int JCB_LENGTH = 16;

    /**
     * Diners Club卡号长度
     */
    public static final int DINERS_LENGTH = 14;

    /**
     * 最小卡号长度
     */
    public static final int MIN_CARD_LENGTH = 13;

    /**
     * 最大卡号长度
     */
    public static final int MAX_CARD_LENGTH = 19;

    // ==================== CVV长度常量 ====================

    /**
     * 标准CVV长度
     */
    public static final int STANDARD_CVV_LENGTH = 3;

    /**
     * American Express CVV长度
     */
    public static final int AMEX_CVV_LENGTH = 4;

    // ==================== 过期时间常量 ====================

    /**
     * 最小过期月份
     */
    public static final int MIN_EXPIRY_MONTH = 1;

    /**
     * 最大过期月份
     */
    public static final int MAX_EXPIRY_MONTH = 12;

    /**
     * 过期年份偏移量（当前年份+20年）
     */
    public static final int EXPIRY_YEAR_OFFSET = 20;

    // ==================== BIN码相关常量 ====================

    /**
     * BIN码最小长度（银行识别码）
     */
    public static final int MIN_BIN_LENGTH = 6;

    // ==================== 工具方法 ====================

    /**
     * 根据卡号前缀确定卡片类型
     * 
     * @param cardNumber 卡号
     * @return 卡片类型
     */
    public static String determineCardType(String cardNumber) {
        if (cardNumber == null || cardNumber.isEmpty()) {
            return CARD_TYPE_UNKNOWN;
        }

        String cleanCardNumber = cardNumber.replaceAll("[\\s-]", "");

        // 使用Map查找替代多重if-else
        return CARD_TYPE_PATTERNS.entrySet().stream()
                .filter(entry -> cleanCardNumber.startsWith(entry.getKey()))
                .map(Map.Entry::getValue)
                .findFirst()
                .orElse(CARD_TYPE_OTHER);
    }

    /**
     * 获取卡片类型对应的CVV长度
     * 
     * @param cardType 卡片类型
     * @return CVV长度
     */
    public static int getCvvLength(String cardType) {
        if (CARD_TYPE_AMEX.equals(cardType)) {
            return AMEX_CVV_LENGTH;
        } else {
            return STANDARD_CVV_LENGTH;
        }
    }

    /**
     * 获取卡片类型对应的标准长度
     * 
     * @param cardType 卡片类型
     * @return 卡号长度
     */
    public static int getCardLength(String cardType) {
        return switch (cardType) {
            case CARD_TYPE_VISA -> VISA_LENGTH;
            case CARD_TYPE_MASTERCARD -> MASTERCARD_LENGTH;
            case CARD_TYPE_AMEX -> AMEX_LENGTH;
            case CARD_TYPE_DISCOVER -> DISCOVER_LENGTH;
            case CARD_TYPE_JCB -> JCB_LENGTH;
            case CARD_TYPE_DINERS -> DINERS_LENGTH;
            default -> VISA_LENGTH; // 默认长度
        };
    }

    /**
     * 验证卡号长度是否有效
     * 
     * @param cardNumber 卡号
     * @return 是否有效
     */
    public static boolean isValidCardLength(String cardNumber) {
        if (cardNumber == null) {
            return false;
        }
        String cleanCardNumber = cardNumber.replaceAll("[\\s-]", "");
        int length = cleanCardNumber.length();
        return length >= MIN_CARD_LENGTH && length <= MAX_CARD_LENGTH;
    }

    /**
     * 验证过期月份是否有效
     * 
     * @param month 月份
     * @return 是否有效
     */
    public static boolean isValidExpiryMonth(int month) {
        return month >= MIN_EXPIRY_MONTH && month <= MAX_EXPIRY_MONTH;
    }

    /**
     * 验证过期年份是否有效
     * 
     * @param year 年份
     * @return 是否有效
     */
    public static boolean isValidExpiryYear(int year) {
        int currentYear = java.time.Year.now().getValue();
        return year >= currentYear && year <= currentYear + EXPIRY_YEAR_OFFSET;
    }
}
