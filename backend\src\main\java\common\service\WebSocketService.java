package common.service;

import domain.entity.Order;
import domain.entity.PaymentTransaction;

//WebSocket消息推送服务接口
public interface WebSocketService {
    //通知前端有新订单
    void notifyNewOrder(Order order);

    //通知前端订单状态发生变化
    void notifyOrderStatusChange(Order order);

    //通知前端有新交易
    void notifyNewTransaction(PaymentTransaction transaction);

    //通知前端交易状态发生变化
    void notifyTransactionStatusChange(PaymentTransaction transaction);

    //通知前端OTP验证码（如邮箱推送）
    void notifyOtpVerification(String email, String otpCode);

    //广播消息给所有连接的客户端
    void broadcastMessage(java.util.Map<String, Object> message);

    //广播域名更新消息
    void broadcastDomainUpdate(domain.entity.Domain domain);

    //广播域名删除消息
    void broadcastDomainDelete(Long domainId);
}