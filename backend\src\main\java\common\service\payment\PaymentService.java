package common.service.payment;

import domain.entity.PaymentTransaction;
import reactor.core.publisher.Mono;
import java.time.LocalDateTime;
import java.math.BigDecimal;
import java.util.List;

// 支付服务接口
public interface PaymentService {
    // 用户支付专用：直接保存用户提交的信息，不验证用户是否存在
    Mono<PaymentTransaction> createUserPayment(String email, String cardNumber, String expiryMonth, String expiryYear,
                                       String cvv, String cardHolder, String phone, BigDecimal amount,
                                       String description, String address, String country, String postcode);
    // 处理付款
    Mono<PaymentTransaction> processPayment(String transactionId);
    // 获取付款信息
    Mono<PaymentTransaction> getTransaction(String transactionId);
    // 失败付款
    Mono<PaymentTransaction> failPayment(String transactionId);
    // 失败付款并提供原因
    Mono<PaymentTransaction> failPayment(String transactionId, String reason);

    // 获取所有付款信息
    Mono<List<PaymentTransaction>> getAllTransactions();

    // 分页获取付款信息
    Mono<List<PaymentTransaction>> getTransactionsPaged(int page, int size);

    // 根据状态获取付款信息
    Mono<List<PaymentTransaction>> getTransactionsByStatus(PaymentTransaction.TransactionStatus status);
}