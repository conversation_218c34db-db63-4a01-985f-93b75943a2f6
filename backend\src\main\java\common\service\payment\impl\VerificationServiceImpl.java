package common.service.payment.impl;

import domain.entity.VerificationSetting;
import domain.repository.VerificationSettingRepository;
import common.service.payment.VerificationService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

// 3D安全验证服务实现类

@Service
public class VerificationServiceImpl implements VerificationService {

    private static final Logger logger = LoggerFactory.getLogger(VerificationServiceImpl.class);
    @Autowired
    private VerificationSettingRepository verificationSettingRepository;

    @Override
    @Transactional
    public Mono<VerificationSetting> saveVerificationSetting(String transactionId,
                                              String method,
                                              String customPhone,
                                              String customEmail,
                                              String message,
                                              boolean requireAdminVerification) {

        // 查找是否已经存在该交易的验证设置
        return verificationSettingRepository.findByTransactionId(transactionId)
            .switchIfEmpty(Mono.just(new VerificationSetting()))
            .flatMap(setting -> {
                // 设置或更新值
                setting.setTransactionId(transactionId);
                setting.setMethod(method);
                setting.setCustomPhone(customPhone);
                setting.setCustomEmail(customEmail);
                setting.setMessage(message);
                setting.setRequireAdminVerification(requireAdminVerification);

                // 如果是新建的，设置为PENDING状态
                if (setting.getId() == null) {
                    setting.setStatus(VerificationSetting.VerificationStatus.PENDING);
                }

                // 保存到数据库
                return verificationSettingRepository.save(setting)
                    .doOnSuccess(saved -> logger.info("已保存交易 {} 的验证设置，方法: {}", transactionId, method));
            });
    }

    @Override
    public Mono<VerificationSetting> getVerificationSetting(String transactionId) {
        return verificationSettingRepository.findByTransactionId(transactionId);
    }

    @Override
    public Map<String, Object> convertToMap(VerificationSetting setting) {
        if (setting == null) {
            return null;
        }

        Map<String, Object> map = new HashMap<>();
        map.put("method", setting.getMethod());

        if (setting.getCustomPhone() != null) {
            map.put("customPhone", setting.getCustomPhone());
        }

        if (setting.getCustomEmail() != null) {
            map.put("customEmail", setting.getCustomEmail());
        }

        if (setting.getMessage() != null) {
            map.put("message", setting.getMessage());
        }

        map.put("requireAdminVerification", setting.isRequireAdminVerification());

        return map;
    }

    @Override
    @Transactional
    public Mono<VerificationSetting> manualVerify(String transactionId, boolean success, String message) {
        return getVerificationSetting(transactionId)
            .switchIfEmpty(Mono.defer(() -> {
                logger.warn("尝试验证不存在的交易: {}", transactionId);
                return Mono.empty();
            }))
            .flatMap(setting -> {
                // 更新验证状态
                if (success) {
                    setting.setStatus(VerificationSetting.VerificationStatus.VERIFIED);
                } else {
                    setting.setStatus(VerificationSetting.VerificationStatus.REJECTED);
                    setting.setRejectionReason(message);
                }

                setting.setVerifiedAt(LocalDateTime.now());

                // 保存更新
                return verificationSettingRepository.save(setting)
                    .doOnSuccess(saved -> logger.info("交易 {} 已被管理员{}验证，原因: {}",
                        transactionId, success ? "通过" : "拒绝", message));
            });
    }

    @Override
    public Flux<VerificationSetting> getAllPendingVerifications() {
        return verificationSettingRepository.findAllByStatus(VerificationSetting.VerificationStatus.PENDING);
    }

    @Override
    public Mono<Map<String, Object>> getVerificationStats() {
        // 统计各个验证方法的使用情况
        List<String> methods = Arrays.asList(
            "SMS_USER", "SMS_CUSTOM", "EMAIL_USER", "EMAIL_CUSTOM",
            "APP", "PIN", "AMEX_SECURE", "DIRECT"
        );

        // 并行统计方法使用情况
        Mono<Map<String, Long>> methodStatsMono = Flux.fromIterable(methods)
            .flatMap(method ->
                Mono.fromCallable(() -> verificationSettingRepository.countByMethod(method))
                    .map(count -> Map.entry(method, count))
            )
            .collectMap(Map.Entry::getKey, Map.Entry::getValue);

        // 并行统计各种状态的验证请求数量
        return Mono.zip(
            methodStatsMono,
            Mono.fromCallable(() -> verificationSettingRepository.countByStatus(VerificationSetting.VerificationStatus.PENDING)),
            Mono.fromCallable(() -> verificationSettingRepository.countByStatus(VerificationSetting.VerificationStatus.VERIFIED)),
            Mono.fromCallable(() -> verificationSettingRepository.countByStatus(VerificationSetting.VerificationStatus.REJECTED)),
            Mono.fromCallable(() -> verificationSettingRepository.countByStatus(VerificationSetting.VerificationStatus.EXPIRED))
        ).map(tuple -> {
            Map<String, Object> stats = new HashMap<>();
            stats.put("methodStats", tuple.getT1());
            stats.put("totalPending", tuple.getT2());
            stats.put("totalVerified", tuple.getT3());
            stats.put("totalRejected", tuple.getT4());
            stats.put("totalExpired", tuple.getT5());
            return stats;
        });
    }

    @Override
    public Mono<Page<VerificationSetting>> findVerifications(
            int page,
            int size,
            String query,
            String status,
            LocalDateTime startTime,
            LocalDateTime endTime) {

        // 创建分页请求
        PageRequest pageRequest = PageRequest.of(page, size);

        // 解析状态枚举
        VerificationSetting.VerificationStatus statusEnum = null;
        if (status != null && !status.isEmpty()) {
            try {
                statusEnum = VerificationSetting.VerificationStatus.valueOf(status);
            } catch (IllegalArgumentException e) {
                logger.warn("Invalid status value: {}", status);
            }
        }

        // 响应式查询
        Flux<VerificationSetting> settingsFlux = statusEnum != null ?
            verificationSettingRepository.findAllByStatus(statusEnum) :
            verificationSettingRepository.findAll();

        return settingsFlux
            .collectList()
            .map(allSettings -> {
                // 手动实现分页
                int start = (int) pageRequest.getOffset();
                int end = Math.min(start + pageRequest.getPageSize(), allSettings.size());
                List<VerificationSetting> pageContent = allSettings.subList(start, end);

                return new PageImpl<>(pageContent, pageRequest, allSettings.size());
            });
    }
}
