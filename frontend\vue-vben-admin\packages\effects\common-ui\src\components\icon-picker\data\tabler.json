{"prefix": "tabler", "total": 5944, "title": "Tabler Icons", "uncategorized": ["a-b", "a-b-2", "a-b-off", "abacus", "abacus-off", "abc", "access-point", "access-point-off", "accessible", "accessible-filled", "accessible-off", "activity", "activity-heartbeat", "ad", "ad-2", "ad-circle", "ad-circle-filled", "ad-circle-off", "ad-filled", "ad-off", "address-book", "address-book-off", "adjustments", "adjustments-alt", "adjustments-bolt", "adjustments-cancel", "adjustments-check", "adjustments-code", "adjustments-cog", "adjustments-dollar", "adjustments-down", "adjustments-exclamation", "adjustments-filled", "adjustments-heart", "adjustments-horizontal", "adjustments-minus", "adjustments-off", "adjustments-pause", "adjustments-pin", "adjustments-plus", "adjustments-question", "adjustments-search", "adjustments-share", "adjustments-spark", "adjustments-star", "adjustments-up", "adjustments-x", "aerial-lift", "aerial-lift-filled", "affiliate", "affiliate-filled", "ai", "air-balloon", "air-balloon-filled", "air-conditioning", "air-conditioning-disabled", "air-traffic-control", "alarm", "alarm-average", "alarm-filled", "alarm-minus", "alarm-minus-filled", "alarm-off", "alarm-plus", "alarm-plus-filled", "alarm-smoke", "alarm-snooze", "alarm-snooze-filled", "album", "album-off", "alert-circle", "alert-circle-filled", "alert-circle-off", "alert-hexagon", "alert-hexagon-filled", "alert-hexagon-off", "alert-octagon", "alert-octagon-filled", "alert-small", "alert-small-off", "alert-square", "alert-square-filled", "alert-square-rounded", "alert-square-rounded-filled", "alert-square-rounded-off", "alert-triangle", "alert-triangle-filled", "alert-triangle-off", "alien", "alien-filled", "align-box-bottom-center", "align-box-bottom-center-filled", "align-box-bottom-left", "align-box-bottom-left-filled", "align-box-bottom-right", "align-box-bottom-right-filled", "align-box-center-bottom", "align-box-center-middle", "align-box-center-middle-filled", "align-box-center-stretch", "align-box-center-top", "align-box-left-bottom", "align-box-left-bottom-filled", "align-box-left-middle", "align-box-left-middle-filled", "align-box-left-stretch", "align-box-left-top", "align-box-left-top-filled", "align-box-right-bottom", "align-box-right-bottom-filled", "align-box-right-middle", "align-box-right-middle-filled", "align-box-right-stretch", "align-box-right-top", "align-box-right-top-filled", "align-box-top-center", "align-box-top-center-filled", "align-box-top-left", "align-box-top-left-filled", "align-box-top-right", "align-box-top-right-filled", "align-center", "align-justified", "align-left", "align-left-2", "align-right", "align-right-2", "alpha", "alphabet-arabic", "alphabet-bangla", "alphabet-cyrillic", "alphabet-greek", "alphabet-hebrew", "alphabet-korean", "alphabet-latin", "alphabet-thai", "alt", "ambulance", "ampersand", "analyze", "analyze-filled", "analyze-off", "anchor", "anchor-off", "angle", "ankh", "antenna", "antenna-bars-1", "antenna-bars-2", "antenna-bars-3", "antenna-bars-4", "antenna-bars-5", "antenna-bars-off", "antenna-off", "aperture", "aperture-off", "api", "api-app", "api-app-off", "api-off", "app-window", "app-window-filled", "apple", "apple-filled", "apps", "apps-filled", "apps-off", "archery-arrow", "archive", "archive-filled", "archive-off", "armchair", "armchair-2", "armchair-2-off", "armchair-off", "arrow-autofit-content", "arrow-autofit-content-filled", "arrow-autofit-down", "arrow-autofit-down-filled", "arrow-autofit-height", "arrow-autofit-height-filled", "arrow-autofit-left", "arrow-autofit-left-filled", "arrow-autofit-right", "arrow-autofit-right-filled", "arrow-autofit-up", "arrow-autofit-up-filled", "arrow-autofit-width", "arrow-autofit-width-filled", "arrow-back", "arrow-back-up", "arrow-back-up-double", "arrow-badge-down", "arrow-badge-down-filled", "arrow-badge-left", "arrow-badge-left-filled", "arrow-badge-right", "arrow-badge-right-filled", "arrow-badge-up", "arrow-badge-up-filled", "arrow-bar-both", "arrow-bar-down", "arrow-bar-left", "arrow-bar-right", "arrow-bar-to-down", "arrow-bar-to-down-dashed", "arrow-bar-to-left", "arrow-bar-to-left-dashed", "arrow-bar-to-right", "arrow-bar-to-right-dashed", "arrow-bar-to-up", "arrow-bar-to-up-dashed", "arrow-bar-up", "arrow-bear-left", "arrow-bear-left-2", "arrow-bear-right", "arrow-bear-right-2", "arrow-big-down", "arrow-big-down-filled", "arrow-big-down-line", "arrow-big-down-line-filled", "arrow-big-down-lines", "arrow-big-down-lines-filled", "arrow-big-left", "arrow-big-left-filled", "arrow-big-left-line", "arrow-big-left-line-filled", "arrow-big-left-lines", "arrow-big-left-lines-filled", "arrow-big-right", "arrow-big-right-filled", "arrow-big-right-line", "arrow-big-right-line-filled", "arrow-big-right-lines", "arrow-big-right-lines-filled", "arrow-big-up", "arrow-big-up-filled", "arrow-big-up-line", "arrow-big-up-line-filled", "arrow-big-up-lines", "arrow-big-up-lines-filled", "arrow-bounce", "arrow-capsule", "arrow-curve-left", "arrow-curve-right", "arrow-down", "arrow-down-bar", "arrow-down-circle", "arrow-down-circle-filled", "arrow-down-dashed", "arrow-down-from-arc", "arrow-down-left", "arrow-down-left-circle", "arrow-down-rhombus", "arrow-down-rhombus-filled", "arrow-down-right", "arrow-down-right-circle", "arrow-down-square", "arrow-down-square-filled", "arrow-down-tail", "arrow-down-to-arc", "arrow-elbow-left", "arrow-elbow-right", "arrow-fork", "arrow-forward", "arrow-forward-up", "arrow-forward-up-double", "arrow-guide", "arrow-guide-filled", "arrow-iteration", "arrow-left", "arrow-left-bar", "arrow-left-circle", "arrow-left-circle-filled", "arrow-left-dashed", "arrow-left-from-arc", "arrow-left-rhombus", "arrow-left-rhombus-filled", "arrow-left-right", "arrow-left-square", "arrow-left-square-filled", "arrow-left-tail", "arrow-left-to-arc", "arrow-loop-left", "arrow-loop-left-2", "arrow-loop-right", "arrow-loop-right-2", "arrow-merge", "arrow-merge-alt-left", "arrow-merge-alt-right", "arrow-merge-both", "arrow-merge-left", "arrow-merge-right", "arrow-move-down", "arrow-move-down-filled", "arrow-move-left", "arrow-move-left-filled", "arrow-move-right", "arrow-move-right-filled", "arrow-move-up", "arrow-move-up-filled", "arrow-narrow-down", "arrow-narrow-down-dashed", "arrow-narrow-left", "arrow-narrow-left-dashed", "arrow-narrow-right", "arrow-narrow-right-dashed", "arrow-narrow-up", "arrow-narrow-up-dashed", "arrow-ramp-left", "arrow-ramp-left-2", "arrow-ramp-left-3", "arrow-ramp-right", "arrow-ramp-right-2", "arrow-ramp-right-3", "arrow-right", "arrow-right-bar", "arrow-right-circle", "arrow-right-circle-filled", "arrow-right-dashed", "arrow-right-from-arc", "arrow-right-rhombus", "arrow-right-rhombus-filled", "arrow-right-square", "arrow-right-square-filled", "arrow-right-tail", "arrow-right-to-arc", "arrow-rotary-first-left", "arrow-rotary-first-right", "arrow-rotary-last-left", "arrow-rotary-last-right", "arrow-rotary-left", "arrow-rotary-right", "arrow-rotary-straight", "arrow-roundabout-left", "arrow-roundabout-right", "arrow-sharp-turn-left", "arrow-sharp-turn-right", "arrow-up", "arrow-up-bar", "arrow-up-circle", "arrow-up-circle-filled", "arrow-up-dashed", "arrow-up-from-arc", "arrow-up-left", "arrow-up-left-circle", "arrow-up-rhombus", "arrow-up-rhombus-filled", "arrow-up-right", "arrow-up-right-circle", "arrow-up-square", "arrow-up-square-filled", "arrow-up-tail", "arrow-up-to-arc", "arrow-wave-left-down", "arrow-wave-left-up", "arrow-wave-right-down", "arrow-wave-right-up", "arrow-zig-zag", "arrows-cross", "arrows-diagonal", "arrows-diagonal-2", "arrows-diagonal-minimize", "arrows-diagonal-minimize-2", "arrows-diff", "arrows-double-ne-sw", "arrows-double-nw-se", "arrows-double-se-nw", "arrows-double-sw-ne", "arrows-down", "arrows-down-up", "arrows-exchange", "arrows-exchange-2", "arrows-horizontal", "arrows-join", "arrows-join-2", "arrows-left", "arrows-left-down", "arrows-left-right", "arrows-maximize", "arrows-minimize", "arrows-move", "arrows-move-horizontal", "arrows-move-vertical", "arrows-random", "arrows-right", "arrows-right-down", "arrows-right-left", "arrows-shuffle", "arrows-shuffle-2", "arrows-sort", "arrows-split", "arrows-split-2", "arrows-transfer-down", "arrows-transfer-up", "arrows-transfer-up-down", "arrows-up", "arrows-up-down", "arrows-up-left", "arrows-up-right", "arrows-vertical", "artboard", "artboard-filled", "artboard-off", "article", "article-filled", "article-off", "aspect-ratio", "aspect-ratio-filled", "aspect-ratio-off", "assembly", "assembly-filled", "assembly-off", "asset", "asset-filled", "asterisk", "asterisk-simple", "at", "at-off", "atom", "atom-2", "atom-2-filled", "atom-off", "augmented-reality", "augmented-reality-2", "augmented-reality-off", "auth-2fa", "automatic-gearbox", "automatic-gearbox-filled", "automation", "avocado", "award", "award-filled", "award-off", "axe", "axis-x", "axis-y", "baby-bottle", "baby-carriage", "baby-carriage-filled", "background", "backhoe", "backpack", "backpack-off", "backslash", "backspace", "backspace-filled", "badge", "badge-2k", "badge-3d", "badge-3d-filled", "badge-3k", "badge-4k", "badge-4k-filled", "badge-5k", "badge-8k", "badge-8k-filled", "badge-ad", "badge-ad-filled", "badge-ad-off", "badge-ar", "badge-ar-filled", "badge-cc", "badge-cc-filled", "badge-filled", "badge-hd", "badge-hd-filled", "badge-off", "badge-sd", "badge-sd-filled", "badge-tm", "badge-tm-filled", "badge-vo", "badge-vo-filled", "badge-vr", "badge-vr-filled", "badge-wc", "badge-wc-filled", "badges", "badges-filled", "badges-off", "baguette", "ball-american-football", "ball-american-football-off", "ball-baseball", "ball-basketball", "ball-bowling", "ball-football", "ball-football-off", "ball-tennis", "ball-volleyball", "balloon", "balloon-filled", "balloon-off", "ballpen", "ballpen-filled", "ballpen-off", "ban", "bandage", "bandage-filled", "bandage-off", "barbell", "barbell-filled", "barbell-off", "barcode", "barcode-off", "barrel", "barrel-off", "barrier-block", "barrier-block-filled", "barrier-block-off", "baseline", "baseline-density-large", "baseline-density-medium", "baseline-density-small", "basket", "basket-bolt", "basket-cancel", "basket-check", "basket-code", "basket-cog", "basket-discount", "basket-dollar", "basket-down", "basket-exclamation", "basket-filled", "basket-heart", "basket-minus", "basket-off", "basket-pause", "basket-pin", "basket-plus", "basket-question", "basket-search", "basket-share", "basket-star", "basket-up", "basket-x", "bat", "bath", "bath-filled", "bath-off", "battery", "battery-1", "battery-1-filled", "battery-2", "battery-2-filled", "battery-3", "battery-3-filled", "battery-4", "battery-4-filled", "battery-automotive", "battery-automotive-filled", "battery-charging", "battery-charging-2", "battery-eco", "battery-exclamation", "battery-filled", "battery-off", "battery-spark", "battery-vertical", "battery-vertical-1", "battery-vertical-1-filled", "battery-vertical-2", "battery-vertical-2-filled", "battery-vertical-3", "battery-vertical-3-filled", "battery-vertical-4", "battery-vertical-4-filled", "battery-vertical-charging", "battery-vertical-charging-2", "battery-vertical-eco", "battery-vertical-exclamation", "battery-vertical-filled", "battery-vertical-off", "beach", "beach-off", "bed", "bed-filled", "bed-flat", "bed-flat-filled", "bed-off", "beer", "beer-filled", "beer-off", "bell", "bell-bolt", "bell-cancel", "bell-check", "bell-code", "bell-cog", "bell-dollar", "bell-down", "bell-exclamation", "bell-filled", "bell-heart", "bell-minus", "bell-minus-filled", "bell-off", "bell-pause", "bell-pin", "bell-plus", "bell-plus-filled", "bell-question", "bell-ringing", "bell-ringing-2", "bell-ringing-2-filled", "bell-ringing-filled", "bell-school", "bell-search", "bell-share", "bell-star", "bell-up", "bell-x", "bell-x-filled", "bell-z", "bell-z-filled", "beta", "bible", "bike", "bike-filled", "bike-off", "binary", "binary-off", "binary-tree", "binary-tree-2", "binary-tree-2-filled", "binary-tree-filled", "binoculars", "binoculars-filled", "biohazard", "biohazard-filled", "biohazard-off", "blade", "blade-filled", "bleach", "bleach-chlorine", "bleach-no-chlorine", "bleach-off", "blend-mode", "blender", "blender-filled", "blob", "blob-filled", "blockquote", "blocks", "bluetooth", "bluetooth-connected", "bluetooth-off", "bluetooth-x", "blur", "blur-off", "bmp", "body-scan", "bold", "bold-off", "bolt", "bolt-filled", "bolt-off", "bomb", "bomb-filled", "bone", "bone-filled", "bone-off", "bong", "bong-filled", "bong-off", "book", "book-2", "book-download", "book-filled", "book-off", "book-upload", "bookmark", "bookmark-ai", "bookmark-edit", "bookmark-filled", "bookmark-minus", "bookmark-off", "bookmark-plus", "bookmark-question", "bookmarks", "bookmarks-filled", "bookmarks-off", "books", "books-off", "boom", "boom-filled", "border-all", "border-bottom", "border-bottom-plus", "border-corner-ios", "border-corner-pill", "border-corner-rounded", "border-corner-square", "border-corners", "border-horizontal", "border-inner", "border-left", "border-left-plus", "border-none", "border-outer", "border-radius", "border-right", "border-right-plus", "border-sides", "border-style", "border-style-2", "border-top", "border-top-plus", "border-vertical", "bottle", "bottle-filled", "bottle-off", "bounce-left", "bounce-left-filled", "bounce-right", "bounce-right-filled", "bow", "bow-filled", "bowl", "bowl-chopsticks", "bowl-chopsticks-filled", "bowl-filled", "bowl-spoon", "bowl-spoon-filled", "bowling", "box", "box-align-bottom", "box-align-bottom-filled", "box-align-bottom-left", "box-align-bottom-left-filled", "box-align-bottom-right", "box-align-bottom-right-filled", "box-align-left", "box-align-left-filled", "box-align-right", "box-align-right-filled", "box-align-top", "box-align-top-filled", "box-align-top-left", "box-align-top-left-filled", "box-align-top-right", "box-align-top-right-filled", "box-margin", "box-model", "box-model-2", "box-model-2-off", "box-model-off", "box-multiple", "box-multiple-0", "box-multiple-1", "box-multiple-2", "box-multiple-3", "box-multiple-4", "box-multiple-5", "box-multiple-6", "box-multiple-7", "box-multiple-8", "box-multiple-9", "box-multiple-filled", "box-off", "box-padding", "braces", "braces-off", "brackets", "brackets-angle", "brackets-angle-off", "brackets-contain", "brackets-contain-end", "brackets-contain-start", "brackets-off", "braille", "brain", "brand-4chan", "brand-abstract", "brand-adobe", "brand-adobe-after-effect", "brand-adobe-illustrator", "brand-adobe-indesign", "brand-adobe-photoshop", "brand-adobe-premier", "brand-adobe-xd", "brand-adonis-js", "brand-airbnb", "brand-airtable", "brand-algolia", "brand-alipay", "brand-alpine-js", "brand-amazon", "brand-amd", "brand-amie", "brand-amigo", "brand-among-us", "brand-android", "brand-angular", "brand-angular-filled", "brand-ansible", "brand-ao3", "brand-appgallery", "brand-apple", "brand-apple-arcade", "brand-apple-filled", "brand-apple-news", "brand-apple-podcast", "brand-appstore", "brand-arc", "brand-asana", "brand-astro", "brand-auth0", "brand-aws", "brand-azure", "brand-backbone", "brand-badoo", "brand-baidu", "brand-bandcamp", "brand-bandlab", "brand-beats", "brand-bebo", "brand-behance", "brand-bilibili", "brand-binance", "brand-bing", "brand-bitbucket", "brand-bitbucket-filled", "brand-blackberry", "brand-blender", "brand-blogger", "brand-bluesky", "brand-booking", "brand-bootstrap", "brand-bulma", "brand-bumble", "brand-bunpo", "brand-c-sharp", "brand-cake", "brand-cakephp", "brand-campaignmonitor", "brand-carbon", "brand-cashapp", "brand-chrome", "brand-cinema-4d", "brand-citymapper", "brand-cloudflare", "brand-codecov", "brand-codepen", "brand-codesandbox", "brand-cohost", "brand-coinbase", "brand-comedy-central", "brand-coreos", "brand-couchdb", "brand-couchsurfing", "brand-cpp", "brand-craft", "brand-crunchbase", "brand-css3", "brand-ctemplar", "brand-cucumber", "brand-cupra", "brand-cypress", "brand-d3", "brand-databricks", "brand-days-counter", "brand-dcos", "brand-debian", "brand-deezer", "brand-deliveroo", "brand-deno", "brand-denodo", "brand-deviantart", "brand-digg", "brand-dingtalk", "brand-discord", "brand-discord-filled", "brand-disney", "brand-disqus", "brand-django", "brand-docker", "brand-doctrine", "brand-dolby-digital", "brand-douban", "brand-dribbble", "brand-dribbble-filled", "brand-drops", "brand-drupal", "brand-edge", "brand-elastic", "brand-electronic-arts", "brand-ember", "brand-envato", "brand-etsy", "brand-evernote", "brand-facebook", "brand-facebook-filled", "brand-feedly", "brand-figma", "brand-filezilla", "brand-finder", "brand-firebase", "brand-firefox", "brand-fiverr", "brand-flickr", "brand-flightradar24", "brand-flipboard", "brand-flutter", "brand-fortnite", "brand-foursquare", "brand-framer", "brand-framer-motion", "brand-funimation", "brand-gatsby", "brand-git", "brand-github", "brand-github-copilot", "brand-github-filled", "brand-gitlab", "brand-gmail", "brand-golang", "brand-google", "brand-google-analytics", "brand-google-big-query", "brand-google-drive", "brand-google-filled", "brand-google-fit", "brand-google-home", "brand-google-maps", "brand-google-one", "brand-google-photos", "brand-google-play", "brand-google-podcasts", "brand-grammarly", "brand-graphql", "brand-gravatar", "brand-grindr", "brand-guardian", "brand-gumroad", "brand-hackerrank", "brand-hbo", "brand-headlessui", "brand-hexo", "brand-hipchat", "brand-html5", "brand-inertia", "brand-instagram", "brand-instagram-filled", "brand-intercom", "brand-itch", "brand-javascript", "brand-juejin", "brand-kako-talk", "brand-kbin", "brand-kick", "brand-kick-filled", "brand-kickstarter", "brand-kotlin", "brand-laravel", "brand-lastfm", "brand-leetcode", "brand-letterboxd", "brand-line", "brand-linkedin", "brand-linkedin-filled", "brand-linktree", "brand-linqpad", "brand-livewire", "brand-loom", "brand-mailgun", "brand-mantine", "brand-mastercard", "brand-mastodon", "brand-matrix", "brand-mcdonalds", "brand-medium", "brand-meetup", "brand-mercedes", "brand-messenger", "brand-messenger-filled", "brand-meta", "brand-metabrainz", "brand-minecraft", "brand-miniprogram", "brand-mixpanel", "brand-monday", "brand-mongodb", "brand-my-oppo", "brand-mysql", "brand-national-geographic", "brand-nem", "brand-netbeans", "brand-netease-music", "brand-netflix", "brand-nexo", "brand-nextcloud", "brand-nextjs", "brand-nodejs", "brand-nord-vpn", "brand-notion", "brand-npm", "brand-nuxt", "brand-nytimes", "brand-oauth", "brand-office", "brand-ok-ru", "brand-onedrive", "brand-onlyfans", "brand-open-source", "brand-open-source-filled", "brand-openai", "brand-openvpn", "brand-opera", "brand-opera-filled", "brand-pagekit", "brand-parsinta", "brand-patreon", "brand-patreon-filled", "brand-paypal", "brand-paypal-filled", "brand-paypay", "brand-peanut", "brand-pepsi", "brand-php", "brand-picsart", "brand-pinterest", "brand-pinterest-filled", "brand-planetscale", "brand-pnpm", "brand-pocket", "brand-polymer", "brand-powershell", "brand-printables", "brand-prisma", "brand-producthunt", "brand-pushbullet", "brand-pushover", "brand-python", "brand-qq", "brand-radix-ui", "brand-react", "brand-react-native", "brand-reason", "brand-reddit", "brand-redhat", "brand-redux", "brand-revolut", "brand-rumble", "brand-rust", "brand-safari", "brand-samsungpass", "brand-sass", "brand-sentry", "brand-sharik", "brand-shazam", "brand-shopee", "brand-sketch", "brand-sketch-filled", "brand-skype", "brand-slack", "brand-snapchat", "brand-snapchat-filled", "brand-snapseed", "brand-snowflake", "brand-socket-io", "brand-solidjs", "brand-soundcloud", "brand-spacehey", "brand-speedtest", "brand-spotify", "brand-spotify-filled", "brand-stackoverflow", "brand-stackshare", "brand-steam", "brand-steam-filled", "brand-stocktwits", "brand-storj", "brand-storybook", "brand-storytel", "brand-strava", "brand-stripe", "brand-stripe-filled", "brand-sublime-text", "brand-sugarizer", "brand-supabase", "brand-superhuman", "brand-supernova", "brand-surfshark", "brand-svelte", "brand-swift", "brand-symfony", "brand-tabler", "brand-tabler-filled", "brand-tailwind", "brand-taobao", "brand-teams", "brand-ted", "brand-telegram", "brand-terraform", "brand-tesla", "brand-tether", "brand-thingiverse", "brand-threads", "brand-threejs", "brand-tidal", "brand-tiktok", "brand-tiktok-filled", "brand-tinder", "brand-tinder-filled", "brand-topbuzz", "brand-torchain", "brand-toyota", "brand-trello", "brand-tripadvisor", "brand-tumblr", "brand-tumblr-filled", "brand-twilio", "brand-twitch", "brand-twitter", "brand-twitter-filled", "brand-typescript", "brand-uber", "brand-ubuntu", "brand-unity", "brand-unsplash", "brand-upwork", "brand-valorant", "brand-vercel", "brand-vercel-filled", "brand-vimeo", "brand-vimeo-filled", "brand-vinted", "brand-visa", "brand-visual-studio", "brand-vite", "brand-vivaldi", "brand-vk", "brand-vlc", "brand-volkswagen", "brand-vsco", "brand-vscode", "brand-vue", "brand-walmart", "brand-waze", "brand-webflow", "brand-wechat", "brand-weibo", "brand-weibo-filled", "brand-whatsapp", "brand-whatsapp-filled", "brand-wikipedia", "brand-windows", "brand-windows-filled", "brand-windy", "brand-wish", "brand-wix", "brand-wordpress", "brand-x", "brand-x-filled", "brand-xamarin", "brand-xbox", "brand-xdeep", "brand-xing", "brand-yahoo", "brand-yandex", "brand-yarn", "brand-yatse", "brand-ycombinator", "brand-youtube", "brand-youtube-filled", "brand-youtube-kids", "brand-zalando", "brand-zapier", "brand-zeit", "brand-zhihu", "brand-zoom", "brand-zulip", "brand-zwift", "bread", "bread-filled", "bread-off", "briefcase", "briefcase-2", "briefcase-2-filled", "briefcase-filled", "briefcase-off", "brightness", "brightness-2", "brightness-auto", "brightness-auto-filled", "brightness-down", "brightness-down-filled", "brightness-filled", "brightness-half", "brightness-off", "brightness-up", "brightness-up-filled", "broadcast", "broadcast-off", "browser", "browser-check", "browser-maximize", "browser-minus", "browser-off", "browser-plus", "browser-share", "browser-x", "brush", "brush-off", "bubble", "bubble-filled", "bubble-minus", "bubble-plus", "bubble-tea", "bubble-tea-2", "bubble-text", "bubble-text-filled", "bubble-x", "bucket", "bucket-droplet", "bucket-off", "bug", "bug-filled", "bug-off", "building", "building-airport", "building-arch", "building-bank", "building-bridge", "building-bridge-2", "building-bridge-2-filled", "building-broadcast-tower", "building-broadcast-tower-filled", "building-burj-al-arab", "building-carousel", "building-castle", "building-church", "building-circus", "building-cog", "building-community", "building-cottage", "building-estate", "building-factory", "building-factory-2", "building-fortress", "building-hospital", "building-lighthouse", "building-minus", "building-monument", "building-mosque", "building-off", "building-pavilion", "building-plus", "building-skyscraper", "building-stadium", "building-store", "building-tunnel", "building-warehouse", "building-wind-turbine", "buildings", "bulb", "bulb-filled", "bulb-off", "bulldozer", "burger", "bus", "bus-filled", "bus-off", "bus-stop", "businessplan", "butterfly", "butterfly-filled", "cactus", "cactus-filled", "cactus-off", "cake", "cake-off", "cake-roll", "calculator", "calculator-filled", "calculator-off", "calendar", "calendar-bolt", "calendar-cancel", "calendar-check", "calendar-clock", "calendar-code", "calendar-cog", "calendar-dollar", "calendar-dot", "calendar-down", "calendar-due", "calendar-event", "calendar-event-filled", "calendar-exclamation", "calendar-filled", "calendar-heart", "calendar-minus", "calendar-month", "calendar-month-filled", "calendar-off", "calendar-pause", "calendar-pin", "calendar-plus", "calendar-question", "calendar-repeat", "calendar-sad", "calendar-search", "calendar-share", "calendar-smile", "calendar-star", "calendar-stats", "calendar-time", "calendar-up", "calendar-user", "calendar-week", "calendar-week-filled", "calendar-x", "camera", "camera-ai", "camera-bitcoin", "camera-bolt", "camera-cancel", "camera-check", "camera-code", "camera-cog", "camera-dollar", "camera-down", "camera-exclamation", "camera-filled", "camera-heart", "camera-minus", "camera-moon", "camera-off", "camera-pause", "camera-pin", "camera-plus", "camera-question", "camera-rotate", "camera-search", "camera-selfie", "camera-share", "camera-spark", "camera-star", "camera-up", "camera-x", "camper", "campfire", "campfire-filled", "cancel", "candle", "candle-filled", "candy", "candy-off", "cane", "cannabis", "cannabis-filled", "cap-projecting", "cap-rounded", "cap-straight", "capsule", "capsule-filled", "capsule-horizontal", "capsule-horizontal-filled", "capture", "capture-filled", "capture-off", "car", "car-4wd", "car-4wd-filled", "car-crane", "car-crane-filled", "car-crash", "car-fan", "car-fan-1", "car-fan-2", "car-fan-3", "car-fan-auto", "car-fan-filled", "car-filled", "car-garage", "car-off", "car-suv", "car-suv-filled", "car-turbine", "carambola", "carambola-filled", "caravan", "caravan-filled", "cardboards", "cardboards-filled", "cardboards-off", "cards", "cards-filled", "caret-down", "caret-down-filled", "caret-left", "caret-left-filled", "caret-left-right", "caret-left-right-filled", "caret-right", "caret-right-filled", "caret-up", "caret-up-down", "caret-up-down-filled", "caret-up-filled", "carousel-horizontal", "carousel-horizontal-filled", "carousel-vertical", "carousel-vertical-filled", "carrot", "carrot-off", "cash", "cash-banknote", "cash-banknote-edit", "cash-banknote-filled", "cash-banknote-heart", "cash-banknote-minus", "cash-banknote-move", "cash-banknote-move-back", "cash-banknote-off", "cash-banknote-plus", "cash-edit", "cash-heart", "cash-minus", "cash-move", "cash-move-back", "cash-off", "cash-plus", "cash-register", "cast", "cast-off", "cat", "category", "category-2", "category-filled", "category-minus", "category-plus", "ce", "ce-off", "cell", "cell-signal-1", "cell-signal-2", "cell-signal-3", "cell-signal-4", "cell-signal-5", "cell-signal-off", "certificate", "certificate-2", "certificate-2-off", "certificate-off", "chair-director", "chalkboard", "chalkboard-off", "chalkboard-teacher", "charging-pile", "charging-pile-filled", "chart-arcs", "chart-arcs-3", "chart-area", "chart-area-filled", "chart-area-line", "chart-area-line-filled", "chart-arrows", "chart-arrows-vertical", "chart-bar", "chart-bar-off", "chart-bar-popular", "chart-bubble", "chart-bubble-filled", "chart-candle", "chart-candle-filled", "chart-circles", "chart-cohort", "chart-column", "chart-covariate", "chart-donut", "chart-donut-2", "chart-donut-3", "chart-donut-4", "chart-donut-filled", "chart-dots", "chart-dots-2", "chart-dots-2-filled", "chart-dots-3", "chart-dots-3-filled", "chart-dots-filled", "chart-funnel", "chart-funnel-filled", "chart-grid-dots", "chart-grid-dots-filled", "chart-histogram", "chart-infographic", "chart-line", "chart-pie", "chart-pie-2", "chart-pie-2-filled", "chart-pie-3", "chart-pie-3-filled", "chart-pie-4", "chart-pie-4-filled", "chart-pie-filled", "chart-pie-off", "chart-ppf", "chart-radar", "chart-sankey", "chart-scatter", "chart-scatter-3d", "chart-treemap", "check", "checkbox", "checklist", "checks", "checkup-list", "cheese", "chef-hat", "chef-hat-filled", "chef-hat-off", "cherry", "cherry-filled", "chess", "chess-bishop", "chess-bishop-filled", "chess-filled", "chess-king", "chess-king-filled", "chess-knight", "chess-knight-filled", "chess-queen", "chess-queen-filled", "chess-rook", "chess-rook-filled", "chevron-compact-down", "chevron-compact-left", "chevron-compact-right", "chevron-compact-up", "chevron-down", "chevron-down-left", "chevron-down-right", "chevron-left", "chevron-left-pipe", "chevron-right", "chevron-right-pipe", "chevron-up", "chevron-up-left", "chevron-up-right", "chevrons-down", "chevrons-down-left", "chevrons-down-right", "chevrons-left", "chevrons-right", "chevrons-up", "chevrons-up-left", "chevrons-up-right", "chisel", "christmas-ball", "christmas-tree", "christmas-tree-filled", "christmas-tree-off", "circle", "circle-arrow-down", "circle-arrow-down-filled", "circle-arrow-down-left", "circle-arrow-down-left-filled", "circle-arrow-down-right", "circle-arrow-down-right-filled", "circle-arrow-left", "circle-arrow-left-filled", "circle-arrow-right", "circle-arrow-right-filled", "circle-arrow-up", "circle-arrow-up-filled", "circle-arrow-up-left", "circle-arrow-up-left-filled", "circle-arrow-up-right", "circle-arrow-up-right-filled", "circle-caret-down", "circle-caret-down-filled", "circle-caret-left", "circle-caret-left-filled", "circle-caret-right", "circle-caret-right-filled", "circle-caret-up", "circle-caret-up-filled", "circle-check", "circle-check-filled", "circle-chevron-down", "circle-chevron-down-filled", "circle-chevron-left", "circle-chevron-left-filled", "circle-chevron-right", "circle-chevron-right-filled", "circle-chevron-up", "circle-chevron-up-filled", "circle-chevrons-down", "circle-chevrons-down-filled", "circle-chevrons-left", "circle-chevrons-left-filled", "circle-chevrons-right", "circle-chevrons-right-filled", "circle-chevrons-up", "circle-chevrons-up-filled", "circle-dashed", "circle-dashed-check", "circle-dashed-letter-a", "circle-dashed-letter-b", "circle-dashed-letter-c", "circle-dashed-letter-d", "circle-dashed-letter-e", "circle-dashed-letter-f", "circle-dashed-letter-g", "circle-dashed-letter-h", "circle-dashed-letter-i", "circle-dashed-letter-j", "circle-dashed-letter-k", "circle-dashed-letter-l", "circle-dashed-letter-m", "circle-dashed-letter-n", "circle-dashed-letter-o", "circle-dashed-letter-p", "circle-dashed-letter-q", "circle-dashed-letter-r", "circle-dashed-letter-s", "circle-dashed-letter-t", "circle-dashed-letter-u", "circle-dashed-letter-v", "circle-dashed-letter-w", "circle-dashed-letter-x", "circle-dashed-letter-y", "circle-dashed-letter-z", "circle-dashed-minus", "circle-dashed-number-0", "circle-dashed-number-1", "circle-dashed-number-2", "circle-dashed-number-3", "circle-dashed-number-4", "circle-dashed-number-5", "circle-dashed-number-6", "circle-dashed-number-7", "circle-dashed-number-8", "circle-dashed-number-9", "circle-dashed-percentage", "circle-dashed-plus", "circle-dashed-x", "circle-dot", "circle-dot-filled", "circle-dotted", "circle-dotted-letter-a", "circle-dotted-letter-b", "circle-dotted-letter-c", "circle-dotted-letter-d", "circle-dotted-letter-e", "circle-dotted-letter-f", "circle-dotted-letter-g", "circle-dotted-letter-h", "circle-dotted-letter-i", "circle-dotted-letter-j", "circle-dotted-letter-k", "circle-dotted-letter-l", "circle-dotted-letter-m", "circle-dotted-letter-n", "circle-dotted-letter-o", "circle-dotted-letter-p", "circle-dotted-letter-q", "circle-dotted-letter-r", "circle-dotted-letter-s", "circle-dotted-letter-t", "circle-dotted-letter-u", "circle-dotted-letter-v", "circle-dotted-letter-w", "circle-dotted-letter-x", "circle-dotted-letter-y", "circle-dotted-letter-z", "circle-filled", "circle-half", "circle-half-2", "circle-half-vertical", "circle-key", "circle-key-filled", "circle-letter-a", "circle-letter-a-filled", "circle-letter-b", "circle-letter-b-filled", "circle-letter-c", "circle-letter-c-filled", "circle-letter-d", "circle-letter-d-filled", "circle-letter-e", "circle-letter-e-filled", "circle-letter-f", "circle-letter-f-filled", "circle-letter-g", "circle-letter-g-filled", "circle-letter-h", "circle-letter-h-filled", "circle-letter-i", "circle-letter-i-filled", "circle-letter-j", "circle-letter-j-filled", "circle-letter-k", "circle-letter-k-filled", "circle-letter-l", "circle-letter-l-filled", "circle-letter-m", "circle-letter-m-filled", "circle-letter-n", "circle-letter-n-filled", "circle-letter-o", "circle-letter-o-filled", "circle-letter-p", "circle-letter-p-filled", "circle-letter-q", "circle-letter-q-filled", "circle-letter-r", "circle-letter-r-filled", "circle-letter-s", "circle-letter-s-filled", "circle-letter-t", "circle-letter-t-filled", "circle-letter-u", "circle-letter-u-filled", "circle-letter-v", "circle-letter-v-filled", "circle-letter-w", "circle-letter-w-filled", "circle-letter-x", "circle-letter-x-filled", "circle-letter-y", "circle-letter-y-filled", "circle-letter-z", "circle-letter-z-filled", "circle-minus", "circle-minus-2", "circle-number-0", "circle-number-0-filled", "circle-number-1", "circle-number-1-filled", "circle-number-2", "circle-number-2-filled", "circle-number-3", "circle-number-3-filled", "circle-number-4", "circle-number-4-filled", "circle-number-5", "circle-number-5-filled", "circle-number-6", "circle-number-6-filled", "circle-number-7", "circle-number-7-filled", "circle-number-8", "circle-number-8-filled", "circle-number-9", "circle-number-9-filled", "circle-off", "circle-percentage", "circle-percentage-filled", "circle-plus", "circle-plus-2", "circle-plus-filled", "circle-rectangle", "circle-rectangle-filled", "circle-rectangle-off", "circle-square", "circle-triangle", "circle-x", "circle-x-filled", "circles", "circles-filled", "circles-relation", "circuit-ammeter", "circuit-battery", "circuit-bulb", "circuit-capacitor", "circuit-capacitor-polarized", "circuit-cell", "circuit-cell-plus", "circuit-changeover", "circuit-diode", "circuit-diode-zener", "circuit-ground", "circuit-ground-digital", "circuit-inductor", "circuit-motor", "circuit-pushbutton", "circuit-resistor", "circuit-switch-closed", "circuit-switch-open", "circuit-voltmeter", "clear-all", "clear-formatting", "click", "cliff-jumping", "clipboard", "clipboard-check", "clipboard-check-filled", "clipboard-copy", "clipboard-data", "clipboard-data-filled", "clipboard-filled", "clipboard-heart", "clipboard-list", "clipboard-list-filled", "clipboard-off", "clipboard-plus", "clipboard-plus-filled", "clipboard-search", "clipboard-smile", "clipboard-smile-filled", "clipboard-text", "clipboard-text-filled", "clipboard-typography", "clipboard-typography-filled", "clipboard-x", "clipboard-x-filled", "clock", "clock-12", "clock-2", "clock-24", "clock-bitcoin", "clock-bolt", "clock-cancel", "clock-check", "clock-code", "clock-cog", "clock-dollar", "clock-down", "clock-edit", "clock-exclamation", "clock-filled", "clock-heart", "clock-hour-1", "clock-hour-1-filled", "clock-hour-10", "clock-hour-10-filled", "clock-hour-11", "clock-hour-11-filled", "clock-hour-12", "clock-hour-12-filled", "clock-hour-2", "clock-hour-2-filled", "clock-hour-3", "clock-hour-3-filled", "clock-hour-4", "clock-hour-4-filled", "clock-hour-5", "clock-hour-5-filled", "clock-hour-6", "clock-hour-6-filled", "clock-hour-7", "clock-hour-7-filled", "clock-hour-8", "clock-hour-8-filled", "clock-hour-9", "clock-hour-9-filled", "clock-minus", "clock-off", "clock-pause", "clock-pin", "clock-play", "clock-plus", "clock-question", "clock-record", "clock-search", "clock-share", "clock-shield", "clock-star", "clock-stop", "clock-up", "clock-x", "clothes-rack", "clothes-rack-off", "cloud", "cloud-bitcoin", "cloud-bolt", "cloud-cancel", "cloud-check", "cloud-code", "cloud-cog", "cloud-computing", "cloud-computing-filled", "cloud-data-connection", "cloud-data-connection-filled", "cloud-dollar", "cloud-down", "cloud-download", "cloud-exclamation", "cloud-filled", "cloud-fog", "cloud-heart", "cloud-lock", "cloud-lock-open", "cloud-minus", "cloud-network", "cloud-off", "cloud-pause", "cloud-pin", "cloud-plus", "cloud-question", "cloud-rain", "cloud-search", "cloud-share", "cloud-snow", "cloud-star", "cloud-storm", "cloud-up", "cloud-upload", "cloud-x", "clover", "clover-2", "clover-filled", "clubs", "clubs-filled", "code", "code-asterisk", "code-circle", "code-circle-2", "code-circle-2-filled", "code-circle-filled", "code-dots", "code-minus", "code-off", "code-plus", "code-variable", "code-variable-minus", "code-variable-plus", "coffee", "coffee-off", "coffin", "coin", "coin-bitcoin", "coin-bitcoin-filled", "coin-euro", "coin-euro-filled", "coin-filled", "coin-monero", "coin-monero-filled", "coin-off", "coin-pound", "coin-pound-filled", "coin-rupee", "coin-rupee-filled", "coin-taka", "coin-taka-filled", "coin-yen", "coin-yen-filled", "coin-yuan", "coin-yuan-filled", "coins", "color-filter", "color-picker", "color-picker-off", "color-swatch", "color-swatch-off", "column-insert-left", "column-insert-right", "column-remove", "columns", "columns-1", "columns-1-filled", "columns-2", "columns-2-filled", "columns-3", "columns-3-filled", "columns-off", "comet", "command", "command-off", "compass", "compass-filled", "compass-off", "components", "components-off", "cone", "cone-2", "cone-2-filled", "cone-filled", "cone-off", "cone-plus", "confetti", "confetti-filled", "confetti-off", "confucius", "congruent-to", "container", "container-filled", "container-off", "contract", "contrast", "contrast-2", "contrast-2-filled", "contrast-2-off", "contrast-filled", "contrast-off", "cooker", "cookie", "cookie-filled", "cookie-man", "cookie-man-filled", "cookie-off", "copy", "copy-check", "copy-check-filled", "copy-minus", "copy-minus-filled", "copy-off", "copy-plus", "copy-plus-filled", "copy-x", "copy-x-filled", "copyleft", "copyleft-filled", "copyleft-off", "copyright", "copyright-filled", "copyright-off", "corner-down-left", "corner-down-left-double", "corner-down-right", "corner-down-right-double", "corner-left-down", "corner-left-down-double", "corner-left-up", "corner-left-up-double", "corner-right-down", "corner-right-down-double", "corner-right-up", "corner-right-up-double", "corner-up-left", "corner-up-left-double", "corner-up-right", "corner-up-right-double", "cpu", "cpu-2", "cpu-off", "crane", "crane-off", "creative-commons", "creative-commons-by", "creative-commons-nc", "creative-commons-nd", "creative-commons-off", "creative-commons-sa", "creative-commons-zero", "credit-card", "credit-card-filled", "credit-card-off", "credit-card-pay", "credit-card-refund", "cricket", "crop", "crop-1-1", "crop-1-1-filled", "crop-16-9", "crop-16-9-filled", "crop-3-2", "crop-3-2-filled", "crop-5-4", "crop-5-4-filled", "crop-7-5", "crop-7-5-filled", "crop-landscape", "crop-landscape-filled", "crop-portrait", "crop-portrait-filled", "cross", "cross-filled", "cross-off", "crosshair", "crown", "crown-off", "crutches", "crutches-off", "crystal-ball", "csv", "cube", "cube-3d-sphere", "cube-3d-sphere-off", "cube-off", "cube-plus", "cube-send", "cube-spark", "cube-unfolded", "cup", "cup-off", "curling", "curly-loop", "currency", "currency-afghani", "currency-bahraini", "currency-baht", "currency-bitcoin", "currency-cent", "currency-dinar", "currency-dirham", "currency-dogecoin", "currency-dollar", "currency-dollar-australian", "currency-dollar-brunei", "currency-dollar-canadian", "currency-dollar-guyanese", "currency-dollar-off", "currency-dollar-singapore", "currency-dollar-zimbabwean", "currency-dong", "currency-dram", "currency-ethereum", "currency-euro", "currency-euro-off", "currency-florin", "currency-forint", "currency-frank", "currency-guarani", "currency-hryvnia", "currency-iranian-rial", "currency-kip", "currency-krone-czech", "currency-krone-danish", "currency-krone-swedish", "currency-lari", "currency-leu", "currency-lira", "currency-litecoin", "currency-lyd", "currency-manat", "currency-monero", "currency-naira", "currency-nano", "currency-off", "currency-paanga", "currency-peso", "currency-pound", "currency-pound-off", "currency-quetzal", "currency-real", "currency-renminbi", "currency-ripple", "currency-riyal", "currency-rubel", "currency-rufiyaa", "currency-rupee", "currency-rupee-nepalese", "currency-shekel", "currency-solana", "currency-som", "currency-taka", "currency-tenge", "currency-tugrik", "currency-won", "currency-xrp", "currency-yen", "currency-yen-off", "currency-yuan", "currency-zloty", "current-location", "current-location-filled", "current-location-off", "cursor-off", "cursor-text", "cut", "cylinder", "cylinder-off", "cylinder-plus", "dashboard", "dashboard-filled", "dashboard-off", "database", "database-cog", "database-dollar", "database-edit", "database-exclamation", "database-export", "database-heart", "database-import", "database-leak", "database-minus", "database-off", "database-plus", "database-search", "database-share", "database-smile", "database-star", "database-x", "decimal", "deer", "delta", "dental", "dental-broken", "dental-off", "deselect", "desk", "details", "details-off", "device-airpods", "device-airpods-case", "device-airtag", "device-analytics", "device-audio-tape", "device-camera-phone", "device-cctv", "device-cctv-filled", "device-cctv-off", "device-computer-camera", "device-computer-camera-off", "device-desktop", "device-desktop-analytics", "device-desktop-bolt", "device-desktop-cancel", "device-desktop-check", "device-desktop-code", "device-desktop-cog", "device-desktop-dollar", "device-desktop-down", "device-desktop-exclamation", "device-desktop-filled", "device-desktop-heart", "device-desktop-minus", "device-desktop-off", "device-desktop-pause", "device-desktop-pin", "device-desktop-plus", "device-desktop-question", "device-desktop-search", "device-desktop-share", "device-desktop-star", "device-desktop-up", "device-desktop-x", "device-floppy", "device-gamepad", "device-gamepad-2", "device-gamepad-3", "device-gamepad-3-filled", "device-heart-monitor", "device-heart-monitor-filled", "device-imac", "device-imac-bolt", "device-imac-cancel", "device-imac-check", "device-imac-code", "device-imac-cog", "device-imac-dollar", "device-imac-down", "device-imac-exclamation", "device-imac-filled", "device-imac-heart", "device-imac-minus", "device-imac-off", "device-imac-pause", "device-imac-pin", "device-imac-plus", "device-imac-question", "device-imac-search", "device-imac-share", "device-imac-star", "device-imac-up", "device-imac-x", "device-ipad", "device-ipad-bolt", "device-ipad-cancel", "device-ipad-check", "device-ipad-code", "device-ipad-cog", "device-ipad-dollar", "device-ipad-down", "device-ipad-exclamation", "device-ipad-filled", "device-ipad-heart", "device-ipad-horizontal", "device-ipad-horizontal-bolt", "device-ipad-horizontal-cancel", "device-ipad-horizontal-check", "device-ipad-horizontal-code", "device-ipad-horizontal-cog", "device-ipad-horizontal-dollar", "device-ipad-horizontal-down", "device-ipad-horizontal-exclamation", "device-ipad-horizontal-heart", "device-ipad-horizontal-minus", "device-ipad-horizontal-off", "device-ipad-horizontal-pause", "device-ipad-horizontal-pin", "device-ipad-horizontal-plus", "device-ipad-horizontal-question", "device-ipad-horizontal-search", "device-ipad-horizontal-share", "device-ipad-horizontal-star", "device-ipad-horizontal-up", "device-ipad-horizontal-x", "device-ipad-minus", "device-ipad-off", "device-ipad-pause", "device-ipad-pin", "device-ipad-plus", "device-ipad-question", "device-ipad-search", "device-ipad-share", "device-ipad-star", "device-ipad-up", "device-ipad-x", "device-landline-phone", "device-laptop", "device-laptop-off", "device-mobile", "device-mobile-bolt", "device-mobile-cancel", "device-mobile-charging", "device-mobile-check", "device-mobile-code", "device-mobile-cog", "device-mobile-dollar", "device-mobile-down", "device-mobile-exclamation", "device-mobile-filled", "device-mobile-heart", "device-mobile-message", "device-mobile-minus", "device-mobile-off", "device-mobile-pause", "device-mobile-pin", "device-mobile-plus", "device-mobile-question", "device-mobile-rotated", "device-mobile-search", "device-mobile-share", "device-mobile-star", "device-mobile-up", "device-mobile-vibration", "device-mobile-x", "device-nintendo", "device-nintendo-off", "device-projector", "device-remote", "device-remote-filled", "device-sd-card", "device-sim", "device-sim-1", "device-sim-2", "device-sim-3", "device-speaker", "device-speaker-filled", "device-speaker-off", "device-tablet", "device-tablet-bolt", "device-tablet-cancel", "device-tablet-check", "device-tablet-code", "device-tablet-cog", "device-tablet-dollar", "device-tablet-down", "device-tablet-exclamation", "device-tablet-filled", "device-tablet-heart", "device-tablet-minus", "device-tablet-off", "device-tablet-pause", "device-tablet-pin", "device-tablet-plus", "device-tablet-question", "device-tablet-search", "device-tablet-share", "device-tablet-star", "device-tablet-up", "device-tablet-x", "device-tv", "device-tv-filled", "device-tv-off", "device-tv-old", "device-tv-old-filled", "device-unknown", "device-unknown-filled", "device-usb", "device-usb-filled", "device-vision-pro", "device-vision-pro-filled", "device-watch", "device-watch-bolt", "device-watch-cancel", "device-watch-check", "device-watch-code", "device-watch-cog", "device-watch-dollar", "device-watch-down", "device-watch-exclamation", "device-watch-filled", "device-watch-heart", "device-watch-minus", "device-watch-off", "device-watch-pause", "device-watch-pin", "device-watch-plus", "device-watch-question", "device-watch-search", "device-watch-share", "device-watch-star", "device-watch-stats", "device-watch-stats-2", "device-watch-up", "device-watch-x", "devices", "devices-2", "devices-bolt", "devices-cancel", "devices-check", "devices-code", "devices-cog", "devices-dollar", "devices-down", "devices-exclamation", "devices-heart", "devices-minus", "devices-off", "devices-pause", "devices-pc", "devices-pc-off", "devices-pin", "devices-plus", "devices-question", "devices-search", "devices-share", "devices-star", "devices-up", "devices-x", "diabolo", "diabolo-off", "diabolo-plus", "dialpad", "dialpad-filled", "dialpad-off", "diamond", "diamond-filled", "diamond-off", "diamonds", "diamonds-filled", "diaper", "dice", "dice-1", "dice-1-filled", "dice-2", "dice-2-filled", "dice-3", "dice-3-filled", "dice-4", "dice-4-filled", "dice-5", "dice-5-filled", "dice-6", "dice-6-filled", "dice-filled", "dimensions", "direction", "direction-arrows", "direction-arrows-filled", "direction-horizontal", "direction-sign", "direction-sign-filled", "direction-sign-off", "directions", "directions-filled", "directions-off", "disabled", "disabled-2", "disabled-off", "disc", "disc-filled", "disc-golf", "disc-off", "discount", "discount-filled", "discount-off", "divide", "dna", "dna-2", "dna-2-off", "dna-off", "dog", "dog-bowl", "door", "door-enter", "door-exit", "door-off", "dots", "dots-circle-horizontal", "dots-diagonal", "dots-diagonal-2", "dots-vertical", "download", "download-off", "drag-drop", "drag-drop-2", "drone", "drone-off", "drop-circle", "drop-circle-filled", "droplet", "droplet-bolt", "droplet-cancel", "droplet-check", "droplet-code", "droplet-cog", "droplet-dollar", "droplet-down", "droplet-exclamation", "droplet-filled", "droplet-half", "droplet-half-2", "droplet-half-2-filled", "droplet-half-filled", "droplet-heart", "droplet-minus", "droplet-off", "droplet-pause", "droplet-pin", "droplet-plus", "droplet-question", "droplet-search", "droplet-share", "droplet-star", "droplet-up", "droplet-x", "droplets", "droplets-filled", "dual-screen", "dual-screen-filled", "dumpling", "dumpling-filled", "e-passport", "ear", "ear-off", "ear-scan", "ease-in", "ease-in-control-point", "ease-in-control-point-filled", "ease-in-out", "ease-in-out-control-points", "ease-in-out-control-points-filled", "ease-out", "ease-out-control-point", "ease-out-control-point-filled", "edit", "edit-circle", "edit-circle-off", "edit-off", "egg", "egg-cracked", "egg-cracked-filled", "egg-filled", "egg-fried", "egg-fried-filled", "egg-off", "eggs", "elevator", "elevator-filled", "elevator-off", "emergency-bed", "empathize", "empathize-off", "emphasis", "engine", "engine-filled", "engine-off", "equal", "equal-double", "equal-not", "eraser", "eraser-off", "error-404", "error-404-off", "escalator", "escalator-down", "escalator-down-filled", "escalator-filled", "escalator-up", "escalator-up-filled", "exchange", "exchange-filled", "exchange-off", "exclamation-circle", "exclamation-circle-filled", "exclamation-mark", "exclamation-mark-off", "explicit", "explicit-filled", "explicit-off", "exposure", "exposure-0", "exposure-filled", "exposure-minus-1", "exposure-minus-2", "exposure-off", "exposure-plus-1", "exposure-plus-2", "external-link", "external-link-off", "eye", "eye-bitcoin", "eye-bolt", "eye-cancel", "eye-check", "eye-closed", "eye-code", "eye-cog", "eye-discount", "eye-dollar", "eye-dotted", "eye-down", "eye-edit", "eye-exclamation", "eye-filled", "eye-heart", "eye-minus", "eye-off", "eye-pause", "eye-pin", "eye-plus", "eye-question", "eye-search", "eye-share", "eye-spark", "eye-star", "eye-table", "eye-table-filled", "eye-up", "eye-x", "eyeglass", "eyeglass-2", "eyeglass-2-filled", "eyeglass-filled", "eyeglass-off", "face-id", "face-id-error", "face-mask", "face-mask-filled", "face-mask-off", "fall", "favicon", "favicon-filled", "feather", "feather-filled", "feather-off", "fence", "fence-filled", "fence-off", "ferry", "ferry-filled", "fidget-spinner", "fidget-spinner-filled", "file", "file-3d", "file-ai", "file-alert", "file-analytics", "file-analytics-filled", "file-arrow-left", "file-arrow-right", "file-barcode", "file-bitcoin", "file-broken", "file-certificate", "file-chart", "file-check", "file-check-filled", "file-code", "file-code-2", "file-code-2-filled", "file-code-filled", "file-cv", "file-cv-filled", "file-database", "file-delta", "file-delta-filled", "file-description", "file-description-filled", "file-diff", "file-diff-filled", "file-digit", "file-digit-filled", "file-dislike", "file-dollar", "file-dots", "file-dots-filled", "file-download", "file-download-filled", "file-euro", "file-excel", "file-export", "file-filled", "file-function", "file-function-filled", "file-horizontal", "file-horizontal-filled", "file-import", "file-infinity", "file-info", "file-info-filled", "file-invoice", "file-invoice-filled", "file-isr", "file-lambda", "file-lambda-filled", "file-like", "file-minus", "file-minus-filled", "file-music", "file-neutral", "file-neutral-filled", "file-off", "file-orientation", "file-pencil", "file-percent", "file-percent-filled", "file-phone", "file-phone-filled", "file-plus", "file-power", "file-power-filled", "file-report", "file-rss", "file-rss-filled", "file-sad", "file-sad-filled", "file-scissors", "file-search", "file-settings", "file-shredder", "file-signal", "file-smile", "file-smile-filled", "file-spark", "file-spreadsheet", "file-stack", "file-star", "file-star-filled", "file-symlink", "file-text", "file-text-ai", "file-text-filled", "file-text-shield", "file-text-spark", "file-time", "file-type-bmp", "file-type-css", "file-type-csv", "file-type-doc", "file-type-docx", "file-type-html", "file-type-jpg", "file-type-js", "file-type-jsx", "file-type-pdf", "file-type-php", "file-type-png", "file-type-ppt", "file-type-rs", "file-type-sql", "file-type-svg", "file-type-ts", "file-type-tsx", "file-type-txt", "file-type-vue", "file-type-xls", "file-type-xml", "file-type-zip", "file-typography", "file-typography-filled", "file-unknown", "file-upload", "file-vector", "file-word", "file-x", "file-x-filled", "file-zip", "files", "files-off", "filter", "filter-2", "filter-2-bolt", "filter-2-cancel", "filter-2-check", "filter-2-code", "filter-2-cog", "filter-2-discount", "filter-2-dollar", "filter-2-down", "filter-2-edit", "filter-2-exclamation", "filter-2-minus", "filter-2-pause", "filter-2-pin", "filter-2-plus", "filter-2-question", "filter-2-search", "filter-2-share", "filter-2-spark", "filter-2-up", "filter-2-x", "filter-bolt", "filter-cancel", "filter-check", "filter-code", "filter-cog", "filter-discount", "filter-dollar", "filter-down", "filter-edit", "filter-exclamation", "filter-filled", "filter-heart", "filter-minus", "filter-off", "filter-pause", "filter-pin", "filter-plus", "filter-question", "filter-search", "filter-share", "filter-spark", "filter-star", "filter-up", "filter-x", "filters", "filters-filled", "fingerprint", "fingerprint-off", "fingerprint-scan", "fire-extinguisher", "fire-hydrant", "fire-hydrant-off", "firetruck", "first-aid-kit", "first-aid-kit-off", "fish", "fish-bone", "fish-bone-filled", "fish-christianity", "fish-hook", "fish-hook-off", "fish-off", "flag", "flag-2", "flag-2-filled", "flag-2-off", "flag-3", "flag-3-filled", "flag-bitcoin", "flag-bolt", "flag-cancel", "flag-check", "flag-code", "flag-cog", "flag-discount", "flag-dollar", "flag-down", "flag-exclamation", "flag-filled", "flag-heart", "flag-minus", "flag-off", "flag-pause", "flag-pin", "flag-plus", "flag-question", "flag-search", "flag-share", "flag-spark", "flag-star", "flag-up", "flag-x", "flame", "flame-filled", "flame-off", "flare", "flare-filled", "flask", "flask-2", "flask-2-filled", "flask-2-off", "flask-filled", "flask-off", "flip-flops", "flip-horizontal", "flip-vertical", "float-center", "float-left", "float-none", "float-right", "flower", "flower-filled", "flower-off", "focus", "focus-2", "focus-auto", "focus-centered", "fold", "fold-down", "fold-up", "folder", "folder-bolt", "folder-cancel", "folder-check", "folder-code", "folder-cog", "folder-dollar", "folder-down", "folder-exclamation", "folder-filled", "folder-heart", "folder-minus", "folder-off", "folder-open", "folder-pause", "folder-pin", "folder-plus", "folder-question", "folder-root", "folder-search", "folder-share", "folder-star", "folder-symlink", "folder-up", "folder-x", "folders", "folders-filled", "folders-off", "forbid", "forbid-2", "forbid-2-filled", "forbid-filled", "forklift", "forms", "fountain", "fountain-filled", "fountain-off", "frame", "frame-off", "free-rights", "freeze-column", "freeze-row", "freeze-row-column", "fridge", "fridge-off", "friends", "friends-off", "frustum", "frustum-off", "frustum-plus", "function", "function-filled", "function-off", "galaxy", "garden-cart", "garden-cart-filled", "garden-cart-off", "gas-station", "gas-station-filled", "gas-station-off", "gauge", "gauge-filled", "gauge-off", "gavel", "gender-agender", "gender-androgyne", "gender-bigender", "gender-demiboy", "gender-demigirl", "gender-epicene", "gender-female", "gender-femme", "gender-genderfluid", "gender-genderless", "gender-genderqueer", "gender-hermaphrodite", "gender-intergender", "gender-male", "gender-neutrois", "gender-third", "gender-transgender", "gender-trasvesti", "geometry", "ghost", "ghost-2", "ghost-2-filled", "ghost-3", "ghost-3-filled", "ghost-filled", "ghost-off", "gif", "gift", "gift-card", "gift-card-filled", "gift-filled", "gift-off", "git-branch", "git-branch-deleted", "git-cherry-pick", "git-commit", "git-compare", "git-fork", "git-merge", "git-pull-request", "git-pull-request-closed", "git-pull-request-draft", "gizmo", "glass", "glass-champagne", "glass-cocktail", "glass-filled", "glass-full", "glass-full-filled", "glass-gin", "glass-off", "globe", "globe-filled", "globe-off", "go-game", "golf", "golf-filled", "golf-off", "gps", "gps-filled", "gradienter", "grain", "graph", "graph-filled", "graph-off", "grave", "grave-2", "grid-3x3", "grid-4x4", "grid-dots", "grid-<PERSON><PERSON><PERSON>", "grid-pattern", "grid-pattern-filled", "grid-scan", "grill", "grill-fork", "grill-off", "grill-spatula", "grip-horizontal", "grip-vertical", "growth", "guitar-pick", "guitar-pick-filled", "gymnastics", "h-1", "h-2", "h-3", "h-4", "h-5", "h-6", "hammer", "hammer-off", "hand-click", "hand-click-off", "hand-finger", "hand-finger-down", "hand-finger-left", "hand-finger-off", "hand-finger-right", "hand-grab", "hand-little-finger", "hand-love-you", "hand-middle-finger", "hand-move", "hand-off", "hand-ring-finger", "hand-sanitizer", "hand-stop", "hand-three-fingers", "hand-two-fingers", "hanger", "hanger-2", "hanger-2-filled", "hanger-off", "hash", "haze", "haze-moon", "hdr", "heading", "heading-off", "headphones", "headphones-filled", "headphones-off", "headset", "headset-off", "health-recognition", "heart", "heart-bitcoin", "heart-bolt", "heart-broken", "heart-broken-filled", "heart-cancel", "heart-check", "heart-code", "heart-cog", "heart-discount", "heart-dollar", "heart-down", "heart-exclamation", "heart-filled", "heart-handshake", "heart-minus", "heart-off", "heart-pause", "heart-pin", "heart-plus", "heart-question", "heart-rate-monitor", "heart-search", "heart-share", "heart-spark", "heart-star", "heart-up", "heart-x", "heartbeat", "hearts", "hearts-off", "helicopter", "helicopter-filled", "helicopter-landing", "helicopter-landing-filled", "helmet", "helmet-off", "help", "help-circle", "help-circle-filled", "help-hexagon", "help-hexagon-filled", "help-octagon", "help-octagon-filled", "help-off", "help-small", "help-square", "help-square-filled", "help-square-rounded", "help-square-rounded-filled", "help-triangle", "help-triangle-filled", "hemisphere", "hemisphere-off", "hemisphere-plus", "hexagon", "hexagon-3d", "hexagon-filled", "hexagon-letter-a", "hexagon-letter-a-filled", "hexagon-letter-b", "hexagon-letter-b-filled", "hexagon-letter-c", "hexagon-letter-c-filled", "hexagon-letter-d", "hexagon-letter-d-filled", "hexagon-letter-e", "hexagon-letter-e-filled", "hexagon-letter-f", "hexagon-letter-f-filled", "hexagon-letter-g", "hexagon-letter-g-filled", "hexagon-letter-h", "hexagon-letter-h-filled", "hexagon-letter-i", "hexagon-letter-i-filled", "hexagon-letter-j", "hexagon-letter-j-filled", "hexagon-letter-k", "hexagon-letter-k-filled", "hexagon-letter-l", "hexagon-letter-l-filled", "hexagon-letter-m", "hexagon-letter-m-filled", "hexagon-letter-n", "hexagon-letter-n-filled", "hexagon-letter-o", "hexagon-letter-o-filled", "hexagon-letter-p", "hexagon-letter-p-filled", "hexagon-letter-q", "hexagon-letter-q-filled", "hexagon-letter-r", "hexagon-letter-r-filled", "hexagon-letter-s", "hexagon-letter-s-filled", "hexagon-letter-t", "hexagon-letter-t-filled", "hexagon-letter-u", "hexagon-letter-u-filled", "hexagon-letter-v", "hexagon-letter-v-filled", "hexagon-letter-w", "hexagon-letter-w-filled", "hexagon-letter-x", "hexagon-letter-x-filled", "hexagon-letter-y", "hexagon-letter-y-filled", "hexagon-letter-z", "hexagon-letter-z-filled", "hexagon-minus", "hexagon-minus-2", "hexagon-minus-filled", "hexagon-number-0", "hexagon-number-0-filled", "hexagon-number-1", "hexagon-number-1-filled", "hexagon-number-2", "hexagon-number-2-filled", "hexagon-number-3", "hexagon-number-3-filled", "hexagon-number-4", "hexagon-number-4-filled", "hexagon-number-5", "hexagon-number-5-filled", "hexagon-number-6", "hexagon-number-6-filled", "hexagon-number-7", "hexagon-number-7-filled", "hexagon-number-8", "hexagon-number-8-filled", "hexagon-number-9", "hexagon-number-9-filled", "hexagon-off", "hexagon-plus", "hexagon-plus-2", "hexagon-plus-filled", "hexagonal-prism", "hexagonal-prism-off", "hexagonal-prism-plus", "hexagonal-pyramid", "hexagonal-pyramid-off", "hexagonal-pyramid-plus", "hexagons", "hexagons-off", "hierarchy", "hierarchy-2", "hierarchy-3", "hierarchy-off", "highlight", "highlight-off", "history", "history-off", "history-toggle", "home", "home-2", "home-bitcoin", "home-bolt", "home-cancel", "home-check", "home-cog", "home-dollar", "home-dot", "home-down", "home-eco", "home-edit", "home-exclamation", "home-filled", "home-hand", "home-heart", "home-infinity", "home-link", "home-minus", "home-move", "home-off", "home-plus", "home-question", "home-ribbon", "home-search", "home-share", "home-shield", "home-signal", "home-spark", "home-star", "home-stats", "home-up", "home-x", "horse", "horse-toy", "horseshoe", "hospital", "hospital-circle", "hospital-circle-filled", "hotel-service", "hourglass", "hourglass-empty", "hourglass-filled", "hourglass-high", "hourglass-low", "hourglass-off", "hours-12", "hours-24", "html", "http-connect", "http-connect-off", "http-delete", "http-delete-off", "http-get", "http-get-off", "http-head", "http-head-off", "http-options", "http-options-off", "http-patch", "http-patch-off", "http-post", "http-post-off", "http-put", "http-put-off", "http-que", "http-que-off", "http-trace", "http-trace-off", "ice-cream", "ice-cream-2", "ice-cream-off", "ice-skating", "icons", "icons-filled", "icons-off", "id", "id-badge", "id-badge-2", "id-badge-off", "id-off", "<PERSON><PERSON><PERSON><PERSON>", "image-in-picture", "inbox", "inbox-off", "indent-decrease", "indent-increase", "infinity", "infinity-off", "info-circle", "info-circle-filled", "info-hexagon", "info-hexagon-filled", "info-octagon", "info-octagon-filled", "info-small", "info-square", "info-square-filled", "info-square-rounded", "info-square-rounded-filled", "info-triangle", "info-triangle-filled", "inner-shadow-bottom", "inner-shadow-bottom-filled", "inner-shadow-bottom-left", "inner-shadow-bottom-left-filled", "inner-shadow-bottom-right", "inner-shadow-bottom-right-filled", "inner-shadow-left", "inner-shadow-left-filled", "inner-shadow-right", "inner-shadow-right-filled", "inner-shadow-top", "inner-shadow-top-filled", "inner-shadow-top-left", "inner-shadow-top-left-filled", "inner-shadow-top-right", "inner-shadow-top-right-filled", "input-ai", "input-check", "input-search", "input-spark", "input-x", "invoice", "ironing", "ironing-1", "ironing-1-filled", "ironing-2", "ironing-2-filled", "ironing-3", "ironing-3-filled", "ironing-filled", "ironing-off", "ironing-steam", "ironing-steam-filled", "ironing-steam-off", "irregular-polyhedron", "irregular-polyhedron-off", "irregular-polyhedron-plus", "italic", "jacket", "jetpack", "jetpack-filled", "jewish-star", "jewish-star-filled", "join-bevel", "join-round", "join-straight", "joker", "jpg", "json", "jump-rope", "karate", "kayak", "kerning", "key", "key-filled", "key-off", "keyboard", "keyboard-filled", "keyboard-hide", "keyboard-off", "keyboard-show", "keyframe", "keyframe-align-center", "keyframe-align-center-filled", "keyframe-align-horizontal", "keyframe-align-horizontal-filled", "keyframe-align-vertical", "keyframe-align-vertical-filled", "keyframe-filled", "keyframes", "keyframes-filled", "label", "label-filled", "label-important", "label-important-filled", "label-off", "ladder", "ladder-off", "ladle", "lambda", "lamp", "lamp-2", "lamp-off", "lane", "language", "language-hiragana", "language-katakana", "language-off", "lasso", "lasso-off", "lasso-polygon", "lasso-polygon-filled", "laurel-wreath", "laurel-wreath-1", "laurel-wreath-1-filled", "laurel-wreath-2", "laurel-wreath-2-filled", "laurel-wreath-3", "laurel-wreath-3-filled", "laurel-wreath-filled", "layers-difference", "layers-intersect", "layers-intersect-2", "layers-linked", "layers-off", "layers-selected", "layers-selected-bottom", "layers-subtract", "layers-union", "layout", "layout-2", "layout-2-filled", "layout-align-bottom", "layout-align-bottom-filled", "layout-align-center", "layout-align-center-filled", "layout-align-left", "layout-align-left-filled", "layout-align-middle", "layout-align-middle-filled", "layout-align-right", "layout-align-right-filled", "layout-align-top", "layout-align-top-filled", "layout-board", "layout-board-filled", "layout-board-split", "layout-board-split-filled", "layout-bottombar", "layout-bottombar-collapse", "layout-bottombar-collapse-filled", "layout-bottombar-expand", "layout-bottombar-expand-filled", "layout-bottombar-filled", "layout-bottombar-inactive", "layout-cards", "layout-cards-filled", "layout-collage", "layout-columns", "layout-dashboard", "layout-dashboard-filled", "layout-distribute-horizontal", "layout-distribute-horizontal-filled", "layout-distribute-vertical", "layout-distribute-vertical-filled", "layout-filled", "layout-grid", "layout-grid-add", "layout-grid-filled", "layout-grid-remove", "layout-kanban", "layout-kanban-filled", "layout-list", "layout-list-filled", "layout-navbar", "layout-navbar-collapse", "layout-navbar-collapse-filled", "layout-navbar-expand", "layout-navbar-expand-filled", "layout-navbar-filled", "layout-navbar-inactive", "layout-off", "layout-rows", "layout-sidebar", "layout-sidebar-filled", "layout-sidebar-inactive", "layout-sidebar-left-collapse", "layout-sidebar-left-collapse-filled", "layout-sidebar-left-expand", "layout-sidebar-left-expand-filled", "layout-sidebar-right", "layout-sidebar-right-collapse", "layout-sidebar-right-collapse-filled", "layout-sidebar-right-expand", "layout-sidebar-right-expand-filled", "layout-sidebar-right-filled", "layout-sidebar-right-inactive", "leaf", "leaf-2", "leaf-off", "lego", "lego-filled", "lego-off", "lemon", "lemon-2", "lemon-2-filled", "letter-a", "letter-a-small", "letter-b", "letter-b-small", "letter-c", "letter-c-small", "letter-case", "letter-case-lower", "letter-case-toggle", "letter-case-upper", "letter-d", "letter-d-small", "letter-e", "letter-e-small", "letter-f", "letter-f-small", "letter-g", "letter-g-small", "letter-h", "letter-h-small", "letter-i", "letter-i-small", "letter-j", "letter-j-small", "letter-k", "letter-k-small", "letter-l", "letter-l-small", "letter-m", "letter-m-small", "letter-n", "letter-n-small", "letter-o", "letter-o-small", "letter-p", "letter-p-small", "letter-q", "letter-q-small", "letter-r", "letter-r-small", "letter-s", "letter-s-small", "letter-spacing", "letter-t", "letter-t-small", "letter-u", "letter-u-small", "letter-v", "letter-v-small", "letter-w", "letter-w-small", "letter-x", "letter-x-small", "letter-y", "letter-y-small", "letter-z", "letter-z-small", "library", "library-filled", "library-minus", "library-photo", "library-plus", "library-plus-filled", "license", "license-off", "lifebuoy", "lifebuoy-filled", "lifebuoy-off", "lighter", "line", "line-dashed", "line-dotted", "line-height", "line-scan", "link", "link-minus", "link-off", "link-plus", "list", "list-check", "list-details", "list-letters", "list-numbers", "list-search", "list-tree", "live-photo", "live-photo-filled", "live-photo-off", "live-view", "live-view-filled", "load-balancer", "loader", "loader-2", "loader-3", "loader-quarter", "location", "location-bolt", "location-broken", "location-cancel", "location-check", "location-code", "location-cog", "location-discount", "location-dollar", "location-down", "location-exclamation", "location-filled", "location-heart", "location-minus", "location-off", "location-pause", "location-pin", "location-plus", "location-question", "location-search", "location-share", "location-star", "location-up", "location-x", "lock", "lock-access", "lock-access-off", "lock-bitcoin", "lock-bolt", "lock-cancel", "lock-check", "lock-code", "lock-cog", "lock-dollar", "lock-down", "lock-exclamation", "lock-filled", "lock-heart", "lock-minus", "lock-off", "lock-open", "lock-open-2", "lock-open-off", "lock-password", "lock-pause", "lock-pin", "lock-plus", "lock-question", "lock-search", "lock-share", "lock-square", "lock-square-rounded", "lock-square-rounded-filled", "lock-star", "lock-up", "lock-x", "logic-and", "logic-buffer", "logic-nand", "logic-nor", "logic-not", "logic-or", "logic-xnor", "logic-xor", "login", "login-2", "logout", "logout-2", "logs", "lollipop", "lollipop-off", "luggage", "luggage-off", "lungs", "lungs-filled", "lungs-off", "macro", "macro-filled", "macro-off", "magnet", "magnet-filled", "magnet-off", "magnetic", "mail", "mail-ai", "mail-bitcoin", "mail-bolt", "mail-cancel", "mail-check", "mail-code", "mail-cog", "mail-dollar", "mail-down", "mail-exclamation", "mail-fast", "mail-filled", "mail-forward", "mail-heart", "mail-minus", "mail-off", "mail-opened", "mail-opened-filled", "mail-pause", "mail-pin", "mail-plus", "mail-question", "mail-search", "mail-share", "mail-spark", "mail-star", "mail-up", "mail-x", "mailbox", "mailbox-off", "man", "man-filled", "manual-gearbox", "manual-gearbox-filled", "map", "map-2", "map-bolt", "map-cancel", "map-check", "map-code", "map-cog", "map-discount", "map-dollar", "map-down", "map-east", "map-exclamation", "map-heart", "map-minus", "map-north", "map-off", "map-pause", "map-pin", "map-pin-2", "map-pin-bolt", "map-pin-cancel", "map-pin-check", "map-pin-code", "map-pin-cog", "map-pin-dollar", "map-pin-down", "map-pin-exclamation", "map-pin-filled", "map-pin-heart", "map-pin-minus", "map-pin-off", "map-pin-pause", "map-pin-pin", "map-pin-plus", "map-pin-question", "map-pin-search", "map-pin-share", "map-pin-star", "map-pin-up", "map-pin-x", "map-pins", "map-plus", "map-question", "map-route", "map-search", "map-share", "map-south", "map-star", "map-up", "map-west", "map-x", "markdown", "markdown-off", "marquee", "marquee-2", "marquee-off", "mars", "mask", "mask-off", "masks-theater", "masks-theater-off", "massage", "matchstick", "math", "math-1-divide-2", "math-1-divide-3", "math-avg", "math-cos", "math-ctg", "math-equal-greater", "math-equal-lower", "math-function", "math-function-off", "math-function-y", "math-greater", "math-integral", "math-integral-x", "math-integrals", "math-lower", "math-max", "math-max-min", "math-min", "math-not", "math-off", "math-pi", "math-pi-divide-2", "math-sec", "math-sin", "math-symbols", "math-tg", "math-x-divide-2", "math-x-divide-y", "math-x-divide-y-2", "math-x-floor-divide-y", "math-x-minus-x", "math-x-minus-y", "math-x-plus-x", "math-x-plus-y", "math-xy", "math-y-minus-y", "math-y-plus-y", "matrix", "maximize", "maximize-off", "meat", "meat-off", "medal", "medal-2", "medical-cross", "medical-cross-circle", "medical-cross-filled", "medical-cross-off", "medicine-syrup", "meeple", "meeple-filled", "melon", "melon-filled", "menorah", "menu", "menu-2", "menu-3", "menu-4", "menu-deep", "menu-order", "message", "message-2", "message-2-bolt", "message-2-cancel", "message-2-check", "message-2-code", "message-2-cog", "message-2-dollar", "message-2-down", "message-2-exclamation", "message-2-filled", "message-2-heart", "message-2-minus", "message-2-off", "message-2-pause", "message-2-pin", "message-2-plus", "message-2-question", "message-2-search", "message-2-share", "message-2-star", "message-2-up", "message-2-x", "message-bolt", "message-cancel", "message-chatbot", "message-chatbot-filled", "message-check", "message-circle", "message-circle-bolt", "message-circle-cancel", "message-circle-check", "message-circle-code", "message-circle-cog", "message-circle-dollar", "message-circle-down", "message-circle-exclamation", "message-circle-filled", "message-circle-heart", "message-circle-minus", "message-circle-off", "message-circle-pause", "message-circle-pin", "message-circle-plus", "message-circle-question", "message-circle-search", "message-circle-share", "message-circle-star", "message-circle-up", "message-circle-user", "message-circle-x", "message-code", "message-cog", "message-dollar", "message-dots", "message-down", "message-exclamation", "message-filled", "message-forward", "message-heart", "message-language", "message-minus", "message-off", "message-pause", "message-pin", "message-plus", "message-question", "message-reply", "message-report", "message-report-filled", "message-search", "message-share", "message-star", "message-up", "message-user", "message-x", "messages", "messages-off", "meteor", "meteor-filled", "meteor-off", "meter-cube", "meter-square", "metronome", "michelin-bib-gourmand", "michelin-star", "michelin-star-filled", "michelin-star-green", "mickey", "mickey-filled", "microphone", "microphone-2", "microphone-2-off", "microphone-filled", "microphone-off", "microscope", "microscope-filled", "microscope-off", "microwave", "microwave-filled", "microwave-off", "military-award", "military-rank", "military-rank-filled", "milk", "milk-filled", "milk-off", "milkshake", "minimize", "minus", "minus-vertical", "mist", "mist-off", "mobiledata", "mobiledata-off", "moneybag", "moneybag-edit", "moneybag-heart", "moneybag-minus", "moneybag-move", "moneybag-move-back", "moneybag-plus", "monkeybar", "mood-angry", "mood-angry-filled", "mood-annoyed", "mood-annoyed-2", "mood-bitcoin", "mood-boy", "mood-check", "mood-cog", "mood-confuzed", "mood-confuzed-filled", "mood-crazy-happy", "mood-crazy-happy-filled", "mood-cry", "mood-dollar", "mood-edit", "mood-empty", "mood-empty-filled", "mood-happy", "mood-happy-filled", "mood-heart", "mood-kid", "mood-kid-filled", "mood-look-down", "mood-look-left", "mood-look-right", "mood-look-up", "mood-minus", "mood-nerd", "mood-nervous", "mood-neutral", "mood-neutral-filled", "mood-off", "mood-pin", "mood-plus", "mood-puzzled", "mood-sad", "mood-sad-2", "mood-sad-dizzy", "mood-sad-filled", "mood-sad-squint", "mood-search", "mood-share", "mood-sick", "mood-silence", "mood-sing", "mood-smile", "mood-smile-beam", "mood-smile-dizzy", "mood-smile-filled", "mood-spark", "mood-surprised", "mood-tongue", "mood-tongue-wink", "mood-tongue-wink-2", "mood-unamused", "mood-up", "mood-wink", "mood-wink-2", "mood-wrrr", "mood-wrrr-filled", "mood-x", "mood-xd", "moon", "moon-2", "moon-filled", "moon-off", "moon-stars", "moped", "motorbike", "motorbike-filled", "mountain", "mountain-filled", "mountain-off", "mouse", "mouse-2", "mouse-filled", "mouse-off", "moustache", "movie", "movie-off", "mug", "mug-filled", "mug-off", "multiplier-0-5x", "multiplier-1-5x", "multiplier-1x", "multiplier-2x", "mushroom", "mushroom-filled", "mushroom-off", "music", "music-bolt", "music-cancel", "music-check", "music-code", "music-cog", "music-discount", "music-dollar", "music-down", "music-exclamation", "music-heart", "music-minus", "music-off", "music-pause", "music-pin", "music-plus", "music-question", "music-search", "music-share", "music-star", "music-up", "music-x", "navigation", "navigation-bolt", "navigation-cancel", "navigation-check", "navigation-code", "navigation-cog", "navigation-discount", "navigation-dollar", "navigation-down", "navigation-east", "navigation-exclamation", "navigation-filled", "navigation-heart", "navigation-minus", "navigation-north", "navigation-off", "navigation-pause", "navigation-pin", "navigation-plus", "navigation-question", "navigation-search", "navigation-share", "navigation-south", "navigation-star", "navigation-top", "navigation-up", "navigation-west", "navigation-x", "needle", "needle-thread", "network", "network-off", "new-section", "news", "news-off", "nfc", "nfc-off", "no-copyright", "no-creative-commons", "no-derivatives", "north-star", "note", "note-off", "notebook", "notebook-off", "notes", "notes-off", "notification", "notification-off", "number", "number-0", "number-0-small", "number-1", "number-1-small", "number-10", "number-10-small", "number-100-small", "number-11", "number-11-small", "number-12-small", "number-123", "number-13-small", "number-14-small", "number-15-small", "number-16-small", "number-17-small", "number-18-small", "number-19-small", "number-2", "number-2-small", "number-20-small", "number-21-small", "number-22-small", "number-23-small", "number-24-small", "number-25-small", "number-26-small", "number-27-small", "number-28-small", "number-29-small", "number-3", "number-3-small", "number-30-small", "number-31-small", "number-32-small", "number-33-small", "number-34-small", "number-35-small", "number-36-small", "number-37-small", "number-38-small", "number-39-small", "number-4", "number-4-small", "number-40-small", "number-41-small", "number-42-small", "number-43-small", "number-44-small", "number-45-small", "number-46-small", "number-47-small", "number-48-small", "number-49-small", "number-5", "number-5-small", "number-50-small", "number-51-small", "number-52-small", "number-53-small", "number-54-small", "number-55-small", "number-56-small", "number-57-small", "number-58-small", "number-59-small", "number-6", "number-6-small", "number-60-small", "number-61-small", "number-62-small", "number-63-small", "number-64-small", "number-65-small", "number-66-small", "number-67-small", "number-68-small", "number-69-small", "number-7", "number-7-small", "number-70-small", "number-71-small", "number-72-small", "number-73-small", "number-74-small", "number-75-small", "number-76-small", "number-77-small", "number-78-small", "number-79-small", "number-8", "number-8-small", "number-80-small", "number-81-small", "number-82-small", "number-83-small", "number-84-small", "number-85-small", "number-86-small", "number-87-small", "number-88-small", "number-89-small", "number-9", "number-9-small", "number-90-small", "number-91-small", "number-92-small", "number-93-small", "number-94-small", "number-95-small", "number-96-small", "number-97-small", "number-98-small", "number-99-small", "numbers", "nurse", "nurse-filled", "nut", "object-scan", "octagon", "octagon-filled", "octagon-minus", "octagon-minus-2", "octagon-minus-filled", "octagon-off", "octagon-plus", "octagon-plus-2", "octagon-plus-filled", "octahedron", "octahedron-off", "octahedron-plus", "old", "olympics", "olympics-off", "om", "omega", "outbound", "outlet", "oval", "oval-filled", "oval-vertical", "oval-vertical-filled", "overline", "package", "package-export", "package-import", "package-off", "packages", "pacman", "page-break", "paint", "paint-filled", "paint-off", "palette", "palette-filled", "palette-off", "panorama-horizontal", "panorama-horizontal-filled", "panorama-horizontal-off", "panorama-vertical", "panorama-vertical-filled", "panorama-vertical-off", "paper-bag", "paper-bag-off", "paperclip", "parachute", "parachute-off", "parentheses", "parentheses-off", "parking", "parking-circle", "parking-circle-filled", "parking-off", "password", "password-fingerprint", "password-mobile-phone", "password-user", "paw", "paw-filled", "paw-off", "paywall", "pdf", "peace", "pencil", "pencil-bolt", "pencil-cancel", "pencil-check", "pencil-code", "pencil-cog", "pencil-discount", "pencil-dollar", "pencil-down", "pencil-exclamation", "pencil-heart", "pencil-minus", "pencil-off", "pencil-pause", "pencil-pin", "pencil-plus", "pencil-question", "pencil-search", "pencil-share", "pencil-star", "pencil-up", "pencil-x", "pennant", "pennant-2", "pennant-2-filled", "pennant-filled", "pennant-off", "pentagon", "pentagon-filled", "pentagon-minus", "pentagon-number-0", "pentagon-number-1", "pentagon-number-2", "pentagon-number-3", "pentagon-number-4", "pentagon-number-5", "pentagon-number-6", "pentagon-number-7", "pentagon-number-8", "pentagon-number-9", "pentagon-off", "pentagon-plus", "pentagon-x", "pentagram", "pepper", "pepper-off", "percentage", "percentage-0", "percentage-10", "percentage-100", "percentage-20", "percentage-25", "percentage-30", "percentage-33", "percentage-40", "percentage-50", "percentage-60", "percentage-66", "percentage-70", "percentage-75", "percentage-80", "percentage-90", "perfume", "perspective", "perspective-off", "phone", "phone-call", "phone-calling", "phone-check", "phone-done", "phone-end", "phone-filled", "phone-incoming", "phone-off", "phone-outgoing", "phone-pause", "phone-plus", "phone-ringing", "phone-spark", "phone-x", "photo", "photo-ai", "photo-bitcoin", "photo-bolt", "photo-cancel", "photo-check", "photo-circle", "photo-circle-minus", "photo-circle-plus", "photo-code", "photo-cog", "photo-dollar", "photo-down", "photo-edit", "photo-exclamation", "photo-filled", "photo-heart", "photo-hexagon", "photo-minus", "photo-off", "photo-pause", "photo-pentagon", "photo-pin", "photo-plus", "photo-question", "photo-scan", "photo-search", "photo-sensor", "photo-sensor-2", "photo-sensor-3", "photo-share", "photo-shield", "photo-spark", "photo-square-rounded", "photo-star", "photo-up", "photo-video", "photo-x", "physotherapist", "piano", "pick", "picnic-table", "picture-in-picture", "picture-in-picture-filled", "picture-in-picture-off", "picture-in-picture-on", "picture-in-picture-top", "picture-in-picture-top-filled", "pig", "pig-filled", "pig-money", "pig-off", "pilcrow", "pilcrow-left", "pilcrow-right", "pill", "pill-filled", "pill-off", "pills", "pin", "pin-end", "pin-filled", "pin-invoke", "ping-pong", "pinned", "pinned-filled", "pinned-off", "pizza", "pizza-filled", "pizza-off", "placeholder", "plane", "plane-arrival", "plane-departure", "plane-inflight", "plane-off", "plane-tilt", "planet", "planet-off", "plant", "plant-2", "plant-2-off", "plant-off", "play-basketball", "play-card", "play-card-1", "play-card-1-filled", "play-card-10", "play-card-10-filled", "play-card-2", "play-card-2-filled", "play-card-3", "play-card-3-filled", "play-card-4", "play-card-4-filled", "play-card-5", "play-card-5-filled", "play-card-6", "play-card-6-filled", "play-card-7", "play-card-7-filled", "play-card-8", "play-card-8-filled", "play-card-9", "play-card-9-filled", "play-card-a", "play-card-a-filled", "play-card-j", "play-card-j-filled", "play-card-k", "play-card-k-filled", "play-card-off", "play-card-q", "play-card-q-filled", "play-card-star", "play-card-star-filled", "play-football", "play-handball", "play-volleyball", "player-eject", "player-eject-filled", "player-pause", "player-pause-filled", "player-play", "player-play-filled", "player-record", "player-record-filled", "player-skip-back", "player-skip-back-filled", "player-skip-forward", "player-skip-forward-filled", "player-stop", "player-stop-filled", "player-track-next", "player-track-next-filled", "player-track-prev", "player-track-prev-filled", "playlist", "playlist-add", "playlist-off", "playlist-x", "playstation-circle", "playstation-square", "playstation-triangle", "playstation-x", "plug", "plug-connected", "plug-connected-x", "plug-off", "plug-x", "plus", "plus-equal", "plus-minus", "png", "podium", "podium-off", "point", "point-filled", "point-off", "pointer", "pointer-bolt", "pointer-cancel", "pointer-check", "pointer-code", "pointer-cog", "pointer-dollar", "pointer-down", "pointer-exclamation", "pointer-filled", "pointer-heart", "pointer-minus", "pointer-off", "pointer-pause", "pointer-pin", "pointer-plus", "pointer-question", "pointer-search", "pointer-share", "pointer-star", "pointer-up", "pointer-x", "pokeball", "pokeball-off", "poker-chip", "polaroid", "polaroid-filled", "polygon", "polygon-off", "poo", "poo-filled", "pool", "pool-off", "power", "pray", "premium-rights", "prescription", "presentation", "presentation-analytics", "presentation-analytics-filled", "presentation-filled", "presentation-off", "printer", "printer-off", "prism", "prism-light", "prism-off", "prism-plus", "prison", "progress", "progress-alert", "progress-bolt", "progress-check", "progress-down", "progress-help", "progress-x", "prompt", "prong", "propeller", "propeller-off", "protocol", "pumpkin-scary", "puzzle", "puzzle-2", "puzzle-filled", "puzzle-off", "pyramid", "pyramid-off", "pyramid-plus", "qrcode", "qrcode-off", "question-mark", "quote", "quote-filled", "quote-off", "quotes", "radar", "radar-2", "radar-filled", "radar-off", "radio", "radio-off", "radioactive", "radioactive-filled", "radioactive-off", "radius-bottom-left", "radius-bottom-right", "radius-top-left", "radius-top-right", "rainbow", "rainbow-off", "rating-12-plus", "rating-14-plus", "rating-16-plus", "rating-18-plus", "rating-21-plus", "razor", "razor-electric", "receipt", "receipt-2", "receipt-bitcoin", "receipt-dollar", "receipt-dollar-filled", "receipt-euro", "receipt-euro-filled", "receipt-filled", "receipt-off", "receipt-pound", "receipt-pound-filled", "receipt-refund", "receipt-rupee", "receipt-rupee-filled", "receipt-tax", "receipt-yen", "receipt-yen-filled", "receipt-yuan", "receipt-yuan-filled", "recharging", "record-mail", "record-mail-off", "rectangle", "rectangle-filled", "rectangle-rounded-bottom", "rectangle-rounded-top", "rectangle-vertical", "rectangle-vertical-filled", "rectangular-prism", "rectangular-prism-off", "rectangular-prism-plus", "recycle", "recycle-off", "refresh", "refresh-alert", "refresh-dot", "refresh-off", "regex", "regex-off", "registered", "relation-many-to-many", "relation-many-to-many-filled", "relation-one-to-many", "relation-one-to-many-filled", "relation-one-to-one", "relation-one-to-one-filled", "reload", "reorder", "repeat", "repeat-off", "repeat-once", "replace", "replace-filled", "replace-off", "replace-user", "report", "report-analytics", "report-medical", "report-money", "report-off", "report-search", "reserved-line", "resize", "restore", "rewind-backward-10", "rewind-backward-15", "rewind-backward-20", "rewind-backward-30", "rewind-backward-40", "rewind-backward-5", "rewind-backward-50", "rewind-backward-60", "rewind-forward-10", "rewind-forward-15", "rewind-forward-20", "rewind-forward-30", "rewind-forward-40", "rewind-forward-5", "rewind-forward-50", "rewind-forward-60", "ribbon-health", "rings", "ripple", "ripple-off", "road", "road-off", "road-sign", "robot", "robot-face", "robot-off", "rocket", "rocket-off", "roller-skating", "rollercoaster", "rollercoaster-filled", "rollercoaster-off", "rosette", "rosette-discount", "rosette-discount-check", "rosette-discount-check-filled", "rosette-discount-check-off", "rosette-discount-filled", "rosette-discount-off", "rosette-filled", "rosette-number-0", "rosette-number-1", "rosette-number-2", "rosette-number-3", "rosette-number-4", "rosette-number-5", "rosette-number-6", "rosette-number-7", "rosette-number-8", "rosette-number-9", "rotate", "rotate-2", "rotate-360", "rotate-3d", "rotate-clockwise", "rotate-clockwise-2", "rotate-dot", "rotate-rectangle", "route", "route-2", "route-alt-left", "route-alt-right", "route-off", "route-scan", "route-square", "route-square-2", "route-x", "route-x-2", "router", "router-off", "row-insert-bottom", "row-insert-top", "row-remove", "rss", "rubber-stamp", "rubber-stamp-off", "ruler", "ruler-2", "ruler-2-off", "ruler-3", "ruler-measure", "ruler-measure-2", "ruler-off", "run", "rv-truck", "s-turn-down", "s-turn-left", "s-turn-right", "s-turn-up", "sailboat", "sailboat-2", "sailboat-off", "salad", "salad-filled", "salt", "sandbox", "satellite", "satellite-off", "sausage", "scale", "scale-off", "scale-outline", "scale-outline-off", "scan", "scan-eye", "scan-position", "schema", "schema-off", "school", "school-bell", "school-off", "scissors", "scissors-off", "scooter", "scooter-electric", "scoreboard", "screen-share", "screen-share-off", "screenshot", "scribble", "scribble-off", "script", "script-minus", "script-plus", "script-x", "scuba-diving", "scuba-diving-tank", "scuba-diving-tank-filled", "scuba-mask", "scuba-mask-off", "sdk", "search", "search-off", "section", "section-filled", "section-sign", "seedling", "seedling-filled", "seedling-off", "select", "select-all", "selector", "send", "send-2", "send-off", "seo", "separator", "separator-horizontal", "separator-vertical", "server", "server-2", "server-bolt", "server-cog", "server-off", "server-spark", "servicemark", "settings", "settings-2", "settings-automation", "settings-bolt", "settings-cancel", "settings-check", "settings-code", "settings-cog", "settings-dollar", "settings-down", "settings-exclamation", "settings-filled", "settings-heart", "settings-minus", "settings-off", "settings-pause", "settings-pin", "settings-plus", "settings-question", "settings-search", "settings-share", "settings-spark", "settings-star", "settings-up", "settings-x", "shadow", "shadow-off", "shape", "shape-2", "shape-3", "shape-off", "share", "share-2", "share-3", "share-off", "shareplay", "shield", "shield-bolt", "shield-cancel", "shield-check", "shield-check-filled", "shield-checkered", "shield-checkered-filled", "shield-chevron", "shield-code", "shield-cog", "shield-dollar", "shield-down", "shield-exclamation", "shield-filled", "shield-half", "shield-half-filled", "shield-heart", "shield-lock", "shield-lock-filled", "shield-minus", "shield-off", "shield-pause", "shield-pin", "shield-plus", "shield-question", "shield-search", "shield-share", "shield-star", "shield-up", "shield-x", "ship", "ship-off", "shirt", "shirt-filled", "shirt-off", "shirt-sport", "shoe", "shoe-off", "shopping-bag", "shopping-bag-check", "shopping-bag-discount", "shopping-bag-edit", "shopping-bag-exclamation", "shopping-bag-heart", "shopping-bag-minus", "shopping-bag-plus", "shopping-bag-search", "shopping-bag-x", "shopping-cart", "shopping-cart-bolt", "shopping-cart-cancel", "shopping-cart-check", "shopping-cart-code", "shopping-cart-cog", "shopping-cart-copy", "shopping-cart-discount", "shopping-cart-dollar", "shopping-cart-down", "shopping-cart-exclamation", "shopping-cart-filled", "shopping-cart-heart", "shopping-cart-minus", "shopping-cart-off", "shopping-cart-pause", "shopping-cart-pin", "shopping-cart-plus", "shopping-cart-question", "shopping-cart-search", "shopping-cart-share", "shopping-cart-star", "shopping-cart-up", "shopping-cart-x", "shovel", "shovel-pitchforks", "shredder", "sign-left", "sign-left-filled", "sign-right", "sign-right-filled", "signal-2g", "signal-3g", "signal-4g", "signal-4g-plus", "signal-5g", "signal-6g", "signal-e", "signal-g", "signal-h", "signal-h-plus", "signal-lte", "signature", "signature-off", "sitemap", "sitemap-filled", "sitemap-off", "skateboard", "skateboard-off", "skateboarding", "skew-x", "skew-y", "ski-jumping", "skull", "slash", "slashes", "sleigh", "slice", "slideshow", "smart-home", "smart-home-off", "smoking", "smoking-no", "snowboarding", "snowflake", "snowflake-off", "snowman", "soccer-field", "social", "social-off", "sock", "sofa", "sofa-off", "solar-electricity", "solar-panel", "solar-panel-2", "sort-0-9", "sort-9-0", "sort-a-z", "sort-ascending", "sort-ascending-2", "sort-ascending-2-filled", "sort-ascending-letters", "sort-ascending-numbers", "sort-ascending-shapes", "sort-ascending-shapes-filled", "sort-ascending-small-big", "sort-descending", "sort-descending-2", "sort-descending-2-filled", "sort-descending-letters", "sort-descending-numbers", "sort-descending-shapes", "sort-descending-shapes-filled", "sort-descending-small-big", "sort-z-a", "sos", "soup", "soup-filled", "soup-off", "source-code", "space", "space-off", "spaces", "spacing-horizontal", "spacing-vertical", "spade", "spade-filled", "sparkles", "speakerphone", "speedboat", "speedboat-filled", "sphere", "sphere-off", "sphere-plus", "spider", "spider-filled", "spiral", "spiral-off", "sport-billard", "spray", "spy", "spy-off", "sql", "square", "square-arrow-down", "square-arrow-down-filled", "square-arrow-left", "square-arrow-left-filled", "square-arrow-right", "square-arrow-right-filled", "square-arrow-up", "square-arrow-up-filled", "square-asterisk", "square-asterisk-filled", "square-check", "square-check-filled", "square-chevron-down", "square-chevron-down-filled", "square-chevron-left", "square-chevron-left-filled", "square-chevron-right", "square-chevron-right-filled", "square-chevron-up", "square-chevron-up-filled", "square-chevrons-down", "square-chevrons-down-filled", "square-chevrons-left", "square-chevrons-left-filled", "square-chevrons-right", "square-chevrons-right-filled", "square-chevrons-up", "square-chevrons-up-filled", "square-dashed", "square-dot", "square-dot-filled", "square-f0", "square-f0-filled", "square-f1", "square-f1-filled", "square-f2", "square-f2-filled", "square-f3", "square-f3-filled", "square-f4", "square-f4-filled", "square-f5", "square-f5-filled", "square-f6", "square-f6-filled", "square-f7", "square-f7-filled", "square-f8", "square-f8-filled", "square-f9", "square-f9-filled", "square-filled", "square-forbid", "square-forbid-2", "square-half", "square-key", "square-letter-a", "square-letter-a-filled", "square-letter-b", "square-letter-b-filled", "square-letter-c", "square-letter-c-filled", "square-letter-d", "square-letter-d-filled", "square-letter-e", "square-letter-e-filled", "square-letter-f", "square-letter-f-filled", "square-letter-g", "square-letter-g-filled", "square-letter-h", "square-letter-h-filled", "square-letter-i", "square-letter-i-filled", "square-letter-j", "square-letter-j-filled", "square-letter-k", "square-letter-k-filled", "square-letter-l", "square-letter-l-filled", "square-letter-m", "square-letter-m-filled", "square-letter-n", "square-letter-n-filled", "square-letter-o", "square-letter-o-filled", "square-letter-p", "square-letter-p-filled", "square-letter-q", "square-letter-q-filled", "square-letter-r", "square-letter-r-filled", "square-letter-s", "square-letter-s-filled", "square-letter-t", "square-letter-t-filled", "square-letter-u", "square-letter-u-filled", "square-letter-v", "square-letter-v-filled", "square-letter-w", "square-letter-w-filled", "square-letter-x", "square-letter-x-filled", "square-letter-y", "square-letter-y-filled", "square-letter-z", "square-letter-z-filled", "square-minus", "square-minus-filled", "square-number-0", "square-number-0-filled", "square-number-1", "square-number-1-filled", "square-number-2", "square-number-2-filled", "square-number-3", "square-number-3-filled", "square-number-4", "square-number-4-filled", "square-number-5", "square-number-5-filled", "square-number-6", "square-number-6-filled", "square-number-7", "square-number-7-filled", "square-number-8", "square-number-8-filled", "square-number-9", "square-number-9-filled", "square-off", "square-percentage", "square-plus", "square-plus-2", "square-root", "square-root-2", "square-rotated", "square-rotated-filled", "square-rotated-forbid", "square-rotated-forbid-2", "square-rotated-off", "square-rounded", "square-rounded-arrow-down", "square-rounded-arrow-down-filled", "square-rounded-arrow-left", "square-rounded-arrow-left-filled", "square-rounded-arrow-right", "square-rounded-arrow-right-filled", "square-rounded-arrow-up", "square-rounded-arrow-up-filled", "square-rounded-check", "square-rounded-check-filled", "square-rounded-chevron-down", "square-rounded-chevron-down-filled", "square-rounded-chevron-left", "square-rounded-chevron-left-filled", "square-rounded-chevron-right", "square-rounded-chevron-right-filled", "square-rounded-chevron-up", "square-rounded-chevron-up-filled", "square-rounded-chevrons-down", "square-rounded-chevrons-down-filled", "square-rounded-chevrons-left", "square-rounded-chevrons-left-filled", "square-rounded-chevrons-right", "square-rounded-chevrons-right-filled", "square-rounded-chevrons-up", "square-rounded-chevrons-up-filled", "square-rounded-filled", "square-rounded-letter-a", "square-rounded-letter-a-filled", "square-rounded-letter-b", "square-rounded-letter-b-filled", "square-rounded-letter-c", "square-rounded-letter-c-filled", "square-rounded-letter-d", "square-rounded-letter-d-filled", "square-rounded-letter-e", "square-rounded-letter-e-filled", "square-rounded-letter-f", "square-rounded-letter-f-filled", "square-rounded-letter-g", "square-rounded-letter-g-filled", "square-rounded-letter-h", "square-rounded-letter-h-filled", "square-rounded-letter-i", "square-rounded-letter-i-filled", "square-rounded-letter-j", "square-rounded-letter-j-filled", "square-rounded-letter-k", "square-rounded-letter-k-filled", "square-rounded-letter-l", "square-rounded-letter-l-filled", "square-rounded-letter-m", "square-rounded-letter-m-filled", "square-rounded-letter-n", "square-rounded-letter-n-filled", "square-rounded-letter-o", "square-rounded-letter-o-filled", "square-rounded-letter-p", "square-rounded-letter-p-filled", "square-rounded-letter-q", "square-rounded-letter-q-filled", "square-rounded-letter-r", "square-rounded-letter-r-filled", "square-rounded-letter-s", "square-rounded-letter-s-filled", "square-rounded-letter-t", "square-rounded-letter-t-filled", "square-rounded-letter-u", "square-rounded-letter-u-filled", "square-rounded-letter-v", "square-rounded-letter-v-filled", "square-rounded-letter-w", "square-rounded-letter-w-filled", "square-rounded-letter-x", "square-rounded-letter-x-filled", "square-rounded-letter-y", "square-rounded-letter-y-filled", "square-rounded-letter-z", "square-rounded-letter-z-filled", "square-rounded-minus", "square-rounded-minus-2", "square-rounded-minus-filled", "square-rounded-number-0", "square-rounded-number-0-filled", "square-rounded-number-1", "square-rounded-number-1-filled", "square-rounded-number-2", "square-rounded-number-2-filled", "square-rounded-number-3", "square-rounded-number-3-filled", "square-rounded-number-4", "square-rounded-number-4-filled", "square-rounded-number-5", "square-rounded-number-5-filled", "square-rounded-number-6", "square-rounded-number-6-filled", "square-rounded-number-7", "square-rounded-number-7-filled", "square-rounded-number-8", "square-rounded-number-8-filled", "square-rounded-number-9", "square-rounded-number-9-filled", "square-rounded-percentage", "square-rounded-plus", "square-rounded-plus-2", "square-rounded-plus-filled", "square-rounded-x", "square-rounded-x-filled", "square-toggle", "square-toggle-horizontal", "square-x", "square-x-filled", "squares", "squares-diagonal", "squares-filled", "squares-selected", "stack", "stack-2", "stack-2-filled", "stack-3", "stack-3-filled", "stack-back", "stack-backward", "stack-filled", "stack-forward", "stack-front", "stack-middle", "stack-pop", "stack-push", "stairs", "stairs-down", "stairs-up", "star", "star-filled", "star-half", "star-half-filled", "star-off", "stars", "stars-filled", "stars-off", "status-change", "steam", "steering-wheel", "steering-wheel-filled", "steering-wheel-off", "step-into", "step-out", "stereo-glasses", "stethoscope", "stethoscope-off", "sticker", "sticker-2", "stopwatch", "storm", "storm-off", "stretching", "stretching-2", "strikethrough", "submarine", "subscript", "subtask", "sum", "sum-off", "sun", "sun-electricity", "sun-filled", "sun-high", "sun-high-filled", "sun-low", "sun-low-filled", "sun-moon", "sun-off", "sun-wind", "sunglasses", "sunglasses-filled", "sunrise", "sunrise-filled", "sunset", "sunset-2", "sunset-2-filled", "sunset-filled", "superscript", "svg", "swimming", "swipe", "swipe-down", "swipe-down-filled", "swipe-left", "swipe-left-filled", "swipe-right", "swipe-right-filled", "swipe-up", "swipe-up-filled", "switch", "switch-2", "switch-3", "switch-horizontal", "switch-vertical", "sword", "sword-off", "swords", "table", "table-alias", "table-column", "table-dashed", "table-down", "table-export", "table-filled", "table-heart", "table-import", "table-minus", "table-off", "table-options", "table-plus", "table-row", "table-share", "table-shortcut", "table-spark", "tag", "tag-filled", "tag-minus", "tag-off", "tag-plus", "tag-starred", "tags", "tags-filled", "tags-off", "tallymark-1", "tallymark-2", "tallymark-3", "tallymark-4", "tallymarks", "tank", "target", "target-arrow", "target-off", "tax", "tax-euro", "tax-pound", "teapot", "telescope", "telescope-off", "temperature", "temperature-celsius", "temperature-fahrenheit", "temperature-minus", "temperature-minus-filled", "temperature-off", "temperature-plus", "temperature-plus-filled", "temperature-snow", "temperature-sun", "template", "template-filled", "template-off", "tent", "tent-off", "terminal", "terminal-2", "test-pipe", "test-pipe-2", "test-pipe-2-filled", "test-pipe-off", "tex", "text-caption", "text-color", "text-decrease", "text-direction-ltr", "text-direction-rtl", "text-grammar", "text-increase", "text-orientation", "text-plus", "text-recognition", "text-resize", "text-scan-2", "text-size", "text-spellcheck", "text-wrap", "text-wrap-column", "text-wrap-disabled", "texture", "theater", "thermometer", "thumb-down", "thumb-down-filled", "thumb-down-off", "thumb-up", "thumb-up-filled", "thumb-up-off", "tic-tac", "ticket", "ticket-off", "tie", "tilde", "tilt-shift", "tilt-shift-filled", "tilt-shift-off", "time-duration-0", "time-duration-10", "time-duration-15", "time-duration-30", "time-duration-45", "time-duration-5", "time-duration-60", "time-duration-90", "time-duration-off", "timeline", "timeline-event", "timeline-event-exclamation", "timeline-event-filled", "timeline-event-minus", "timeline-event-plus", "timeline-event-text", "timeline-event-x", "timezone", "tip-jar", "tip-jar-euro", "tip-jar-pound", "tir", "toggle-left", "toggle-left-filled", "toggle-right", "toggle-right-filled", "toilet-paper", "toilet-paper-off", "toml", "tool", "tools", "tools-kitchen", "tools-kitchen-2", "tools-kitchen-2-off", "tools-kitchen-3", "tools-kitchen-off", "tools-off", "tooltip", "topology-bus", "topology-complex", "topology-full", "topology-full-hierarchy", "topology-ring", "topology-ring-2", "topology-ring-3", "topology-star", "topology-star-2", "topology-star-3", "topology-star-ring", "topology-star-ring-2", "topology-star-ring-3", "to<PERSON>i", "tornado", "tournament", "tower", "tower-off", "track", "tractor", "trademark", "traffic-cone", "traffic-cone-off", "traffic-lights", "traffic-lights-off", "train", "train-filled", "transaction-bitcoin", "transaction-dollar", "transaction-euro", "transaction-pound", "transaction-rupee", "transaction-yen", "transaction-yuan", "transfer", "transfer-in", "transfer-out", "transfer-vertical", "transform", "transform-filled", "transform-point", "transform-point-bottom-left", "transform-point-bottom-right", "transform-point-top-left", "transform-point-top-right", "transition-bottom", "transition-bottom-filled", "transition-left", "transition-left-filled", "transition-right", "transition-right-filled", "transition-top", "transition-top-filled", "trash", "trash-filled", "trash-off", "trash-x", "trash-x-filled", "treadmill", "tree", "trees", "trekking", "trending-down", "trending-down-2", "trending-down-3", "trending-up", "trending-up-2", "trending-up-3", "triangle", "triangle-filled", "triangle-inverted", "triangle-inverted-filled", "triangle-minus", "triangle-minus-2", "triangle-off", "triangle-plus", "triangle-plus-2", "triangle-square-circle", "triangle-square-circle-filled", "triangles", "trident", "trolley", "trolley-filled", "trophy", "trophy-filled", "trophy-off", "trowel", "truck", "truck-delivery", "truck-filled", "truck-loading", "truck-off", "truck-return", "txt", "typeface", "typography", "typography-off", "u-turn-left", "u-turn-right", "ufo", "ufo-filled", "ufo-off", "uhd", "umbrella", "umbrella-2", "umbrella-closed", "umbrella-closed-2", "umbrella-filled", "umbrella-off", "underline", "universe", "unlink", "upload", "urgent", "usb", "user", "user-bitcoin", "user-bolt", "user-cancel", "user-check", "user-circle", "user-code", "user-cog", "user-dollar", "user-down", "user-edit", "user-exclamation", "user-filled", "user-heart", "user-hexagon", "user-minus", "user-off", "user-pause", "user-pentagon", "user-pin", "user-plus", "user-question", "user-scan", "user-screen", "user-search", "user-share", "user-shield", "user-square", "user-square-rounded", "user-star", "user-up", "user-x", "users", "users-group", "users-minus", "users-plus", "uv-index", "ux-circle", "vaccine", "vaccine-bottle", "vaccine-bottle-off", "vaccine-off", "vacuum-cleaner", "variable", "variable-minus", "variable-off", "variable-plus", "vector", "vector-bezier", "vector-bezier-2", "vector-bezier-arc", "vector-bezier-circle", "vector-off", "vector-spline", "vector-triangle", "vector-triangle-off", "venus", "versions", "versions-filled", "versions-off", "video", "video-filled", "video-minus", "video-off", "video-plus", "view-360", "view-360-arrow", "view-360-number", "view-360-off", "viewfinder", "viewfinder-off", "viewport-narrow", "viewport-short", "viewport-tall", "viewport-wide", "vinyl", "vip", "vip-off", "virus", "virus-off", "virus-search", "vocabulary", "vocabulary-off", "volcano", "volume", "volume-2", "volume-3", "volume-off", "vs", "walk", "wall", "wall-off", "wallet", "wallet-off", "wallpaper", "wallpaper-off", "wand", "wand-off", "wash", "wash-dry", "wash-dry-1", "wash-dry-2", "wash-dry-3", "wash-dry-a", "wash-dry-dip", "wash-dry-f", "wash-dry-flat", "wash-dry-hang", "wash-dry-off", "wash-dry-p", "wash-dry-shade", "wash-dry-w", "wash-dryclean", "wash-dryclean-off", "wash-eco", "wash-gentle", "wash-hand", "wash-machine", "wash-off", "wash-press", "wash-temperature-1", "wash-temperature-2", "wash-temperature-3", "wash-temperature-4", "wash-temperature-5", "wash-temperature-6", "wash-tumble-dry", "wash-tumble-off", "waterpolo", "wave-saw-tool", "wave-sine", "wave-square", "waves-electricity", "webhook", "webhook-off", "weight", "wheat", "wheat-off", "wheel", "wheelchair", "wheelchair-off", "whirl", "wifi", "wifi-0", "wifi-1", "wifi-2", "wifi-off", "wind", "wind-electricity", "wind-off", "windmill", "windmill-filled", "windmill-off", "window", "window-maximize", "window-minimize", "window-off", "windsock", "windsock-filled", "wiper", "wiper-wash", "woman", "woman-filled", "wood", "world", "world-bolt", "world-cancel", "world-check", "world-code", "world-cog", "world-dollar", "world-down", "world-download", "world-exclamation", "world-heart", "world-latitude", "world-longitude", "world-minus", "world-off", "world-pause", "world-pin", "world-plus", "world-question", "world-search", "world-share", "world-star", "world-up", "world-upload", "world-www", "world-x", "wrecking-ball", "writing", "writing-off", "writing-sign", "writing-sign-off", "x", "x-power-y", "xbox-a", "xbox-a-filled", "xbox-b", "xbox-b-filled", "xbox-x", "xbox-x-filled", "xbox-y", "xbox-y-filled", "xd", "xxx", "yin-yang", "yin-yang-filled", "yoga", "zeppelin", "zeppelin-filled", "zeppelin-off", "zip", "zodiac-aquarius", "zodiac-aries", "zodiac-cancer", "zodiac-capricorn", "zodiac-gemini", "zodiac-leo", "zodiac-libra", "zodiac-pisces", "zodiac-sagittarius", "zodiac-scorpio", "zodiac-taurus", "zodiac-virgo", "zoom", "zoom-cancel", "zoom-cancel-filled", "zoom-check", "zoom-check-filled", "zoom-code", "zoom-code-filled", "zoom-exclamation", "zoom-exclamation-filled", "zoom-filled", "zoom-in", "zoom-in-area", "zoom-in-area-filled", "zoom-in-filled", "zoom-money", "zoom-money-filled", "zoom-out", "zoom-out-area", "zoom-out-area-filled", "zoom-out-filled", "zoom-pan", "zoom-pan-filled", "zoom-question", "zoom-question-filled", "zoom-replace", "zoom-reset", "zoom-scan", "zoom-scan-filled", "zzz", "zzz-off"], "hidden": ["accessible-off-filled", "arrow-bottom-circle", "article-filled-filled", "barell", "box-seam", "circle-0-filled", "circle-1-filled", "circle-2-filled", "circle-3-filled", "circle-4-filled", "circle-5-filled", "circle-6-filled", "circle-7-filled", "circle-8-filled", "circle-9-filled", "discount-check-filled", "droplet-filled-2", "hexagon-0-filled", "hexagon-1-filled", "hexagon-2-filled", "hexagon-3-filled", "hexagon-4-filled", "hexagon-5-filled", "hexagon-6-filled", "hexagon-7-filled", "hexagon-8-filled", "hexagon-9-filled", "letters-case", "message-circle-2", "message-circle-2-filled", "pause", "play", "question-circle", "skip-back", "skip-forward", "square-0-filled", "square-1-filled", "square-2-filled", "square-3-filled", "square-4-filled", "square-5-filled", "square-6-filled", "square-7-filled", "square-8-filled", "square-9-filled", "test", "track-next", "track-prev"], "aliases": {"123": "number-123", "360": "view-360-arrow", "12-hours": "hours-12", "24-hours": "hours-24", "2fa": "auth-2fa", "360-view": "view-360-number", "3d-cube-sphere": "cube-3d-sphere", "3d-cube-sphere-off": "cube-3d-sphere-off", "3d-rotate": "rotate-3d", "arrow-big-top": "arrow-big-up", "arrow-bottom-bar": "arrow-down-bar", "arrow-bottom-square": "arrow-down-square", "arrow-bottom-tail": "arrow-down-tail", "arrow-top-bar": "arrow-up-bar", "arrow-top-circle": "arrow-up-circle", "arrow-top-square": "arrow-up-square", "arrow-top-tail": "arrow-up-tail", "ballon": "balloon", "ballon-off": "balloon-off", "braile": "braille", "brand-albolia": "brand-algolia", "brand-amongus": "brand-among-us", "brand-blackbery": "brand-blackberry", "brand-microsoft-teams": "brand-teams", "brand-stack-ofverflow": "brand-stackoverflow", "brand-tikto-filled": "brand-tiktok-filled", "brand-vcypress": "brand-cypress", "building-pavilon": "building-pavilion", "buldozer": "bulldozer", "c-sharp": "brand-c-sharp", "cart-bolt": "shopping-cart-bolt", "cart-cancel": "shopping-cart-cancel", "cart-check": "shopping-cart-check", "cart-code": "shopping-cart-code", "cart-cog": "shopping-cart-cog", "cart-copy": "shopping-cart-check", "cart-discount": "shopping-cart-discount", "cart-dollar": "shopping-cart-dollar", "cart-down": "shopping-cart-down", "cart-exclamation": "shopping-cart-exclamation", "cart-heart": "shopping-cart-heart", "cart-minus": "shopping-cart-minus", "cart-pause": "shopping-cart-pause", "cart-pin": "shopping-cart-pin", "cart-plus": "shopping-cart-plus", "cart-question": "shopping-cart-question", "cart-search": "shopping-cart-search", "cart-share": "shopping-cart-share", "cart-star": "shopping-cart-star", "cart-up": "shopping-cart-up", "cart-x": "shopping-cart-x", "circle-0": "circle-number-0", "circle-1": "circle-number-1", "circle-2": "circle-number-2", "circle-3": "circle-number-3", "circle-4": "circle-number-4", "circle-5": "circle-number-5", "circle-6": "circle-number-6", "circle-7": "circle-number-7", "circle-8": "circle-number-8", "circle-9": "circle-number-9", "circle-a": "circle-letter-a", "circle-b": "circle-letter-b", "circle-c": "circle-letter-c", "circle-d": "circle-letter-d", "circle-dashed-letter-letter-v": "circle-dashed-letter-v", "circle-e": "circle-letter-e", "circle-f": "circle-letter-f", "circle-g": "circle-letter-g", "circle-h": "circle-letter-h", "circle-i": "circle-letter-i", "circle-j": "circle-letter-j", "circle-k": "circle-letter-k", "circle-l": "circle-letter-l", "circle-m": "circle-letter-m", "circle-n": "circle-letter-n", "circle-o": "circle-letter-o", "circle-p": "circle-letter-p", "circle-q": "circle-letter-q", "circle-r": "circle-letter-r", "circle-s": "circle-letter-s", "circle-t": "circle-letter-t", "circle-u": "circle-letter-u", "circle-w": "circle-letter-w", "circle-y": "circle-letter-y", "circle-z": "circle-letter-z", "code-asterix": "code-asterisk", "currency-bath": "currency-baht", "device-game-pad": "device-gamepad-3", "discount-2": "rosette-discount", "discount-2-off": "rosette-discount-off", "discount-check": "rosette-discount-check", "electric-scooter": "scooter", "grid": "layout-grid-add", "ground": "circuit-ground", "hand-rock": "hand-love-you", "hexagon-0": "hexagon-number-0", "hexagon-1": "hexagon-number-1", "hexagon-2": "hexagon-number-2", "hexagon-3": "hexagon-number-3", "hexagon-4": "hexagon-number-4", "hexagon-5": "hexagon-number-5", "hexagon-6": "hexagon-number-6", "hexagon-7": "hexagon-number-7", "hexagon-8": "hexagon-number-8", "hexagon-9": "hexagon-number-9", "hexagon-a": "hexagon-letter-a", "hexagon-b": "hexagon-letter-b", "hexagon-c": "hexagon-letter-c", "hexagon-d": "hexagon-letter-d", "hexagon-e": "hexagon-letter-e", "hexagon-f": "hexagon-letter-f", "hexagon-g": "hexagon-letter-g", "hexagon-h": "hexagon-letter-h", "hexagon-i": "hexagon-letter-i", "hexagon-j": "hexagon-letter-j", "hexagon-k": "hexagon-letter-k", "hexagon-l": "hexagon-letter-l", "hexagon-m": "hexagon-letter-m", "hexagon-n": "hexagon-letter-n", "hexagon-o": "hexagon-letter-o", "hexagon-p": "hexagon-letter-p", "hexagon-q": "hexagon-letter-q", "hexagon-r": "hexagon-letter-r", "hexagon-s": "hexagon-letter-s", "hexagon-t": "hexagon-letter-t", "hexagon-u": "hexagon-letter-u", "hexagon-w": "hexagon-letter-w", "hexagon-x": "hexagon-letter-x", "hexagon-y": "hexagon-letter-y", "hexagon-z": "hexagon-letter-z", "http-path": "http-patch", "kering": "kerning", "miliraty-award": "military-award", "mood-suprised": "mood-surprised", "packge-export": "package-export", "packge-import": "package-import", "seeding": "seedling", "seeding-filled": "seedling-filled", "seeding-off": "seedling-off", "shi-jumping": "ski-jumping", "sort-deacending-small-big": "sort-descending-small-big", "square-0": "square-number-0", "square-1": "square-number-1", "square-2": "square-number-2", "square-3": "square-number-3", "square-4": "square-number-4", "square-5": "square-number-5", "square-6": "square-number-6", "square-7": "square-number-7", "square-8": "square-number-8", "square-9": "square-number-9", "square-a": "square-letter-a", "square-b": "square-letter-b", "square-c": "square-letter-c", "square-d": "square-letter-d", "square-e": "square-letter-e", "square-f": "square-letter-f", "square-g": "square-letter-g", "square-h": "square-letter-h", "square-i": "square-letter-i", "square-j": "square-letter-j", "square-k": "square-letter-k", "square-l": "square-letter-l", "square-m": "square-letter-m", "square-n": "square-letter-n", "square-o": "square-letter-o", "square-p": "square-letter-p", "square-q": "square-letter-q", "square-r": "square-letter-r", "square-s": "square-letter-s", "square-t": "square-letter-t", "square-u": "square-letter-u", "square-w": "square-letter-w", "square-y": "square-letter-y", "square-z": "square-letter-z", "sthetoscope": "stethoscope", "sunshine": "sunset", "swiming": "swimming", "uf-off": "ufo-off", "vector-beizer": "vector-bezier", "vector-beizer-2": "vector-bezier-2", "wave-triangle": "activity"}}