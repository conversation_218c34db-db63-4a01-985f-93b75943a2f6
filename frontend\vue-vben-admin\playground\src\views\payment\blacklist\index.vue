<template>
  <Page
    content-class="flex flex-col gap-4"
    description="管理系统IP黑名单，封禁恶意IP地址和IP段"
    title="🚫 IP封禁管理"
  >


    <!-- 黑名单列表 -->
    <Card title="📋 IP封禁列表">
      <template #extra>
        <span class="px-2 py-1 bg-gray-100 text-gray-800 rounded text-xs">{{ filteredBlacklist.length }} 条记录</span>
      </template>



      <!-- 黑名单表格 -->
      <div class="bg-card border border-border rounded-lg overflow-hidden">
        <div v-if="loading" class="flex items-center justify-center py-8">
          <div class="text-muted-foreground">加载中...</div>
        </div>
        <div v-else-if="filteredBlacklist.length === 0" class="flex items-center justify-center py-12">
          <div class="text-center">
            <div class="text-4xl mb-4">🛡️</div>
            <div class="text-lg font-medium text-muted-foreground mb-2">暂无IP封禁数据</div>
            <div class="text-sm text-muted-foreground">当前没有IP封禁记录，系统运行正常</div>
          </div>
        </div>
        <div v-else class="overflow-x-auto">
          <table class="w-full border-collapse">
            <thead>
              <tr class="border-b border-border bg-muted">
                <th class="text-left p-3 font-medium">IP地址</th>
                <th class="text-left p-3 font-medium">原因</th>
                <th class="text-left p-3 font-medium">状态</th>
                <th class="text-left p-3 font-medium">操作</th>
              </tr>
            </thead>
            <tbody>
              <tr
                v-for="item in paginatedBlacklist"
                :key="item.id"
                class="border-b border-border hover:bg-muted/50"
              >
                <!-- IP地址 -->
                <td class="p-3">
                  <div class="font-mono text-sm">{{ item.value }}</div>
                  <div v-if="item.location" class="text-xs text-muted-foreground mt-1">
                    📍 {{ item.location }}
                  </div>
                </td>

                <!-- 原因 -->
                <td class="p-3 max-w-48">
                  <div class="truncate" :title="item.reason">{{ item.reason || '未填写' }}</div>
                </td>

                <!-- 状态 -->
                <td class="p-3">
                  <span class="px-2 py-1 bg-red-100 text-red-800 rounded text-xs">
                    🔴 生效中
                  </span>
                </td>

                <!-- 操作 -->
                <td class="p-3">
                  <VbenButton
                    @click="removeFromBlacklist(item)"
                    variant="destructive"
                    size="sm"
                  >
                    🔓 移除黑名单
                  </VbenButton>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <!-- 分页 -->
      <div v-if="filteredBlacklist.length > pageSize" class="flex justify-between items-center mt-4">
        <div class="text-sm text-gray-600">
          显示 {{ (currentPage - 1) * pageSize + 1 }} - {{ Math.min(currentPage * pageSize, filteredBlacklist.length) }}
          共 {{ filteredBlacklist.length }} 条记录
        </div>
        <div class="flex space-x-2">
          <VbenButton
            @click="currentPage--"
            :disabled="currentPage === 1"
            size="small"
          >
            上一页
          </VbenButton>
          <span class="px-3 py-1 bg-gray-100 rounded text-sm">
            {{ currentPage }} / {{ totalPages }}
          </span>
          <VbenButton
            @click="currentPage++"
            :disabled="currentPage === totalPages"
            size="small"
          >
            下一页
          </VbenButton>
        </div>
      </div>
    </Card>
  </Page>
</template>

<script setup lang="ts">
import { reactive, ref, computed, onMounted } from 'vue'
import { Page, VbenButton } from '@vben/common-ui'
import { Card } from 'ant-design-vue'

// 黑名单项接口
interface BlacklistItem {
  id: number
  type: 'ip'
  value: string
  reason: string
  status: 'active'
  createdAt: string
  expiresAt?: string
  location?: string
  createdBy: string
}

// 状态
const loading = ref(false)



// 分页
const currentPage = ref(1)
const pageSize = ref(10)

// 黑名单数据
const blacklistData = ref<BlacklistItem[]>([])



// 计算属性
const filteredBlacklist = computed(() => {
  return blacklistData.value
})

const paginatedBlacklist = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return filteredBlacklist.value.slice(start, end)
})

const totalPages = computed(() => {
  return Math.ceil(filteredBlacklist.value.length / pageSize.value)
})











// 移除黑名单
const removeFromBlacklist = async (item: BlacklistItem) => {
  if (confirm(`确定要将 ${item.value} 移除黑名单吗？`)) {
    try {
      // TODO: 调用实际的API
      // await api.removeFromBlacklist(item.id)

      const index = blacklistData.value.findIndex(i => i.id === item.id)
      if (index > -1) {
        blacklistData.value.splice(index, 1)
      }
      console.log('已移除黑名单:', item.value)
    } catch (error) {
      console.error('移除黑名单失败:', error)
    }
  }
}











// 初始化
onMounted(() => {
  console.log('黑名单管理页面已加载')
})
</script>
