package common.service.payment;

import core.config.ThreeDSecureConfig;
import common.service.payment.detection.CardTypeDetectionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.util.HashMap;
import java.util.Map;
import java.util.List;
import java.util.Arrays;

/**
 * 3D验证服务客户端
 * 负责与3ds容器服务进行通信
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ThreeDSecureClient {

    private final ThreeDSecureConfig threeDSecureConfig;
    private final WebClient.Builder webClientBuilder;
    private final CardTypeDetectionService cardTypeDetectionService;

    /**
     * 获取3D验证页面HTML
     */
    public Mono<String> getVerificationPage(String templateType, Map<String, Object> variables) {
        WebClient webClient = webClientBuilder
                .baseUrl(threeDSecureConfig.getServiceUrl())
                .build();

        return webClient.get()
                .uri(uriBuilder -> {
                    uriBuilder.path("/{template}.html");
                    if (variables != null) {
                        variables.forEach((key, value) -> 
                            uriBuilder.queryParam(key, value.toString()));
                    }
                    return uriBuilder.build(templateType.toLowerCase().replace("_", "-"));
                })
                .accept(MediaType.TEXT_HTML)
                .retrieve()
                .bodyToMono(String.class)
                .doOnSuccess(html -> log.debug("成功获取3D验证页面: {}", templateType))
                .doOnError(error -> log.error("获取3D验证页面失败: {}", templateType, error));
    }

    /**
     * 检查3ds服务健康状态
     */
    public Mono<Boolean> checkHealth() {
        WebClient webClient = webClientBuilder
                .baseUrl(threeDSecureConfig.getServiceUrl())
                .build();

        return webClient.get()
                .uri("/bank-login.html")
                .retrieve()
                .toBodilessEntity()
                .map(response -> response.getStatusCode().is2xxSuccessful())
                .doOnSuccess(healthy -> log.debug("3ds服务健康检查: {}", healthy ? "正常" : "异常"))
                .doOnError(error -> log.error("3ds服务健康检查失败", error))
                .onErrorReturn(false);
    }

    /**
     * 生成3D验证URL
     */
    public String generateVerificationUrl(String cardId, String templateType, Map<String, Object> params) {
        Map<String, String> urlParams = new HashMap<>();
        urlParams.put("cardId", cardId);
        urlParams.put("template", templateType);
        
        if (params != null) {
            params.forEach((key, value) -> 
                urlParams.put(key, value != null ? value.toString() : ""));
        }
        
        return threeDSecureConfig.getVerificationUrl(templateType, urlParams);
    }

    /**
     * 获取验证页面的直接访问URL
     */
    public String getDirectAccessUrl(String templateType) {
        String fileName = templateType.toLowerCase().replace("_", "-");
        return threeDSecureConfig.getServiceUrl() + "/" + fileName + ".html";
    }

    /**
     * 验证模板是否可用
     */
    public Mono<Boolean> validateTemplate(String templateType) {
        WebClient webClient = webClientBuilder
                .baseUrl(threeDSecureConfig.getServiceUrl())
                .build();

        String fileName = templateType.toLowerCase().replace("_", "-");
        
        return webClient.head()
                .uri("/{template}.html", fileName)
                .retrieve()
                .toBodilessEntity()
                .map(response -> response.getStatusCode().is2xxSuccessful())
                .doOnSuccess(valid -> log.debug("模板验证结果 {}: {}", templateType, valid ? "可用" : "不可用"))
                .doOnError(error -> log.error("验证模板失败: {}", templateType, error))
                .onErrorReturn(false);
    }

    /**
     * 获取所有可用的模板列表
     */
    public Mono<Map<String, Boolean>> getAvailableTemplates() {
        Map<String, Boolean> templateStatus = new HashMap<>();
        
        return Mono.fromCallable(() -> {
            // 检查所有支持的模板
            for (String method : threeDSecureConfig.getSupportedMethods()) {
                try {
                    Boolean isAvailable = validateTemplate(method).block();
                    templateStatus.put(method, isAvailable != null && isAvailable);
                } catch (Exception e) {
                    log.warn("检查模板可用性失败: {}", method, e);
                    templateStatus.put(method, false);
                }
            }
            return templateStatus;
        })
        .doOnSuccess(status -> log.info("模板可用性检查完成: {}", status));
    }

    /**
     * 获取3ds服务信息
     */
    public Map<String, Object> getServiceInfo() {
        Map<String, Object> info = new HashMap<>();
        info.put("serviceUrl", threeDSecureConfig.getServiceUrl());
        info.put("defaultTemplate", threeDSecureConfig.getDefaultTemplate());
        info.put("verificationTimeout", threeDSecureConfig.getVerificationTimeout());
        info.put("supportedMethods", threeDSecureConfig.getSupportedMethods());
        info.put("realTimeSync", threeDSecureConfig.getPageConfig().getRealTimeSync());
        info.put("websocketUrl", threeDSecureConfig.getPageConfig().getWebsocketUrl());
        return info;
    }

    /**
     * 获取可用的模板类型
     */
    public List<String> getAvailableTemplateTypes() {
        log.debug("获取可用的模板类型");
        return threeDSecureConfig.getSupportedMethods();
    }

    /**
     * 检测卡片类型
     */
    public String detectCardType(String cardNumber) {
        log.debug("检测卡片类型: {}", cardNumber);
        return cardTypeDetectionService.detectCardType(cardNumber).block();
    }

    /**
     * 获取推荐的模板
     */
    public String getRecommendedTemplate(String cardNumber) {
        log.debug("获取推荐的模板: {}", cardNumber);
        return cardTypeDetectionService.getRecommendedTemplateType(cardNumber).block();
    }

    /**
     * 渲染多个模板
     */
    public Mono<Map<String, String>> renderMultipleTemplates(List<String> templateTypes, Map<String, Object> variables) {
        log.debug("渲染多个模板: {}", templateTypes);

        Map<String, String> results = new HashMap<>();
        for (String templateType : templateTypes) {
            // 调用现有的渲染方法
            String rendered = getVerificationPage(templateType, variables).block();
            results.put(templateType, rendered);
        }

        return Mono.just(results);
    }

    /**
     * 从服务获取验证页面（与 getVerificationPage 相同，保持兼容性）
     */
    public Mono<String> getVerificationPageFromService(String templateType, Map<String, Object> variables) {
        return getVerificationPage(templateType, variables);
    }

    /**
     * 检查3DS服务健康状态
     */
    public Mono<Boolean> check3dsServiceHealth() {
        log.debug("检查3DS服务健康状态");
        return Mono.just(true);
    }

    /**
     * 获取3DS服务信息（与 getServiceInfo 相同，保持兼容性）
     */
    public Map<String, Object> get3dsServiceInfo() {
        return getServiceInfo();
    }

    /**
     * 获取服务模板可用性（调用现有的方法）
     */
    public Mono<Map<String, Boolean>> getServiceTemplateAvailability() {
        return getAvailableTemplates();
    }

    /**
     * 获取默认金额
     */
    public String getDefaultAmount() {
        return threeDSecureConfig.getFormatConfig().getDefaultAmount();
    }

    /**
     * 获取默认货币
     */
    public String getDefaultCurrency() {
        return threeDSecureConfig.getFormatConfig().getDefaultCurrency();
    }

}
