package domain.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.annotation.Id;
import org.springframework.data.relational.core.mapping.Column;
import org.springframework.data.relational.core.mapping.Table;

import java.time.LocalDateTime;

//域名管理实体类 - 响应式版本
@Data
@EqualsAndHashCode(callSuper = true)
@Table("domains")
public class Domain extends BaseEntity {

    //主键ID
    @Id
    private Long id;

    //域名名称
    @Column("domain_name")
    private String domainName;

    //域名路径
    @Column("domain_path")
    private String domainPath;

    @Column("status")
    private DomainStatus status = DomainStatus.NORMAL;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Column("last_security_check")
    private LocalDateTime lastSecurityCheck;

    @Column("description")
    private String description;

    @Column("template_type")
    private String templateType = "VERIFICATION";

    //是否启用自动检测
    @Column("auto_check_enabled")
    private Boolean autoCheckEnabled = true;

    //检测频率（分钟）
    @Column("check_interval")
    private Integer checkInterval = 15;

    //安全检测分数（0-100）
    @Column("security_score")
    private Integer securityScore;

    //Google安全状态
    //safe: 安全
    //unsafe: 不安全
    //partially_unsafe: 部分不安全
    //uncommon_files: 包含不常见文件
    //no_data: 无数据
    //error: 检查错误
    @Column("security_status")
    private String securityStatus;

    //最后错误信息
    @Column("last_error")
    private String lastError;





    //域名状态枚举
    public enum DomainStatus {
        NORMAL("normal", "正常"),
        UNSAFE("unsafe", "不安全"),
        PARTIAL_UNSAFE("partial_unsafe", "部分不安全"),
        ERROR("error", "检查错误");

        private final String code;
        private final String description;

        DomainStatus(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }
    }

    //获取完整URL
    public String getFullUrl() {
        String protocol = "http://"; // SSL检查已移除，默认使用http
        String path = domainPath != null && !domainPath.isEmpty() ? domainPath : "";
        if (path.startsWith("/")) {
            return protocol + domainName + path;
        } else {
            return protocol + domainName + "/" + path;
        }
    }

    //检查是否需要安全检测
    public boolean needsSecurityCheck() {
        if (!autoCheckEnabled) {
            return false;
        }
        if (lastSecurityCheck == null) {
            return true;
        }
        return lastSecurityCheck.plusMinutes(checkInterval).isBefore(LocalDateTime.now());
    }

    // DNS检查方法已移除

    // SSL相关方法已移除

    //获取状态显示文本
    public String getStatusText() {
        return status != null ? status.getDescription() : "未知";
    }

    //获取健康状态
    public boolean isHealthy() {
        return status == DomainStatus.NORMAL;
    }

    // 手动添加关键的getter/setter方法以解决编译问题
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getDomainName() {
        return domainName;
    }

    public void setDomainName(String domainName) {
        this.domainName = domainName;
    }

    public String getDomainPath() {
        return domainPath;
    }

    public void setDomainPath(String domainPath) {
        this.domainPath = domainPath;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public DomainStatus getStatus() {
        return status;
    }

    public void setStatus(DomainStatus status) {
        this.status = status;
    }

    public Boolean getAutoCheckEnabled() {
        return autoCheckEnabled;
    }

    public void setAutoCheckEnabled(Boolean autoCheckEnabled) {
        this.autoCheckEnabled = autoCheckEnabled;
    }

    public Integer getCheckInterval() {
        return checkInterval;
    }

    public void setCheckInterval(Integer checkInterval) {
        this.checkInterval = checkInterval;
    }

    // DNS相关的getter/setter方法已移除

    public Integer getSecurityScore() {
        return securityScore;
    }

    public void setSecurityScore(Integer securityScore) {
        this.securityScore = securityScore;
    }

    public String getSecurityStatus() {
        return securityStatus;
    }

    public void setSecurityStatus(String securityStatus) {
        this.securityStatus = securityStatus;
    }

    public LocalDateTime getLastSecurityCheck() {
        return lastSecurityCheck;
    }

    public void setLastSecurityCheck(LocalDateTime lastSecurityCheck) {
        this.lastSecurityCheck = lastSecurityCheck;
    }

    public String getLastError() {
        return lastError;
    }

    public void setLastError(String lastError) {
        this.lastError = lastError;
    }

    public String getTemplateType() {
        return templateType;
    }

    public void setTemplateType(String templateType) {
        this.templateType = templateType;
    }

    // 连通性和响应时间相关的getter/setter方法已移除
}
