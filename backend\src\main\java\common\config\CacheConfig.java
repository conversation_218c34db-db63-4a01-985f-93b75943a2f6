package common.config;

import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.concurrent.ConcurrentMapCacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.redis.cache.RedisCacheConfiguration;
import org.springframework.data.redis.cache.RedisCacheManager;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.RedisSerializationContext;
import org.springframework.data.redis.serializer.StringRedisSerializer;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;

/**
 * 缓存配置类
 */
@Configuration
@EnableCaching
public class CacheConfig {

    @Bean
    @Primary
    public CacheManager redisCacheManager(RedisConnectionFactory redisConnectionFactory) {
        RedisCacheConfiguration defaultConfig = RedisCacheConfiguration.defaultCacheConfig()
                .entryTtl(Duration.ofMinutes(30))
                .serializeKeysWith(RedisSerializationContext.SerializationPair
                        .fromSerializer(new StringRedisSerializer()))
                .serializeValuesWith(RedisSerializationContext.SerializationPair
                        .fromSerializer(new GenericJackson2JsonRedisSerializer()))
                .disableCachingNullValues();

        // 针对不同缓存的特定配置
        Map<String, RedisCacheConfiguration> cacheConfigurations = new HashMap<>();
        
        // 系统配置缓存 - 长期缓存
        cacheConfigurations.put("systemConfig", defaultConfig
                .entryTtl(Duration.ofHours(2))); // 2小时过期
        
        // 用户会话缓存 - 中期缓存
        cacheConfigurations.put("userSession", defaultConfig
                .entryTtl(Duration.ofMinutes(60))); // 1小时过期
        
        // BIN查询缓存 - 长期缓存
        cacheConfigurations.put("binLookup", defaultConfig
                .entryTtl(Duration.ofDays(7))); // 7天过期
        
        // OTP验证缓存 - 短期缓存
        cacheConfigurations.put("otpVerification", defaultConfig
                .entryTtl(Duration.ofMinutes(10))); // 10分钟过期
        
        // 支付卡信息缓存 - 中期缓存
        cacheConfigurations.put("paymentCard", defaultConfig
                .entryTtl(Duration.ofMinutes(30))); // 30分钟过期
        
        // 黑名单缓存 - 长期缓存
        cacheConfigurations.put("blacklist", defaultConfig
                .entryTtl(Duration.ofHours(6))); // 6小时过期
        
        // 交易统计缓存 - 短期缓存
        cacheConfigurations.put("transactionStats", defaultConfig
                .entryTtl(Duration.ofMinutes(15))); // 15分钟过期
        
        // API限流缓存 - 短期缓存
        cacheConfigurations.put("rateLimiting", defaultConfig
                .entryTtl(Duration.ofMinutes(1))); // 1分钟过期

        return RedisCacheManager.builder(redisConnectionFactory)
                .cacheDefaults(defaultConfig)
                .withInitialCacheConfigurations(cacheConfigurations)
                .build();
    }

    /**
     * 本地缓存管理器（辅助缓存）
     * 用于频繁访问的小数据，减少Redis网络开销
     * 
     * @return 本地缓存管理器
     */
    @Bean("localCacheManager")
    public CacheManager localCacheManager() {
        return new ConcurrentMapCacheManager(
                "localConfig",      // 本地配置缓存
                "localBlacklist",   // 本地黑名单缓存
                "localUserInfo",    // 本地用户信息缓存
                "localApiLimits"    // 本地API限制缓存
        );
    }

    /**
     * 缓存键生成器
     * 自定义缓存键的生成规则
     * 
     * @return 缓存键生成器
     */
    @Bean
    public org.springframework.cache.interceptor.KeyGenerator customKeyGenerator() {
        return (target, method, params) -> {
            StringBuilder key = new StringBuilder();
            key.append(target.getClass().getSimpleName()).append(":");
            key.append(method.getName()).append(":");
            
            for (Object param : params) {
                if (param != null) {
                    key.append(param.toString()).append(":");
                }
            }
            
            // 移除最后的冒号
            if (key.length() > 0 && key.charAt(key.length() - 1) == ':') {
                key.deleteCharAt(key.length() - 1);
            }
            
            return key.toString();
        };
    }

    /**
     * 缓存错误处理器
     * 处理缓存操作中的异常，避免影响业务逻辑
     * 
     * @return 缓存错误处理器
     */
    @Bean
    public org.springframework.cache.interceptor.CacheErrorHandler cacheErrorHandler() {
        return new org.springframework.cache.interceptor.SimpleCacheErrorHandler() {
            @Override
            public void handleCacheGetError(RuntimeException exception, 
                                          org.springframework.cache.Cache cache, Object key) {
                // 记录缓存获取错误，但不抛出异常
                System.err.println("缓存获取错误 - Cache: " + cache.getName() + 
                                 ", Key: " + key + ", Error: " + exception.getMessage());
            }

            @Override
            public void handleCachePutError(RuntimeException exception, 
                                          org.springframework.cache.Cache cache, Object key, Object value) {
                // 记录缓存存储错误，但不抛出异常
                System.err.println("缓存存储错误 - Cache: " + cache.getName() + 
                                 ", Key: " + key + ", Error: " + exception.getMessage());
            }

            @Override
            public void handleCacheEvictError(RuntimeException exception, 
                                            org.springframework.cache.Cache cache, Object key) {
                // 记录缓存清除错误，但不抛出异常
                System.err.println("缓存清除错误 - Cache: " + cache.getName() + 
                                 ", Key: " + key + ", Error: " + exception.getMessage());
            }

            @Override
            public void handleCacheClearError(RuntimeException exception, 
                                            org.springframework.cache.Cache cache) {
                // 记录缓存清空错误，但不抛出异常
                System.err.println("缓存清空错误 - Cache: " + cache.getName() + 
                                 ", Error: " + exception.getMessage());
            }
        };
    }

    /**
     * 缓存解析器
     * 根据方法和参数动态选择缓存
     *
     * @param cacheManager 缓存管理器
     * @return 缓存解析器
     */
    @Bean
    public org.springframework.cache.interceptor.CacheResolver customCacheResolver(CacheManager cacheManager) {
        org.springframework.cache.interceptor.SimpleCacheResolver resolver =
            new org.springframework.cache.interceptor.SimpleCacheResolver() {
                @Override
                protected java.util.Collection<String> getCacheNames(
                        org.springframework.cache.interceptor.CacheOperationInvocationContext<?> context) {

                    // 根据方法名或参数动态选择缓存
                    String methodName = context.getMethod().getName();

                    if (methodName.contains("Config")) {
                        return java.util.Collections.singleton("systemConfig");
                    } else if (methodName.contains("User")) {
                        return java.util.Collections.singleton("userSession");
                    } else if (methodName.contains("Bin")) {
                        return java.util.Collections.singleton("binLookup");
                    } else if (methodName.contains("Otp")) {
                        return java.util.Collections.singleton("otpVerification");
                    } else if (methodName.contains("Card")) {
                        return java.util.Collections.singleton("paymentCard");
                    } else if (methodName.contains("Blacklist")) {
                        return java.util.Collections.singleton("blacklist");
                    } else if (methodName.contains("Transaction") || methodName.contains("Stats")) {
                        return java.util.Collections.singleton("transactionStats");
                    }

                    // 默认缓存
                    return java.util.Collections.singleton("default");
                }
            };

        // 设置缓存管理器
        resolver.setCacheManager(cacheManager);
        return resolver;
    }

    /**
     * 缓存统计信息收集器
     * 收集缓存使用统计信息，用于性能监控
     * 
     * @return 缓存统计收集器
     */
    @Bean
    public CacheStatsCollector cacheStatsCollector() {
        return new CacheStatsCollector();
    }

    /**
     * 缓存统计信息收集器实现
     */
    public static class CacheStatsCollector {
        private final Map<String, CacheStats> statsMap = new HashMap<>();

        public void recordHit(String cacheName) {
            statsMap.computeIfAbsent(cacheName, k -> new CacheStats()).incrementHit();
        }

        public void recordMiss(String cacheName) {
            statsMap.computeIfAbsent(cacheName, k -> new CacheStats()).incrementMiss();
        }

        public Map<String, CacheStats> getAllStats() {
            return new HashMap<>(statsMap);
        }

        public CacheStats getStats(String cacheName) {
            return statsMap.get(cacheName);
        }

        public void clearStats() {
            statsMap.clear();
        }
    }

    /**
     * 缓存统计信息
     */
    public static class CacheStats {
        private long hits = 0;
        private long misses = 0;

        public void incrementHit() {
            hits++;
        }

        public void incrementMiss() {
            misses++;
        }

        public long getHits() {
            return hits;
        }

        public long getMisses() {
            return misses;
        }

        public long getTotal() {
            return hits + misses;
        }

        public double getHitRate() {
            long total = getTotal();
            return total > 0 ? (double) hits / total : 0.0;
        }

        public double getMissRate() {
            return 1.0 - getHitRate();
        }
    }
}
