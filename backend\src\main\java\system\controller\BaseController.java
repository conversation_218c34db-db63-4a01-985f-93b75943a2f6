package system.controller;

import core.common.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;

import java.util.HashMap;
import java.util.Map;

/**
 * 控制器基类
 */
@Slf4j
public abstract class BaseController {
    
    /**
     * 日志记录器
     */
    protected final org.slf4j.Logger logger = org.slf4j.LoggerFactory.getLogger(this.getClass());
    
    /**
     * 成功响应
     */
    protected <T> ResponseEntity<ApiResponse<T>> success(T data) {
        return ResponseEntity.ok(ApiResponse.success(data));
    }
    
    /**
     * 成功响应带消息
     */
    protected <T> ResponseEntity<ApiResponse<T>> success(T data, String message) {
        return ResponseEntity.ok(ApiResponse.success(data, message));
    }
    
    /**
     * 成功响应无数据
     */
    protected <T> ResponseEntity<ApiResponse<T>> success(String message) {
        return ResponseEntity.ok(ApiResponse.success(message));
    }
    
    /**
     * 失败响应
     */
    protected <T> ResponseEntity<ApiResponse<T>> error(String message) {
        return ResponseEntity.ok(ApiResponse.error(message));
    }
    
    /**
     * 失败响应带状态码
     */
    protected <T> ResponseEntity<ApiResponse<T>> error(int code, String message) {
        return ResponseEntity.status(code).body(ApiResponse.error(code, message));
    }
    
    /**
     * 参数错误响应
     */
    protected <T> ResponseEntity<ApiResponse<T>> badRequest(String message) {
        return ResponseEntity.badRequest().body(ApiResponse.badRequest(message));
    }
    
    /**
     * 未授权响应
     */
    protected <T> ResponseEntity<ApiResponse<T>> unauthorized(String message) {
        return ResponseEntity.status(401).body(ApiResponse.unauthorized(message));
    }
    
    /**
     * 禁止访问响应
     */
    protected <T> ResponseEntity<ApiResponse<T>> forbidden(String message) {
        return ResponseEntity.status(403).body(ApiResponse.forbidden(message));
    }
    
    /**
     * 资源不存在响应
     */
    protected <T> ResponseEntity<ApiResponse<T>> notFound(String message) {
        return ResponseEntity.status(404).body(ApiResponse.notFound(message));
    }
    
    /**
     * 服务器内部错误响应
     */
    protected <T> ResponseEntity<ApiResponse<T>> internalError(String message) {
        return ResponseEntity.status(500).body(ApiResponse.internalError(message));
    }
    
    /**
     * 处理异常
     */
    protected <T> ResponseEntity<ApiResponse<T>> handleException(Exception e, String operation) {
        logger.error("{}失败", operation, e);
        return internalError(operation + "失败: " + e.getMessage());
    }
    
    /**
     * 处理异常带自定义消息
     */
    protected <T> ResponseEntity<ApiResponse<T>> handleException(Exception e, String operation, String customMessage) {
        logger.error("{}失败: {}", operation, customMessage, e);
        return internalError(customMessage);
    }
    
    /**
     * 验证参数
     */
    protected <T> ResponseEntity<ApiResponse<T>> validateParam(Object param, String paramName) {
        if (param == null) {
            return badRequest(paramName + "不能为空");
        }
        if (param instanceof String && ((String) param).trim().isEmpty()) {
            return badRequest(paramName + "不能为空");
        }
        return null;
    }
    
    /**
     * 验证ID参数
     */
    protected <T> ResponseEntity<ApiResponse<T>> validateId(Long id, String idName) {
        if (id == null || id <= 0) {
            return badRequest(idName + "无效");
        }
        return null;
    }

    /**
     * 创建分页对象
     */
    protected Pageable createPageable(int page, int size) {
        // 页码从1开始，转换为从0开始
        return PageRequest.of(Math.max(0, page - 1), Math.max(1, size), Sort.by("createdAt").descending());
    }

    /**
     * 分页成功响应
     */
    protected <T> ResponseEntity<ApiResponse<Object>> pageSuccess(Page<T> page, String message) {
        Map<String, Object> result = new HashMap<>();
        result.put("content", page.getContent());
        result.put("totalElements", page.getTotalElements());
        result.put("totalPages", page.getTotalPages());
        result.put("currentPage", page.getNumber() + 1); // 转换为从1开始
        result.put("pageSize", page.getSize());
        result.put("hasNext", page.hasNext());
        result.put("hasPrevious", page.hasPrevious());
        result.put("isFirst", page.isFirst());
        result.put("isLast", page.isLast());

        return success(result, message);
    }

    /**
     * 统计成功响应
     */
    protected <T> ResponseEntity<ApiResponse<T>> statsSuccess(T data, String message) {
        return success(data, message);
    }

    /**
     * 错误响应带数据
     */
    protected <T> ResponseEntity<ApiResponse<T>> error(int code, String message, T data) {
        return ResponseEntity.status(code).body(ApiResponse.error(code, message, data));
    }
}
