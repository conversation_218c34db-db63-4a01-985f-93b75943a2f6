package external.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * IP检测配置
 * 用于配置IPRegistry API和IP类型检测规则
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "external.api.ip-registry")
public class IPDetectionConfig {

    /**
     * 是否启用IP检测
     */
    private boolean enabled = false;

    /**
     * API密钥
     */
    private String apiKey;

    /**
     * API基础URL
     */
    private String baseUrl = "https://api.ipregistry.co";

    /**
     * 超时时间（毫秒）
     */
    private int timeout = 5000;

    /**
     * 重试次数
     */
    private int retryCount = 2;

    /**
     * 缓存启用
     */
    private boolean cacheEnabled = true;

    /**
     * 缓存TTL（秒）
     */
    private int cacheTtl = 3600;

    /**
     * 检测类型配置
     */
    private DetectionTypes detectionTypes = new DetectionTypes();

    /**
     * 拦截后跳转地址
     */
    private String redirectUrl = "https://example.com";

    /**
     * 检测类型配置
     */
    @Data
    public static class DetectionTypes {

        /**
         * 连接类型检测
         */
        private boolean connectionType = false;

        /**
         * 代理检测
         */
        private boolean proxy = true;

        /**
         * VPN检测
         */
        private boolean vpn = false;

        /**
         * 匿名IP检测
         */
        private boolean anonymous = true;

        /**
         * 云服务商检测
         */
        private boolean cloudProvider = false;

        /**
         * 中继服务器检测
         */
        private boolean relay = true;

        /**
         * 威胁检测
         */
        private boolean threat = true;

        /**
         * 滥用者检测
         */
        private boolean abuser = true;

        /**
         * 攻击者检测
         */
        private boolean attacker = true;

        /**
         * 虚假IP检测
         */
        private boolean fakeIp = true;

        /**
         * Tor网络检测
         */
        private boolean tor = true;

        /**
         * Tor出口节点检测
         */
        private boolean torExit = true;
    }

    /**
     * 检查IP是否应该被拦截
     *
     * @param ipResult IP查询结果
     * @return 是否应该拦截
     */
    public boolean shouldBlock(external.dto.IPRegistryResult ipResult) {
        if (ipResult == null || ipResult.getSecurity() == null) {
            return false;
        }

        external.dto.IPRegistryResult.Security security = ipResult.getSecurity();

        // 使用流式处理简化复杂条件判断
        return java.util.Arrays.<java.util.function.Supplier<Boolean>>asList(
            () -> detectionTypes.isThreat() && security.isThreat(),
            () -> detectionTypes.isAttacker() && security.isAttacker(),
            () -> detectionTypes.isAbuser() && security.isAbuser(),
            () -> detectionTypes.isTor() && security.isTor(),
            () -> detectionTypes.isTorExit() && security.isTorExit(),
            () -> detectionTypes.isProxy() && security.isProxy(),
            () -> detectionTypes.isVpn() && security.isVpn(),
            () -> detectionTypes.isAnonymous() && security.isAnonymous(),
            () -> detectionTypes.isRelay() && security.isRelay(),
            () -> detectionTypes.isCloudProvider() && security.isCloudProvider()
        ).stream().anyMatch(java.util.function.Supplier::get);
    }

    /**
     * 获取拦截原因
     *
     * @param ipResult IP查询结果
     * @return 拦截原因列表
     */
    public String[] getBlockReasons(external.dto.IPRegistryResult ipResult) {
        if (ipResult == null || ipResult.getSecurity() == null) {
            return new String[0];
        }

        external.dto.IPRegistryResult.Security security = ipResult.getSecurity();
        java.util.List<String> reasons = new java.util.ArrayList<>();

        if (detectionTypes.isThreat() && security.isThreat()) reasons.add("威胁IP");
        if (detectionTypes.isAttacker() && security.isAttacker()) reasons.add("攻击者IP");
        if (detectionTypes.isAbuser() && security.isAbuser()) reasons.add("滥用者IP");
        if (detectionTypes.isTor() && security.isTor()) reasons.add("Tor网络");
        if (detectionTypes.isTorExit() && security.isTorExit()) reasons.add("Tor出口节点");
        if (detectionTypes.isProxy() && security.isProxy()) reasons.add("代理服务器");
        if (detectionTypes.isVpn() && security.isVpn()) reasons.add("VPN");
        if (detectionTypes.isAnonymous() && security.isAnonymous()) reasons.add("匿名IP");
        if (detectionTypes.isRelay() && security.isRelay()) reasons.add("中继服务器");
        if (detectionTypes.isCloudProvider() && security.isCloudProvider()) reasons.add("云服务商");

        return reasons.toArray(new String[0]);
    }
}
