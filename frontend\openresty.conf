# BakaOTP 管理后台 OpenResty 配置

# Netty后端
upstream netty_backend {
    server backend:8080;
    keepalive 32;
    keepalive_requests 100;
    keepalive_timeout 60s;
}

# 服务器配置
server {
    listen 80;
    server_name bakaotp-admin-frontend;

    # 隐藏服务器信息
    server_tokens off;

    # 安全头配置
    add_header X-Frame-Options "DENY" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' ws: wss:;" always;
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header Permissions-Policy "geolocation=(), microphone=(), camera=()" always;

    # 静态文件根目录
    root /usr/share/nginx/html;
    index index.html;

    # 启用gzip压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;

    # 错误页面
    location = /404.html {
        internal;
    }

    # 阻止访问敏感文件和目录
    location ~* \.(pem|jks|cer|key|crt|p12|pfx)$ {
        deny all;
        return 404;
    }

    # 阻止访问备份文件
    location ~* \.(tar|tgz|tar\.bz2|tar\.lzma|alz|zip|rar|7z|gz|bz2|xz)$ {
        deny all;
        return 404;
    }

    # 阻止访问部署文件
    location ~* \.(war|egg|jar)$ {
        deny all;
        return 404;
    }

    # 阻止访问配置文件
    location ~* \.(env|ini|conf|config|yaml|yml|json)$ {
        deny all;
        return 404;
    }

    # 阻止访问Dockerfile和构建文件
    location ~* ^/(Dockerfile|docker-compose\.yml|\.dockerignore|Makefile|build\.sh)$ {
        deny all;
        return 404;
    }

    # 阻止访问源码文件
    location ~* \.(java|py|php|rb|go|rs|cpp|c|h)$ {
        deny all;
        return 404;
    }

    # 阻止访问版本控制文件
    location ~* /\.(git|svn|hg|bzr)/ {
        deny all;
        return 404;
    }

    # 阻止访问隐藏文件
    location ~ /\. {
        deny all;
        return 404;
    }

    # 阻止访问特定目录
    location ~* ^/(oekaki|admin|phpmyadmin|wp-admin|wp-content|wp-includes)/ {
        deny all;
        return 404;
    }

    # 阻止访问日志文件
    location ~* \.(log|logs)$ {
        deny all;
        return 404;
    }

    # 静态资源缓存
    location /assets {
        expires 30d;
        add_header Cache-Control "public, max-age=2592000";
        try_files $uri =404;
    }

    # Vben Admin 资源
    location /_vben {
        expires 30d;
        add_header Cache-Control "public, max-age=2592000";
        try_files $uri =404;
    }

    # 静态资源文件缓存
    location ~* \.(jpg|jpeg|png|gif|ico|svg|woff|woff2|ttf|css|js)$ {
        expires 7d;
        add_header Cache-Control "public, max-age=604800";
    }

    # API反向代理
    location /api/ {
        proxy_pass http://netty_backend;
        proxy_http_version 1.1;
        proxy_set_header Connection "";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Server-Type "openresty";

        # 超时配置
        proxy_connect_timeout 10s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;

        # 启用连接复用
        proxy_buffering on;
        proxy_buffer_size 4k;
        proxy_buffers 8 4k;
        proxy_busy_buffers_size 8k;

        # 处理CORS
        add_header Access-Control-Allow-Origin *;
        add_header Access-Control-Allow-Methods 'GET, POST, PUT, DELETE, OPTIONS';
        add_header Access-Control-Allow-Headers 'DNT,X-Mx-ReqToken,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization,X-Server-Type,X-Client-Type';

        # 确保安全头在代理响应中也存在
        add_header X-Frame-Options "DENY" always;
        add_header X-Content-Type-Options "nosniff" always;
        add_header X-XSS-Protection "1; mode=block" always;
        add_header Referrer-Policy "strict-origin-when-cross-origin" always;

        if ($request_method = 'OPTIONS') {
            return 204;
        }
    }

    # WebSocket代理
    location /api/ws {
        proxy_pass http://netty_backend/ws;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Server-Type "openresty";

        # WebSocket优化
        proxy_read_timeout 3600s;
        proxy_send_timeout 3600s;
        proxy_connect_timeout 10s;

        # 禁用缓冲
        proxy_buffering off;
        proxy_cache off;

        # 确保安全头在WebSocket升级响应中也存在
        add_header X-Frame-Options "DENY" always;
        add_header X-Content-Type-Options "nosniff" always;
        add_header X-XSS-Protection "1; mode=block" always;
        add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    }

    # 原生WebSocket代理
    location /api/ws-native {
        proxy_pass http://netty_backend/ws-native;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # 原生WebSocket优化
        proxy_read_timeout 3600s;
        proxy_send_timeout 3600s;
        proxy_connect_timeout 5s;
        proxy_buffering off;

        # 确保安全头在WebSocket升级响应中也存在
        add_header X-Frame-Options "DENY" always;
        add_header X-Content-Type-Options "nosniff" always;
        add_header X-XSS-Protection "1; mode=block" always;
        add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    }

    # 对于明显的文件请求，如果不存在则返回404
    location ~* \.(php|asp|aspx|jsp|cgi|pl|py|rb|sh|exe|dll|bat|cmd|com|scr|vbs|jar|war|ear|class|sql|db|mdb|bak|backup|old|orig|save|tmp|temp|swp|swo|~|#.*#)$ {
        try_files $uri =404;
    }

    # 前端路由支持（仅对可能的SPA路由使用fallback）
    location / {
        try_files $uri $uri/ @fallback;

        # SPA应用不缓存index.html
        add_header Cache-Control "no-cache, no-store, must-revalidate";
        add_header Pragma "no-cache";
        add_header Expires "0";
    }

    # SPA路由回退
    location @fallback {
        rewrite ^.*$ /index.html last;
    }

    # 健康检查端点
    location /health {
        access_log off;
        add_header Content-Type application/json;
        return 200 '{"status":"UP","server":"OpenResty","timestamp":"$time_iso8601"}';
    }
}
