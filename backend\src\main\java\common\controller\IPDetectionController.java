package common.controller;

import core.common.ApiResponse;
import system.controller.BaseController;
import external.service.IPDetectionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

import java.util.Map;

/**
 * IP检测控制器
 * 提供IP地址类型检测和威胁分析API
 */
@Slf4j
@RestController
@RequestMapping("/api/ip-detection")
public class IPDetectionController extends BaseController {

    @Autowired
    private IPDetectionService ipDetectionService;

    /**
     * 检测单个IP地址
     *
     * @param ip IP地址
     * @return 检测结果
     */
    @GetMapping("/detect")
    public Mono<ResponseEntity<ApiResponse<Map<String, Object>>>> detectIP(@RequestParam String ip) {
        if (ip == null || ip.trim().isEmpty()) {
            return Mono.just(error("IP地址不能为空"));
        }

        log.info("收到IP检测请求: {}", ip);

        return ipDetectionService.detectIP(ip.trim())
                .map(result -> success(result, "IP检测完成"))
                .onErrorResume(Exception.class, e -> {
                    logger.error("IP检测失败", e);
                    return Mono.just(handleException(e, "IP检测"));
                });
    }

    /**
     * 批量检测IP地址
     *
     * @param request 包含IP地址列表的请求
     * @return 批量检测结果
     */
    @PostMapping("/batch-detect")
    public Mono<ResponseEntity<ApiResponse<Map<String, Map<String, Object>>>>> batchDetectIP(
            @RequestBody Map<String, Object> request) {
        
        Object ipsObj = request.get("ips");
        if (!(ipsObj instanceof String[])) {
            return Mono.just(error("请提供有效的IP地址数组"));
        }

        String[] ips = (String[]) ipsObj;
        if (ips.length == 0) {
            return Mono.just(error("IP地址数组不能为空"));
        }

        if (ips.length > 100) {
            return Mono.just(error("批量检测最多支持100个IP地址"));
        }

        log.info("收到批量IP检测请求，数量: {}", ips.length);

        return ipDetectionService.batchDetectIP(ips)
                .map(results -> success(results, "批量IP检测完成"))
                .onErrorResume(Exception.class, e -> {
                    logger.error("批量IP检测失败", e);
                    return Mono.just(handleException(e, "批量IP检测"));
                });
    }

    /**
     * 检查IP是否应该被拦截
     *
     * @param ip IP地址
     * @return 拦截检查结果
     */
    @GetMapping("/check-block")
    public Mono<ResponseEntity<ApiResponse<Map<String, Object>>>> checkIPBlock(@RequestParam String ip) {
        if (ip == null || ip.trim().isEmpty()) {
            return Mono.just(error("IP地址不能为空"));
        }

        log.info("收到IP拦截检查请求: {}", ip);

        return ipDetectionService.checkIPBlock(ip.trim())
                .map(result -> success(result, "IP拦截检查完成"))
                .onErrorResume(Exception.class, e -> {
                    logger.error("IP拦截检查失败", e);
                    return Mono.just(handleException(e, "IP拦截检查"));
                });
    }

    /**
     * 获取IP检测服务状态
     *
     * @return 服务状态信息
     */
    @GetMapping("/status")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getServiceStatus() {
        try {
            Map<String, Object> status = ipDetectionService.getServiceInfo();
            return success(status, "获取服务状态成功");
        } catch (Exception e) {
            logger.error("获取IP检测服务状态失败", e);
            return handleException(e, "获取服务状态");
        }
    }

    /**
     * 更新IP检测配置
     *
     * @param request 配置数据
     * @return 更新结果
     */
    @PostMapping("/config")
    public ResponseEntity<ApiResponse<String>> updateConfig(@RequestBody Map<String, Object> request) {
        try {
            // 这里应该更新配置文件或数据库中的配置
            // 由于当前使用的是application.yml配置，这里只做验证
            log.info("收到IP检测配置更新请求: {}", request);

            // 验证必要的配置项
            if (request.containsKey("enabled") && Boolean.TRUE.equals(request.get("enabled"))) {
                String apiKey = (String) request.get("apiKey");
                if (apiKey == null || apiKey.trim().isEmpty()) {
                    return error("启用IP检测时必须提供API Key");
                }
            }

            return success("配置更新成功（注意：需要重启服务生效）", "配置更新成功");
        } catch (Exception e) {
            logger.error("更新IP检测配置失败", e);
            return handleException(e, "更新配置");
        }
    }

    /**
     * 测试IP检测服务连接
     *
     * @return 连接测试结果
     */
    @GetMapping("/test")
    public Mono<ResponseEntity<ApiResponse<Map<String, Object>>>> testService() {
        log.info("开始测试IP检测服务连接");

        // 使用一个已知的测试IP地址
        String testIP = "*******";

        return ipDetectionService.detectIP(testIP)
                .map(result -> {
                    Map<String, Object> testResult = new java.util.HashMap<>();
                    testResult.put("testIP", testIP);
                    testResult.put("success", result.get("success"));
                    testResult.put("available", ipDetectionService.isServiceAvailable());
                    testResult.put("responseTime", System.currentTimeMillis());

                    if (Boolean.TRUE.equals(result.get("success"))) {
                        testResult.put("hasData", result.get("riskLevel") != null);
                        testResult.put("riskLevel", result.get("riskLevel"));
                    } else {
                        testResult.put("error", result.get("error"));
                    }

                    return success((Map<String, Object>) testResult, "IP检测服务连接测试完成");
                })
                .onErrorResume(Exception.class, e -> {
                    logger.error("IP检测服务连接测试失败", e);
                    ResponseEntity<ApiResponse<Map<String, Object>>> errorResponse = handleException(e, "IP检测服务连接测试");
                    return Mono.just(errorResponse);
                });
    }
}
