/*--
Author: W3layouts
Author URL: http://w3layouts.com
License: Creative Commons Attribution 3.0 Unported
License URL: http://creativecommons.org/licenses/by/3.0/
--*/
/* reset */
html,body,div,span,applet,object,iframe,h1,h2,h3,h4,h5,h6,p,blockquote,pre,a,abbr,acronym,address,big,cite,code,del,dfn,em,img,ins,kbd,q,s,samp,small,strike,strong,sub,sup,tt,var,b,u,i,dl,dt,dd,ol,nav ul,nav li,fieldset,form,label,legend,table,caption,tbody,tfoot,thead,tr,th,td,article,aside,canvas,details,embed,figure,figcaption,footer,header,hgroup,menu,nav,output,ruby,section,summary,time,mark,audio,video{margin:0;padding:0;border:0;font-size:100%;font:inherit;vertical-align:baseline;}
article, aside, details, figcaption, figure,footer, header, hgroup, menu, nav, section {display: block;}
ol,ul{list-style:none;margin:0px;padding:0px;}
blockquote,q{quotes:none;}
blockquote:before,blockquote:after,q:before,q:after{content:'';content:none;}
table{border-collapse:collapse;border-spacing:0;}
/* start editing from here */
a{text-decoration:none;}
.txt-rt{text-align:right;}/* text align right */
.txt-lt{text-align:left;}/* text align left */
.txt-center{text-align:center;}/* text align center */
.float-rt{float:right;}/* float right */
.float-lt{float:left;}/* float left */
.clear{clear:both;}/* clear float */
.pos-relative{position:relative;}/* Position Relative */
.pos-absolute{position:absolute;}/* Position Absolute */
.vertical-base{	vertical-align:baseline;}/* vertical align baseline */
.vertical-top{	vertical-align:top;}/* vertical align top */
nav.vertical ul li{	display:block;}/* vertical menu */
nav.horizontal ul li{	display: inline-block;}/* horizontal menu */
img{max-width:100%;}
/*end reset*/

body{
	background-attachment: fixed;
    background-position: center;
    background-size: cover;
    -webkit-background-size: cover;
    -moz-background-size: cover;
    -o-background-size: cover;
	padding:0;
	margin:0; 
	font-size: 100%;
	/* background:url("../images/bg6.jpg") no-repeat 0px 0px; */
    background-size: cover;
	font-family: 'Muli', sans-serif;
} 
h1,h2,h3,h4,h5,h6{
	margin:0;			   
}	
p{
	margin:0;
}
ul{
	margin:0;
	padding:0;
}
label{
	margin:0;
}
a{
	transition: 0.5s all;
  -webkit-transition: 0.5s all;
  -moz-transition: 0.5s all;
  -o-transition: 0.5s all;
}
a:hover{
	transition: 0.5s all;
  -webkit-transition: 0.5s all;
  -moz-transition: 0.5s all;
  -o-transition: 0.5s all;
}

/*--main--*/
/*--header start here--*/
.w3ls-header {
    padding: 0em 0 0;
}
h1 {
    text-align: center;
    font-size: 45px;
    padding: 28px 0;
    /*text-transform: uppercase;*/
    word-spacing: 6px;
    color: #fff;
}

.icon1 {
    margin: 0 0 2em;
    border-bottom: 1px solid #525252;
    padding: .7em 0em;
}
.wthree li {
    display: inline-block;
}
a {
    color: #585858;
    margin: 0em;
}
a:hover {
    color: #ff4f81;
}
.bottom {
    margin: 3em 0 0;
}
.header-main {
    padding: 11em 4em 2.5em;
    width: 25%;
    margin: 0 auto;
    /* background: rgba(0, 0, 0, 0.56); */
    background: #9e9e9e0a;
    text-align: center;
    position: relative;
    z-index: 999;
}
.header-main h2 {
    font-size: 1.6em;
    line-height: 1.6em;
    color: #585858;
    text-align: center;
    padding: 0 2em;
    padding-bottom: 25px;
    /*text-transform: capitalize;*/
}
.sign-up {
    margin: 2em 0;
}
.header-left {
  background: #fff;
  padding: 0px;
}
.sign-up h2 {
    font-size: 22px;
    color: #000;
    text-align: center;
    background: #fbbc05;
    width: 40px;
    height: 40px;
    line-height: 1.9em;
    border-radius: 50%;
    margin: 0 auto;
}
::-webkit-input-placeholder{
    color: #999!important;
}
.header-left-bottom input[type="email"] {
    outline: none;
    font-size: 15px;
    color: #000;
	border:none;
    width: 90%;
    display: inline-block;
    background: transparent;
	font-family: 'Muli', sans-serif;
    text-align: center;
    letter-spacing: 2px;
}

.header-left-bottom input[type="tel"] {
    outline: none;
    font-size: 15px;
    color: #000;
	border:none;
    width: 90%;
    display: inline-block;
    background: transparent;
	font-family: 'Muli', sans-serif;
    text-align: center;
    letter-spacing: 2px;
}

.header-left-bottom input[type="password"]{
  outline: none;
  font-size: 15px;
    color: #000;
  border:none;
    width: 90%;
  display: inline-block;
  background: transparent;
	font-family: 'Muli', sans-serif;
    text-align: center;
    letter-spacing: 2px;
}
.header-left-bottom input[type="submit"] {
    background: #000000;
    color: #FFF;
    font-size: 17px;
    /*text-transform: uppercase;*/
    padding: .5em 2em;
    letter-spacing: 0px;
    transition: 0.5s all;
    -webkit-transition: 0.5s all;
    -moz-transition: 0.5s all;
    -o-transition: 0.5s all;
    display: inline-block;
    cursor: pointer;
    outline: none;
    border: none;
	font-family: 'Muli', sans-serif;
}
.header-left-bottom input[type="submit"]:hover {
    background: #aeb0b6;
    transition: 0.5s all;
    -webkit-transition: 0.5s all;
    -moz-transition: 0.5s all;
    -o-transition: 0.5s all;
}
/*-- agileits --*/
.header-left-bottom p {
    font-size: 17px;
    color: #000;
    display: inline-block;
    text-align: center;
    width: 100%;
    margin: 20px 0 0;
    letter-spacing: 1px;
}
.header-left-bottom p a {
    font-size: 22px;
    color: #dd4b39;
}
.header-left-bottom p a:hover {
    color: #3be8b0;
    text-decoration: underline;
}

.social {
    margin: 1em 0 0;
}
.heading h5 {
    color: #c5c5c5;
    color: #000000;
    margin-top: 8px;
    font-size: 20px;
}
.social i.fa {
    font-size: 37px;
    margin: 0 5px;
	transition:0.5s all;
}

.social i.fa.fa-facebook-square:hover{
    color: #3b5998;
}
.social i.fa.fa-twitter-square:hover{
    color: #1da1f2;
}
.social i.fa.fa-linkedin-square:hover {
    color: #00a0dc;
}
.social i.fa.fa-google-plus-square:hover {
    color: #dd4b39;
}
.social i.fa {
    color: #fff;
    color: #505050;
}
.heading,.icons {
    width: 50%;
    float: left;
}
.login-check {
    position: relative;
}
.checkbox i {
    position: absolute;
    top: 0px;
    left: 29%;
    display: block;
    width: 20px;
    height: 18px;
    outline: none;
    border: 1px solid #9c9c9c;
    background: #fff;
    border-radius: 0px;
    -webkit-border-radius: 0px;
    -moz-border-radius: 0px;
    -o-border-radius: 0px;
    cursor: pointer;
}
.checkbox input:checked + i:after {
    opacity: 1;
}
.checkbox input + i:after {
    position: absolute;
    opacity: 0;
    transition: opacity 0.1s;
    -o-transition: opacity 0.1s;
    -ms-transition: opacity 0.1s;
    -moz-transition: opacity 0.1s;
    -webkit-transition: opacity 0.1s;
}
.checkbox input + i:after {
    content: url(../images/tick.png);
    top: -2px;
    left: 2px;
    width: 15px;
    height: 15px;
}
.checkbox {
    position: relative;
    display: block;
    padding-left: 30px;
    letter-spacing: 0px;
    font-size: 16px;
    color: #000;
}
input[type="checkbox" i] {
    display: none;
}
/*-- w3layouts --*/
/*-- header end here --*/
/*-- copyright start here --*/
.copyright {
  padding: 30px 0px;
  text-align: center;
}
.copyright p {
    font-size: 15px;
    font-weight: 100;
    color: #000000;
    word-spacing: .2em;
    letter-spacing: 0px;
}
.copyright p a{
  font-size: 15px;
  font-weight: 400;
  color: #FFF;
}
.copyright p a:hover{
	color:#34a853;
	text-decoration:none;
}
/*-- copyright end here --*/

.header-main:before {
    content: '';
    position: absolute;
    top: 0px;
    left: 0px;
    width: 0;
    height: 0;
    border-top: 170px solid #aeb0b6;
    border-right: 322px solid transparent;
    border-left: 0px solid transparent;
}
.header-main:after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 0;
    height: 0;
    border-top: 260px solid #000000;
    border-right: 0px solid transparent;
    border-left: 340px solid transparent;
    z-index: -1;
}
span {
    /*text-transform: lowercase;*/
    color: #ff4f81;
}
/*-- responsive --*/
@media (max-width:1920px) {
	h1 {
		padding: 100px 0 30px;
	}
}
@media (max-width:1680px) {
	h1 {
		padding: 60px 0 30px;
	}
}
@media (max-width:1600px) {
	h1 {
		padding: 20px 0;
	}
	.copyright {
		padding: 20px 0px;
	}
}
@media (max-width:1440px) {
	.header-main h2 {
		padding: 0 1em;
		padding-bottom: 25px;
	}
	.checkbox i {
		left: 27%;
	}
}
@media (max-width:1366px) {
	.header-main {
		width: 27%;
	}
}
@media (max-width:1280px) {
	.header-main {
		width: 30%;
	}
}
@media (max-width:1080px) {
	.header-main h1 {
		font-size: 2.1em;
	}
	.header-main {
		width: 37%;
	}
}
@media (max-width:1024px) {
	.header-main {
		width: 39%;
	}
	.sign-up h2 {
		font-size: 1.2em;
		width: 35px;
		height: 35px;
	}
}
@media (max-width:991px) {
	.header-main {
		width: 37%;
	}
}

@media (max-width:900px) {
	.header-main {
		width: 38%;
	}
}

@media (max-width:800px) {
	.header-main {
		width: 44%;
	}
	.header-left-bottom input[type="email"] {
		width: 88%;
	}
	h1 {
		font-size: 40px;
	}
}
@media (max-width:768px) {
	.header-main {
		width: 46%;
	}
	.copyright {
		padding: 30px 0px;
	}
}
@media (max-width:736px) {
	.header-main {
		width: 50%;
	}
}
@media (max-width: 667px) {
	.header-main {
		width: 54%;
	}
}
@media (max-width:600px) {
	.header-main {
		width: 57%;
	}
	h1 {
		font-size: 40px;
	}
	.header-main h2 {
		font-size: 1.5em;
	}
	.checkbox i {
		left: 24%;
	}
}
@media (max-width:568px) {
	.header-main h1 {
		font-size: 2em;
	}
	.copyright p {
		margin: 0 1em;
	}
	.sign-up h2 {
		font-size: 1.1em;
		width: 32px;
		height: 32px;
		line-height: 2em;
	}
	h1 {
		font-size: 38px;
	}
	.header-left-bottom input[type="text"],.header-left-bottom input[type="tel"] {
		width: 88%;
		font-size: 14px;
	}
	.header-left-bottom input[type="email"],.header-left-bottom input[type="password"] {
		width: 88%;
		font-size: 14px;
	}
	.header-main h2 {
		font-size: 1.4em;
	}
	.checkbox i {
		left: 24%;
	}
}
@media (max-width:480px) {
	.header-main {
		width: 65%;
	}
	h1 {
		font-size: 34px;
	}
}
@media (max-width:414px) {
	h1 {
		font-size: 32px;
	}
	.checkbox {
		letter-spacing: 0px;
	}
	.icon1 i.fa {
		font-size: 1.2em;
	}
	.header-left-bottom input[type="text"],.header-left-bottom input[type="email"],.header-left-bottom input[type="tel"],.header-left-bottom input[type="password"] {
		width: 80%;
	}
	.sign-up h2 {
		font-size: 1em;
		width: 30px;
		height: 30px;
	}
	.header-left-bottom input[type="submit"] {
		width: 50%;
	}
	.header-main h2 {
		font-size: 1.5em;
	}
	.heading, .icons {
		width: 100%;
		text-align: center;
		margin-bottom: 20px;
	}
	.header-main h2 {
		font-size: 1.3em;
		padding: 0 .5em;
		padding-bottom: 25px;
	}
	.header-main:before {
		border-right: 240px solid transparent;
	}
	.header-main:after {
		border-top: 230px solid #000000;
		border-left: 250px solid transparent;
	}
	.header-main {
		padding: 10em 4em 2.5em;
	}
	.checkbox i {
		left: 20%;
	}
}
@media (max-width:384px) {
	.copyright p {
		font-size: .9em;
	}
	.sign-up {
		margin: 1em 0;
	}
	h1 {
		font-size: 30px;
	}
	.header-main {
		width: 68%;
		padding: 10em 3em 2.5em;
	}
	.header-main h2 {
		padding: 0;
		padding-bottom: 25px;
	}
	.header-left-bottom p {
		font-size: 16px;
	}
	.checkbox i {
		left: 16%;
	}
}
@media (max-width:375px) {
	.header-main {
		width: 70%;
		padding: 10em 3em 2.5em;
	}
}
@media (max-width:320px) {
	.w3ls-header {
		padding: 0em 0 0;
	}
	.header-main {
		padding: 9em 2em 2.5em;
	}
	.icon1 {
		margin: 0 0 1.5em;
	}
	.header-left-bottom input[type="text"], .header-left-bottom input[type="email"], .header-left-bottom input[type="password"] {
		font-size: 13px;
	}
	.copyright p a {
		font-size: 1em;
	}
	h1 {
		font-size: 25px;
		word-spacing: 1px;
	}
	.header-main h2 {
		font-size: 1.2em;
		padding-bottom: 15px;
	}
	.social i.fa {
		font-size: 34px;
	}
	.header-left-bottom input[type="submit"] {
		font-size: 15px;
	}
	.header-main:before {
		border-right: 200px solid transparent;
		border-top: 130px solid #aeb0b6;
	}
	.header-main:after {
		border-top: 180px solid #000000;
		border-left: 200px solid transparent;
	}
	.checkbox i {
		left: 14%;
	}
	.header-left-bottom p a {
		font-size: 17px;
	}
	.header-left-bottom p {
		font-size: 14.5px;
	}
}
/*-- //responsive --*/
