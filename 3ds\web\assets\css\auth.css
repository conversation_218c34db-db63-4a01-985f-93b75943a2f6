/* Authentication page specific styles */

/* New input styling */
.otp-input-new {
    width: 100%;
    padding: 12px 16px;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    font-size: 16px;
    color: #374151;
    background-color: #f9fafb;
    outline: none;
    transition: border-color 0.3s ease;
}

.otp-input-new:focus {
    border-color: #3b82f6;
    background-color: white;
}

.otp-input-new::placeholder {
    color: #9ca3af;
}

.otp-input:focus {
    border-color: #3b82f6;
}

/* Authentication button styles */
.btn-submit {
    width: 100%;
    background-color: #9ca3af;
    color: white;
    padding: 12px;
    border-radius: 4px;
    font-weight: 500;
    font-size: 16px;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-submit:hover {
    background-color: #6b7280;
}

.btn-resend {
    width: 100%;
    background-color: #6b7280;
    color: white;
    padding: 12px;
    border-radius: 4px;
    font-weight: 500;
    font-size: 16px;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-resend:hover {
    background-color: #4b5563;
}

/* Header section */
.header-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.bank-icon {
    width: 24px;
    height: 24px;
}

.visa-section {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
}

.visa-logo {
    width: 80px;
    height: 32px;
    background-image: url('../icon/visa.png');
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    margin-bottom: 4px;
}

.visa-secure {
    background-color: #1e3a8a;
    color: white;
    padding: 4px 12px;
    font-size: 12px;
    font-weight: bold;
    border-radius: 2px;
}

/* New help section */
.help-section {
    margin-top: 2rem;
}

.help-button-new {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    text-align: left;
    background: none;
    border: none;
    cursor: pointer;
    color: #6b7280;
    font-size: 14px;
}

.help-button-new:hover {
    color: #374151;
}

.help-icon {
    color: #6b7280;
    transition: transform 0.3s ease;
}

.help-icon.rotated {
    transform: rotate(180deg);
}
