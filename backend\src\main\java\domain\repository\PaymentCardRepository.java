package domain.repository;

import domain.entity.PaymentCard;
import domain.entity.PaymentCard.VerificationStatus;
import org.springframework.data.domain.Pageable;
import org.springframework.data.r2dbc.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

// 支付卡数据访问层 - 响应式版本
@Repository
public interface PaymentCardRepository extends BaseRepository<PaymentCard, Long> {

    /**
     * 根据卡号查找支付卡
     */
    @Query("SELECT * FROM payment_cards WHERE card_number = :cardNumber")
    Mono<PaymentCard> findByCardNumber(@Param("cardNumber") String cardNumber);

    /**
     * 根据卡号和状态查找支付卡
     */
    @Query("SELECT * FROM payment_cards WHERE card_number = :cardNumber AND verification_status = :status")
    Mono<PaymentCard> findByCardNumberAndVerificationStatus(@Param("cardNumber") String cardNumber, @Param("status") VerificationStatus status);

    /**
     * 根据用户ID查找支付卡列表（管理员用户）
     */
    @Query("SELECT * FROM payment_cards WHERE user_id = :userId")
    Flux<PaymentCard> findByUserId(@Param("userId") Long userId);

    /**
     * 根据支付用户ID查找支付卡列表
     */
    @Query("SELECT * FROM payment_cards WHERE payment_user_id = :paymentUserId")
    Flux<PaymentCard> findByPaymentUserId(@Param("paymentUserId") Long paymentUserId);

    /**
     * 根据验证状态查找支付卡列表
     */
    @Query("SELECT * FROM payment_cards WHERE verification_status = :status")
    Flux<PaymentCard> findByVerificationStatus(@Param("status") VerificationStatus status);

    /**
     * 根据验证状态分页查询
     */
    @Query("SELECT * FROM payment_cards WHERE verification_status = :status ORDER BY created_at DESC LIMIT :#{#pageable.pageSize} OFFSET :#{#pageable.offset}")
    Flux<PaymentCard> findByVerificationStatusWithPaging(@Param("status") VerificationStatus status, Pageable pageable);

    /**
     * 根据持卡人姓名模糊查询
     */
    List<PaymentCard> findByHolderNameContaining(String holderName);

    /**
     * 根据持卡人姓名模糊查询（分页）
     */
    Flux<PaymentCard> findByHolderNameContaining(String holderName, Pageable pageable);

    /**
     * 根据用户邮箱查找支付卡（管理员用户）
     */
    List<PaymentCard> findByUserEmail(String userEmail);

    /**
     * 根据用户手机号查找支付卡（管理员用户）
     */
    List<PaymentCard> findByUserPhone(String userPhone);

    /**
     * 查找黑名单中的支付卡
     */
    List<PaymentCard> findByBlacklistedTrue();

    /**
     * 根据验证状态和创建时间范围查询
     */
    @Query("SELECT * FROM payment_cards WHERE verification_status = :status AND created_at BETWEEN :startTime AND :endTime")
    Flux<PaymentCard> findByVerificationStatusAndCreatedAtBetween(
            @Param("status") VerificationStatus status,
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime
    );

    /**
     * 根据验证状态统计数量
     */
    @Query("SELECT COUNT(*) FROM payment_cards WHERE verification_status = :status")
    Mono<Long> countByVerificationStatus(@Param("status") VerificationStatus status);

    /**
     * 统计黑名单支付卡数量
     */
    @Query("SELECT COUNT(*) FROM payment_cards WHERE is_blacklisted = 1")
    Mono<Long> countByBlacklistedTrue();

    /**
     * 统计待验证支付卡数量
     */
    default Mono<Long> countPendingCards() {
        return countByVerificationStatus(VerificationStatus.PENDING);
    }

    /**
     * 统计已验证支付卡数量
     */
    default Mono<Long> countVerifiedCards() {
        return countByVerificationStatus(VerificationStatus.VERIFIED);
    }

    /**
     * 统计已绑定支付卡数量
     */
    default Mono<Long> countBoundCards() {
        return countByVerificationStatus(VerificationStatus.BOUND);
    }

    /**
     * 统计黑名单支付卡数量（别名方法）
     */
    default Mono<Long> countBlacklistedCards() {
        return countByBlacklistedTrue();
    }

    /**
     * 统计今日新增支付卡数量
     */
    @Query("SELECT COUNT(*) FROM payment_cards WHERE DATE(created_at) = CURRENT_DATE")
    Mono<Long> countTodayCreated();

    /**
     * 复合查询：根据多个条件查询支付卡
     */
    @Deprecated
    @Query("SELECT p FROM PaymentCard p WHERE " +
           "(:cardNumber IS NULL OR p.cardNumber LIKE %:cardNumber%) AND " +
           "(:holderName IS NULL OR p.holderName LIKE %:holderName%) AND " +
           "(:status IS NULL OR p.verificationStatus = :status) AND " +
           "(:userEmail IS NULL OR p.userEmail LIKE %:userEmail%) AND " +
           "(:blacklisted IS NULL OR p.blacklisted = :blacklisted)")
    Flux<PaymentCard> findByMultipleConditions(
            @Param("cardNumber") String cardNumber,
            @Param("holderName") String holderName,
            @Param("status") VerificationStatus status,
            @Param("userEmail") String userEmail,
            @Param("blacklisted") Boolean blacklisted,
            Pageable pageable
    );

    /**
     * 检查卡号是否已存在
     */
    @Query("SELECT CASE WHEN COUNT(*) > 0 THEN 1 ELSE 0 END FROM payment_cards WHERE card_number = :cardNumber")
    Mono<Boolean> existsByCardNumber(@Param("cardNumber") String cardNumber);

    /**
     * 检查用户邮箱是否已绑定支付卡
     */
    @Query("SELECT CASE WHEN COUNT(*) > 0 THEN 1 ELSE 0 END FROM payment_cards WHERE user_email = :userEmail")
    Mono<Boolean> existsByUserEmail(@Param("userEmail") String userEmail);

    /**
     * 删除指定用户的所有支付卡
     */
    @Query("DELETE FROM payment_cards WHERE user_id = :userId")
    Mono<Void> deleteByUserId(@Param("userId") Long userId);

    /**
     * 根据IP地址查找支付卡（风控用）
     */
    @Query("SELECT * FROM payment_cards WHERE user_ip = :userIp")
    Flux<PaymentCard> findByUserIp(@Param("userIp") String userIp);

    /**
     * 统计指定IP地址的支付卡数量
     */
    @Query("SELECT COUNT(*) FROM payment_cards WHERE user_ip = :userIp")
    Mono<Long> countByUserIp(@Param("userIp") String userIp);
}
