package auth.controller;

import system.controller.BaseController;
import auth.config.JwtUtil;
import domain.entity.User;
import domain.repository.UserRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.web.bind.annotation.*;
import java.util.HashMap;
import java.util.Map;
import org.springframework.data.redis.core.RedisTemplate;
import java.util.concurrent.TimeUnit;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import core.common.ApiResponse;
import java.util.Arrays;
import reactor.core.publisher.Mono;

@RestController
@RequestMapping("/api/auth")
public class AuthController extends BaseController {

    @Autowired
    private UserRepository userRepository;
    @Autowired
    private JwtUtil jwtUtil;
    @Autowired
    private PasswordEncoder passwordEncoder;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @GetMapping("/login")
    public ResponseEntity<ApiResponse<Object>> loginGet() {
        logger.warn("GET request to login endpoint - method not allowed");
        return error("请使用POST方法进行登录");
    }

    @PostMapping("/login")
    public Mono<ResponseEntity<? extends ApiResponse<?>>> login(@RequestBody Map<String, Object> loginRequest) {
        if (loginRequest == null) {
            logger.warn("Login attempt with empty request.");
            return Mono.just(error("登录请求不能为空"));
        }

        String username = (String) loginRequest.get("username");
        String password = (String) loginRequest.get("password");
        Boolean captcha = (Boolean) loginRequest.get("captcha");

        if (username == null || username.trim().isEmpty() || password == null || password.trim().isEmpty()) {
            logger.warn("Login attempt with empty username or password.");
            return Mono.just(error("用户名和密码不能为空"));
        }

        // 简单的验证码检查 - 目前只检查是否为true
        if (captcha == null || !captcha) {
            logger.warn("Login attempt without valid captcha verification.");
            return Mono.just(error("请完成验证码验证"));
        }

        logger.info("Login attempt for username: {}", username);

        return userRepository.findByUsername(username)
            .flatMap(user -> {
                logger.info("User found: {}, role: {}, status: {}", user.getUsername(), user.getRole(), user.getStatus());
                logger.debug("Input password: {}", password);
                logger.debug("Stored password hash: {}", user.getPassword());

                if (passwordEncoder.matches(password, user.getPassword())) {
                    logger.info("Password matches for user: {}", user.getUsername());
                    String token = jwtUtil.generateToken(user.getId().toString());
                    logger.info("Generated JWT token for user: {}", user.getId());

                    // 保存用户信息到Redis
                    String userKey = "user:" + user.getId();
                    Map<String, Object> userMap = new HashMap<>();
                    userMap.put("id", user.getId().toString());
                    userMap.put("username", user.getUsername());
                    userMap.put("name", user.getName());
                    userMap.put("role", user.getRole());
                    redisTemplate.opsForHash().putAll(userKey, userMap);
                    redisTemplate.expire(userKey, 24, TimeUnit.HOURS);

                    Map<String, Object> userInfo = new HashMap<>();
                    userInfo.put("id", user.getId());
                    userInfo.put("userId", user.getId());  // 兼容性字段
                    userInfo.put("username", user.getUsername());
                    userInfo.put("name", user.getName());
                    userInfo.put("realName", user.getName());  // 兼容性字段
                    userInfo.put("email", user.getEmail() != null ? user.getEmail() : "<EMAIL>");
                    userInfo.put("avatar", "");
                    userInfo.put("role", user.getRole());
                    userInfo.put("roles", Arrays.asList(user.getRole()));  // 兼容性字段

                    Map<String, Object> response = new HashMap<>();
                    response.put("accessToken", token); // 前端期望的字段名
                    response.put("user", userInfo);
                    response.put("userInfo", userInfo);  // 兼容性字段

                    logger.info("Login successful for user: {} with token: {}", user.getUsername(), token.substring(0, 20) + "...");
                    return Mono.just(ResponseEntity.ok(new ApiResponse<>(200, "success", response, true)));
                } else {
                    logger.warn("Password mismatch for user: {}", username);
                    return Mono.just(ResponseEntity.status(401).body(new ApiResponse<>(401, "用户名或密码错误", null, false)));
                }
            })
            .switchIfEmpty(Mono.defer(() -> {
                logger.warn("User not found with username: {}", username);
                return Mono.just(ResponseEntity.status(401).body(new ApiResponse<Object>(401, "用户名或密码错误", null, false)));
            }))
            .onErrorResume(e -> {
                logger.error("Login error for username: {}", username, e);
                return Mono.<ResponseEntity<ApiResponse<Object>>>just(
                    ResponseEntity.status(500).body(new ApiResponse<>(500, "登录失败: " + e.getMessage(), null, false))
                );
            });
    }

    @GetMapping("/info")
    public ResponseEntity<ApiResponse<Object>> getCurrentUser(@AuthenticationPrincipal User user) {
        if (user != null) {
            Map<String, Object> userInfo = new HashMap<>();
            // 基础用户信息 - 符合前端 BasicUserInfo 接口
            userInfo.put("userId", user.getId().toString()); // 前端期望的字段名
            userInfo.put("username", user.getUsername());
            userInfo.put("realName", user.getName()); // 前端期望的字段名
            userInfo.put("avatar", "/cirno.webp"); // BakaOTP默认头像
            userInfo.put("roles", new String[]{user.getRole()}); // 前端期望的角色数组

            // 扩展用户信息 - 符合前端 UserInfo 接口
            userInfo.put("desc", "BakaOTP管理员"); // 用户描述
            userInfo.put("homePath", "/dashboard/analytics"); // 默认首页路径 - 数据分析仪表板
            userInfo.put("token", ""); // accessToken字段（可选）

            logger.info("获取当前用户信息成功: {}", user.getUsername());
            return ResponseEntity.ok(new ApiResponse<>(200, "success", userInfo, true));
        } else {
            logger.warn("获取当前用户信息失败，用户未认证");
            return ResponseEntity.status(401).body(new ApiResponse<>(401, "用户未认证", null, false));
        }
    }

    @PostMapping("/refresh")
    public ResponseEntity<ApiResponse<Object>> refreshToken(@RequestHeader("Authorization") String authHeader) {
        try {
            if (authHeader != null && authHeader.startsWith("Bearer ")) {
                String token = authHeader.substring(7);
                String userId = jwtUtil.extractUserId(token);

                if (userId != null) {
                    // 生成新的token
                    String newToken = jwtUtil.generateToken(userId);
                    logger.info("Token refreshed for user: {}", userId);
                    return ResponseEntity.ok(new ApiResponse<>(200, "success", newToken, true));
                }
            }
            return ResponseEntity.status(401).body(new ApiResponse<>(401, "无效的token", null, false));
        } catch (Exception e) {
            logger.error("Token refresh failed", e);
            return ResponseEntity.status(401).body(new ApiResponse<>(401, "Token刷新失败", null, false));
        }
    }

    @PostMapping("/logout")
    public ResponseEntity<ApiResponse<Object>> logout(@RequestHeader(value = "Authorization", required = false) String authHeader) {
        try {
            if (authHeader != null && authHeader.startsWith("Bearer ")) {
                String token = authHeader.substring(7);
                String userId = jwtUtil.extractUserId(token);

                if (userId != null) {
                    // 从Redis中删除用户信息
                    String userKey = "user:" + userId;
                    redisTemplate.delete(userKey);
                    logger.info("User logged out: {}", userId);
                }
            }
            return ResponseEntity.ok(new ApiResponse<>(200, "退出成功", null, true));
        } catch (Exception e) {
            logger.error("Logout failed", e);
            return ResponseEntity.ok(new ApiResponse<>(200, "退出成功", null, true)); // 即使失败也返回成功
        }
    }

    @GetMapping("/codes")
    public ResponseEntity<ApiResponse<Object>> getAccessCodes(@AuthenticationPrincipal User user) {
        try {
            // 根据用户角色返回权限码
            String[] codes;
            if (user != null && "ADMIN".equals(user.getRole())) {
                codes = new String[]{
                    "AC_100100", // 系统管理
                    "AC_100110", // 用户管理
                    "AC_100120", // 角色管理
                    "AC_100200", // 支付管理
                    "AC_100210", // 支付卡管理
                    "AC_100220", // 交易管理
                    "AC_100300", // 系统设置
                    "AC_100400"  // 日志管理
                };
            } else {
                codes = new String[]{
                    "AC_100200", // 支付管理
                    "AC_100210"  // 支付卡管理
                };
            }

            logger.info("Access codes retrieved for user: {}", user != null ? user.getUsername() : "anonymous");
            return ResponseEntity.ok(new ApiResponse<>(200, "success", codes, true));
        } catch (Exception e) {
            logger.error("Failed to get access codes", e);
            return ResponseEntity.status(500).body(new ApiResponse<>(500, "获取权限码失败", null, false));
        }
    }
}
