package core.exception;

import core.common.ApiResponse;
import core.constants.AppConstants;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;

import org.springframework.http.server.reactive.ServerHttpRequest;
import java.util.HashMap;
import java.util.Map;

/**
 * 全局异常处理器
 * 统一处理应用中的各种异常，返回标准的API响应格式
 */
@RestControllerAdvice
public class GlobalExceptionHandler {

    private static final Logger logger = LoggerFactory.getLogger(GlobalExceptionHandler.class);

    /**
     * 处理业务异常
     */
    @ExceptionHandler(BusinessException.class)
    public ResponseEntity<ApiResponse<Object>> handleBusinessException(BusinessException e, ServerHttpRequest request) {
        logger.warn("业务异常: {} - {}", request.getURI().getPath(), e.getMessage());
        return ResponseEntity.status(e.getCode())
                .body(new ApiResponse<>(e.getCode(), e.getMessage(), null, false));
    }

    /**
     * 处理参数验证异常
     */
    @ExceptionHandler({MethodArgumentNotValidException.class, BindException.class})
    public ResponseEntity<ApiResponse<Object>> handleValidationException(Exception e, ServerHttpRequest request) {
        logger.warn("参数验证异常: {} - {}", request.getURI().getPath(), e.getMessage());
        
        Map<String, String> errors = new HashMap<>();
        
        if (e instanceof MethodArgumentNotValidException) {
            MethodArgumentNotValidException ex = (MethodArgumentNotValidException) e;
            ex.getBindingResult().getAllErrors().forEach(error -> {
                String fieldName = ((FieldError) error).getField();
                String errorMessage = error.getDefaultMessage();
                errors.put(fieldName, errorMessage);
            });
        } else if (e instanceof BindException) {
            BindException ex = (BindException) e;
            ex.getBindingResult().getAllErrors().forEach(error -> {
                String fieldName = ((FieldError) error).getField();
                String errorMessage = error.getDefaultMessage();
                errors.put(fieldName, errorMessage);
            });
        }

        String message = errors.isEmpty() ? "参数验证失败" : "参数验证失败: " + errors.toString();
        return ResponseEntity.badRequest()
                .body(new ApiResponse<>(400, message, errors, false));
    }

    /**
     * 处理参数类型转换异常
     */
    @ExceptionHandler(MethodArgumentTypeMismatchException.class)
    public ResponseEntity<ApiResponse<Object>> handleTypeMismatchException(MethodArgumentTypeMismatchException e, ServerHttpRequest request) {
        logger.warn("参数类型转换异常: {} - {}", request.getURI().getPath(), e.getMessage());
        String message = String.format("参数 '%s' 的值 '%s' 无法转换为 %s 类型", 
                e.getName(), e.getValue(), e.getRequiredType().getSimpleName());
        return ResponseEntity.badRequest()
                .body(new ApiResponse<>(400, message, null, false));
    }

    /**
     * 处理权限不足异常
     */
    @ExceptionHandler(AccessDeniedException.class)
    public ResponseEntity<ApiResponse<Object>> handleAccessDeniedException(AccessDeniedException e, ServerHttpRequest request) {
        logger.warn("权限不足: {} - {}", request.getURI().getPath(), e.getMessage());
        return ResponseEntity.status(HttpStatus.FORBIDDEN)
                .body(new ApiResponse<>(403, "权限不足", null, false));
    }

    /**
     * 处理非法参数异常
     */
    @ExceptionHandler(IllegalArgumentException.class)
    public ResponseEntity<ApiResponse<Object>> handleIllegalArgumentException(IllegalArgumentException e, ServerHttpRequest request) {
        logger.warn("非法参数异常: {} - {}", request.getURI().getPath(), e.getMessage());
        return ResponseEntity.badRequest()
                .body(new ApiResponse<>(400, e.getMessage(), null, false));
    }

    /**
     * 处理空指针异常
     */
    @ExceptionHandler(NullPointerException.class)
    public ResponseEntity<ApiResponse<Object>> handleNullPointerException(NullPointerException e, ServerHttpRequest request) {
        logger.error("空指针异常: {} - {}", request.getURI().getPath(), e.getMessage(), e);
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(new ApiResponse<>(AppConstants.HttpStatus.INTERNAL_SERVER_ERROR,
                    AppConstants.ErrorMessage.SYSTEM_INTERNAL_ERROR, null, false));
    }

    /**
     * 处理运行时异常
     */
    @ExceptionHandler(RuntimeException.class)
    public ResponseEntity<ApiResponse<Object>> handleRuntimeException(RuntimeException e, ServerHttpRequest request) {
        logger.error("运行时异常: {} - {}", request.getURI().getPath(), e.getMessage(), e);
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(new ApiResponse<>(AppConstants.HttpStatus.INTERNAL_SERVER_ERROR,
                    AppConstants.ErrorMessage.SYSTEM_INTERNAL_ERROR + ": " + e.getMessage(), null, false));
    }

    /**
     * 处理其他所有异常
     */
    @ExceptionHandler(Exception.class)
    public ResponseEntity<ApiResponse<Object>> handleGenericException(Exception e, ServerHttpRequest request) {
        logger.error("未处理的异常: {} - {}", request.getURI().getPath(), e.getMessage(), e);
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(new ApiResponse<>(AppConstants.HttpStatus.INTERNAL_SERVER_ERROR,
                    AppConstants.ErrorMessage.SYSTEM_INTERNAL_ERROR, null, false));
    }
}
