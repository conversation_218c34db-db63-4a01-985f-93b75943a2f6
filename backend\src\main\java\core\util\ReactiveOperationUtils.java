package core.util;

import core.common.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import reactor.core.publisher.Mono;

import java.util.function.Function;
import java.util.function.Supplier;

/**
 * 响应式操作工具类
 * 提供常用的响应式操作模板，消除重复的Mono操作模式
 */
@Slf4j
public class ReactiveOperationUtils {

    /**
     * 执行操作并处理"不存在"的情况
     * 
     * @param operation 操作Mono
     * @param notFoundMessage 资源不存在时的错误消息
     * @param successMessage 成功时的消息
     * @return 处理后的响应
     */
    public static <T> Mono<ResponseEntity<ApiResponse<T>>> executeWithNotFoundHandling(
            Mono<T> operation, 
            String notFoundMessage, 
            String successMessage) {
        return operation
            .map(result -> ResponseEntity.ok(ApiResponse.success(result, successMessage)))
            .switchIfEmpty(Mono.just(ResponseEntity.ok(ApiResponse.error(notFoundMessage))));
    }

    /**
     * 执行操作并处理"不存在"的情况（无数据返回）
     * 
     * @param operation 操作Mono
     * @param notFoundMessage 资源不存在时的错误消息
     * @param successMessage 成功时的消息
     * @return 处理后的响应
     */
    public static <T> Mono<ResponseEntity<ApiResponse<T>>> executeWithNotFoundHandlingNoData(
            Mono<T> operation, 
            String notFoundMessage, 
            String successMessage) {
        return operation
            .map(result -> ResponseEntity.ok(ApiResponse.<T>success(successMessage)))
            .switchIfEmpty(Mono.just(ResponseEntity.ok(ApiResponse.error(notFoundMessage))));
    }

    /**
     * 执行存在性检查操作
     * 
     * @param existsCheck 存在性检查Mono
     * @param operation 主要操作
     * @param notFoundMessage 不存在时的错误消息
     * @return 处理后的响应
     */
    public static <T> Mono<ResponseEntity<ApiResponse<T>>> executeWithExistenceCheck(
            Mono<Boolean> existsCheck,
            Supplier<Mono<ResponseEntity<ApiResponse<T>>>> operation,
            String notFoundMessage) {
        return existsCheck
            .flatMap(exists -> {
                if (exists) {
                    return operation.get();
                } else {
                    return Mono.just(ResponseEntity.ok(ApiResponse.error(notFoundMessage)));
                }
            });
    }

    /**
     * 执行条件操作
     * 
     * @param condition 条件检查
     * @param trueOperation 条件为真时的操作
     * @param falseOperation 条件为假时的操作
     * @return 处理后的响应
     */
    public static <T> Mono<T> executeConditional(
            Mono<Boolean> condition,
            Supplier<Mono<T>> trueOperation,
            Supplier<Mono<T>> falseOperation) {
        return condition
            .flatMap(isTrue -> isTrue ? trueOperation.get() : falseOperation.get());
    }

    /**
     * 执行带重试的操作
     * 
     * @param operation 操作
     * @param maxRetries 最大重试次数
     * @param operationName 操作名称（用于日志）
     * @return 处理后的结果
     */
    public static <T> Mono<T> executeWithRetry(Mono<T> operation, int maxRetries, String operationName) {
        return operation
            .retry(maxRetries)
            .doOnError(error -> log.warn("{}重试{}次后仍然失败", operationName, maxRetries, error));
    }

    /**
     * 执行带超时的操作
     * 
     * @param operation 操作
     * @param timeout 超时时间（毫秒）
     * @param operationName 操作名称（用于日志）
     * @return 处理后的结果
     */
    public static <T> Mono<T> executeWithTimeout(Mono<T> operation, long timeout, String operationName) {
        return operation
            .timeout(java.time.Duration.ofMillis(timeout))
            .doOnError(error -> log.warn("{}操作超时: {}ms", operationName, timeout, error));
    }

    /**
     * 执行带日志的操作
     * 
     * @param operation 操作
     * @param operationName 操作名称
     * @param logSuccess 是否记录成功日志
     * @return 处理后的结果
     */
    public static <T> Mono<T> executeWithLogging(Mono<T> operation, String operationName, boolean logSuccess) {
        return operation
            .doOnSubscribe(subscription -> log.debug("开始执行: {}", operationName))
            .doOnSuccess(result -> {
                if (logSuccess) {
                    log.debug("执行成功: {}", operationName);
                }
            })
            .doOnError(error -> log.error("执行失败: {}", operationName, error));
    }

    /**
     * 执行带性能监控的操作
     * 
     * @param operation 操作
     * @param operationName 操作名称
     * @return 处理后的结果
     */
    public static <T> Mono<T> executeWithPerformanceMonitoring(Mono<T> operation, String operationName) {
        long startTime = System.currentTimeMillis();
        return operation
            .doOnSuccess(result -> {
                long duration = System.currentTimeMillis() - startTime;
                log.debug("{}执行完成，耗时: {}ms", operationName, duration);
            })
            .doOnError(error -> {
                long duration = System.currentTimeMillis() - startTime;
                log.warn("{}执行失败，耗时: {}ms", operationName, duration, error);
            });
    }

    /**
     * 执行带缓存回退的操作
     * 
     * @param primaryOperation 主要操作
     * @param fallbackOperation 回退操作
     * @param operationName 操作名称
     * @return 处理后的结果
     */
    public static <T> Mono<T> executeWithFallback(
            Mono<T> primaryOperation, 
            Supplier<Mono<T>> fallbackOperation, 
            String operationName) {
        return primaryOperation
            .onErrorResume(error -> {
                log.warn("{}主要操作失败，使用回退方案", operationName, error);
                return fallbackOperation.get();
            });
    }

    /**
     * 执行批量操作并收集结果
     * 
     * @param operations 操作列表
     * @param operationName 操作名称
     * @return 收集的结果列表
     */
    public static <T> Mono<java.util.List<T>> executeBatch(
            java.util.List<Mono<T>> operations, 
            String operationName) {
        return Mono.zip(operations, results -> {
            java.util.List<T> resultList = new java.util.ArrayList<>();
            for (Object result : results) {
                resultList.add((T) result);
            }
            return resultList;
        })
        .doOnSuccess(results -> log.debug("{}批量操作完成，处理了{}个项目", operationName, results.size()))
        .doOnError(error -> log.error("{}批量操作失败", operationName, error));
    }

    /**
     * 创建标准的成功响应
     * 
     * @param data 响应数据
     * @param message 成功消息
     * @return 成功响应
     */
    public static <T> ResponseEntity<ApiResponse<T>> createSuccessResponse(T data, String message) {
        return ResponseEntity.ok(ApiResponse.success(data, message));
    }

    /**
     * 创建标准的错误响应
     * 
     * @param message 错误消息
     * @return 错误响应
     */
    public static <T> ResponseEntity<ApiResponse<T>> createErrorResponse(String message) {
        return ResponseEntity.ok(ApiResponse.error(message));
    }

    /**
     * 创建标准的不存在响应
     * 
     * @param message 不存在消息
     * @return 不存在响应
     */
    public static <T> ResponseEntity<ApiResponse<T>> createNotFoundResponse(String message) {
        return ResponseEntity.status(404).body(ApiResponse.notFound(message));
    }
}
