package payment.service;

import domain.entity.PaymentCard;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * 支付卡更新服务
 * 专门处理支付卡的字段更新逻辑，从Controller中分离复杂的业务逻辑
 */
@Slf4j
@Service
public class PaymentCardUpdateService {

    /**
     * 更新支付卡字段
     * 
     * @param existingCard 现有的支付卡实体
     * @param updateRequest 更新请求数据
     * @return 更新后的支付卡实体
     */
    public PaymentCard updateCardFields(PaymentCard existingCard, Map<String, Object> updateRequest) {
        log.debug("开始更新支付卡字段: cardId={}", existingCard.getId());
        
        // 更新持卡人姓名
        updateCardHolderName(existingCard, updateRequest);
        
        // 更新过期月份
        updateExpiryMonth(existingCard, updateRequest);
        
        // 更新过期年份
        updateExpiryYear(existingCard, updateRequest);
        
        // 更新用户邮箱
        updateUserEmail(existingCard, updateRequest);
        
        log.debug("支付卡字段更新完成: cardId={}", existingCard.getId());
        return existingCard;
    }

    /**
     * 更新持卡人姓名
     */
    private void updateCardHolderName(PaymentCard card, Map<String, Object> updateRequest) {
        if (updateRequest.containsKey("cardHolderName")) {
            String newName = (String) updateRequest.get("cardHolderName");
            if (newName != null && !newName.trim().isEmpty()) {
                card.setCardHolderName(newName.trim());
                log.debug("更新持卡人姓名: {}", newName);
            }
        }
    }

    /**
     * 更新过期月份
     */
    private void updateExpiryMonth(PaymentCard card, Map<String, Object> updateRequest) {
        if (updateRequest.containsKey("expiryMonth")) {
            Object expiryMonthObj = updateRequest.get("expiryMonth");
            String expiryMonth = convertToString(expiryMonthObj);
            if (expiryMonth != null && isValidMonth(expiryMonth)) {
                card.setExpiryMonth(expiryMonth);
                log.debug("更新过期月份: {}", expiryMonth);
            }
        }
    }

    /**
     * 更新过期年份
     */
    private void updateExpiryYear(PaymentCard card, Map<String, Object> updateRequest) {
        if (updateRequest.containsKey("expiryYear")) {
            Object expiryYearObj = updateRequest.get("expiryYear");
            String expiryYear = convertToString(expiryYearObj);
            if (expiryYear != null && isValidYear(expiryYear)) {
                card.setExpiryYear(expiryYear);
                log.debug("更新过期年份: {}", expiryYear);
            }
        }
    }

    /**
     * 更新用户邮箱
     */
    private void updateUserEmail(PaymentCard card, Map<String, Object> updateRequest) {
        if (updateRequest.containsKey("userEmail")) {
            String newEmail = (String) updateRequest.get("userEmail");
            if (newEmail != null && isValidEmail(newEmail)) {
                card.setUserEmail(newEmail.trim());
                log.debug("更新用户邮箱: {}", newEmail);
            }
        }
    }

    /**
     * 将对象转换为字符串
     */
    private String convertToString(Object obj) {
        if (obj == null) {
            return null;
        }
        if (obj instanceof String) {
            return ((String) obj).trim();
        }
        if (obj instanceof Integer) {
            return String.valueOf(obj);
        }
        return obj.toString().trim();
    }

    /**
     * 验证月份格式
     */
    private boolean isValidMonth(String month) {
        if (month == null || month.isEmpty()) {
            return false;
        }
        try {
            int monthInt = Integer.parseInt(month);
            return monthInt >= 1 && monthInt <= 12;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    /**
     * 验证年份格式
     */
    private boolean isValidYear(String year) {
        if (year == null || year.isEmpty()) {
            return false;
        }
        try {
            int yearInt = Integer.parseInt(year);
            int currentYear = java.time.Year.now().getValue();
            return yearInt >= currentYear && yearInt <= currentYear + 20;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    /**
     * 验证邮箱格式
     */
    private boolean isValidEmail(String email) {
        if (email == null || email.trim().isEmpty()) {
            return false;
        }
        return email.matches("^[A-Za-z0-9+_.-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$");
    }
}
