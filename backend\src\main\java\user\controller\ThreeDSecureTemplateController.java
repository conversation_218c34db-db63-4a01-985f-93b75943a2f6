package user.controller;

import core.common.ApiResponse;
import common.service.payment.ThreeDSecureClient;
import common.constants.TemplateConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 3D验证模板控制器
 * 提供模板渲染和管理功能
 */
@Slf4j
@RestController
@RequestMapping("/api/3d-secure/templates")
public class ThreeDSecureTemplateController {



    @Autowired
    private ThreeDSecureClient templateService;

    /**
     * 获取模板列表
     */
    @GetMapping
    public ResponseEntity<ApiResponse<List<Map<String, Object>>>> getTemplates(
            @RequestParam(required = false) String type,
            @RequestParam(required = false, defaultValue = "true") Boolean enabled,
            @RequestParam(required = false) String cardNumber) {
        try {
            log.info("获取模板列表请求 - type: {}, enabled: {}, cardNumber: {}", type, enabled, cardNumber);

            List<String> availableTypes = templateService.getAvailableTemplateTypes();
            log.info("可用模板类型: {}", availableTypes);

            // 构建模板信息列表
            List<Map<String, Object>> templates = availableTypes.stream()
                .map(templateType -> {
                    Map<String, Object> template = new java.util.HashMap<>();
                    template.put("id", templateType);
                    template.put("templateName", getTemplateDisplayName(templateType));
                    template.put("templateType", getTemplateCategory(templateType));
                    template.put("language", "zh-CN");
                    template.put("description", getTemplateDescription(templateType));
                    template.put("isEnabled", true); // 默认启用
                    template.put("updatedAt", java.time.LocalDateTime.now());

                    // 如果提供了卡号，添加推荐信息
                    if (cardNumber != null && !cardNumber.trim().isEmpty()) {
                        String cardType = templateService.detectCardType(cardNumber);
                        String recommendedTemplate = templateService.getRecommendedTemplate(cardType);
                        template.put("recommended", templateType.equals(recommendedTemplate));
                        template.put("cardType", cardType);
                        template.put("cardIcon", getCardIcon(cardType));
                    }

                    return template;
                })
                .filter(template -> type == null || type.equals(template.get("type")))
                .collect(java.util.stream.Collectors.toList());

            log.info("构建的模板列表数量: {}", templates.size());
            return ResponseEntity.ok(ApiResponse.success(templates, "获取模板列表成功"));
        } catch (Exception e) {
            log.error("获取模板列表失败", e);
            return ResponseEntity.ok(ApiResponse.error("获取模板列表失败: " + e.getMessage()));
        }
    }

    // ==================== 新增API端点 ====================

    /**
     * 获取所有可用的模板类型
     */
    @GetMapping("/types")
    public ResponseEntity<ApiResponse<List<String>>> getAvailableTemplateTypes() {
        try {
            List<String> availableTypes = templateService.getAvailableTemplateTypes();
            return ResponseEntity.ok(ApiResponse.success(availableTypes));
        } catch (Exception e) {
            log.error("获取可用模板类型失败", e);
            return ResponseEntity.ok(ApiResponse.error("获取模板类型失败: " + e.getMessage()));
        }
    }


    /**
     * 更新模板状态
     */
    @PutMapping("/{templateId}")
    public ResponseEntity<ApiResponse<Object>> updateTemplateStatus(
            @PathVariable String templateId,
            @RequestBody Map<String, Object> updateData) {
        try {
            // 这里简化处理，实际应该有数据库存储模板状态
            boolean isEnabled = (Boolean) updateData.getOrDefault("isEnabled", true);

            Map<String, Object> result = new HashMap<>();
            result.put("id", templateId);
            result.put("isEnabled", isEnabled);
            result.put("message", isEnabled ? "模板已启用" : "模板已禁用");

            return ResponseEntity.ok(ApiResponse.success(result, "模板状态更新成功"));
        } catch (Exception e) {
            log.error("更新模板状态失败: templateId={}", templateId, e);
            return ResponseEntity.ok(ApiResponse.error("更新模板状态失败: " + e.getMessage()));
        }
    }

    /**
     * 创建新模板
     */
    @PostMapping
    public ResponseEntity<ApiResponse<Object>> createTemplate(@RequestBody Map<String, Object> templateData) {
        try {
            // 这里简化处理，实际应该保存到数据库
            Map<String, Object> result = new HashMap<>();
            result.put("id", System.currentTimeMillis());
            result.put("templateName", templateData.get("templateName"));
            result.put("templateType", templateData.get("templateType"));
            result.put("isEnabled", templateData.getOrDefault("isEnabled", true));
            result.put("message", "模板创建成功");

            return ResponseEntity.ok(ApiResponse.success(result, "模板创建成功"));
        } catch (Exception e) {
            log.error("创建模板失败", e);
            return ResponseEntity.ok(ApiResponse.error("创建模板失败: " + e.getMessage()));
        }
    }

    /**
     * 删除模板
     */
    @DeleteMapping("/{templateId}")
    public ResponseEntity<ApiResponse<Object>> deleteTemplate(@PathVariable String templateId) {
        try {
            // 这里简化处理，实际应该从数据库删除
            Map<String, Object> result = new HashMap<>();
            result.put("id", templateId);
            result.put("message", "模板删除成功");

            return ResponseEntity.ok(ApiResponse.success(result, "模板删除成功"));
        } catch (Exception e) {
            log.error("删除模板失败: templateId={}", templateId, e);
            return ResponseEntity.ok(ApiResponse.error("删除模板失败: " + e.getMessage()));
        }
    }

    /**
     * 根据卡号推荐模板类型
     */
    @GetMapping("/recommend")
    public ResponseEntity<ApiResponse<Map<String, Object>>> recommendTemplate(
            @RequestParam(required = false) String cardNumber) {
        try {
            String cardType = templateService.detectCardType(cardNumber);
            String recommendedTemplate = templateService.getRecommendedTemplate(cardType);

            Map<String, Object> result = new java.util.HashMap<>();
            result.put("cardType", cardType);
            result.put("recommendedTemplate", recommendedTemplate);
            result.put("cardIcon", getCardIcon(cardType));
            result.put("templateIcon", getTemplateIcon(recommendedTemplate));
            result.put("templateDescription", getTemplateDescription(recommendedTemplate));

            return ResponseEntity.ok(ApiResponse.success(result, "模板推荐成功"));
        } catch (Exception e) {
            log.error("推荐模板失败", e);
            return ResponseEntity.ok(ApiResponse.error("推荐模板失败: " + e.getMessage()));
        }
    }

    /**
     * 根据银行卡号获取推荐模板和图标信息
     */
    @GetMapping("/card-info")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getCardInfo(
            @RequestParam String cardNumber) {
        try {
            if (cardNumber == null || cardNumber.trim().isEmpty()) {
                return ResponseEntity.ok(ApiResponse.error("银行卡号不能为空"));
            }

            String cardType = templateService.detectCardType(cardNumber);
            String recommendedTemplate = templateService.getRecommendedTemplate(cardType);

            Map<String, Object> result = new java.util.HashMap<>();
            result.put("cardNumber", cardNumber);
            result.put("cardType", cardType);
            result.put("cardIcon", getCardIcon(cardType));
            result.put("recommendedTemplate", recommendedTemplate);
            result.put("templateIcon", getTemplateIcon(recommendedTemplate));
            result.put("templateDescription", getTemplateDescription(recommendedTemplate));
            result.put("templateCategory", getTemplateCategory(recommendedTemplate));

            return ResponseEntity.ok(ApiResponse.success(result, "获取银行卡信息成功"));
        } catch (Exception e) {
            log.error("获取银行卡信息失败: cardNumber={}", cardNumber, e);
            return ResponseEntity.ok(ApiResponse.error("获取银行卡信息失败: " + e.getMessage()));
        }
    }

    /**
     * 批量预览多个模板
     */
    @PostMapping("/preview/batch")
    public ResponseEntity<ApiResponse<Map<String, String>>> previewMultipleTemplates(
            @RequestBody Map<String, Object> request) {
        try {
            @SuppressWarnings("unchecked")
            List<String> templateTypes = (List<String>) request.get("templateTypes");
            @SuppressWarnings("unchecked")
            Map<String, Object> variables = (Map<String, Object>) request.get("variables");

            if (templateTypes == null || templateTypes.isEmpty()) {
                return ResponseEntity.ok(ApiResponse.error("模板类型列表不能为空"));
            }

            return templateService.renderMultipleTemplates(templateTypes, variables)
                .map(results -> ResponseEntity.ok(ApiResponse.success(results)))
                .onErrorReturn(ResponseEntity.ok(ApiResponse.error("批量预览失败")))
                .block();
        } catch (Exception e) {
            log.error("批量预览模板失败", e);
            return ResponseEntity.ok(ApiResponse.error("批量预览失败: " + e.getMessage()));
        }
    }

    /**
     * 获取模板分类
     */
    private String getTemplateCategory(String templateType) {
        if (templateType.contains("COMPLETE") || templateType.contains("SUCCESS")) {
            return "complete";
        } else if (templateType.contains("ERROR") || templateType.contains("FAIL")) {
            return "error";
        } else {
            return "verification";
        }
    }

    /**
     * 获取模板显示名称
     */
    private String getTemplateDisplayName(String templateType) {
        // 先尝试转换旧版模板类型
        String convertedType = TemplateConstants.convertLegacyTemplateType(templateType);

        // 使用TemplateConstants的方法获取显示名称
        if (TemplateConstants.isValidTemplateType(convertedType)) {
            return TemplateConstants.getTemplateDisplayName(convertedType);
        }

        // 处理特殊的完成和错误页面
        return switch (templateType.toUpperCase()) {
            case "COMPLETE" -> "验证完成页面";
            case "ERROR" -> "错误页面";
            default -> templateType;
        };
    }

    /**
     * 获取模板描述
     */
    private String getTemplateDescription(String templateType) {
        // 先尝试转换旧版模板类型
        String convertedType = TemplateConstants.convertLegacyTemplateType(templateType);

        // 使用TemplateConstants的方法获取显示名称并添加"模板"后缀
        if (TemplateConstants.isValidTemplateType(convertedType)) {
            return TemplateConstants.getTemplateDisplayName(convertedType) + "模板";
        }

        // 处理特殊的完成和错误页面
        return switch (templateType.toUpperCase()) {
            case "COMPLETE" -> "验证完成页面模板";
            case "ERROR" -> "错误页面模板";
            default -> "3D安全验证模板";
        };
    }

    /**
     * 获取模板图标
     */
    private String getTemplateIcon(String templateType) {
        return TemplateConstants.getTemplateIcon(templateType);
    }

    /**
     * 根据银行卡类型获取图标
     */
    private String getCardIcon(String cardType) {
        switch (cardType.toUpperCase()) {
            case "VISA":
                return "visa.png";
            case "MASTERCARD":
                return "mastercard.png";
            case "AMEX":
            case "AMERICAN_EXPRESS":
                return "amex.png";
            case "DISCOVER":
                return "discover.png";
            case "JCB":
                return "jcb.png";
            case "UNIONPAY":
                return "unionpay.png";
            case "DINERS":
                return "diners.png";
            default:
                return "card.png";
        }
    }

    // ========== 3ds服务对接API ==========

    /**
     * 从3ds服务获取验证页面
     */
    @GetMapping(value = "/service/{templateType}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<String> getTemplateFromService(@PathVariable String templateType,
                                                        @RequestParam(required = false) Map<String, Object> variables) {
        return templateService.getVerificationPageFromService(templateType, variables)
                .map(content -> ResponseEntity.ok()
                        .contentType(MediaType.APPLICATION_JSON)
                        .body(content))
                .onErrorReturn(ResponseEntity.status(500)
                        .contentType(MediaType.APPLICATION_JSON)
                        .body("从3ds服务获取模板失败"))
                .block();
    }

    /**
     * 生成3D验证URL
     */
    @PostMapping("/generate-url")
    public ResponseEntity<ApiResponse<Map<String, String>>> generateVerificationUrl(@RequestBody Map<String, Object> request) {
        try {
            String cardId = (String) request.get("cardId");
            String templateType = (String) request.get("templateType");
            Map<String, Object> params = (Map<String, Object>) request.getOrDefault("params", new HashMap<>());

            String url = templateService.generateVerificationUrl(cardId, templateType, params);

            Map<String, String> result = new HashMap<>();
            result.put("verificationUrl", url);
            result.put("templateType", templateType);
            result.put("cardId", cardId);

            return ResponseEntity.ok(ApiResponse.success(result, "验证URL生成成功"));
        } catch (Exception e) {
            log.error("生成验证URL失败", e);
            return ResponseEntity.ok(ApiResponse.error("生成验证URL失败: " + e.getMessage()));
        }
    }

    /**
     * 检查3ds服务健康状态
     */
    @GetMapping("/service/health")
    public ResponseEntity<ApiResponse<Map<String, Object>>> check3dsServiceHealth() {
        return templateService.check3dsServiceHealth()
                .map(healthy -> {
                    Map<String, Object> result = new HashMap<>();
                    result.put("healthy", healthy);
                    result.put("serviceUrl", templateService.get3dsServiceInfo().get("serviceUrl"));
                    result.put("timestamp", System.currentTimeMillis());

                    return ResponseEntity.ok(ApiResponse.success(result,
                            healthy ? "3ds服务运行正常" : "3ds服务不可用"));
                })
                .onErrorReturn(ResponseEntity.ok(ApiResponse.error("检查3ds服务状态失败")))
                .block();
    }

    /**
     * 获取3ds服务模板可用性
     */
    @GetMapping("/service/availability")
    public ResponseEntity<ApiResponse<Map<String, Boolean>>> getServiceTemplateAvailability() {
        return templateService.getServiceTemplateAvailability()
                .map(availability -> ResponseEntity.ok(ApiResponse.success(availability, "获取模板可用性成功")))
                .onErrorReturn(ResponseEntity.ok(ApiResponse.error("获取模板可用性失败")))
                .block();
    }

    /**
     * 获取3ds服务信息
     */
    @GetMapping("/service/info")
    public ResponseEntity<ApiResponse<Map<String, Object>>> get3dsServiceInfo() {
        try {
            Map<String, Object> info = templateService.get3dsServiceInfo();
            return ResponseEntity.ok(ApiResponse.success(info, "获取3ds服务信息成功"));
        } catch (Exception e) {
            log.error("获取3ds服务信息失败", e);
            return ResponseEntity.ok(ApiResponse.error("获取3ds服务信息失败: " + e.getMessage()));
        }
    }

}
