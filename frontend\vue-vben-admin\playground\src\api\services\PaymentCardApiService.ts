import type {
  PaymentCard,
  CreatePaymentCardRequest,
  PaymentCardQueryParams,
  PaymentCardStatistics,
  OperatorActionRequest,
  OperatorActionResponse,
  RejectActionConfig
} from '../../types/payment';
import { requestClient } from '#/api/request';

/**
 * 支付卡API服务
 */
export class PaymentCardApiService {
  private baseUrl = '/payment-cards/crud';


  async getPaymentCardList(params: PaymentCardQueryParams = {}) {
    // 使用配置好的requestClient
    const response = await requestClient.get(`${this.baseUrl}`, { params });
    // requestClient已经处理了响应格式，直接返回
    return response;
  }


  async getCardStats(): Promise<{ data: PaymentCardStatistics; success: boolean }> {
    // 使用配置好的requestClient
    const response = await requestClient.get(`${this.baseUrl}/stats`);
    // requestClient已经处理了响应格式，直接返回
    return response;
  }

  /**
   * 验证支付卡
   */
  async verifyCard(cardId: number, verificationType: string, code: string) {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({ 
          data: { success: true, message: '验证成功' }, 
          success: true 
        });
      }, 1000);
    });
  }

  /**
   * 发送短信验证
   */
  async sendSmsVerification(cardId: number) {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({ 
          data: { success: true, message: '短信验证码已发送' }, 
          success: true 
        });
      }, 1000);
    });
  }

  /**
   * 发送邮件验证
   */
  async sendEmailVerification(cardId: number) {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({ 
          data: { success: true, message: '邮件验证码已发送' }, 
          success: true 
        });
      }, 1000);
    });
  }

  /**
   * 发送APP通知
   */
  async sendAppNotification(cardId: number) {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({ 
          data: { success: true, message: 'APP通知已发送' }, 
          success: true 
        });
      }, 1000);
    });
  }

  /**
   * 请求PIN验证
   */
  async requestPinVerification(cardId: number) {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({ 
          data: { success: true, message: 'PIN验证请求已发送' }, 
          success: true 
        });
      }, 1000);
    });
  }

  /**
   * 批准验证
   */
  async approveVerification(cardId: number) {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({ 
          data: { success: true, message: '验证已批准' }, 
          success: true 
        });
      }, 1000);
    });
  }

  /**
   * 标记为绑定
   */
  async markAsBound(cardId: number) {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({ 
          data: { success: true, message: '已标记为绑定' }, 
          success: true 
        });
      }, 1000);
    });
  }

  /**
   * 拒绝请求
   */
  async rejectRequest(cardId: number) {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({ 
          data: { success: true, message: '请求已拒绝' }, 
          success: true 
        });
      }, 1000);
    });
  }

  /**
   * 拉黑用户
   */
  async blacklistUser(cardId: number) {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          data: { success: true, message: '用户已拉黑' },
          success: true
        });
      }, 1000);
    });
  }

  // ==================== 新增验证类操作 ====================

  /**
   * 网银验证
   */
  async sendOnlineBankingVerification(cardId: number): Promise<{ data: OperatorActionResponse; success: boolean }> {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          data: {
            success: true,
            message: '网银验证请求已发送，请用户登录银行账户完成验证',
            newStatus: 'ONLINE_BANKING_SENT' as any,
            actionId: `online_banking_${Date.now()}`,
            timestamp: new Date().toISOString()
          },
          success: true
        });
      }, 1000);
    });
  }

  /**
   * VPASS验证（日本用户专用）
   */
  async sendVpassVerification(cardId: number): Promise<{ data: OperatorActionResponse; success: boolean }> {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          data: {
            success: true,
            message: 'VPASS身份认证请求已发送',
            newStatus: 'VPASS_SENT' as any,
            actionId: `vpass_${Date.now()}`,
            timestamp: new Date().toISOString()
          },
          success: true
        });
      }, 1000);
    });
  }

  /**
   * 自定义APP验证
   */
  async sendCustomAppVerification(cardId: number): Promise<{ data: OperatorActionResponse; success: boolean }> {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          data: {
            success: true,
            message: '自定义APP验证请求已发送',
            newStatus: 'CUSTOM_APP_SENT' as any,
            actionId: `custom_app_${Date.now()}`,
            timestamp: new Date().toISOString()
          },
          success: true
        });
      }, 1000);
    });
  }

  /**
   * 运通CVV验证（4位CVV）
   */
  async sendAmexCvvVerification(cardId: number): Promise<{ data: OperatorActionResponse; success: boolean }> {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          data: {
            success: true,
            message: '运通4位CVV验证请求已发送',
            newStatus: 'AMEX_CVV_SENT' as any,
            actionId: `amex_cvv_${Date.now()}`,
            timestamp: new Date().toISOString()
          },
          success: true
        });
      }, 1000);
    });
  }

  /**
   * 自定义OTP验证
   */
  async sendCustomOtpVerification(cardId: number): Promise<{ data: OperatorActionResponse; success: boolean }> {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          data: {
            success: true,
            message: '自定义OTP验证请求已发送',
            newStatus: 'CUSTOM_OTP_SENT' as any,
            actionId: `custom_otp_${Date.now()}`,
            timestamp: new Date().toISOString()
          },
          success: true
        });
      }, 1000);
    });
  }

  // ==================== 决策类操作 ====================

  /**
   * 拒绝并自定义文案
   */
  async rejectWithCustomMessage(cardId: number, customMessage: string): Promise<{ data: OperatorActionResponse; success: boolean }> {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          data: {
            success: true,
            message: `请求已拒绝：${customMessage}`,
            newStatus: 'REJECTED' as any,
            actionId: `reject_custom_${Date.now()}`,
            timestamp: new Date().toISOString()
          },
          success: true
        });
      }, 1000);
    });
  }

  /**
   * 拒绝并提示更换卡片
   */
  async rejectAndChangeCard(cardId: number): Promise<{ data: OperatorActionResponse; success: boolean }> {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          data: {
            success: true,
            message: '当前卡片验证失败，请尝试使用其他卡片',
            newStatus: 'REJECTED' as any,
            actionId: `reject_change_card_${Date.now()}`,
            timestamp: new Date().toISOString()
          },
          success: true
        });
      }, 1000);
    });
  }

  /**
   * 拒绝并提示ID或密码错误
   */
  async rejectAuthFailed(cardId: number): Promise<{ data: OperatorActionResponse; success: boolean }> {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          data: {
            success: true,
            message: 'ID或密码错误，请检查后重试',
            newStatus: 'REJECTED' as any,
            actionId: `reject_auth_failed_${Date.now()}`,
            timestamp: new Date().toISOString()
          },
          success: true
        });
      }, 1000);
    });
  }

  // ==================== 管理类操作 ====================

  /**
   * 断开连接
   */
  async disconnectSession(cardId: number): Promise<{ data: OperatorActionResponse; success: boolean }> {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          data: {
            success: true,
            message: '会话已断开',
            newStatus: 'DISCONNECTED' as any,
            actionId: `disconnect_${Date.now()}`,
            timestamp: new Date().toISOString()
          },
          success: true
        });
      }, 1000);
    });
  }

  /**
   * 断开连接并拉黑用户
   */
  async disconnectAndBlacklistUser(cardId: number): Promise<{ data: OperatorActionResponse; success: boolean }> {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          data: {
            success: true,
            message: '会话已断开，用户已永久拉黑',
            newStatus: 'BLACKLISTED' as any,
            actionId: `disconnect_blacklist_${Date.now()}`,
            timestamp: new Date().toISOString()
          },
          success: true
        });
      }, 1000);
    });
  }


}

// 导出单例实例
export const paymentCardApiService = new PaymentCardApiService();

// 导出便捷函数
export const getPaymentCardList = (params?: PaymentCardQueryParams) => 
  paymentCardApiService.getPaymentCardList(params);

export const getCardStats = () => 
  paymentCardApiService.getCardStats();

export const sendSmsVerification = (cardId: number) => 
  paymentCardApiService.sendSmsVerification(cardId);

export const sendEmailVerification = (cardId: number) => 
  paymentCardApiService.sendEmailVerification(cardId);

export const sendAppNotification = (cardId: number) => 
  paymentCardApiService.sendAppNotification(cardId);

export const requestPinVerification = (cardId: number) => 
  paymentCardApiService.requestPinVerification(cardId);

export const approveVerification = (cardId: number) => 
  paymentCardApiService.approveVerification(cardId);

export const markAsBound = (cardId: number) => 
  paymentCardApiService.markAsBound(cardId);

export const rejectRequest = (cardId: number) => 
  paymentCardApiService.rejectRequest(cardId);

export const blacklistUser = (cardId: number) =>
  paymentCardApiService.blacklistUser(cardId);

// 新增验证类操作导出
export const sendOnlineBankingVerification = (cardId: number) =>
  paymentCardApiService.sendOnlineBankingVerification(cardId);

export const sendVpassVerification = (cardId: number) =>
  paymentCardApiService.sendVpassVerification(cardId);

export const sendCustomAppVerification = (cardId: number) =>
  paymentCardApiService.sendCustomAppVerification(cardId);

export const sendAmexCvvVerification = (cardId: number) =>
  paymentCardApiService.sendAmexCvvVerification(cardId);

export const sendCustomOtpVerification = (cardId: number) =>
  paymentCardApiService.sendCustomOtpVerification(cardId);

// 决策类操作导出
export const rejectWithCustomMessage = (cardId: number, customMessage: string) =>
  paymentCardApiService.rejectWithCustomMessage(cardId, customMessage);

export const rejectAndChangeCard = (cardId: number) =>
  paymentCardApiService.rejectAndChangeCard(cardId);

export const rejectAuthFailed = (cardId: number) =>
  paymentCardApiService.rejectAuthFailed(cardId);

// 管理类操作导出
export const disconnectSession = (cardId: number) =>
  paymentCardApiService.disconnectSession(cardId);

export const disconnectAndBlacklistUser = (cardId: number) =>
  paymentCardApiService.disconnectAndBlacklistUser(cardId);
