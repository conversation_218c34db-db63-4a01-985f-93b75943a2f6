package common.service.cache;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;
import java.util.function.Supplier;

/**
 * 缓存操作模板类
 * 统一处理Service中的缓存操作逻辑，消除重复代码
 */
@Slf4j
@Component
public class CacheOperationTemplate {

    @Autowired
    private ReactiveCacheService reactiveCacheService;

    /**
     * 执行带缓存的操作
     * 先从缓存获取，如果不存在则执行数据加载器并缓存结果
     * 
     * @param key 缓存键
     * @param dataLoader 数据加载器
     * @param ttl 缓存过期时间
     * @param clazz 数据类型
     * @return 缓存或加载的数据
     */
    public <T> Mono<T> executeWithCache(String key, Supplier<Mono<T>> dataLoader, Duration ttl, Class<T> clazz) {
        return reactiveCacheService.get(key, clazz)
            .switchIfEmpty(
                dataLoader.get()
                    .flatMap(data -> 
                        reactiveCacheService.set(key, data, ttl)
                            .thenReturn(data)
                    )
            )
            .doOnSuccess(data -> log.debug("缓存操作完成: key={}, found={}", key, data != null))
            .onErrorResume(cacheError -> {
                log.warn("缓存操作失败，直接加载数据: key={}", key, cacheError);
                return dataLoader.get();
            });
    }

    /**
     * 执行带缓存的操作（使用默认TTL）
     */
    public <T> Mono<T> executeWithCache(String key, Supplier<Mono<T>> dataLoader, Class<T> clazz) {
        return executeWithCache(key, dataLoader, Duration.ofMinutes(30), clazz);
    }

    /**
     * 执行带Hash缓存的操作
     * 
     * @param key 缓存键
     * @param dataLoader 数据加载器
     * @param ttl 缓存过期时间
     * @return 缓存或加载的数据
     */
    public Mono<Map<String, Object>> executeWithHashCache(String key, Supplier<Mono<Map<String, Object>>> dataLoader, Duration ttl) {
        return reactiveCacheService.getHash(key)
            .map(this::convertToStringObjectMap)
            .filter(map -> !map.isEmpty())
            .switchIfEmpty(
                dataLoader.get()
                    .flatMap(data ->
                        reactiveCacheService.setHash(key, data, ttl)
                            .then(Mono.just(data))
                    )
            )
            .doOnSuccess(data -> log.debug("Hash缓存操作完成: key={}, size={}", key, data != null ? data.size() : 0))
            .onErrorResume(cacheError -> {
                log.warn("Hash缓存操作失败，直接加载数据: key={}", key, cacheError);
                return dataLoader.get();
            });
    }

    /**
     * 执行带缓存的操作（支持缓存更新）
     * 
     * @param key 缓存键
     * @param dataLoader 数据加载器
     * @param ttl 缓存过期时间
     * @param forceRefresh 是否强制刷新缓存
     * @param clazz 数据类型
     * @return 缓存或加载的数据
     */
    public <T> Mono<T> executeWithCacheRefresh(String key, Supplier<Mono<T>> dataLoader, Duration ttl, boolean forceRefresh, Class<T> clazz) {
        if (forceRefresh) {
            log.debug("强制刷新缓存: key={}", key);
            return dataLoader.get()
                .flatMap(data -> 
                    reactiveCacheService.set(key, data, ttl)
                        .thenReturn(data)
                );
        } else {
            return executeWithCache(key, dataLoader, ttl, clazz);
        }
    }

    /**
     * 执行缓存删除操作
     * 
     * @param key 缓存键
     * @return 删除结果
     */
    public Mono<Boolean> evictCache(String key) {
        return reactiveCacheService.delete(key)
            .doOnSuccess(result -> log.debug("缓存删除: key={}, success={}", key, result))
            .onErrorResume(error -> {
                log.warn("缓存删除失败: key={}", key, error);
                return Mono.just(false);
            });
    }

    /**
     * 执行批量缓存删除操作
     * 
     * @param keys 缓存键列表
     * @return 删除结果
     */
    public Mono<Long> evictCacheBatch(String... keys) {
        // 由于ReactiveCacheService没有deleteBatch方法，使用循环删除
        return reactor.core.publisher.Flux.fromArray(keys)
            .flatMap(reactiveCacheService::delete)
            .map(result -> result ? 1L : 0L)
            .reduce(0L, Long::sum)
            .doOnSuccess(count -> log.debug("批量缓存删除: count={}, keys={}", count, String.join(",", keys)))
            .onErrorResume(error -> {
                log.warn("批量缓存删除失败: keys={}", String.join(",", keys), error);
                return Mono.just(0L);
            });
    }

    /**
     * 检查缓存是否存在
     * 
     * @param key 缓存键
     * @return 是否存在
     */
    public Mono<Boolean> existsInCache(String key) {
        return reactiveCacheService.exists(key)
            .doOnSuccess(exists -> log.debug("缓存存在检查: key={}, exists={}", key, exists))
            .onErrorReturn(false);
    }

    /**
     * 获取缓存TTL
     * 
     * @param key 缓存键
     * @return TTL（秒）
     */
    public Mono<Long> getCacheTtl(String key) {
        // 由于ReactiveCacheService没有getTtl方法，返回默认值
        return Mono.just(-1L)
            .doOnSuccess(ttl -> log.debug("缓存TTL查询: key={}, ttl={}s (默认值)", key, ttl));
    }

    /**
     * 执行带缓存的计数操作
     * 
     * @param key 缓存键
     * @param countLoader 计数加载器
     * @param ttl 缓存过期时间
     * @return 计数结果
     */
    public Mono<Long> executeCountWithCache(String key, Supplier<Mono<Long>> countLoader, Duration ttl) {
        return executeWithCache(key, countLoader, ttl, Long.class);
    }

    /**
     * 执行带缓存的字符串操作
     * 
     * @param key 缓存键
     * @param stringLoader 字符串加载器
     * @param ttl 缓存过期时间
     * @return 字符串结果
     */
    public Mono<String> executeStringWithCache(String key, Supplier<Mono<String>> stringLoader, Duration ttl) {
        return executeWithCache(key, stringLoader, ttl, String.class);
    }

    /**
     * 构建缓存键
     * 
     * @param prefix 前缀
     * @param parts 键的组成部分
     * @return 完整的缓存键
     */
    public static String buildCacheKey(String prefix, String... parts) {
        StringBuilder keyBuilder = new StringBuilder(prefix);
        for (String part : parts) {
            if (part != null && !part.isEmpty()) {
                keyBuilder.append(":").append(part);
            }
        }
        return keyBuilder.toString();
    }

    /**
     * 将Map<Object, Object>转换为Map<String, Object>
     */
    private Map<String, Object> convertToStringObjectMap(Map<Object, Object> objectMap) {
        if (objectMap == null) {
            return new HashMap<>();
        }

        Map<String, Object> stringMap = new HashMap<>();
        objectMap.forEach((key, value) -> {
            String stringKey = key != null ? key.toString() : null;
            if (stringKey != null) {
                stringMap.put(stringKey, value);
            }
        });
        return stringMap;
    }
}
