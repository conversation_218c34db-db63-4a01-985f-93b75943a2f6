package admin.controller;

import core.common.ApiResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import domain.entity.PaymentCard;
import common.service.payment.PaymentCardService;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * 支付卡片CRUD控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/payment-cards/crud")
@RequiredArgsConstructor
public class PaymentCardCrudController {

    private final PaymentCardService paymentCardService;

    /**
     * 创建支付卡片
     */
    @PostMapping
    public Mono<ApiResponse<PaymentCard>> createCard(@RequestBody PaymentCard card) {
        log.info("创建支付卡片: {}", card.getCardNumber());
        return paymentCardService.createCard(card)
                .map(ApiResponse::success)
                .onErrorResume(error -> {
                    log.error("创建支付卡片失败", error);
                    return Mono.just(ApiResponse.error("创建支付卡片失败: " + error.getMessage()));
                });
    }

    /**
     * 根据ID获取支付卡片
     */
    @GetMapping("/{id}")
    public Mono<ApiResponse<PaymentCard>> getCardById(@PathVariable String id) {
        log.info("获取支付卡片: {}", id);
        return paymentCardService.findById(Long.parseLong(id))
                .map(ApiResponse::success)
                .switchIfEmpty(Mono.just(ApiResponse.notFound("支付卡片不存在")))
                .onErrorResume(error -> {
                    log.error("获取支付卡片失败: {}", id, error);
                    return Mono.just(ApiResponse.error("获取支付卡片失败: " + error.getMessage()));
                });
    }

    /**
     * 分页获取支付卡片
     */
    @GetMapping
    public Mono<ApiResponse<java.util.HashMap<String,Object>>> getCardsPaged(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int pageSize,
            @RequestParam(required = false) String cardNumber,
            @RequestParam(required = false) String holderName) {
        log.info("分页获取支付卡片: page={}, pageSize={}, cardNumber={}, holderName={}",
                page, pageSize, cardNumber, holderName);

        return paymentCardService.findAll()
                .collectList()
                .map(cards -> {
                    // 简单的内存分页和过滤
                    var filteredCards = cards.stream()
                            .filter(card -> cardNumber == null || cardNumber.isEmpty() ||
                                    card.getCardNumber().contains(cardNumber))
                            .filter(card -> holderName == null || holderName.isEmpty() ||
                                    card.getHolderName().contains(holderName))
                            .toList();

                    int total = filteredCards.size();
                    int start = (page - 1) * pageSize;
                    int end = Math.min(start + pageSize, total);

                    var pagedCards = start < total ? filteredCards.subList(start, end) : java.util.List.of();

                    // 构建分页响应 - 使用前端期望的格式
                    var response = new java.util.HashMap<String, Object>();
                    response.put("list", pagedCards);  // 前端期望的字段名
                    response.put("total", total);
                    response.put("page", page);
                    response.put("pageSize", pageSize);
                    response.put("pages", (int) Math.ceil((double) total / pageSize));

                    return ApiResponse.success(response);
                })
                .onErrorResume(error -> {
                    log.error("获取支付卡片失败", error);
                    return Mono.just(ApiResponse.error("获取支付卡片失败: " + error.getMessage()));
                });
    }

    /**
     * 更新支付卡片
     */
    @PutMapping("/{id}")
    public Mono<ApiResponse<PaymentCard>> updateCard(@PathVariable Long id, @RequestBody PaymentCard card) {
        log.info("更新支付卡片: {}", id);
        card.setId(id);
        return paymentCardService.updateCard(card)
                .map(ApiResponse::success)
                .onErrorResume(error -> {
                    log.error("更新支付卡片失败: {}", id, error);
                    return Mono.just(ApiResponse.error("更新支付卡片失败: " + error.getMessage()));
                });
    }

    /**
     * 删除支付卡片
     */
    @DeleteMapping("/{id}")
    public Mono<ApiResponse<String>> deleteCard(@PathVariable Long id) {
        log.info("删除支付卡片: {}", id);
        return paymentCardService.deleteById(id)
                .then(Mono.just(ApiResponse.<String>success("删除成功")))
                .onErrorResume(error -> {
                    log.error("删除支付卡片失败: {}", id, error);
                    return Mono.just(ApiResponse.error("删除支付卡片失败: " + error.getMessage()));
                });
    }

    /**
     * 批量删除支付卡片
     */
    @DeleteMapping("/batch")
    public Mono<ApiResponse<String>> deleteCards(@RequestBody String[] ids) {
        log.info("批量删除支付卡片: {}", String.join(",", ids));
        return paymentCardService.deleteByIds(ids)
                .then(Mono.just(ApiResponse.<String>success("批量删除成功")))
                .onErrorResume(error -> {
                    log.error("批量删除支付卡片失败", error);
                    return Mono.just(ApiResponse.error("批量删除支付卡片失败: " + error.getMessage()));
                });
    }

    /**
     * 根据卡号查找支付卡片
     */
    @GetMapping("/by-card-number/{cardNumber}")
    public Mono<ApiResponse<PaymentCard>> getCardByNumber(@PathVariable String cardNumber) {
        log.info("根据卡号查找支付卡片: {}", cardNumber);
        return paymentCardService.findByCardNumber(cardNumber)
                .map(ApiResponse::success)
                .switchIfEmpty(Mono.just(ApiResponse.notFound("支付卡片不存在")))
                .onErrorResume(error -> {
                    log.error("根据卡号查找支付卡片失败: {}", cardNumber, error);
                    return Mono.just(ApiResponse.error("查找支付卡片失败: " + error.getMessage()));
                });
    }
}
