package common.service.payment.base;

import core.service.CoreService;
import domain.entity.OtpVerification;
import domain.repository.OtpVerificationRepository;
import common.service.cache.TransactionalCacheManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import reactor.core.publisher.Mono;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.Random;

/**
 * 验证服务基类
 */
public abstract class BaseVerificationService {
    
    protected final Logger logger = LoggerFactory.getLogger(this.getClass());
    
    @Autowired
    protected OtpVerificationRepository otpVerificationRepository;

    @Autowired
    protected TransactionalCacheManager transactionalCacheManager;

    @Autowired
    protected CoreService coreService;
    
    // Redis键前缀
    protected static final String REDIS_OTP_PREFIX = "cache:otp:code:";
    protected static final String REDIS_OTP_INFO_PREFIX = "cache:otp:info:";
    

    
    /**
     * 构建OTP键
     */
    protected String buildOtpKey(String identifier, String method, String cardId) {
        return String.format("%s:%s:%s", identifier, method, cardId);
    }
    
    /**
     * 构建OTP信息映射
     */
    protected Map<String, Object> buildOtpInfoMap(OtpVerification otp) {
        Map<String, Object> info = new HashMap<>();
        info.put("id", otp.getId());
        info.put("code", otp.getOtpCode());
        info.put("method", otp.getMethod());
        info.put("identifier", otp.getIdentifier());
        info.put("cardId", otp.getCardId());
        info.put("attempts", otp.getAttempts());
        info.put("maxAttempts", otp.getMaxAttempts());
        info.put("createdAt", otp.getCreatedAt().toString());
        info.put("expiresAt", otp.getExpiresAt().toString());
        info.put("used", otp.isUsed());
        return info;
    }
    
    /**
     * 检查验证方式是否启用
     */
    protected boolean isMethodEnabled(String method) {
        return coreService.isFeatureEnabled(method + "_verification");
    }
    
    /**
     * 保存OTP到数据库和缓存 - 响应式版本
     */
    protected Mono<OtpVerification> saveOtpWithCache(OtpVerification otp) {
        return otpVerificationRepository.save(otp)
                .flatMap(savedOtp -> {
                    // 缓存到Redis
                    String otpKey = buildOtpKey(otp.getIdentifier(), otp.getMethod(), otp.getCardId());
                    Map<String, Object> otpInfo = buildOtpInfoMap(savedOtp);

                    return transactionalCacheManager.saveHashWithCache(
                            () -> Mono.just(savedOtp),
                            REDIS_OTP_INFO_PREFIX + otpKey,
                            otpInfo,
                            Duration.ofMinutes(10) // 使用默认值
                    );
                });
    }
    
    /**
     * 验证参数
     */
    protected void validateOtpParameters(String identifier, String method, String cardId) {
        core.util.ValidationUtil.validateOtpParameters(identifier, method, cardId);

        if (!isMethodEnabled(method)) {
            throw new IllegalArgumentException("验证方式未启用: " + method);
        }
    }
}
