/**
 * BakaOTP 3DS API客户端
 * 用于3D验证页面直接与后端API通信
 * 
 * @package BakaOTP
 * @version 1.0.0
 */

class BakaOTPApiClient {
    constructor(config = {}) {
        // 使用相对路径，通过3DS服务代理转发
        this.config = {
            apiBaseUrl: config.apiBaseUrl || '/api',
            timeout: config.timeout || 30000,
            debug: config.debug || false,
            ...config
        };
        
        // 从URL参数获取交易信息
        this.urlParams = new URLSearchParams(window.location.search);
        this.transactionId = this.urlParams.get('transaction_id') || this.urlParams.get('id');
        this.cardId = this.urlParams.get('card_id');
        this.amount = this.urlParams.get('amount');
        this.currency = this.urlParams.get('currency') || 'USD';
        
        this.log('BakaOTP API客户端初始化', {
            transactionId: this.transactionId,
            cardId: this.cardId,
            amount: this.amount
        });
    }

    /**
     * 日志输出
     */
    log(message, data = null) {
        if (this.config.debug) {
            console.log(`[BakaOTP API] ${message}`, data || '');
        }
    }
    
    /**
     * 错误日志
     */
    error(message, error = null) {
        console.error(`[BakaOTP API Error] ${message}`, error || '');
    }
    
    /**
     * 发送HTTP请求
     */
    async request(endpoint, options = {}) {
        const url = `${this.config.apiBaseUrl}${endpoint}`;
        const defaultOptions = {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            timeout: this.config.timeout
        };
        
        const requestOptions = { ...defaultOptions, ...options };
        
        this.log(`发送请求: ${requestOptions.method} ${url}`, requestOptions.body);
        
        try {
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), this.config.timeout);
            
            const response = await fetch(url, {
                ...requestOptions,
                signal: controller.signal
            });
            
            clearTimeout(timeoutId);
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const result = await response.json();
            this.log(`请求成功: ${url}`, result);
            
            return result;
        } catch (error) {
            this.error(`请求失败: ${url}`, error);
            throw error;
        }
    }
    
    /**
     * 提交3D验证码
     */
    async submitVerificationCode(verificationCode, verificationType = 'SMS') {
        if (!this.transactionId) {
            throw new Error('缺少交易ID');
        }
        
        if (!verificationCode) {
            throw new Error('验证码不能为空');
        }
        
        const payload = {
            transaction_id: this.transactionId,
            verification_code: verificationCode,
            verification_type: verificationType,
            timestamp: Date.now()
        };
        
        return await this.request('/secure-payment/verify', {
            method: 'POST',
            body: JSON.stringify(payload)
        });
    }
    
    /**
     * 重新发送验证码
     */
    async resendVerificationCode() {
        if (!this.transactionId) {
            throw new Error('缺少交易ID');
        }
        
        const payload = {
            transaction_id: this.transactionId,
            timestamp: Date.now()
        };
        
        return await this.request('/secure-payment/resend', {
            method: 'POST',
            body: JSON.stringify(payload)
        });
    }
    
    /**
     * 获取验证状态
     */
    async getVerificationStatus() {
        if (!this.transactionId) {
            throw new Error('缺少交易ID');
        }
        
        return await this.request(`/payment-transactions/${this.transactionId}/verification-status`);
    }
    
    /**
     * 验证支付卡信息
     */
    async validateCardInfo(cardInfo) {
        return await this.request('/payment-cards/verification/validate-card', {
            method: 'POST',
            body: JSON.stringify(cardInfo)
        });
    }
    
    /**
     * 获取支持的验证方式
     */
    async getSupportedVerificationMethods() {
        return await this.request('/payment-cards/verification/supported-methods');
    }
    
    /**
     * 启动验证流程
     */
    async startVerification(cardId, verificationType) {
        const payload = {
            cardId: cardId || this.cardId,
            verificationType: verificationType,
            transactionId: this.transactionId
        };
        
        return await this.request('/payment-cards/verification/start', {
            method: 'POST',
            body: JSON.stringify(payload)
        });
    }
    
    /**
     * 轮询验证状态
     */
    startStatusPolling(callback, interval = 3000) {
        const pollStatus = async () => {
            try {
                const result = await this.getVerificationStatus();
                if (callback) {
                    callback(result);
                }
                
                // 如果验证完成，停止轮询
                if (result.data && (result.data.status === 'verified' || result.data.status === 'rejected')) {
                    return;
                }
                
                // 继续轮询
                setTimeout(pollStatus, interval);
            } catch (error) {
                this.error('状态轮询失败', error);
                // 出错时也继续轮询，但间隔更长
                setTimeout(pollStatus, interval * 2);
            }
        };
        
        // 开始轮询
        setTimeout(pollStatus, interval);
    }
}

// 全局实例
window.BakaOTPApiClient = BakaOTPApiClient;
