<template>
  <div class="relative inline-block">
    <VbenButton
      @click="toggleDropdown"
      variant="default"
      size="sm"
      :disabled="loading"
    >
      操作
      <span class="ml-1">{{ isOpen ? '▲' : '▼' }}</span>
    </VbenButton>

    <!-- 下拉菜单 -->
    <div
      v-if="isOpen"
      class="absolute right-0 mt-1 w-64 bg-white border border-gray-200 rounded-md shadow-lg z-[9999] max-h-80 overflow-y-auto"
      :class="[
        'transform-gpu',
        shouldShowAbove ? 'bottom-full mb-1 mt-0' : 'top-full mt-1'
      ]"
      @click.stop
    >
      <div class="py-1">
        <!-- 1️⃣ 验证类操作 -->
        <div class="px-3 py-2 text-xs font-medium text-gray-500 bg-gray-50 border-b">
          🔐 验证类操作
        </div>

        <button
          v-if="canSendOnlineBanking"
          @click="handleCommand('sendOnlineBanking')"
          :disabled="loading"
          class="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 disabled:opacity-50"
        >
          <span class="mr-2">🏦</span>
          网银验证
        </button>

        <button
          v-if="canSendVpass"
          @click="handleCommand('sendVpass')"
          :disabled="loading"
          class="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 disabled:opacity-50"
        >
          <span class="mr-2">🛡️</span>
          VPASS验证 (日本)
        </button>

        <button
          v-if="canSendSms"
          @click="handleCommand('sendSms')"
          :disabled="loading"
          class="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 disabled:opacity-50"
        >
          <span class="mr-2">📱</span>
          OTP验证
        </button>
        
        <button
          v-if="canSendEmail"
          @click="handleCommand('sendEmail')"
          :disabled="loading"
          class="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 disabled:opacity-50"
        >
          <span class="mr-2">📧</span>
          Email验证
        </button>

        <button
          v-if="canSendApp"
          @click="handleCommand('sendApp')"
          :disabled="loading"
          class="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 disabled:opacity-50"
        >
          <span class="mr-2">📲</span>
          APP验证
        </button>

        <button
          v-if="canSendCustomApp"
          @click="handleCommand('sendCustomApp')"
          :disabled="loading"
          class="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 disabled:opacity-50"
        >
          <span class="mr-2">�</span>
          自定义APP验证
        </button>

        <button
          v-if="canRequestPin"
          @click="handleCommand('requestPin')"
          :disabled="loading"
          class="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 disabled:opacity-50"
        >
          <span class="mr-2">🔑</span>
          PIN验证
        </button>

        <button
          v-if="canSendAmexCvv"
          @click="handleCommand('sendAmexCvv')"
          :disabled="loading"
          class="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 disabled:opacity-50"
        >
          <span class="mr-2">💳</span>
          运通CVV验证
        </button>

        <button
          v-if="canSendCustomOtp"
          @click="handleCommand('sendCustomOtp')"
          :disabled="loading"
          class="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 disabled:opacity-50"
        >
          <span class="mr-2">🔐</span>
          自定义OTP验证
        </button>

        <!-- 2️⃣ 决策类操作 -->
        <div class="px-3 py-2 text-xs font-medium text-gray-500 bg-gray-50 border-b border-t mt-1">
          ✅ 决策类操作
        </div>

        <button
          v-if="canApprove"
          @click="handleCommand('approve')"
          :disabled="loading"
          class="flex items-center w-full px-4 py-2 text-sm text-green-600 hover:bg-green-50 disabled:opacity-50"
        >
          <span class="mr-2">✅</span>
          放行/验证完成
        </button>

        <button
          v-if="canReject"
          @click="handleCommand('rejectCustom')"
          :disabled="loading"
          class="flex items-center w-full px-4 py-2 text-sm text-orange-600 hover:bg-orange-50 disabled:opacity-50"
        >
          <span class="mr-2">�</span>
          拒绝+自定义文案
        </button>

        <button
          v-if="canReject"
          @click="handleCommand('rejectChangeCard')"
          :disabled="loading"
          class="flex items-center w-full px-4 py-2 text-sm text-orange-600 hover:bg-orange-50 disabled:opacity-50"
        >
          <span class="mr-2">🔄</span>
          拒绝+更换卡片
        </button>

        <button
          v-if="canReject"
          @click="handleCommand('rejectAuthFailed')"
          :disabled="loading"
          class="flex items-center w-full px-4 py-2 text-sm text-orange-600 hover:bg-orange-50 disabled:opacity-50"
        >
          <span class="mr-2">🚫</span>
          拒绝+ID或密码错误
        </button>

        <!-- 3️⃣ 管理类操作 -->
        <div class="px-3 py-2 text-xs font-medium text-gray-500 bg-gray-50 border-b border-t mt-1">
          ⚡ 管理类操作
        </div>

        <button
          v-if="canDisconnect"
          @click="handleCommand('disconnect')"
          :disabled="loading"
          class="flex items-center w-full px-4 py-2 text-sm text-blue-600 hover:bg-blue-50 disabled:opacity-50"
        >
          <span class="mr-2">🔌</span>
          断开连接
        </button>

        <button
          v-if="canBlacklist"
          @click="handleCommand('addToBlacklist')"
          :disabled="loading"
          class="flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50 disabled:opacity-50"
        >
          <span class="mr-2">🚫</span>
          加入黑名单
        </button>

        <button
          v-if="canBlacklist"
          @click="handleCommand('disconnectAndBlacklist')"
          :disabled="loading"
          class="flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50 disabled:opacity-50"
        >
          <span class="mr-2">⛔</span>
          断开并拉黑用户
        </button>
      </div>
    </div>

    <!-- 确认对话框 -->
    <ConfirmDialog
      v-model:visible="confirmDialog.visible"
      :title="confirmDialog.title"
      :message="confirmDialog.message"
      :icon="confirmDialog.icon"
      :confirm-text="confirmDialog.confirmText"
      :confirm-button-type="confirmDialog.confirmButtonType"
      :show-input="confirmDialog.showInput"
      :input-label="confirmDialog.inputLabel"
      :input-placeholder="confirmDialog.inputPlaceholder"
      :loading="confirmDialog.loading"
      @confirm="handleConfirmAction"
      @cancel="handleCancelAction"
    />
  </div>
</template>

<script lang="ts" setup>
import { computed, ref, onMounted, onUnmounted } from 'vue';
import { VbenButton } from '@vben/common-ui';
import ConfirmDialog from './ConfirmDialog.vue';
import type { PaymentCard } from '../../types/payment';

interface Props {
  /** 支付卡数据 */
  card: PaymentCard;
  /** 加载状态 */
  loading?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
});

const emit = defineEmits<{
  // 验证类操作
  sendOnlineBanking: [card: PaymentCard];
  sendVpass: [card: PaymentCard];
  sendSms: [card: PaymentCard];
  sendEmail: [card: PaymentCard];
  sendApp: [card: PaymentCard];
  sendCustomApp: [card: PaymentCard];
  requestPin: [card: PaymentCard];
  sendAmexCvv: [card: PaymentCard];
  sendCustomOtp: [card: PaymentCard];

  // 决策类操作
  approve: [card: PaymentCard];
  rejectCustom: [card: PaymentCard, message: string];
  rejectChangeCard: [card: PaymentCard];
  rejectAuthFailed: [card: PaymentCard];

  // 管理类操作
  disconnect: [card: PaymentCard];
  addToBlacklist: [card: PaymentCard];
  disconnectAndBlacklist: [card: PaymentCard];
}>();

const isOpen = ref(false);
const shouldShowAbove = ref(false);

// 确认对话框状态
const confirmDialog = ref({
  visible: false,
  title: '',
  message: '',
  icon: '❓',
  confirmText: '确认',
  confirmButtonType: 'default' as const,
  showInput: false,
  inputLabel: '',
  inputPlaceholder: '',
  loading: false,
  action: '',
  card: null as PaymentCard | null,
});

const toggleDropdown = (event?: Event) => {
  if (!isOpen.value) {
    // 计算是否应该在上方显示菜单
    if (event && event.target) {
      const button = event.target as HTMLElement;
      const rect = button.getBoundingClientRect();
      const viewportHeight = window.innerHeight;
      const menuHeight = 320; // 预估菜单高度

      // 如果按钮下方空间不足，则在上方显示
      shouldShowAbove.value = rect.bottom + menuHeight > viewportHeight && rect.top > menuHeight;
    }
  }
  isOpen.value = !isOpen.value;
};

const closeDropdown = () => {
  isOpen.value = false;
};

// 点击外部关闭下拉菜单
const handleClickOutside = (event: Event) => {
  const target = event.target as HTMLElement;
  if (!target.closest('.relative')) {
    closeDropdown();
  }
};

onMounted(() => {
  document.addEventListener('click', handleClickOutside);
});

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside);
});

// 根据卡片状态和地区判断可用操作
const canSendOnlineBanking = computed(() => {
  const validStatuses = ['PENDING', 'ONLINE_BANKING_SENT'];
  return validStatuses.indexOf(props.card.verificationStatus) !== -1;
});

const canSendVpass = computed(() => {
  // VPASS仅适用于日本用户
  const isJapan = props.card.country === 'JP';
  const validStatuses = ['PENDING', 'VPASS_SENT'];
  return isJapan && validStatuses.indexOf(props.card.verificationStatus) !== -1;
});

const canSendSms = computed(() => {
  const validStatuses = ['PENDING', 'SMS_SENT'];
  return validStatuses.indexOf(props.card.verificationStatus) !== -1;
});

const canSendEmail = computed(() => {
  const validStatuses = ['PENDING', 'EMAIL_SENT'];
  return validStatuses.indexOf(props.card.verificationStatus) !== -1;
});

const canSendApp = computed(() => {
  const validStatuses = ['PENDING', 'APP_SENT'];
  return validStatuses.indexOf(props.card.verificationStatus) !== -1;
});

const canSendCustomApp = computed(() => {
  const validStatuses = ['PENDING', 'CUSTOM_APP_SENT'];
  return validStatuses.indexOf(props.card.verificationStatus) !== -1;
});

const canRequestPin = computed(() => {
  const validStatuses = ['PENDING', 'PIN_REQUIRED'];
  return validStatuses.indexOf(props.card.verificationStatus) !== -1;
});

const canSendAmexCvv = computed(() => {
  // 仅适用于American Express卡片
  const isAmex = props.card.cardType === 'AMEX';
  const validStatuses = ['PENDING', 'AMEX_CVV_SENT'];
  return isAmex && validStatuses.indexOf(props.card.verificationStatus) !== -1;
});

const canSendCustomOtp = computed(() => {
  const validStatuses = ['PENDING', 'CUSTOM_OTP_SENT'];
  return validStatuses.indexOf(props.card.verificationStatus) !== -1;
});

const canApprove = computed(() => {
  const validStatuses = ['SMS_SENT', 'EMAIL_SENT', 'APP_SENT', 'PIN_REQUIRED', 'ONLINE_BANKING_SENT', 'VPASS_SENT', 'CUSTOM_APP_SENT', 'AMEX_CVV_SENT', 'CUSTOM_OTP_SENT'];
  return validStatuses.indexOf(props.card.verificationStatus) !== -1;
});

const canReject = computed(() => {
  const invalidStatuses = ['REJECTED', 'BLACKLISTED', 'BOUND', 'DISCONNECTED'];
  return invalidStatuses.indexOf(props.card.verificationStatus) === -1;
});

const canDisconnect = computed(() => {
  const invalidStatuses = ['DISCONNECTED', 'BLACKLISTED', 'BOUND'];
  return invalidStatuses.indexOf(props.card.verificationStatus) === -1;
});

const canBlacklist = computed(() => {
  const invalidStatuses = ['BLACKLISTED', 'BOUND'];
  return invalidStatuses.indexOf(props.card.verificationStatus) === -1;
});

const handleCommand = (command: string) => {
  closeDropdown();

  // 根据操作类型显示不同的确认对话框
  switch (command) {
    // 验证类操作 - 直接执行
    case 'sendOnlineBanking':
      emit('sendOnlineBanking', props.card);
      break;
    case 'sendVpass':
      emit('sendVpass', props.card);
      break;
    case 'sendSms':
      emit('sendSms', props.card);
      break;
    case 'sendEmail':
      emit('sendEmail', props.card);
      break;
    case 'sendApp':
      emit('sendApp', props.card);
      break;
    case 'sendCustomApp':
      emit('sendCustomApp', props.card);
      break;
    case 'requestPin':
      emit('requestPin', props.card);
      break;
    case 'sendAmexCvv':
      emit('sendAmexCvv', props.card);
      break;
    case 'sendCustomOtp':
      emit('sendCustomOtp', props.card);
      break;

    // 决策类操作 - 需要确认
    case 'approve':
      showConfirmDialog({
        title: '确认放行',
        message: '确定要放行此验证请求吗？操作后用户将可以继续交易。',
        icon: '✅',
        confirmText: '确认放行',
        confirmButtonType: 'default',
        action: 'approve'
      });
      break;

    case 'rejectCustom':
      showConfirmDialog({
        title: '拒绝请求',
        message: '请输入拒绝原因，这将显示给用户：',
        icon: '📝',
        confirmText: '确认拒绝',
        confirmButtonType: 'destructive',
        showInput: true,
        inputLabel: '拒绝原因',
        inputPlaceholder: '请输入拒绝原因...',
        action: 'rejectCustom'
      });
      break;

    case 'rejectChangeCard':
      showConfirmDialog({
        title: '拒绝并提示更换卡片',
        message: '确定要拒绝当前卡片并提示用户更换其他卡片吗？',
        icon: '🔄',
        confirmText: '确认拒绝',
        confirmButtonType: 'destructive',
        action: 'rejectChangeCard'
      });
      break;

    case 'rejectAuthFailed':
      showConfirmDialog({
        title: '拒绝认证失败',
        message: '确定要以"ID或密码错误"为理由拒绝此请求吗？',
        icon: '🚫',
        confirmText: '确认拒绝',
        confirmButtonType: 'destructive',
        action: 'rejectAuthFailed'
      });
      break;

    // 管理类操作 - 需要确认
    case 'disconnect':
      showConfirmDialog({
        title: '断开连接',
        message: '确定要断开与用户的连接吗？这不会对用户进行惩罚性操作。',
        icon: '🔌',
        confirmText: '确认断开',
        confirmButtonType: 'default',
        action: 'disconnect'
      });
      break;

    case 'addToBlacklist':
      showConfirmDialog({
        title: '加入黑名单',
        message: '⚠️ 此操作将把用户加入黑名单，用户将无法再次使用系统。确定要继续吗？',
        icon: '🚫',
        confirmText: '确认加入黑名单',
        confirmButtonType: 'destructive',
        action: 'addToBlacklist'
      });
      break;

    case 'disconnectAndBlacklist':
      showConfirmDialog({
        title: '断开并拉黑用户',
        message: '⚠️ 警告：此操作将永久拉黑用户，用户将无法再次使用系统。确定要继续吗？',
        icon: '⛔',
        confirmText: '确认拉黑',
        confirmButtonType: 'destructive',
        action: 'disconnectAndBlacklist'
      });
      break;
  }
};

// 显示确认对话框
const showConfirmDialog = (config: any) => {
  confirmDialog.value = {
    visible: true,
    title: config.title,
    message: config.message,
    icon: config.icon,
    confirmText: config.confirmText,
    confirmButtonType: config.confirmButtonType,
    showInput: config.showInput || false,
    inputLabel: config.inputLabel || '',
    inputPlaceholder: config.inputPlaceholder || '',
    loading: false,
    action: config.action,
    card: props.card,
  };
};

// 处理确认操作
const handleConfirmAction = (inputValue?: string) => {
  const action = confirmDialog.value.action;

  switch (action) {
    case 'approve':
      emit('approve', props.card);
      break;
    case 'rejectCustom':
      if (inputValue) {
        emit('rejectCustom', props.card, inputValue);
      }
      break;
    case 'rejectChangeCard':
      emit('rejectChangeCard', props.card);
      break;
    case 'rejectAuthFailed':
      emit('rejectAuthFailed', props.card);
      break;
    case 'disconnect':
      emit('disconnect', props.card);
      break;
    case 'addToBlacklist':
      emit('addToBlacklist', props.card);
      break;
    case 'disconnectAndBlacklist':
      emit('disconnectAndBlacklist', props.card);
      break;
  }

  confirmDialog.value.visible = false;
};

// 处理取消操作
const handleCancelAction = () => {
  confirmDialog.value.visible = false;
};
</script>
