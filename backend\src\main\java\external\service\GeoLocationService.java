package external.service;

import external.config.ExternalApiConfig;
import external.exception.ExternalApiException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientResponseException;
import org.springframework.core.ParameterizedTypeReference;
import reactor.core.publisher.Mono;

import java.time.Duration;

import java.util.HashMap;
import java.util.Map;
import java.util.regex.Pattern;

/**
 * IP地理位置查询服务
 * 根据IP地址查询地理位置和风险信息
 */
@Service
public class GeoLocationService {

    private static final Logger logger = LoggerFactory.getLogger(GeoLocationService.class);

    // IP地址正则表达式
    private static final Pattern IP_PATTERN = Pattern.compile(
        "^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$"
    );

    @Autowired
    private ExternalApiConfig externalApiConfig;

    @Autowired
    @Qualifier("externalApiWebClient")
    private WebClient webClient;

    /**
     * 根据IP地址查询地理位置信息（响应式）
     * 修复：改为响应式实现，返回Mono<Map<String, Object>>
     *
     * @param ipAddress IP地址
     * @return 地理位置信息的Mono
     */
    @Cacheable(value = "geoLocation", key = "#ipAddress", condition = "#ipAddress != null")
    public Mono<Map<String, Object>> getLocationInfo(String ipAddress) {
        if (!externalApiConfig.getGeoLocation().isEnabled()) {
            logger.debug("地理位置查询服务已禁用，返回默认信息");
            return Mono.just(getDefaultLocationInfo());
        }

        if (!isValidIpAddress(ipAddress)) {
            logger.warn("无效的IP地址: {}", ipAddress);
            return Mono.just(getDefaultLocationInfo());
        }

        return performGeoLookup(ipAddress)
            .onErrorResume(Exception.class, e -> {
                logger.error("地理位置查询失败: ip={}, error={}", ipAddress, e.getMessage());
                return Mono.just(getDefaultLocationInfo());
            });
    }

    /**
     * 执行地理位置查询（响应式）
     */
    private Mono<Map<String, Object>> performGeoLookup(String ipAddress) {
        ExternalApiConfig.GeoLocationConfig config = externalApiConfig.getGeoLocation();

        String provider = config.getProvider();
        switch (provider.toLowerCase()) {
            case "ip-api.io":
                return lookupFromIpApiIo(ipAddress, config);
            case "ip.sb":
                return lookupFromIpSb(ipAddress, config);
            default:
                logger.warn("未知的地理位置查询提供商: {}", provider);
                return Mono.just(getDefaultLocationInfo());
        }
    }



    /**
     * 从ip-api.io查询IP地址（响应式）
     * API文档: https://ip-api.io/api/v1/ip/{ip}
     */
    private Mono<Map<String, Object>> lookupFromIpApiIo(String ipAddress, ExternalApiConfig.GeoLocationConfig config) {
        String url = config.getBaseUrl() + "/ip/" + ipAddress;

        logger.debug("调用ip-api.io API: {}", url);

        return webClient
            .get()
            .uri(url)
            .headers(httpHeaders -> {
                httpHeaders.setContentType(MediaType.APPLICATION_JSON);
                httpHeaders.set("User-Agent", "BakaOTP-System/1.0");
            })
            .retrieve()
            .bodyToMono(new ParameterizedTypeReference<Map<String, Object>>() {})
            .map(this::convertIpApiIoResponse)
            .switchIfEmpty(Mono.error(new ExternalApiException("ip-api.io API返回空响应")))
            .onErrorResume(WebClientResponseException.class, e -> {
                logger.error("ip-api.io API调用失败: status={}, message={}", e.getStatusCode(), e.getMessage());
                return Mono.just(getDefaultLocationInfo());
            })
            .onErrorResume(Exception.class, e -> {
                logger.error("ip-api.io API连接异常: {}", e.getMessage());
                return Mono.just(getDefaultLocationInfo());
            })
            .timeout(Duration.ofSeconds(10));
    }

    /**
     * 从api.ip.sb查询IP地址（响应式）
     * API文档: https://api.ip.sb/geoip/{ip}
     */
    private Mono<Map<String, Object>> lookupFromIpSb(String ipAddress, ExternalApiConfig.GeoLocationConfig config) {
        String url = "https://api.ip.sb/geoip/" + ipAddress;

        logger.debug("调用api.ip.sb API: {}", url);

        return webClient
            .get()
            .uri(url)
            .headers(httpHeaders -> {
                httpHeaders.setContentType(MediaType.APPLICATION_JSON);
                httpHeaders.set("User-Agent", "BakaOTP-System/1.0");
            })
            .retrieve()
            .bodyToMono(new ParameterizedTypeReference<Map<String, Object>>() {})
            .map(this::convertIpSbResponse)
            .switchIfEmpty(Mono.error(new ExternalApiException("api.ip.sb API返回空响应")))
            .onErrorResume(WebClientResponseException.class, e -> {
                logger.error("api.ip.sb API调用失败: status={}, message={}", e.getStatusCode(), e.getMessage());
                return Mono.just(getDefaultLocationInfo());
            })
            .onErrorResume(Exception.class, e -> {
                logger.error("api.ip.sb API连接异常: {}", e.getMessage());
                return Mono.just(getDefaultLocationInfo());
            })
            .timeout(Duration.ofSeconds(10));
    }



    /**
     * 转换ip-api.io响应为标准格式
     */
    private Map<String, Object> convertIpApiIoResponse(Map<String, Object> response) {
        Map<String, Object> info = new HashMap<>();

        // 基础信息
        info.put("ip", response.get("ip"));

        // 位置信息
        @SuppressWarnings("unchecked")
        Map<String, Object> location = (Map<String, Object>) response.get("location");
        if (location != null) {
            info.put("country", location.get("country"));
            info.put("countryCode", location.get("country_code"));
            info.put("city", location.get("city"));
            info.put("latitude", location.get("latitude"));
            info.put("longitude", location.get("longitude"));
            info.put("timeZone", location.get("timezone"));
        }

        // 安全信息
        @SuppressWarnings("unchecked")
        Map<String, Object> suspiciousFactors = (Map<String, Object>) response.get("suspicious_factors");
        if (suspiciousFactors != null) {
            info.put("isProxy", suspiciousFactors.get("is_proxy"));
            info.put("isVpn", suspiciousFactors.get("is_vpn"));
            info.put("isTor", suspiciousFactors.get("is_tor_node"));
            info.put("isHosting", suspiciousFactors.get("is_datacenter"));
            info.put("isThreat", suspiciousFactors.get("is_threat"));

            // 设置默认风险等级（风险评估由专门的服务处理）
            info.put("riskLevel", "LOW");
        } else {
            // 默认安全信息
            info.put("isProxy", false);
            info.put("isVpn", false);
            info.put("isTor", false);
            info.put("isHosting", false);
            info.put("isThreat", false);
            info.put("riskLevel", "LOW");
        }

        // 连接信息（ip-api.io不提供ISP信息，设置默认值）
        info.put("isp", "未知ISP");
        info.put("asn", "未知");
        info.put("connectionType", "未知");

        return info;
    }

    /**
     * 转换api.ip.sb响应为标准格式
     */
    private Map<String, Object> convertIpSbResponse(Map<String, Object> response) {
        Map<String, Object> info = new HashMap<>();

        // 基础信息
        info.put("ip", response.get("ip"));

        // 位置信息
        info.put("country", response.get("country"));
        info.put("countryCode", response.get("country_code"));
        info.put("region", response.get("region"));
        info.put("regionCode", response.get("region_code"));
        info.put("city", response.get("city"));
        info.put("latitude", response.get("latitude"));
        info.put("longitude", response.get("longitude"));

        // 连接信息
        info.put("isp", response.get("isp"));
        info.put("asn", response.get("asn"));
        info.put("organization", response.get("organization"));
        info.put("asnOrganization", response.get("asn_organization"));

        // 时区信息
        info.put("timeZone", response.get("timezone"));
        info.put("continentCode", response.get("continent_code"));
        info.put("offset", response.get("offset"));

        // 安全信息（api.ip.sb不提供详细安全信息，设置默认值）
        info.put("isProxy", false);
        info.put("isVpn", false);
        info.put("isTor", false);
        info.put("isHosting", false);
        info.put("isThreat", false);
        info.put("riskLevel", "LOW");

        return info;
    }





    /**
     * 获取默认地理位置信息
     */
    private Map<String, Object> getDefaultLocationInfo() {
        Map<String, Object> info = new HashMap<>();
        info.put("country", "未知");
        info.put("countryCode", "UNKNOWN");
        info.put("city", "未知");
        info.put("isp", "未知ISP");
        info.put("riskLevel", "MEDIUM");
        info.put("isProxy", false);
        info.put("isVpn", false);
        info.put("isTor", false);
        return info;
    }

    /**
     * 验证IP地址格式
     */
    private boolean isValidIpAddress(String ipAddress) {
        if (ipAddress == null || ipAddress.trim().isEmpty()) {
            return false;
        }
        return IP_PATTERN.matcher(ipAddress.trim()).matches();
    }



    /**
     * 检查地理位置查询服务是否可用
     */
    public boolean isServiceAvailable() {
        return externalApiConfig.getGeoLocation().isEnabled();
    }

    /**
     * 获取服务配置信息
     */
    public Map<String, Object> getServiceInfo() {
        ExternalApiConfig.GeoLocationConfig config = externalApiConfig.getGeoLocation();
        Map<String, Object> info = new HashMap<>();
        info.put("enabled", config.isEnabled());
        info.put("provider", config.getProvider());
        info.put("cacheEnabled", config.isCacheEnabled());
        info.put("timeout", config.getTimeout());
        return info;
    }
}
