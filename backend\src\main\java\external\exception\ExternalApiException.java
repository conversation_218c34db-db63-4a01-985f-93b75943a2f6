package external.exception;

/**
 * 外部API调用异常
 * 用于封装外部API调用过程中的各种异常情况
 */
public class ExternalApiException extends RuntimeException {

    private String apiProvider;
    private String errorCode;
    private int httpStatus;

    public ExternalApiException(String message) {
        super(message);
    }

    public ExternalApiException(String message, Throwable cause) {
        super(message, cause);
    }

    public ExternalApiException(String message, String apiProvider) {
        super(message);
        this.apiProvider = apiProvider;
    }

    public ExternalApiException(String message, String apiProvider, String errorCode) {
        super(message);
        this.apiProvider = apiProvider;
        this.errorCode = errorCode;
    }

    public ExternalApiException(String message, String apiProvider, String errorCode, int httpStatus) {
        super(message);
        this.apiProvider = apiProvider;
        this.errorCode = errorCode;
        this.httpStatus = httpStatus;
    }

    public String getApiProvider() {
        return apiProvider;
    }

    public void setApiProvider(String apiProvider) {
        this.apiProvider = apiProvider;
    }

    public String getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }

    public int getHttpStatus() {
        return httpStatus;
    }

    public void setHttpStatus(int httpStatus) {
        this.httpStatus = httpStatus;
    }
}
