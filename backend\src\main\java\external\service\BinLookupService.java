package external.service;

import external.client.BinLookupApiClient;
import core.common.ApiResponse;
import common.constants.CardConstants;
import common.constants.TemplateConstants;
import core.constants.AppConstants;
import external.dto.BinLookupResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import reactor.core.publisher.Mono;

/**
 * BIN码查询工具类
 * 提供银行卡BIN码查询的便捷方法和格式转换
 */
@Component
public class BinLookupService {

    private static final Logger logger = LoggerFactory.getLogger(BinLookupService.class);

    @Autowired
    private BinLookupApiClient binLookupApiClient;

    /**
     * 根据BIN码查询银行信息
     * 委托给BinLookupApiClient处理，并转换为Map格式
     *
     * @param bin 银行卡前6位数字
     * @return 银行信息
     */
    @Cacheable(value = "binLookup", key = "#bin", condition = "#bin != null && #bin.length() >= 6")
    public Mono<Map<String, Object>> lookupBankInfo(String bin) {
        if (bin == null || bin.length() < CardConstants.MIN_BIN_LENGTH) {
            logger.warn("无效的BIN码: {}", bin);
            return Mono.just(getDefaultBankInfo());
        }

        return binLookupApiClient.lookupBin(bin)
            .map(response -> {
                if (response.isSuccess() && response.getData() != null) {
                    return convertBinLookupResultToMap(response.getData());
                } else {
                    logger.warn("BIN查询失败: {}", response.getMessage());
                    return getDefaultBankInfo();
                }
            })
            .onErrorResume(e -> {
                logger.error("BIN查询异常: bin={}, error={}", bin, e.getMessage());
                return Mono.just(getDefaultBankInfo());
            });
    }

    /**
     * 转换BinLookupResult为Map格式
     * 统一的格式转换方法
     */
    private Map<String, Object> convertBinLookupResultToMap(BinLookupResult response) {
        Map<String, Object> info = new HashMap<>();

        // 卡片信息
        info.put("scheme", response.getScheme() != null ? response.getScheme().toUpperCase() : CardConstants.CARD_TYPE_UNKNOWN);
        info.put("cardType", response.getType() != null ? response.getType().toUpperCase() : CardConstants.CARD_TYPE_UNKNOWN);
        info.put("cardLevel", "CLASSIC"); // 默认卡等级

        // 国家信息
        if (response.getCountry() != null) {
            info.put("countryCode", response.getCountry().getAlpha2());
            info.put("countryName", response.getCountry().getName());
        } else {
            info.put("countryCode", "US");
            info.put("countryName", "United States");
        }

        // 银行信息
        if (response.getBank() != null) {
            info.put("bankName", response.getBank().getName());
            info.put("bankUrl", response.getBank().getUrl());
            info.put("bankPhone", response.getBank().getPhone());

            // 组合银行联系方式
            String contact = "";
            if (response.getBank().getUrl() != null) {
                contact += response.getBank().getUrl();
            }
            if (response.getBank().getPhone() != null) {
                if (!contact.isEmpty()) contact += " / ";
                contact += response.getBank().getPhone();
            }
            info.put("bankContact", contact.isEmpty() ? "-" : contact);
        } else {
            info.put("bankName", TemplateConstants.DEFAULT_MERCHANT_NAME);
            info.put("bankContact", "-");
        }

        return info;
    }

    /**
     * 获取默认银行信息
     */
    private Map<String, Object> getDefaultBankInfo() {
        Map<String, Object> info = new HashMap<>();
        info.put("scheme", CardConstants.CARD_TYPE_UNKNOWN);
        info.put("cardType", CardConstants.DEFAULT_CARD_TYPE);
        info.put("cardLevel", CardConstants.DEFAULT_CARD_LEVEL);
        info.put("countryCode", AppConstants.System.DEFAULT_COUNTRY_CODE);
        info.put("countryName", AppConstants.System.DEFAULT_COUNTRY_NAME);
        info.put("bankName", TemplateConstants.DEFAULT_MERCHANT_NAME);
        info.put("bankContact", AppConstants.System.DEFAULT_CONTACT);
        return info;
    }

    /**
     * 根据卡号获取银行信息（便捷方法）
     */
    public Mono<Map<String, Object>> getBankInfoByCardNumber(String cardNumber) {
        if (cardNumber == null || cardNumber.length() < CardConstants.MIN_BIN_LENGTH) {
            return Mono.just(getDefaultBankInfo());
        }

        // 移除空格和特殊字符
        String cleanCardNumber = cardNumber.replaceAll("[^0-9]", "");
        if (cleanCardNumber.length() < CardConstants.MIN_BIN_LENGTH) {
            return Mono.just(getDefaultBankInfo());
        }

        return lookupBankInfo(cleanCardNumber);
    }

    /**
     * 检查BIN查询服务是否可用
     * 委托给BinLookupApiClient处理
     */
    public Mono<Boolean> isServiceAvailable() {
        return binLookupApiClient.isServiceAvailable();
    }

    /**
     * 获取服务配置信息
     * 委托给BinLookupApiClient处理
     */
    public Mono<Map<String, Object>> getServiceInfo() {
        return binLookupApiClient.getServiceInfo();
    }
}
