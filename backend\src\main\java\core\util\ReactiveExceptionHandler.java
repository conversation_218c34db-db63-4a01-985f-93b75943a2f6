package core.util;

import core.common.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import reactor.core.publisher.Mono;

import java.util.function.Function;

/**
 * 响应式异常处理工具类
 * 统一处理Controller中的异常处理逻辑，消除重复代码
 */
@Slf4j
public class ReactiveExceptionHandler {

    /**
     * 处理异常并返回标准错误响应
     * 
     * @param operation 操作名称，用于日志记录
     * @return 异常处理函数
     */
    public static <T> Function<Throwable, Mono<ResponseEntity<ApiResponse<T>>>> handleError(String operation) {
        return throwable -> {
            log.error("{}失败", operation, throwable);
            String errorMessage = operation + "失败: " + throwable.getMessage();
            return Mono.just(ResponseEntity.status(500)
                .body(ApiResponse.internalError(errorMessage)));
        };
    }

    /**
     * 处理异常并返回自定义错误消息
     * 
     * @param customMessage 自定义错误消息
     * @return 异常处理函数
     */
    public static <T> Function<Throwable, Mono<ResponseEntity<ApiResponse<T>>>> handleErrorWithMessage(String customMessage) {
        return throwable -> {
            log.error("操作失败: {}", customMessage, throwable);
            return Mono.just(ResponseEntity.status(500)
                .body(ApiResponse.internalError(customMessage)));
        };
    }

    /**
     * 处理异常并返回标准错误响应（带详细日志）
     * 
     * @param operation 操作名称
     * @param context 上下文信息，用于更详细的日志
     * @return 异常处理函数
     */
    public static <T> Function<Throwable, Mono<ResponseEntity<ApiResponse<T>>>> handleErrorWithContext(String operation, String context) {
        return throwable -> {
            log.error("{}失败 - 上下文: {}", operation, context, throwable);
            String errorMessage = operation + "失败: " + throwable.getMessage();
            return Mono.just(ResponseEntity.status(500)
                .body(ApiResponse.internalError(errorMessage)));
        };
    }

    /**
     * 处理特定异常类型
     * 
     * @param operation 操作名称
     * @param exceptionClass 异常类型
     * @param customHandler 自定义异常处理逻辑
     * @return 异常处理函数
     */
    public static <T, E extends Throwable> Function<Throwable, Mono<ResponseEntity<ApiResponse<T>>>> handleSpecificError(
            String operation, 
            Class<E> exceptionClass, 
            Function<E, ResponseEntity<ApiResponse<T>>> customHandler) {
        return throwable -> {
            if (exceptionClass.isInstance(throwable)) {
                log.warn("{}发生预期异常: {}", operation, throwable.getMessage());
                return Mono.just(customHandler.apply(exceptionClass.cast(throwable)));
            } else {
                log.error("{}发生未预期异常", operation, throwable);
                String errorMessage = operation + "失败: " + throwable.getMessage();
                return Mono.just(ResponseEntity.status(500)
                    .body(ApiResponse.internalError(errorMessage)));
            }
        };
    }

    /**
     * 处理业务异常（400错误）
     * 
     * @param operation 操作名称
     * @return 异常处理函数
     */
    public static <T> Function<Throwable, Mono<ResponseEntity<ApiResponse<T>>>> handleBusinessError(String operation) {
        return throwable -> {
            log.warn("{}业务异常: {}", operation, throwable.getMessage());
            return Mono.just(ResponseEntity.status(400)
                .body(ApiResponse.error(throwable.getMessage())));
        };
    }

    /**
     * 处理资源不存在异常（404错误）
     * 
     * @param resourceName 资源名称
     * @return 异常处理函数
     */
    public static <T> Function<Throwable, Mono<ResponseEntity<ApiResponse<T>>>> handleNotFoundError(String resourceName) {
        return throwable -> {
            log.warn("{}不存在: {}", resourceName, throwable.getMessage());
            String errorMessage = resourceName + "不存在";
            return Mono.just(ResponseEntity.status(404)
                .body(ApiResponse.notFound(errorMessage)));
        };
    }

    /**
     * 处理权限异常（403错误）
     * 
     * @param operation 操作名称
     * @return 异常处理函数
     */
    public static <T> Function<Throwable, Mono<ResponseEntity<ApiResponse<T>>>> handleForbiddenError(String operation) {
        return throwable -> {
            log.warn("{}权限不足: {}", operation, throwable.getMessage());
            String errorMessage = "权限不足，无法执行" + operation;
            return Mono.just(ResponseEntity.status(403)
                .body(ApiResponse.forbidden(errorMessage)));
        };
    }

    /**
     * 通用异常处理，根据异常类型自动选择合适的处理方式
     * 
     * @param operation 操作名称
     * @return 异常处理函数
     */
    public static <T> Function<Throwable, Mono<ResponseEntity<ApiResponse<T>>>> handleGenericError(String operation) {
        return throwable -> {
            String className = throwable.getClass().getSimpleName();
            
            // 根据异常类型选择不同的处理方式
            if (throwable instanceof IllegalArgumentException ||
                throwable instanceof IllegalStateException) {
                return ReactiveExceptionHandler.<T>handleBusinessError(operation).apply(throwable);
            } else if (throwable instanceof RuntimeException &&
                       throwable.getMessage() != null &&
                       throwable.getMessage().contains("不存在")) {
                return ReactiveExceptionHandler.<T>handleNotFoundError(operation).apply(throwable);
            } else {
                return ReactiveExceptionHandler.<T>handleError(operation).apply(throwable);
            }
        };
    }
}
