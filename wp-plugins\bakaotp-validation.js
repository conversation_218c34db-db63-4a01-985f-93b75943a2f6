/**
 * BakaOTP 前端验证服务
 * 统一的客户端验证逻辑，避免重复代码
 * 
 * @package BakaOTP
 * @version 1.0.0
 */

class BakaOTPValidation {
    constructor() {
        // 卡片类型检测规则
        this.cardTypeRules = {
            'visa': /^4[0-9]{12}(?:[0-9]{3})?$/,
            'mastercard': /^5[1-5][0-9]{14}$/,
            'amex': /^3[47][0-9]{13}$/,
            'discover': /^6(?:011|5[0-9]{2})[0-9]{12}$/,
            'jcb': /^(?:2131|1800|35\d{3})\d{11}$/,
            'diners': /^3[0689][0-9]{11}$/
        };
    }

    /**
     * Luhn算法验证卡号
     */
    luhnCheck(cardNumber) {
        if (!cardNumber || typeof cardNumber !== 'string') {
            return false;
        }

        const cleanNumber = cardNumber.replace(/\s+/g, '');
        
        if (!/^\d+$/.test(cleanNumber)) {
            return false;
        }

        const checkDigit = parseInt(cleanNumber.slice(-1));
        const digits = cleanNumber.slice(0, -1).split('').reverse().map(Number);
        let sum = 0;

        for (let i = 0; i < digits.length; i++) {
            let num = digits[i];
            if (i % 2 === 0) {
                num *= 2;
                if (num > 9) num -= 9;
            }
            sum += num;
        }

        const calculatedCheck = (10 - (sum % 10)) % 10;
        return checkDigit === calculatedCheck;
    }

    /**
     * 检测卡片类型
     */
    detectCardType(cardNumber) {
        if (!cardNumber) {
            return 'unknown';
        }

        const cleanNumber = cardNumber.replace(/\s+/g, '');
        
        for (const [type, pattern] of Object.entries(this.cardTypeRules)) {
            if (pattern.test(cleanNumber)) {
                return type;
            }
        }
        
        return 'unknown';
    }

    /**
     * 验证卡号
     */
    validateCardNumber(cardNumber) {
        const result = {
            valid: false,
            cardType: 'unknown',
            errors: []
        };

        if (!cardNumber || cardNumber.trim() === '') {
            result.errors.push('卡号不能为空');
            return result;
        }

        const cleanNumber = cardNumber.replace(/\s+/g, '');
        
        // 检查长度
        if (cleanNumber.length < 13 || cleanNumber.length > 19) {
            result.errors.push('卡号长度无效');
            return result;
        }

        // 检查是否只包含数字
        if (!/^\d+$/.test(cleanNumber)) {
            result.errors.push('卡号只能包含数字');
            return result;
        }

        // Luhn算法验证
        if (!this.luhnCheck(cleanNumber)) {
            result.errors.push('卡号校验失败');
            return result;
        }

        // 检测卡片类型
        result.cardType = this.detectCardType(cleanNumber);
        result.valid = true;
        
        return result;
    }

    /**
     * 验证有效期
     */
    validateExpiryDate(dateStr) {
        const result = {
            valid: false,
            errors: []
        };

        if (!dateStr || dateStr.trim() === '') {
            result.errors.push('有效期不能为空');
            return result;
        }

        // 支持MM/YY和MMYY格式
        let month, year;
        
        if (dateStr.includes('/')) {
            const parts = dateStr.split('/');
            if (parts.length !== 2) {
                result.errors.push('有效期格式无效');
                return result;
            }
            month = parseInt(parts[0]);
            year = parseInt('20' + parts[1]);
        } else if (dateStr.length === 4) {
            month = parseInt(dateStr.slice(0, 2));
            year = parseInt('20' + dateStr.slice(2));
        } else {
            result.errors.push('有效期格式无效');
            return result;
        }

        // 验证月份
        if (isNaN(month) || month < 1 || month > 12) {
            result.errors.push('月份无效');
            return result;
        }

        // 验证年份
        if (isNaN(year)) {
            result.errors.push('年份无效');
            return result;
        }

        // 检查是否过期
        const now = new Date();
        const currentYear = now.getFullYear();
        const currentMonth = now.getMonth() + 1;

        if (year < currentYear || (year === currentYear && month < currentMonth)) {
            result.errors.push('卡片已过期');
            return result;
        }

        // 检查年份是否太远
        if (year > currentYear + 20) {
            result.errors.push('年份无效');
            return result;
        }

        result.valid = true;
        return result;
    }

    /**
     * 验证CVV
     */
    validateCvv(cvv, cardType = null) {
        const result = {
            valid: false,
            errors: []
        };

        if (!cvv || cvv.trim() === '') {
            result.errors.push('CVV不能为空');
            return result;
        }

        const cleanCvv = cvv.trim();

        // 检查是否只包含数字
        if (!/^\d+$/.test(cleanCvv)) {
            result.errors.push('CVV只能包含数字');
            return result;
        }

        // 根据卡片类型验证长度
        let expectedLength = 3; // 默认长度
        if (cardType === 'amex') {
            expectedLength = 4;
        }

        if (cleanCvv.length !== expectedLength) {
            result.errors.push(`CVV长度应为${expectedLength}位`);
            return result;
        }

        result.valid = true;
        return result;
    }

    /**
     * 验证邮箱
     */
    validateEmail(email) {
        const result = {
            valid: false,
            errors: []
        };

        if (!email || email.trim() === '') {
            result.errors.push('邮箱不能为空');
            return result;
        }

        const emailPattern = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
        
        if (!emailPattern.test(email.trim())) {
            result.errors.push('邮箱格式无效');
            return result;
        }

        result.valid = true;
        return result;
    }

    /**
     * 验证手机号
     */
    validatePhone(phone) {
        const result = {
            valid: false,
            errors: []
        };

        if (!phone || phone.trim() === '') {
            result.errors.push('手机号不能为空');
            return result;
        }

        const cleanPhone = phone.replace(/[\s\-\(\)]/g, '');
        const phonePattern = /^[+]?[1-9]\d{1,14}$/;
        
        if (!phonePattern.test(cleanPhone)) {
            result.errors.push('手机号格式无效');
            return result;
        }

        result.valid = true;
        return result;
    }

    /**
     * 批量验证支付卡信息
     */
    validatePaymentCard(cardData) {
        const result = {
            valid: true,
            errors: {},
            cardType: 'unknown'
        };

        // 验证卡号
        const cardResult = this.validateCardNumber(cardData.cardNumber);
        if (!cardResult.valid) {
            result.valid = false;
            result.errors.cardNumber = cardResult.errors;
        } else {
            result.cardType = cardResult.cardType;
        }

        // 验证有效期
        const dateResult = this.validateExpiryDate(cardData.expiryDate);
        if (!dateResult.valid) {
            result.valid = false;
            result.errors.expiryDate = dateResult.errors;
        }

        // 验证CVV
        const cvvResult = this.validateCvv(cardData.cvv, result.cardType);
        if (!cvvResult.valid) {
            result.valid = false;
            result.errors.cvv = cvvResult.errors;
        }

        // 验证持卡人姓名
        if (!cardData.holderName || cardData.holderName.trim() === '') {
            result.valid = false;
            result.errors.holderName = ['持卡人姓名不能为空'];
        }

        return result;
    }

    /**
     * 格式化卡号显示
     */
    formatCardNumber(cardNumber) {
        if (!cardNumber) return '';
        
        const cleanNumber = cardNumber.replace(/\s+/g, '');
        const cardType = this.detectCardType(cleanNumber);
        
        // 根据卡片类型格式化
        if (cardType === 'amex') {
            // AMEX: 4-6-5 格式
            return cleanNumber.replace(/(\d{4})(\d{6})(\d{5})/, '$1 $2 $3');
        } else {
            // 其他: 4-4-4-4 格式
            return cleanNumber.replace(/(\d{4})(?=\d)/g, '$1 ');
        }
    }

    /**
     * 格式化有效期显示
     */
    formatExpiryDate(dateStr) {
        if (!dateStr) return '';
        
        const cleanDate = dateStr.replace(/\D/g, '');
        if (cleanDate.length >= 2) {
            return cleanDate.slice(0, 2) + (cleanDate.length > 2 ? '/' + cleanDate.slice(2, 4) : '');
        }
        return cleanDate;
    }
}

// 全局实例
window.BakaOTPValidation = BakaOTPValidation;
