package domain.repository;

import domain.entity.PaymentTransaction;
import org.springframework.data.domain.Pageable;
import org.springframework.data.r2dbc.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 支付交易数据访问层 - 响应式版本
 */
@Repository
public interface PaymentTransactionRepository extends BaseRepository<PaymentTransaction, Long> {

    /**
     * 根据交易ID查找
     */
    Mono<PaymentTransaction> findByTransactionId(String transactionId);

    /**
     * 根据支付ID查找
     */
    Mono<PaymentTransaction> findByPaymentId(String paymentId);

    /**
     * 根据订单ID查找
     */
    Mono<PaymentTransaction> findByOrderId(String orderId);

    /**
     * 根据用户ID查找交易（管理员用户）
     */
    Flux<PaymentTransaction> findByUserId(Long userId);

    /**
     * 根据支付用户ID查找交易
     */
    Flux<PaymentTransaction> findByPaymentUserId(Long paymentUserId);

    /**
     * 根据支付卡ID查找交易
     */
    Flux<PaymentTransaction> findByCardId(Long cardId);

    /**
     * 根据状态查找交易
     */
    Flux<PaymentTransaction> findByStatus(PaymentTransaction.TransactionStatus status);

    /**
     * 根据状态分页查找交易
     */
    Flux<PaymentTransaction> findByStatus(PaymentTransaction.TransactionStatus status, Pageable pageable);

    /**
     * 根据金额范围查找交易
     */
    Flux<PaymentTransaction> findByAmountBetween(BigDecimal minAmount, BigDecimal maxAmount);

    /**
     * 根据创建时间范围查找交易
     */
    Flux<PaymentTransaction> findByCreatedAtBetween(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 根据多个条件查找交易
     */
    @Query("SELECT t FROM PaymentTransaction t WHERE " +
           "(:status IS NULL OR t.status = :status) AND " +
           "(:userId IS NULL OR t.userId = :userId) AND " +
           "(:cardId IS NULL OR t.cardId = :cardId) AND " +
           "(:minAmount IS NULL OR t.amount >= :minAmount) AND " +
           "(:maxAmount IS NULL OR t.amount <= :maxAmount) AND " +
           "(:startDate IS NULL OR t.createdAt >= :startDate) AND " +
           "(:endDate IS NULL OR t.createdAt <= :endDate)")
    Flux<PaymentTransaction> findByMultipleConditions(
            @Param("status") PaymentTransaction.TransactionStatus status,
            @Param("userId") Long userId,
            @Param("cardId") Long cardId,
            @Param("minAmount") BigDecimal minAmount,
            @Param("maxAmount") BigDecimal maxAmount,
            @Param("startDate") LocalDateTime startDate,
            @Param("endDate") LocalDateTime endDate,
            Pageable pageable);

    /**
     * 统计各状态交易数量
     */
    @Query("SELECT t.status, COUNT(t) FROM PaymentTransaction t GROUP BY t.status")
    Flux<Object[]> countByStatusGrouped();

    /**
     * 统计总交易金额
     */
    @Query("SELECT SUM(t.amount) FROM PaymentTransaction t WHERE t.status = :status")
    BigDecimal sumAmountByStatus(@Param("status") PaymentTransaction.TransactionStatus status);

    /**
     * 查找最近的交易
     */
    Flux<PaymentTransaction> findTop10ByOrderByCreatedAtDesc();

    /**
     * 查找指定时间之后的交易
     */
    Flux<PaymentTransaction> findByCreatedAtAfter(LocalDateTime since);

    /**
     * 按创建时间倒序查找所有交易
     */
    Flux<PaymentTransaction> findAllByOrderByCreatedAtDesc();

    /**
     * 按创建时间倒序分页查找所有交易
     */
    Flux<PaymentTransaction> findAllByOrderByCreatedAtDesc(Pageable pageable);

    /**
     * 删除指定时间之前的交易
     */
    @Query("DELETE FROM PaymentTransaction t WHERE t.createdAt < :before")
    void deleteOldTransactions(@Param("before") LocalDateTime before);

    /**
     * 查找失败的交易
     */
    Flux<PaymentTransaction> findByStatusAndCreatedAtAfter(
            PaymentTransaction.TransactionStatus status,
            LocalDateTime since);

    /**
     * 统计指定时间范围内的交易数量
     */
    @Query("SELECT COUNT(t) FROM PaymentTransaction t WHERE t.createdAt BETWEEN :startDate AND :endDate")
    long countByDateRange(@Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate);

    /**
     * 查找需要处理的待处理交易
     */
    @Query("SELECT t FROM PaymentTransaction t WHERE t.status = 'PENDING' AND t.createdAt < :expireTime")
    Flux<PaymentTransaction> findExpiredPendingTransactions(@Param("expireTime") LocalDateTime expireTime);
}
