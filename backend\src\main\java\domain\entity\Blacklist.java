package domain.entity;

import org.springframework.data.annotation.Id;
import org.springframework.data.relational.core.mapping.Column;
import org.springframework.data.relational.core.mapping.Table;

import java.time.LocalDateTime;

//统一黑名单实体，支持多种类型的黑名单项（IP、用户、卡号、邮箱）- 响应式版本
@Table("blacklist_items")
public class Blacklist extends BaseEntity {
    @Id
    private Long id;

    @Column("type")
    private BlacklistType type;

    @Column("value")
    private String value;

    @Column("reason")
    private String reason;

    @Column("description")
    private String description;

    @Column("created_by")
    private String createdBy;

    @Column("expires_at")
    private LocalDateTime expiresAt;

    @Column("status")
    private BlacklistStatus status = BlacklistStatus.ACTIVE;

    //黑名单类型枚举
    public enum BlacklistType {
        USER("用户"),
        CARD("卡号"),
        IP("IP地址"),
        EMAIL("邮箱");

        private final String description;

        BlacklistType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    //黑名单状态枚举
    public enum BlacklistStatus {
        ACTIVE("有效"),
        EXPIRED("已过期"),
        REMOVED("已移除");

        private final String description;

        BlacklistStatus(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    // 构造函数
    public Blacklist() {
        if (status == null) {
            status = BlacklistStatus.ACTIVE;
        }
    }

    // Getter和Setter方法
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public BlacklistType getType() {
        return type;
    }

    public void setType(BlacklistType type) {
        this.type = type;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }



    public LocalDateTime getExpiresAt() {
        return expiresAt;
    }

    public void setExpiresAt(LocalDateTime expiresAt) {
        this.expiresAt = expiresAt;
    }

    public BlacklistStatus getStatus() {
        return status;
    }

    public void setStatus(BlacklistStatus status) {
        this.status = status;
    }

    // 便捷方法
    public boolean isActive() {
        return status == BlacklistStatus.ACTIVE && (expiresAt == null || expiresAt.isAfter(LocalDateTime.now()));
    }

    public boolean isExpired() {
        return status == BlacklistStatus.EXPIRED || (expiresAt != null && expiresAt.isBefore(LocalDateTime.now()));
    }

    public void markAsRemoved() {
        this.status = BlacklistStatus.REMOVED;
    }

    public void markAsExpired() {
        this.status = BlacklistStatus.EXPIRED;
    }


}

