package domain.entity;

import org.springframework.data.annotation.Id;
import org.springframework.data.relational.core.mapping.Column;
import org.springframework.data.relational.core.mapping.Table;

import java.time.LocalDateTime;

/**
 * OTP验证实体类
 */
@Table("otp_verifications")
public class OtpVerification extends BaseEntity {
    @Id
    private Long id;

    @Column("otp_code")
    private String otpCode;

    @Column("email")
    private String email;

    @Column("expires_at")
    private LocalDateTime expiresAt;

    @Column("used")
    private boolean used;

    @Column("type")
    private OtpType type;

    @Column("related_id")
    private String relatedId;

    @Column("method")
    private String method;

    @Column("identifier")
    private String identifier;

    @Column("card_id")
    private String cardId;

    @Column("attempts")
    private int attempts = 0;

    @Column("max_attempts")
    private int maxAttempts = 3;

    @Column("verified_by")
    private String verifiedBy;

    public enum OtpType {
        PAYMENT_VERIFICATION,
        ORDER_VERIFICATION,
        ADMIN_APPROVAL,
        CARD_VERIFICATION
    }


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getOtpCode() {
        return otpCode;
    }

    public void setOtpCode(String otpCode) {
        this.otpCode = otpCode;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public LocalDateTime getExpiresAt() {
        return expiresAt;
    }

    public void setExpiresAt(LocalDateTime expiresAt) {
        this.expiresAt = expiresAt;
    }

    public boolean isUsed() {
        return used;
    }

    public void setUsed(boolean used) {
        this.used = used;
    }

    public OtpType getType() {
        return type;
    }

    public void setType(OtpType type) {
        this.type = type;
    }

    public String getRelatedId() {
        return relatedId;
    }

    public void setRelatedId(String relatedId) {
        this.relatedId = relatedId;
    }

    public String getMethod() {
        return method;
    }

    public void setMethod(String method) {
        this.method = method;
    }

    public String getIdentifier() {
        return identifier;
    }

    public void setIdentifier(String identifier) {
        this.identifier = identifier;
    }

    public String getCardId() {
        return cardId;
    }

    public void setCardId(String cardId) {
        this.cardId = cardId;
    }

    public int getAttempts() {
        return attempts;
    }

    public void setAttempts(int attempts) {
        this.attempts = attempts;
    }

    public int getMaxAttempts() {
        return maxAttempts;
    }

    public void setMaxAttempts(int maxAttempts) {
        this.maxAttempts = maxAttempts;
    }

    public String getVerifiedBy() {
        return verifiedBy;
    }

    public void setVerifiedBy(String verifiedBy) {
        this.verifiedBy = verifiedBy;
    }
}