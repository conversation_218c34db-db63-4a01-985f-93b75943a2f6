<?php
/**
 * BakaOTP Card Form Component
 * 
 * 可重用的信用卡输入表单组件
 * 
 * @package BakaOTP
 * @version 1.0.0
 */

// 防止直接访问
if (!defined('ABSPATH')) {
    exit;
}
?>


<!-- 安全提示 -->
<div class="bakaotp-security-notice">
    <p>All transactions are secure and encrypted.</p>
</div>

<div class="bakaotp-form-container">
    <!-- 三列布局 -->
    <div class="bakaotp-form-row-three">
        <!-- 卡号 -->
        <div class="bakaotp-form-group bakaotp-form-group-card">
            <label class="bakaotp-form-label">卡号</label>
            <div class="bakaotp-input-container" ref="cardNoDiv">
                <input
                    type="text"
                    class="bakaotp-form-input"
                    placeholder="4242 4242 4242 4242"
                    name="number"
                    maxlength="19"
                    autocomplete="cc-number"
                    @blur="changeCard('cardNo', $event.target.value)"
                    onkeyup="this.value = this.value.replace(/\s/g,'').replace(/[^\d]/g,'').replace(/(\d{4})(?=\d)/g,'$1 ');"
                    v-model="addressInfore.cardNo"
                />
                <div class="bakaotp-input-icon">
                    <!-- VISA图标 -->
                    <svg v-if="getCardType(addressInfore.cardNo) === 'visa'" class="bakaotp-card-icon" viewBox="0 0 40 24" width="32" height="20">
                        <path d="M8.5 16.5l1.5-9h2.4l-1.5 9H8.5zm7.9-9h-2.2c-.5 0-.9.3-1.1.7l-3.9 8.3h2.5l.5-1.4h3l.3 1.4h2.2l-1.3-9zm-2.8 5.9l1.2-3.3.7 3.3h-1.9zm7.5-5.9l-2.3 9h2.4l2.3-9h-2.4zm7.1 0c-.5 0-.9.3-1.1.7l-3.9 8.3h2.5l.5-1.4h3l.3 1.4h2.2l-1.3-9h-2.2zm-2.8 5.9l1.2-3.3.7 3.3h-1.9z" fill="#1434CB"/>
                    </svg>

                    <!-- MasterCard图标 -->
                    <svg v-if="getCardType(addressInfore.cardNo) === 'mastercard'" class="bakaotp-card-icon" viewBox="0 0 40 24" width="32" height="20">
                        <circle cx="15" cy="12" r="7" fill="#EB001B"/>
                        <circle cx="25" cy="12" r="7" fill="#F79E1B"/>
                        <path d="M20 5.5c1.5 1.3 2.5 3.2 2.5 5.5s-1 4.2-2.5 5.5c-1.5-1.3-2.5-3.2-2.5-5.5s1-4.2 2.5-5.5z" fill="#FF5F00"/>
                    </svg>

                    <!-- American Express图标 -->
                    <svg v-if="getCardType(addressInfore.cardNo) === 'amex'" class="bakaotp-card-icon" viewBox="0 0 40 24" width="32" height="20">
                        <rect width="40" height="24" rx="4" fill="#006FCF"/>
                        <path d="M8 8h4l1 2 1-2h4v8h-4v-4l-1 2-1-2v4H8V8zm12 0h8v2h-6v1h6v2h-6v1h6v2h-8V8z" fill="white"/>
                    </svg>

                    <!-- Discover图标 -->
                    <svg v-if="getCardType(addressInfore.cardNo) === 'discover'" class="bakaotp-card-icon" viewBox="0 0 40 24" width="32" height="20">
                        <rect width="40" height="24" rx="4" fill="#FF6000"/>
                        <path d="M8 8h8v2H8V8zm0 4h12v2H8v-2zm0 4h16v2H8v-2z" fill="white"/>
                        <circle cx="32" cy="12" r="8" fill="#FF6000"/>
                    </svg>

                    <!-- JCB图标 -->
                    <svg v-if="getCardType(addressInfore.cardNo) === 'jcb'" class="bakaotp-card-icon" viewBox="0 0 40 24" width="32" height="20">
                        <rect width="40" height="24" rx="4" fill="#0E4C96"/>
                        <path d="M8 8h6v8H8V8zm8 0h6v8h-6V8zm8 0h6v8h-6V8z" fill="white"/>
                    </svg>

                    <!-- 默认通用图标 -->
                    <svg v-if="!getCardType(addressInfore.cardNo)" class="bakaotp-card-icon" viewBox="0 0 24 24" width="24" height="20">
                        <path d="M20 4H4c-1.11 0-1.99.89-1.99 2L2 18c0 1.11.89 2 2 2h16c1.11 0 2-.89 2-2V6c0-1.11-.89-2-2-2zm0 14H4v-6h16v6zm0-10H4V6h16v2z" fill="#666"/>
                    </svg>
                </div>
            </div>
            <div ref="cardNoTip" class="bakaotp-error-message" hidden>
                <p>The credit card number is invalid.</p>
            </div>
        </div>

        <!-- 有效期 -->
        <div class="bakaotp-form-group bakaotp-form-group-expiry">
            <label class="bakaotp-form-label">有效期</label>
            <div class="bakaotp-input-container" ref="cardDateDiv">
                <input
                    type="text"
                    class="bakaotp-form-input"
                    placeholder="月/年"
                    name="dateTime"
                    maxlength="5"
                    autocomplete="cc-exp"
                    @blur="changeCard('cardDate', $event.target.value)"
                    onkeyup="this.value=this.value.replace(/^(\d\d)(\d)$/g,'$1/$2').replace(/[^\d\/]/g,'')"
                    v-model="addressInfore.cardRiqi"
                />
            </div>
            <div ref="cardDateTip" class="bakaotp-error-message" hidden>
                <p>Credit card has expired.</p>
            </div>
        </div>

        <!-- 安全码 -->
        <div class="bakaotp-form-group bakaotp-form-group-cvv">
            <label class="bakaotp-form-label">安全码</label>
            <div class="bakaotp-input-container" ref="cardCvvDiv">
                <input
                    type="text"
                    class="bakaotp-form-input"
                    placeholder="CVC"
                    name="cvc"
                    maxlength="4"
                    autocomplete="cc-csc"
                    @blur="changeCard('cardCvv', $event.target.value)"
                    v-model="addressInfore.cardCvv"
                />
                <div class="bakaotp-input-icon">
                    <svg class="bakaotp-lock-icon" viewBox="0 0 24 24" width="16" height="16">
                        <path d="M18 8h-1V6c0-2.76-2.24-5-5-5S7 3.24 7 6v2H6c-1.1 0-2 .9-2 2v10c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V10c0-1.1-.9-2-2-2zM9 6c0-1.66 1.34-3 3-3s3 1.34 3 3v2H9V6zm9 14H6V10h12v10zm-6-3c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2z" fill="#666"/>
                    </svg>
                </div>
            </div>
            <div ref="cardCvvTip" class="bakaotp-error-message" hidden>
                <p>CVC is invalid.</p>
            </div>
        </div>
    </div>
</div>
