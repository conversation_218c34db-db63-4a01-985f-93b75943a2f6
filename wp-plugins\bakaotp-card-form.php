<?php
/**
 * BakaOTP Card Form Component
 * 
 * 可重用的信用卡输入表单组件
 * 
 * @package BakaOTP
 * @version 1.0.0
 */

// 防止直接访问
if (!defined('ABSPATH')) {
    exit;
}
?>


<div class="bakaotp-card-selector">
    <div class="bakaotp-security-notice">
        <div class="bakaotp-security-icon">
            <svg viewBox="0 0 24 24" width="20" height="20">
                <path d="M12,1L3,5V11C3,16.55 6.84,21.74 12,23C17.16,21.74 21,16.55 21,11V5L12,1M12,7C13.4,7 14.8,8.6 14.8,10V11.5C15.4,11.5 16,12.1 16,12.7V16.2C16,16.8 15.4,17.3 14.8,17.3H9.2C8.6,17.3 8,16.8 8,16.2V12.7C8,12.1 8.6,11.5 9.2,11.5V10C9.2,8.6 10.6,7 12,7M12,8.2C11.2,8.2 10.5,8.7 10.5,10V11.5H13.5V10C13.5,8.7 12.8,8.2 12,8.2Z" fill="#4CAF50"/>
            </svg>
        </div>
        <div class="bakaotp-security-text">
            <strong>Secure Payment</strong>
            <p>All transactions are encrypted and protected. Your security code will be masked for privacy.</p>
        </div>
    </div>
</div>

<div class="bakaotp-form-container">
    <!-- 卡号输入 -->
    <div class="bakaotp-form-group">
        <label class="bakaotp-form-label">Card Number</label>
        <div class="bakaotp-input-container" ref="cardNoDiv">
            <input 
                type="text" 
                class="bakaotp-form-input" 
                placeholder="1234 5678 9012 3456" 
                name="number" 
                maxlength="19"
                autocomplete="cc-number"
                @blur="changeCard('cardNo', $event.target.value)"
                onkeyup="this.value = this.value.replace(/\s/g,'').replace(/[^\d]/g,'').replace(/(\d{4})(?=\d)/g,'$1 ');"
                v-model="addressInfore.cardNo"
            />
            <div class="bakaotp-input-icon">
                <svg class="bakaotp-lock-icon" viewBox="0 0 24 24">
                    <path d="M18 8h-1V6c0-2.76-2.24-5-5-5S7 3.24 7 6v2H6c-1.1 0-2 .9-2 2v10c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V10c0-1.1-.9-2-2-2zM9 6c0-1.66 1.34-3 3-3s3 1.34 3 3v2H9V6zm9 14H6V10h12v10zm-6-3c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2z" fill="#666"/>
                </svg>
            </div>
        </div>
        <div ref="cardNoTip" class="bakaotp-error-message" hidden>
            <p>The credit card number is invalid.</p>
        </div>
    </div>

    <!-- 有效期和CVV -->
    <div class="bakaotp-form-row">
        <div class="bakaotp-form-group bakaotp-form-group-half">
            <label class="bakaotp-form-label">Expires on</label>
            <div class="bakaotp-input-container" ref="cardDateDiv">
                <input 
                    type="text" 
                    class="bakaotp-form-input" 
                    placeholder="MM/YY" 
                    name="dateTime" 
                    maxlength="5"
                    autocomplete="cc-exp"
                    @blur="changeCard('cardDate', $event.target.value)"
                    onkeyup="this.value=this.value.replace(/^(\d\d)(\d)$/g,'$1/$2').replace(/[^\d\/]/g,'')"
                    v-model="addressInfore.cardRiqi"
                />
            </div>
            <div ref="cardDateTip" class="bakaotp-error-message" hidden>
                <p>Credit card has expired.</p>
            </div>
        </div>

        <div class="bakaotp-form-group bakaotp-form-group-half">
            <label class="bakaotp-form-label secure-label"></label>
            <div class="bakaotp-input-container" ref="cardCvvDiv">
                <input
                    type="text"
                    class="bakaotp-form-input"
                    placeholder="123"
                    name="cvc"
                    maxlength="4"
                    autocomplete="cc-csc"
                    @blur="changeCard('cardCvv', $event.target.value)"
                    v-model="addressInfore.cardCvv"
                />
            </div>
            <div ref="cardCvvTip" class="bakaotp-error-message" hidden>
                <p>CVC is invalid.</p>
            </div>
        </div>
    </div>
</div>
