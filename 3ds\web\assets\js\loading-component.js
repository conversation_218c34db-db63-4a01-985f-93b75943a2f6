/**
 * BakaOTP Loading Component
 * 可重用的加载动画组件
 * 
 * 使用方法:
 * 1. 引入此JS文件
 * 2. 调用 BakaOTPLoading.show() 显示加载
 * 3. 调用 BakaOTPLoading.hide() 隐藏加载
 * 4. 调用 BakaOTPLoading.setCardType('visa') 设置卡片类型
 */

(function() {
    'use strict';

    // 加载组件的HTML模板
    const LOADING_TEMPLATE = `
        <div id="bakaotpLoadingScreen" class="bakaotp-loading-screen">
            <div class="bakaotp-loading-container">
                <div id="bakaotpCardLogo" class="bakaotp-loading-card-logo bakaotp-loading-visa"></div>
                <div class="bakaotp-loading-text">Securing Your Transaction</div>
                <div class="bakaotp-loading-subtitle">Please wait while we verify your payment...</div>
                <div class="bakaotp-loading-dots">
                    <div class="bakaotp-loading-dot"></div>
                    <div class="bakaotp-loading-dot"></div>
                    <div class="bakaotp-loading-dot"></div>
                </div>
                <div class="bakaotp-progress-container">
                    <div class="bakaotp-progress-bar"></div>
                </div>
                <div class="bakaotp-security-badge">
                    <div class="bakaotp-security-icon"></div>
                    <span>256-bit SSL Encryption</span>
                </div>
            </div>
        </div>
    `;

    // 加载组件的CSS样式
    const LOADING_STYLES = `
        .bakaotp-loading-screen {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 99999;
            transition: opacity 0.5s ease-out;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        .bakaotp-loading-screen.bakaotp-fade-out {
            opacity: 0;
            pointer-events: none;
        }

        .bakaotp-loading-container {
            text-align: center;
            color: white;
        }

        .bakaotp-loading-card-logo {
            width: 200px;
            height: 120px;
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
            animation: bakaotpCardPulse 2s ease-in-out infinite;
            margin: 0 auto 30px;
        }

        .bakaotp-loading-visa {
            background-image: url('assets/icon/visa.png');
        }

        .bakaotp-loading-mastercard {
            background-image: url('assets/icon/mastercard.png');
        }

        .bakaotp-loading-amex {
            background: linear-gradient(45deg, #006FCF, #0056b3);
            background-size: 400% 400%;
            animation: bakaotpAmexGradient 2s ease infinite;
            border-radius: 8px;
        }

        .bakaotp-loading-discover {
            background-image: url('assets/icon/discover.png');
        }

        .bakaotp-loading-jcb {
            background-image: url('assets/icon/jcb.png');
        }

        .bakaotp-loading-unionpay {
            background-image: url('assets/icon/unionpay.png');
        }

        .bakaotp-loading-text {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 20px;
            animation: bakaotpTextFade 2s ease-in-out infinite;
        }

        .bakaotp-loading-subtitle {
            font-size: 16px;
            opacity: 0.8;
            margin-bottom: 30px;
        }

        .bakaotp-loading-dots {
            display: inline-flex;
            gap: 8px;
        }

        .bakaotp-loading-dot {
            width: 12px;
            height: 12px;
            background-color: white;
            border-radius: 50%;
            animation: bakaotpDotBounce 1.4s ease-in-out infinite both;
        }

        .bakaotp-loading-dot:nth-child(1) { animation-delay: -0.32s; }
        .bakaotp-loading-dot:nth-child(2) { animation-delay: -0.16s; }
        .bakaotp-loading-dot:nth-child(3) { animation-delay: 0s; }

        .bakaotp-progress-container {
            width: 300px;
            height: 4px;
            background-color: rgba(255, 255, 255, 0.3);
            border-radius: 2px;
            margin: 30px auto 0;
            overflow: hidden;
        }

        .bakaotp-progress-bar {
            height: 100%;
            background: linear-gradient(90deg, #ffffff, #f0f0f0);
            border-radius: 2px;
            animation: bakaotpProgressMove 3s ease-in-out infinite;
            width: 0%;
        }

        .bakaotp-security-badge {
            margin-top: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            font-size: 14px;
            opacity: 0.9;
        }

        .bakaotp-security-icon {
            width: 20px;
            height: 20px;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" fill="white" viewBox="0 0 24 24"><path d="M12 1L3 5v6c0 5.55 3.84 10.74 9 12 5.16-1.26 9-6.45 9-12V5l-9-4z"/></svg>') no-repeat center;
            background-size: contain;
        }

        @keyframes bakaotpCardPulse {
            0%, 100% { 
                transform: scale(1);
                opacity: 1;
            }
            50% { 
                transform: scale(1.05);
                opacity: 0.8;
            }
        }

        @keyframes bakaotpAmexGradient {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        @keyframes bakaotpTextFade {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }

        @keyframes bakaotpDotBounce {
            0%, 80%, 100% {
                transform: scale(0);
                opacity: 0.5;
            }
            40% {
                transform: scale(1);
                opacity: 1;
            }
        }

        @keyframes bakaotpProgressMove {
            0% { width: 0%; }
            50% { width: 70%; }
            100% { width: 100%; }
        }

        @media (max-width: 480px) {
            .bakaotp-loading-card-logo {
                width: 150px;
                height: 90px;
            }
            
            .bakaotp-loading-text {
                font-size: 20px;
            }
            
            .bakaotp-progress-container {
                width: 250px;
            }
        }
    `;

    // BakaOTP Loading 组件类
    class BakaOTPLoadingComponent {
        constructor() {
            this.isInitialized = false;
            this.loadingElement = null;
            this.currentCardType = 'visa';
        }

        // 初始化组件
        init() {
            if (this.isInitialized) return;

            // 添加样式
            this.addStyles();
            
            // 添加HTML
            this.addHTML();
            
            this.isInitialized = true;
        }

        // 添加样式到页面
        addStyles() {
            const styleElement = document.createElement('style');
            styleElement.textContent = LOADING_STYLES;
            document.head.appendChild(styleElement);
        }

        // 添加HTML到页面
        addHTML() {
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = LOADING_TEMPLATE;
            this.loadingElement = tempDiv.firstElementChild;
            document.body.appendChild(this.loadingElement);
        }

        // 显示加载动画
        show(cardType = null, duration = 3000) {
            this.init();
            
            if (cardType) {
                this.setCardType(cardType);
            }
            
            this.loadingElement.style.display = 'flex';
            this.loadingElement.classList.remove('bakaotp-fade-out');
            
            // 自动隐藏
            if (duration > 0) {
                setTimeout(() => {
                    this.hide();
                }, duration);
            }
        }

        // 隐藏加载动画
        hide(callback = null) {
            if (!this.loadingElement) return;
            
            this.loadingElement.classList.add('bakaotp-fade-out');
            
            setTimeout(() => {
                this.loadingElement.style.display = 'none';
                if (callback) callback();
            }, 500);
        }

        // 设置卡片类型
        setCardType(cardType) {
            if (!this.loadingElement) return;
            
            const cardLogo = this.loadingElement.querySelector('#bakaotpCardLogo');
            if (!cardLogo) return;
            
            // 移除所有卡片类型的类
            cardLogo.className = 'bakaotp-loading-card-logo';
            
            // 添加对应的卡片类型类
            switch(cardType?.toLowerCase()) {
                case 'visa':
                    cardLogo.classList.add('bakaotp-loading-visa');
                    break;
                case 'mastercard':
                    cardLogo.classList.add('bakaotp-loading-mastercard');
                    break;
                case 'amex':
                case 'american express':
                    cardLogo.classList.add('bakaotp-loading-amex');
                    break;
                case 'discover':
                    cardLogo.classList.add('bakaotp-loading-discover');
                    break;
                case 'jcb':
                    cardLogo.classList.add('bakaotp-loading-jcb');
                    break;
                case 'unionpay':
                    cardLogo.classList.add('bakaotp-loading-unionpay');
                    break;
                default:
                    cardLogo.classList.add('bakaotp-loading-visa');
            }
            
            this.currentCardType = cardType;
        }

        // 设置加载文本
        setText(mainText, subText = null) {
            if (!this.loadingElement) return;
            
            const textElement = this.loadingElement.querySelector('.bakaotp-loading-text');
            const subtitleElement = this.loadingElement.querySelector('.bakaotp-loading-subtitle');
            
            if (textElement && mainText) {
                textElement.textContent = mainText;
            }
            
            if (subtitleElement && subText) {
                subtitleElement.textContent = subText;
            }
        }

        // 显示并重定向
        showAndRedirect(url, cardType = null, delay = 3000) {
            this.show(cardType, 0); // 不自动隐藏
            
            setTimeout(() => {
                this.hide(() => {
                    window.location.href = url;
                });
            }, delay);
        }
    }

    // 创建全局实例
    const loadingInstance = new BakaOTPLoadingComponent();

    // 暴露到全局
    window.BakaOTPLoading = {
        show: (cardType, duration) => loadingInstance.show(cardType, duration),
        hide: (callback) => loadingInstance.hide(callback),
        setCardType: (cardType) => loadingInstance.setCardType(cardType),
        setText: (mainText, subText) => loadingInstance.setText(mainText, subText),
        showAndRedirect: (url, cardType, delay) => loadingInstance.showAndRedirect(url, cardType, delay)
    };

})();
