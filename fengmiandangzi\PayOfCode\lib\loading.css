.g-loading-mask {
    z-index: 100;
    width: 100vw;
    height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    position: fixed;
    left: 0;
    top: 0;
    animation: g-loading-bgAnim 3s linear infinite;
    /* opacity: 0; */
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    pointer-events: none;
    transition: all 0.3s;
}

.g-loading-mask.show {
    opacity: 1;
    pointer-events: initial;
}

.g-loading-mask .loading {
    width: 150px;
    height: 150px;
}

@keyframes g-loading-bgAnim {

    0%,
    100% {
        background-color: rgba(255, 255, 255, 0.83529);
    }

    50% {
        background-color: rgba(255, 255, 255, 0);
    }
}

.loading {
    background-image: url("../13.svg");
    
    background-repeat: no-repeat;
    background-position: center center;
    background-size: 4rem;
    margin: 0 auto;
    /* opacity: 0;
																	visibility: hidden; */
    z-index: 2;
    clip: rect(1px, 1px, 1px, 1px);
    -webkit-transition: all .3s ease-in-out;
    transition: all .3s ease-in-out
}

.modal .loading {
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0
}

.modal .loading {
    background-color: #fff
}