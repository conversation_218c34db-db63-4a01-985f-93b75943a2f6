<template>
  <Page
    content-class="flex flex-col gap-4"
    description="域名管理"
    title="🌐 域名管理"
  >
    <!-- 域名统计卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
      <Card>
        <div class="flex items-center gap-3">
          <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
            <Icon icon="svg:antdv-logo" class="w-6 h-6 text-blue-600" />
          </div>
          <div>
            <p class="text-sm text-gray-500 dark:text-gray-400">总域名数</p>
            <p class="text-2xl font-bold text-gray-900 dark:text-gray-100">{{ statistics.totalDomains || 0 }}</p>
          </div>
        </div>
      </Card>

      <Card>
        <div class="flex items-center gap-3">
          <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
            <Icon icon="lucide:check-circle" class="w-6 h-6 text-green-600" />
          </div>
          <div>
            <p class="text-sm text-gray-500 dark:text-gray-400">活跃域名</p>
            <p class="text-2xl font-bold text-gray-900 dark:text-gray-100">{{ statistics.activeDomains || 0 }}</p>
          </div>
        </div>
      </Card>

      <Card>
        <div class="flex items-center gap-3">
          <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
            <Icon icon="lucide:alert-triangle" class="w-6 h-6 text-red-600" />
          </div>
          <div>
            <p class="text-sm text-gray-500 dark:text-gray-400">不安全域名</p>
            <p class="text-2xl font-bold text-gray-900 dark:text-gray-100">{{ statistics.unsafeDomains || 0 }}</p>
          </div>
        </div>
      </Card>

      <Card>
        <div class="flex items-center gap-3">
          <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
            <Icon icon="lucide:clock" class="w-6 h-6 text-purple-600" />
          </div>
          <div>
            <p class="text-sm text-gray-500 dark:text-gray-400">最近检测</p>
            <p class="text-sm font-medium text-gray-900 dark:text-gray-100">{{ formatTime(statistics.lastCheckTime) || '未检测' }}</p>
          </div>
        </div>
      </Card>
    </div>

    <!-- 域名安全检测状态显示 -->
    <Card title="🔍 域名安全检测规则">
      <div class="mb-4">
        <p class="text-sm text-gray-600 mb-3">
          数据来自：
          <a
            href="https://transparencyreport.google.com"
            target="_blank"
            rel="noopener noreferrer"
            class="text-blue-600 hover:text-blue-800 underline"
          >
            transparencyreport.google.com
          </a>
          <span class="text-xs text-gray-500 ml-2">• 每15分钟自动检测</span>
        </p>
        <div class="grid grid-cols-2 md:grid-cols-5 gap-4">
          <div class="flex items-center gap-2">
            <div class="w-3 h-3 rounded-full bg-green-500"></div>
            <span class="text-sm">绿色：安全</span>
          </div>
          <div class="flex items-center gap-2">
            <div class="w-3 h-3 rounded-full bg-red-500"></div>
            <span class="text-sm">红色：不安全</span>
          </div>
          <div class="flex items-center gap-2">
            <div class="w-3 h-3 rounded-full bg-yellow-500"></div>
            <span class="text-sm">黄色：部分不安全</span>
          </div>
          <div class="flex items-center gap-2">
            <div class="w-3 h-3 rounded-full bg-purple-500"></div>
            <span class="text-sm">紫色：新添加</span>
          </div>
          <div class="flex items-center gap-2">
            <div class="w-3 h-3 rounded-full bg-gray-400"></div>
            <span class="text-sm">灰色：未检测</span>
          </div>
        </div>
      </div>
    </Card>

    <!-- 域名列表 -->
    <Card title="📋 域名列表">
      <template #extra>
        <div class="flex items-center gap-2">
          <Button @click="refreshCorsConfig" :loading="corsRefreshing" size="small">
            <Icon icon="lucide:refresh-cw" class="w-4 h-4 mr-1" />
            刷新配置
          </Button>
          <Button type="primary" @click="showAddModal = true">
            <Icon icon="lucide:plus" class="w-4 h-4 mr-1" />
            添加域名
          </Button>
        </div>
      </template>

      <div class="space-y-4">
        <!-- 加载状态 -->
        <div v-if="loading" class="text-center py-8">
          <Icon icon="lucide:loader-2" class="w-8 h-8 mx-auto text-blue-500 animate-spin mb-2" />
          <p class="text-gray-500">正在加载域名列表...</p>
        </div>

        <div v-for="domain in domains" :key="domain.id" class="border rounded-lg p-4">
          <div class="flex items-center justify-between mb-3">
            <div class="flex items-center gap-3">
              <!-- 状态指示器 -->
              <div 
                class="w-4 h-4 rounded-full"
                :class="getStatusColor(domain.status)"
              ></div>
              <div>
                  <h3 class="font-medium">{{ domain.domainName }}</h3>
                <p class="text-sm text-gray-500">{{ domain.domainPath || '/' }}</p>
              </div>
            </div>
            <div class="flex items-center gap-2">
              <span class="text-xs px-2 py-1 rounded" :class="getStatusBadgeClass(domain.status)">
                {{ getStatusText(domain.status) }}
              </span>
            </div>
          </div>

          <!-- 3D验证域名管理区 -->
          <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-3 mb-3">
            <div class="flex items-center gap-2">
              <span class="text-sm text-gray-600 dark:text-gray-400">3D验证URL:</span>
              <div class="flex-1 flex items-center gap-2">
                <input
                  type="text"
                  :value="`https://${domain.domainName}${domain.domainPath || ''}`"
                  readonly
                  class="flex-1 px-2 py-1 text-sm border rounded bg-white dark:bg-gray-700 dark:border-gray-600 dark:text-gray-100"
                />
                <Button
                  size="small"
                  @click="copyUrl(domain)"
                  title="复制URL"
                >
                  <Icon icon="svg:copy" class="w-4 h-4" />
                </Button>
              </div>
            </div>
          </div>

          <!-- 域名信息 - 简化显示，移除重复信息 -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm mb-3">
            <div>
              <span class="text-gray-500">安全状态:</span>
              <div class="flex items-center gap-1">
                <div :class="getSecurityStatusColor(domain.securityStatus)" class="w-2 h-2 rounded-full"></div>
                <span :class="getSecurityStatusTextColor(domain.securityStatus)">
                  {{ getSecurityStatusText(domain.securityStatus) }}
                </span>
              </div>
            </div>
            <div>
              <span class="text-gray-500">最后检测:</span>
              <span class="text-gray-700">{{ formatTime(domain.lastSecurityCheck) }}</span>
            </div>
          </div>

          <!-- 底部操作按钮组 -->
          <div class="flex items-center gap-2">
            <Button size="small" @click="configureDomain(domain)">
              <Icon icon="lucide:settings" class="w-4 h-4 mr-1" />
              配置
            </Button>


            <Button size="small" @click="testConnectivity(domain)">
              <Icon icon="lucide:wifi" class="w-4 h-4 mr-1" />
              连通性测试
            </Button>
            <Button size="small" @click="checkSecurity(domain)">
              <Icon icon="lucide:shield-check" class="w-4 h-4 mr-1" />
              安全检查
            </Button>
            <Button size="small" danger @click="deleteDomain(domain)">
              <Icon icon="lucide:trash-2" class="w-4 h-4 mr-1" />
              删除
            </Button>
          </div>
        </div>

        <!-- 空状态 -->
        <div v-if="domains.length === 0" class="text-center py-8">
          <Icon icon="lucide:globe" class="w-12 h-12 mx-auto text-gray-400 mb-2" />
          <p class="text-gray-500">暂无域名配置</p>
          <Button type="primary" class="mt-2" @click="showAddModal = true">
            添加第一个域名
          </Button>
        </div>
      </div>
    </Card>

    <!-- 添加域名模态框 -->
    <Modal
      v-model:open="showAddModal"
      title="添加新域名"
      @ok="handleAddDomain"
      @cancel="resetAddForm"
    >
      <div class="space-y-4">
        <div>
          <label class="block text-sm font-medium mb-1">域名</label>
          <input
            v-model="addForm.domainName"
            type="text"
            placeholder="例如：example.com 或 localhost"
            class="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
        <div>
          <label class="block text-sm font-medium mb-1">路径 (可选)</label>
          <input
            v-model="addForm.domainPath"
            type="text"
            placeholder="例如：/payment"
            class="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
      </div>
    </Modal>

    <!-- 3D验证配置模态框 -->
    <Modal
      v-model:open="show3DConfigModal"
      title="3D验证配置"
      width="800px"
      @ok="handle3DConfigSave"
      @cancel="reset3DConfigForm"
    >
      <div class="space-y-6">
        <!-- 基础配置 -->
        <div class="border rounded-lg p-4">
          <h3 class="text-lg font-medium mb-4">🔧 基础配置</h3>
          <div class="grid grid-cols-2 gap-4">
            <div>
              <label class="block text-sm font-medium mb-1">启用3D验证</label>
              <input
                v-model="threeDConfig.isEnabled"
                type="checkbox"
                class="w-4 h-4"
              />
            </div>
            <div>
              <label class="block text-sm font-medium mb-1">默认语言</label>
              <select
                v-model="threeDConfig.defaultLanguage"
                class="w-full px-3 py-2 border rounded-lg"
              >
                <option value="en">English</option>
                <option value="zh">中文</option>
                <option value="es">Español</option>
                <option value="fr">Français</option>
              </select>
            </div>
            <div>
              <label class="block text-sm font-medium mb-1">验证超时(分钟)</label>
              <input
                v-model="threeDConfig.verificationTimeout"
                type="number"
                min="5"
                max="60"
                class="w-full px-3 py-2 border rounded-lg"
              />
            </div>
            <div>
              <label class="block text-sm font-medium mb-1">最大重试次数</label>
              <input
                v-model="threeDConfig.maxRetryCount"
                type="number"
                min="1"
                max="10"
                class="w-full px-3 py-2 border rounded-lg"
              />
            </div>
          </div>
        </div>

        <!-- 模板配置 -->
        <div class="border rounded-lg p-4">
          <h3 class="text-lg font-medium mb-4">🎨 模板配置</h3>
          <div class="grid grid-cols-2 gap-4">
            <div>
              <label class="block text-sm font-medium mb-1">验证页面模板</label>
              <select
                v-model="threeDConfig.verificationTemplateId"
                class="w-full px-3 py-2 border rounded-lg"
              >
                <option value="">选择模板</option>
                <option
                  v-for="template in verificationTemplates"
                  :key="template.id"
                  :value="template.id"
                >
                  {{ template.templateName }}
                </option>
              </select>
            </div>
            <div>
              <label class="block text-sm font-medium mb-1">完成页面模板</label>
              <select
                v-model="threeDConfig.completeTemplateId"
                class="w-full px-3 py-2 border rounded-lg"
              >
                <option value="">选择模板</option>
                <option
                  v-for="template in completeTemplates"
                  :key="template.id"
                  :value="template.id"
                >
                  {{ template.templateName }}
                </option>
              </select>
            </div>
          </div>

          <div class="mt-4 flex gap-2">
            <Button size="small" @click="previewTemplate('verification')">
              <Icon icon="lucide:eye" class="w-4 h-4 mr-1" />
              预览验证页面
            </Button>
            <Button size="small" @click="previewTemplate('complete')">
              <Icon icon="lucide:eye" class="w-4 h-4 mr-1" />
              预览完成页面
            </Button>
          </div>
        </div>

        <!-- 3D验证URL预览 -->
        <div class="border rounded-lg p-4">
          <h3 class="text-lg font-medium mb-4">🔗 3D验证URL</h3>
          <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-3">
            <div class="flex items-center gap-2">
              <span class="text-sm text-gray-600 dark:text-gray-400">验证页面URL:</span>
              <div class="flex-1 flex items-center gap-2">
                <input
                  :value="get3DVerificationUrl()"
                  readonly
                  class="flex-1 px-2 py-1 text-sm border rounded bg-white dark:bg-gray-700 dark:border-gray-600 dark:text-gray-100"
                />
                <Button
                  size="small"
                  @click="copy3DUrl('verification')"
                  title="复制URL"
                >
                  <Icon icon="svg:copy" class="w-4 h-4" />
                </Button>
              </div>
            </div>
            <div class="flex items-center gap-2 mt-2">
              <span class="text-sm text-gray-600 dark:text-gray-400">完成页面URL:</span>
              <div class="flex-1 flex items-center gap-2">
                <input
                  :value="get3DCompleteUrl()"
                  readonly
                  class="flex-1 px-2 py-1 text-sm border rounded bg-white dark:bg-gray-700 dark:border-gray-600 dark:text-gray-100"
                />
                <Button
                  size="small"
                  @click="copy3DUrl('complete')"
                  title="复制URL"
                >
                  <Icon icon="svg:copy" class="w-4 h-4" />
                </Button>
              </div>
            </div>
          </div>
        </div>

        <!-- 测试配置 -->
        <div class="border rounded-lg p-4">
          <h3 class="text-lg font-medium mb-4">🧪 测试配置</h3>
          <div class="flex gap-2">
            <Button @click="test3DConfig">
              <Icon icon="lucide:play" class="w-4 h-4 mr-1" />
              测试配置
            </Button>
            <Button @click="open3DVerificationPage">
              <Icon icon="lucide:external-link" class="w-4 h-4 mr-1" />
              打开验证页面
            </Button>
          </div>
        </div>
      </div>
    </Modal>

    <!-- 域名配置模态框 -->
    <Modal
      v-model:open="showDomainConfigModal"
      title="域名配置"
      width="600px"
      @ok="handleDomainConfigSave"
      @cancel="resetDomainConfigForm"
    >
      <div class="space-y-6">
        <!-- 基础信息 -->
        <div class="border rounded-lg p-4">
          <h3 class="text-lg font-medium mb-4">📋 基础信息</h3>
          <div class="grid grid-cols-1 gap-4">
            <div>
              <label class="block text-sm font-medium mb-1">域名</label>
              <input
                v-model="domainConfig.domainName"
                type="text"
                placeholder="例如：example.com"
                class="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                readonly
              />
            </div>
            <div>
              <label class="block text-sm font-medium mb-1">路径</label>
              <input
                v-model="domainConfig.domainPath"
                type="text"
                placeholder="例如：/payment"
                class="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>


          </div>
        </div>




      </div>
    </Modal>


  </Page>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue';
import { Card, Button, Modal, message } from 'ant-design-vue';
import { IconifyIcon as Icon } from '@vben/icons';
import { Page } from '@vben/common-ui';
import { requestClient } from '#/api/request';
import { useAccessStore } from '@vben/stores';

const domains = ref([]);
const statistics = ref({
  totalDomains: 0,
  activeDomains: 0,
  unsafeDomains: 0,
  lastCheckTime: null
});
const showAddModal = ref(false);
const show3DConfigModal = ref(false);
const showDomainConfigModal = ref(false);
const currentDomain = ref(null);
const verificationTemplates = ref([]);
const completeTemplates = ref([]);
const loading = ref(false);
const corsRefreshing = ref(false);

const addForm = ref({
  domainName: '',
  domainPath: ''
});

const threeDConfig = ref({
  isEnabled: true,
  defaultLanguage: 'en',
  verificationTimeout: 15,
  maxRetryCount: 3,
  verificationTemplateId: null,
  completeTemplateId: null
});

const domainConfig = ref({
  domainName: '',
  domainPath: '',
  autoCheckEnabled: true,
  checkInterval: 15,
  sslEnabled: true,
  status: 'active'
});

// WebSocket连接
let websocket: WebSocket | null = null;
let heartbeatInterval: number | null = null;

// 页面可见性状态
const isPageVisible = ref(true);

// 页面可见性变化处理
const handleVisibilityChange = () => {
  isPageVisible.value = !document.hidden;

  if (isPageVisible.value) {
    console.log('📱 页面变为可见，检查WebSocket连接状态');
    // 如果WebSocket连接已断开，重新连接
    if (!websocket || websocket.readyState !== WebSocket.OPEN) {
      console.log('🔄 WebSocket连接已断开，重新建立连接');
      initWebSocket();
    }
    // 刷新数据
    loadDomains();
    loadStatistics();
  } else {
    console.log('🌙 页面变为隐藏，保持WebSocket连接以接收实时更新');
    // 不关闭WebSocket连接，保持后台接收数据
  }
};

// 生命周期
onMounted(() => {
  loadDomains();
  loadStatistics();
  initWebSocket();

  // 监听页面可见性变化
  document.addEventListener('visibilitychange', handleVisibilityChange);
});

onUnmounted(() => {
  // 移除页面可见性监听器
  document.removeEventListener('visibilitychange', handleVisibilityChange);

  // 停止心跳
  stopHeartbeat();

  // 只在页面真正卸载时关闭WebSocket
  if (websocket) {
    websocket.close();
  }
});

// 加载域名列表
const loadDomains = async () => {
  loading.value = true;
  try {
    const response = await requestClient.get('/domains');
    // 响应拦截器已经处理了数据，直接使用response作为数据
    domains.value = response || [];
    message.success(`成功加载 ${domains.value.length} 个域名`);
    // 加载完域名后更新统计信息
    await loadStatistics();
  } catch (error) {
    console.error('加载域名列表失败:', error);
    message.error('连接服务器失败');
    domains.value = [];
  } finally {
    loading.value = false;
  }
};



// 加载域名统计信息
const loadStatistics = async () => {
  try {
    const response = await requestClient.get('/domains/statistics');
    // 响应拦截器已经处理了数据，直接使用response作为数据
    const data = response || {};
    statistics.value = {
      totalDomains: data.total || 0,
      activeDomains: data.normal || 0,
      unsafeDomains: data.unsafe || 0,
      lastCheckTime: new Date().toISOString()
    };
  } catch (error) {
    console.error('加载统计信息失败:', error);
    statistics.value = { totalDomains: 0, activeDomains: 0, unsafeDomains: 0, lastCheckTime: new Date().toISOString() };
  }
};



// 启动心跳机制
const startHeartbeat = () => {
  // 清除现有心跳
  stopHeartbeat();

  // 每30秒发送一次心跳
  heartbeatInterval = window.setInterval(() => {
    if (websocket && websocket.readyState === WebSocket.OPEN) {
      websocket.send(JSON.stringify({
        action: 'ping',
        timestamp: Date.now()
      }));
      console.log('💓 发送心跳包');
    }
  }, 30000);
};

// 停止心跳机制
const stopHeartbeat = () => {
  if (heartbeatInterval) {
    clearInterval(heartbeatInterval);
    heartbeatInterval = null;
  }
};

// 初始化WebSocket
const initWebSocket = () => {
  try {
    // 获取认证token
    const accessStore = useAccessStore();
    const token = accessStore.accessToken;

    if (!token) {
      console.warn('没有认证token，无法建立WebSocket连接');
      return;
    }

    // 在WebSocket URL中传递token作为查询参数
    const wsUrl = `ws://${window.location.hostname}:8080/ws?token=${encodeURIComponent(token)}`;
    websocket = new WebSocket(wsUrl);

    websocket.onopen = () => {
      console.log('WebSocket连接已建立');

      // 发送连接确认消息
      websocket.send(JSON.stringify({
        action: 'connect',
        type: 'subscribe',
        channel: 'domain-management',
        clientType: 'admin-domain',
        timestamp: Date.now()
      }));

      // 启动心跳机制
      startHeartbeat();
    };

    websocket.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        handleWebSocketMessage(data);
      } catch (error) {
        console.error('解析WebSocket消息失败:', error);
      }
    };

    websocket.onclose = () => {
      console.log('WebSocket连接已关闭');
      // 停止心跳
      stopHeartbeat();
      // 5秒后重连
      setTimeout(initWebSocket, 5000);
    };

    websocket.onerror = (error) => {
      console.error('WebSocket连接错误:', error);
      // 停止心跳
      stopHeartbeat();
    };

  } catch (error) {
    console.error('初始化WebSocket失败:', error);
  }
};

// 处理WebSocket消息
const handleWebSocketMessage = (data: any) => {
  console.log('收到WebSocket消息:', data);

  switch (data.type) {
    case 'connection_ack':
      console.log('WebSocket连接确认成功:', data.message);
      break;
    case 'security_status_update':
    case 'scheduled_security_check':
      // 更新域名的安全状态
      const domainIndex = domains.value.findIndex(d => d.id === data.domainId || d.domainName === data.domain);
      if (domainIndex !== -1) {
        domains.value[domainIndex].securityStatus = data.securityStatus || data.status;
        domains.value[domainIndex].lastSecurityCheck = new Date().toISOString();

        // 显示通知
        if (data.type === 'scheduled_security_check') {
          const statusText = getSecurityStatusText(data.securityStatus);
          message.info(`域名 ${data.domainName} 安全检查完成: ${statusText}`);
        }
      }
      break;
    case 'pong':
      console.log('收到心跳响应');
      break;
    default:
      console.log('未知消息类型:', data.type);
  }
};

// 获取状态颜色
const getStatusColor = (status: string) => {
  const colors = {
    normal: 'bg-green-500',
    unsafe: 'bg-red-500',
    partial_unsafe: 'bg-yellow-500',
    new: 'bg-purple-500'
  };
  return colors[status] || 'bg-gray-500';
};

// 获取状态徽章样式
const getStatusBadgeClass = (status: string) => {
  const classes = {
    normal: 'bg-green-100 text-green-800',
    unsafe: 'bg-red-100 text-red-800',
    partial_unsafe: 'bg-yellow-100 text-yellow-800',
    new: 'bg-purple-100 text-purple-800'
  };
  return classes[status] || 'bg-gray-100 text-gray-800';
};

// 获取状态文本
const getStatusText = (status: string) => {
  const texts = {
    normal: '正常',
    unsafe: '不安全',
    partial_unsafe: '部分不安全',
    new: '新添加'
  };
  return texts[status] || '未知';
};

// 获取安全状态颜色
const getSecurityStatusColor = (securityStatus: string) => {
  const colorMap = {
    'safe': 'bg-green-500',
    'unsafe': 'bg-red-500',
    'partially_unsafe': 'bg-yellow-500',
    'uncommon_files': 'bg-yellow-500',
    'no_data': 'bg-purple-500',
    'error': 'bg-purple-500'
  };
  return colorMap[securityStatus] || 'bg-gray-400'; // 未检测状态使用灰色
};

// 获取安全状态文本
const getSecurityStatusText = (securityStatus: string) => {
  const textMap = {
    'safe': '安全',
    'unsafe': '不安全',
    'partially_unsafe': '部分不安全',
    'uncommon_files': '不常见文件',
    'no_data': '无数据',
    'error': '检查错误'
  };
  return textMap[securityStatus] || '未检测';
};

// 获取安全状态文本颜色
const getSecurityStatusTextColor = (securityStatus: string) => {
  const colorMap = {
    'safe': 'text-green-600',
    'unsafe': 'text-red-600',
    'partially_unsafe': 'text-yellow-600',
    'uncommon_files': 'text-yellow-600',
    'no_data': 'text-purple-600',
    'error': 'text-purple-600'
  };
  return colorMap[securityStatus] || 'text-gray-500'; // 未检测状态使用灰色
};

// 获取状态文本颜色
const getStatusTextColor = (status: string) => {
  const colorMap = {
    'normal': 'text-green-600',
    'unsafe': 'text-red-600',
    'partial_unsafe': 'text-yellow-600',
    'new': 'text-purple-600',
    'error': 'text-red-600'
  };
  return colorMap[status] || 'text-gray-600';
};

// 重复的getStatusTextColor函数已删除

// 格式化时间
const formatTime = (time: string) => {
  if (!time) return '未检测';
  return new Date(time).toLocaleString();
};

// 复制URL
const copyUrl = async (domain: any) => {
  const url = `https://${domain.domainName}${domain.domainPath || ''}`;
  try {
    await navigator.clipboard.writeText(url);
    message.success('URL已复制到剪贴板');
  } catch (error) {
    message.error('复制失败');
  }
};

// 配置域名
const configureDomain = (domain: any) => {
  currentDomain.value = domain;

  // 加载域名配置数据
  domainConfig.value = {
    domainName: domain.domainName || '',
    domainPath: domain.domainPath || '',
    autoCheckEnabled: domain.autoCheckEnabled !== false,
    checkInterval: domain.checkInterval || 15,
    sslEnabled: domain.sslEnabled !== false,
    status: domain.status || 'active'
  };

  showDomainConfigModal.value = true;
};


// 连通性测试
const testConnectivity = async (domain: any) => {
  try {
    message.loading('正在测试域名连通性...', 0);

    const response = await requestClient.post(`/domains/${domain.id}/connectivity-test`);
    message.destroy();

    if (response.data.success) {
      const testResult = response.data.data;

      // 显示详细的连通性测试结果
      Modal.info({
        title: '连通性测试结果',
        width: 600,
        content: `
          <div style="padding: 10px;">
            <div style="margin-bottom: 10px;">
              <strong>域名:</strong> ${domain.domainName}
            </div>
            <div style="margin-bottom: 10px;">
              <strong>状态:</strong>
              <span style="color: ${testResult.isReachable ? 'green' : 'red'};">
                ${testResult.isReachable ? '✅ 可访问' : '❌ 不可访问'}
              </span>
            </div>
            <div style="margin-bottom: 10px;">
              <strong>响应时间:</strong> ${testResult.responseTime || 'N/A'} ms
            </div>
            <div style="margin-bottom: 10px;">
              <strong>HTTP状态码:</strong> ${testResult.httpStatus || 'N/A'}
            </div>
            <div style="margin-bottom: 10px;">
              <strong>IP地址:</strong> ${testResult.ipAddress || 'N/A'}
            </div>
            <div style="margin-bottom: 10px;">
              <strong>SSL证书:</strong>
              <span style="color: ${testResult.sslValid ? 'green' : 'red'};">
                ${testResult.sslValid ? '✅ 有效' : '❌ 无效'}
              </span>
            </div>
            ${testResult.error ? `<div style="color: red; margin-top: 10px;"><strong>错误信息:</strong> ${testResult.error}</div>` : ''}
          </div>
        `,
        okText: '确定'
      });
    } else {
      message.error('连通性测试失败: ' + (response.data.message || '未知错误'));
    }
  } catch (error) {
    console.error('连通性测试失败:', error);
    message.destroy();
    message.error('连通性测试失败，请检查网络连接');
  }
};

// 安全检查
const checkSecurity = async (domain: any) => {
  try {
    message.loading('正在检查域名安全状态...', 0);

    const response = await requestClient.post(`/domains/${domain.id}/security-check`);
    message.destroy();

    if (response.data.success) {
      // 更新域名的安全状态
      const domainIndex = domains.value.findIndex(d => d.id === domain.id);
      if (domainIndex !== -1) {
        domains.value[domainIndex].securityStatus = response.data.data.status;
        domains.value[domainIndex].lastSecurityCheck = new Date().toISOString();
      }

      const statusText = getSecurityStatusText(response.data.data.status);
      message.success(`安全检查完成 - ${statusText}`);
    } else {
      message.error('安全检查失败: ' + (response.data.message || '未知错误'));
    }
  } catch (error) {
    console.error('安全检查失败:', error);
    message.destroy();
    message.error('安全检查失败，请检查网络连接');
  }
};

// 删除域名
const deleteDomain = (domain: any) => {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除域名 ${domain.domainName} 吗？此操作不可恢复。`,
    okText: '确认删除',
    cancelText: '取消',
    okType: 'danger',
    onOk: async () => {
      try {
        const response = await requestClient.delete(`/domains/${domain.id}`);

        if (response.data.success) {
          message.success('域名删除成功');
          // 重新加载域名列表
          await loadDomains();
        } else {
          message.error('删除失败: ' + (response.data.message || '未知错误'));
        }
      } catch (error) {
        console.error('删除域名失败:', error);
        message.error('删除失败，请检查网络连接');
      }
    }
  });
};

// 添加域名
const handleAddDomain = async () => {
  if (!addForm.value.domainName) {
    message.error('请输入域名');
    return;
  }

  // 域名格式验证（支持localhost等特殊域名）
  const domainName = addForm.value.domainName.trim().toLowerCase();

  // 特殊域名列表
  const specialDomains = [
    'localhost',
    '127.0.0.1',
    '0.0.0.0',
    '::1'
  ];

  // 检查是否为特殊域名
  const isSpecialDomain = specialDomains.includes(domainName);

  // 检查是否为IP地址
  const ipRegex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
  const isIPAddress = ipRegex.test(domainName);

  // 检查是否为带端口的localhost
  const localhostWithPortRegex = /^(localhost|127\.0\.0\.1):\d+$/;
  const isLocalhostWithPort = localhostWithPortRegex.test(domainName);

  // 标准域名格式验证
  const domainRegex = /^[a-zA-Z0-9][a-zA-Z0-9-]{1,61}[a-zA-Z0-9]\.[a-zA-Z]{2,}$/;
  const isValidDomain = domainRegex.test(domainName);

  // 综合验证
  if (!isSpecialDomain && !isIPAddress && !isLocalhostWithPort && !isValidDomain) {
    message.error('请输入有效的域名格式（支持：域名、IP地址、localhost、localhost:端口）');
    return;
  }

  try {
    const domainData = {
      domainName: addForm.value.domainName.trim(),
      domainPath: addForm.value.domainPath?.trim() || '/',
      autoCheckEnabled: true,
      checkInterval: 15
    };

    const response = await requestClient.post('/domains', domainData);

    if (response.data.success) {
      message.success('域名添加成功');
      showAddModal.value = false;
      resetAddForm();
      // 重新加载域名列表
      await loadDomains();
    } else {
      message.error('添加失败: ' + (response.data.message || '未知错误'));
    }
  } catch (error) {
    console.error('添加域名失败:', error);
    message.error('添加域名失败，请检查网络连接');
  }
};

// 重置添加表单
const resetAddForm = () => {
  addForm.value = {
    domainName: '',
    domainPath: ''
  };
};

// 保存域名配置
const handleDomainConfigSave = async () => {
  if (!currentDomain.value) {
    message.error('未选择域名');
    return;
  }

  try {
    const updateData = {
      domainName: domainConfig.value.domainName,
      domainPath: domainConfig.value.domainPath,
      autoCheckEnabled: domainConfig.value.autoCheckEnabled,
      checkInterval: domainConfig.value.checkInterval,
      sslEnabled: domainConfig.value.sslEnabled,
      status: domainConfig.value.status
    };

    const response = await requestClient.put(`/domains/${currentDomain.value.id}`, updateData);

    if (response.data.success) {
      message.success('域名配置保存成功');
      showDomainConfigModal.value = false;
      resetDomainConfigForm();
      // 重新加载域名列表
      await loadDomains();
    } else {
      message.error('保存失败: ' + (response.data.message || '未知错误'));
    }
  } catch (error) {
    console.error('保存域名配置失败:', error);
    message.error('保存域名配置失败，请检查网络连接');
  }
};

// 重置域名配置表单
const resetDomainConfigForm = () => {
  domainConfig.value = {
    domainName: '',
    domainPath: '',
    autoCheckEnabled: true,
    checkInterval: 15,
    sslEnabled: true,
    status: 'active'
  };
  currentDomain.value = null;
};



// 加载3D验证模板
const load3DTemplates = async () => {
  try {
    // 加载验证页面模板
    const verifyResponse = await requestClient.get('/3d-secure/templates?type=verification&enabled=true');
    verificationTemplates.value = verifyResponse.data.success ? (verifyResponse.data.data || []) : [];

    // 加载完成页面模板
    const completeResponse = await requestClient.get('/3d-secure/templates?type=complete&enabled=true');
    completeTemplates.value = completeResponse.data.success ? (completeResponse.data.data || []) : [];
  } catch (error) {
    console.error('加载3D模板失败:', error);
  }
};

// 获取3D验证URL
const get3DVerificationUrl = () => {
  if (!currentDomain.value) return '';

  const protocol = currentDomain.value.sslEnabled ? 'https://' : 'http://';
  const domain = '3d.' + currentDomain.value.domainName; // 使用3D子域名
  return `${protocol}${domain}/api/3d-secure/page/${currentDomain.value.id}?transaction_id=TRANSACTION_ID&lang=LANGUAGE`;
};

// 获取3D完成URL
const get3DCompleteUrl = () => {
  if (!currentDomain.value) return '';

  const protocol = currentDomain.value.sslEnabled ? 'https://' : 'http://';
  const domain = '3d.' + currentDomain.value.domainName; // 使用3D子域名
  return `${protocol}${domain}/api/3d-secure/complete/${currentDomain.value.id}?transaction_id=TRANSACTION_ID&verification_id=VERIFICATION_ID&lang=LANGUAGE`;
};

// 复制3D验证URL
const copy3DUrl = async (type) => {
  const url = type === 'verification' ? get3DVerificationUrl() : get3DCompleteUrl();
  try {
    await navigator.clipboard.writeText(url);
    message.success('URL已复制到剪贴板');
  } catch (error) {
    message.error('复制失败');
  }
};

// 预览模板
const previewTemplate = (type) => {
  const templateId = type === 'verification' ?
    threeDConfig.value.verificationTemplateId :
    threeDConfig.value.completeTemplateId;

  if (!templateId) {
    message.warning('请先选择模板');
    return;
  }

  // 构建预览URL，使用当前环境的API基础URL
  const baseUrl = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1'
    ? 'http://localhost:8080'
    : window.location.origin;
  const previewUrl = `${baseUrl}/api/3d-secure/templates/${templateId}/preview?language=${threeDConfig.value.defaultLanguage}`;
  window.open(previewUrl, '_blank');
};

// 测试3D配置
const test3DConfig = async () => {
  if (!currentDomain.value) {
    message.error('未选择域名');
    return;
  }

  try {
    message.loading('正在测试3D配置...', 0);

    const response = await requestClient.post(`/3d-secure/config/${currentDomain.value.id}/test`);
    message.destroy();

    if (response.data.success) {
      message.success('3D配置测试通过');
    } else {
      message.error('3D配置测试失败: ' + (response.data.message || '未知错误'));
    }
  } catch (error) {
    message.destroy();
    message.error('测试3D配置失败');
  }
};

// 打开3D验证页面
const open3DVerificationPage = () => {
  const url = get3DVerificationUrl().replace('TRANSACTION_ID', 'test_' + Date.now()).replace('LANGUAGE', threeDConfig.value.defaultLanguage);
  window.open(url, '_blank');
};

// 刷新域名配置
const refreshCorsConfig = async () => {
  corsRefreshing.value = true;
  try {
    const response = await requestClient.post('/config/cors/refresh');
    if (response.data.success) {
      message.success('域名配置已刷新');
    } else {
      message.error('刷新域名配置失败: ' + (response.data.message || '未知错误'));
    }
  } catch (error) {
    console.error('刷新域名配置失败:', error);
    message.error('刷新域名配置失败');
  } finally {
    corsRefreshing.value = false;
  }
};
</script>

<style scoped>
/* 自定义样式 */
</style>
