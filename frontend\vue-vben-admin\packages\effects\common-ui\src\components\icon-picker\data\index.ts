interface IconifyResponse {
  prefix: string;
  total: number;
  title: string;
  uncategorized?: string[];
  categories?: Record<string, string[]>;
  aliases?: Record<string, string>;
}

/**
 * 本地图标数据映射
 */
const LOCAL_ICON_DATA: Record<string, () => Promise<IconifyResponse>> = {
  'ant-design': async () => {
    const data = await import('./ant-design.json');
    return data.default || data;
  },
  'mdi': async () => {
    const data = await import('./mdi.json');
    return data.default || data;
  },
  'lucide': async () => {
    const data = await import('./lucide.json');
    return data.default || data;
  },
  'carbon': async () => {
    const data = await import('./carbon.json');
    return data.default || data;
  },
  'mdi-light': async () => {
    const data = await import('./mdi-light.json');
    return data.default || data;
  },
  'svg-spinners': async () => {
    const data = await import('./svg-spinners.json');
    return data.default || data;
  },
  'line-md': async () => {
    const data = await import('./line-md.json');
    return data.default || data;
  },
  'tabler': async () => {
    const data = await import('./tabler.json');
    return data.default || data;
  },
};

/**
 * 加载本地图标数据
 */
export async function loadLocalIconData(prefix: string): Promise<IconifyResponse | null> {
  const loader = LOCAL_ICON_DATA[prefix];
  if (!loader) {
    return null;
  }
  
  try {
    return await loader();
  } catch (error) {
    console.error(`Failed to load local data for prefix: ${prefix}:`, error);
    return null;
  }
}

/**
 * 检查是否有本地数据
 */
export function hasLocalIconData(prefix: string): boolean {
  return prefix in LOCAL_ICON_DATA;
}

/**
 * 获取所有支持的图标集列表
 */
export function getSupportedIconPrefixes(): string[] {
  return Object.keys(LOCAL_ICON_DATA);
}

/**
 * 获取支持的图标集信息
 */
export function getSupportedIconInfo(): Array<{ prefix: string; title: string }> {
  return [
    { prefix: 'ant-design', title: 'Ant Design Icons' },
    { prefix: 'mdi', title: 'Material Design Icons' },
    { prefix: 'lucide', title: 'Lucide Icons' },
    { prefix: 'carbon', title: 'Carbon Icons' },
    { prefix: 'mdi-light', title: 'Material Design Icons Light' },
    { prefix: 'svg-spinners', title: 'SVG Spinners' },
    { prefix: 'line-md', title: 'Line MD Icons' },
    { prefix: 'tabler', title: 'Tabler Icons' },
  ];
}
