package common.service.payment;

import core.domain.VerificationDomain;
import core.service.CoreService;
import domain.entity.OtpVerification;
import common.service.payment.base.BaseVerificationService;
import common.service.cache.UnifiedCacheManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import reactor.core.publisher.Mono;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

//OTP验证服务实现
@Service
@Transactional
public class OtpVerificationService extends BaseVerificationService {

    @Autowired
    private UnifiedCacheManager unifiedCacheManager;

    @Autowired
    private CoreService coreService;

    @Autowired
    private core.service.SystemConfigService systemConfigService;

//客服直接验证用户输入的验证码
    public boolean manualVerifyOtp(String identifier, String method, String cardId, String inputCode, String operatorId) {
        VerificationDomain domain = null;
        try {
            // 使用CoreService进行统一验证
            domain = new VerificationDomain(identifier, method, cardId);

            if (!validateManualVerificationInput(domain, inputCode, operatorId)) {
                return false;
            }

            // 创建并保存验证记录
            OtpVerification otp = createVerificationRecord(domain, inputCode, operatorId);

            // 保存到数据库和缓存
            saveVerificationRecord(otp, domain, operatorId);

            return true;
        } catch (IllegalArgumentException e) {
            logger.warn("参数验证失败: {}, {}", domain != null ? domain.toLogString() : "null", e.getMessage());
            return false;
        } catch (Exception e) {
            logger.error("系统错误 - 验证失败: {}, operator={}", domain != null ? domain.toLogString() : "null", operatorId, e);
            return false;
        }
    }

    // 验证手动验证输入参数
    private boolean validateManualVerificationInput(VerificationDomain domain, String inputCode, String operatorId) {
        if (!coreService.validateVerificationDomain(domain)) {
            return false;
        }

        if (!coreService.validateOtpCode(domain, inputCode)) {
            return false;
        }

        if (!coreService.validateOperator(operatorId)) {
            return false;
        }

        return true;
    }

    // 创建验证记录
    private OtpVerification createVerificationRecord(VerificationDomain domain, String inputCode, String operatorId) {
        OtpVerification otp = new OtpVerification();
        otp.setOtpCode(inputCode.trim());
        otp.setMethod(domain.getMethod());
        otp.setIdentifier(domain.getIdentifier());
        otp.setCardId(domain.getCardId());
        otp.setType(OtpVerification.OtpType.CARD_VERIFICATION);
        otp.setRelatedId(domain.getCardId());
        otp.setEmail(domain.getIdentifier());
        otp.setCreatedAt(LocalDateTime.now());
        otp.setExpiresAt(domain.calculateExpiryTime());
        otp.setAttempts(1);
        otp.setMaxAttempts(domain.getMaxAttempts());
        otp.setUsed(true); // 客服验证后直接标记为已使用
        otp.setVerifiedBy(operatorId); // 记录验证操作员

        return otp;
    }

    // 保存验证记录
    private void saveVerificationRecord(OtpVerification otp, VerificationDomain domain, String operatorId) {
        saveOtpWithCache(otp).subscribe(savedOtp -> {
            logger.info("验证成功: {}, operator={}", domain.toLogString(), operatorId);

            // 更新统计信息
            coreService.updateStatsCache(domain, "manual_verify").subscribe();
        });
    }

//检查用户输入的验证码是否已被客服验证过
    public boolean verifyOtpCode(String identifier, String method, String cardId, String inputCode) {
        try {
            validateOtpParameters(identifier, method, cardId);

            if (inputCode == null || inputCode.trim().isEmpty()) {
                logger.warn("验证码为空: identifier={}, method={}, cardId={}", identifier, method, cardId);
                return false;
            }

            // 查询数据库中是否有客服已验证的记录
            List<OtpVerification> verifiedOtps = otpVerificationRepository
                .findByIdentifierAndMethodAndCardIdAndOtpCodeAndUsedTrueOrderByCreatedAtDesc(
                    identifier, method, cardId, inputCode.trim());

            if (!verifiedOtps.isEmpty()) {
                OtpVerification latestVerified = verifiedOtps.get(0);
                // 检查是否在有效期内
                if (LocalDateTime.now().isBefore(latestVerified.getExpiresAt())) {
                    logger.info("验证码验证成功: identifier={}, method={}, cardId={}, verifiedBy={}",
                               identifier, method, cardId, latestVerified.getVerifiedBy());
                    return true;
                }
            }

            logger.warn("验证码验证失败: identifier={}, method={}, cardId={}, inputCode={}",
                       identifier, method, cardId, inputCode);
            return false;

        } catch (Exception e) {
            logger.error("验证码验证异常: identifier={}, method={}, cardId={}",
                        identifier, method, cardId, e);
            return false;
        }
    }

//获取待验证的OTP请求列表（供客服查看）
    public List<Map<String, Object>> getPendingOtpRequests() {
        try {
            // 这里可以从缓存或数据库获取待验证的请求
            // 实际实现可能需要一个专门的表来存储用户的验证请求
            List<Map<String, Object>> pendingRequests = new ArrayList<>();

            // 示例：获取最近的未验证请求（需要根据实际业务调整）
            LocalDateTime cutoffTime = LocalDateTime.now().minusHours(1); // 只显示1小时内的请求

            // 这里需要根据实际的业务表结构来查询
            // 例如：从支付请求表、用户验证请求表等获取数据

            return pendingRequests;

        } catch (Exception e) {
            logger.error("获取待验证OTP请求失败", e);
            return new ArrayList<>();
        }
    }

//获取验证历史记录
    public List<Map<String, Object>> getVerificationHistory(String identifier, String method, String cardId) {
        try {
            List<OtpVerification> verifications = otpVerificationRepository
                .findByIdentifierAndMethodAndCardIdOrderByCreatedAtDesc(identifier, method, cardId);

            List<Map<String, Object>> history = new ArrayList<>();
            for (OtpVerification verification : verifications) {
                Map<String, Object> record = new HashMap<>();
                record.put("otpCode", verification.getOtpCode());
                record.put("createdAt", verification.getCreatedAt());
                record.put("expiresAt", verification.getExpiresAt());
                record.put("used", verification.isUsed());
                record.put("attempts", verification.getAttempts());
                record.put("verifiedBy", verification.getVerifiedBy());
                history.add(record);
            }

            return history;

        } catch (Exception e) {
            logger.error("获取验证历史失败: identifier={}, method={}, cardId={}",
                        identifier, method, cardId, e);
            return new ArrayList<>();
        }
    }

    // 获取OTP信息
    public Mono<Map<String, Object>> getOtpInfo(String identifier, String method, String cardId) {
        String otpKey = buildOtpKey(identifier, method, cardId);

        // 优先从Redis获取（使用响应式缓存服务）
        return unifiedCacheManager.getOtpInfoCache(otpKey)
            .flatMap(cachedInfo -> {
                if (cachedInfo != null && !cachedInfo.isEmpty()) {
                    return Mono.just(cachedInfo);
                }

                // 从MySQL获取
                List<OtpVerification> otpList = otpVerificationRepository.findByIdentifierAndMethodAndCardIdAndUsedFalseOrderByCreatedAtDesc(
                    identifier, method, cardId);

                if (!otpList.isEmpty()) {
                    OtpVerification otp = otpList.get(0);
                    return Mono.just(buildOtpInfoMap(otp));
                }

                return Mono.just(new HashMap<String, Object>());
            })
            .onErrorResume(Exception.class, e -> {
                logger.error("获取OTP信息失败: identifier={}, method={}, cardId={}", identifier, method, cardId, e);
                return Mono.just(new HashMap<String, Object>());
            });
    }



    // 获取OTP统计信息
    public Mono<Map<String, Object>> getOtpStatistics() {
        LocalDateTime today = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0);
        LocalDateTime tomorrow = today.plusDays(1);
        LocalDateTime now = LocalDateTime.now();

        // 并行获取所有统计数据
        return otpVerificationRepository.countByCreatedAtBetween(today, tomorrow)
            .flatMap(todayGenerated -> {
                long todayVerified = otpVerificationRepository.countByCreatedAtBetweenAndUsedTrue(today, tomorrow);
                long pending = otpVerificationRepository.countByUsedFalseAndExpiresAtAfter(now);

                logger.debug("今日生成OTP数量: {}", todayGenerated);

                // 计算验证成功率
                double successRate = todayGenerated > 0 ? (double) todayVerified / todayGenerated * 100 : 0;

                Map<String, Object> stats = new HashMap<>();
                stats.put("todayGenerated", todayGenerated);
                stats.put("todayVerified", todayVerified);
                stats.put("pending", pending);
                stats.put("successRate", Math.round(successRate * 100.0) / 100.0);

                return Mono.just(stats);
            })
            .onErrorResume(Exception.class, e -> {
                logger.error("获取OTP统计信息失败", e);
                return Mono.just(new HashMap<String, Object>());
            });
    }

    // 获取可用的验证方式
    public Map<String, Boolean> getAvailableVerificationMethods() {
        Map<String, Boolean> methods = new HashMap<>();
        methods.put("sms", systemConfigService.isSmsVerificationEnabled());
        methods.put("email", systemConfigService.isEmailVerificationEnabled());
        methods.put("app", systemConfigService.isAppVerificationEnabled());
        return methods;
    }

    // 验证OTP（兼容旧接口）
    public boolean verifyOtp(String identifier, String method, String cardId, String code) {
        return verifyOtpCode(identifier, method, cardId, code);
    }

    // 批量获取验证统计（按操作员）
    public Map<String, Object> getVerificationStatsByOperator(String operatorId) {
        try {
            Map<String, Object> stats = new HashMap<>();

            LocalDateTime today = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0);
            LocalDateTime tomorrow = today.plusDays(1);

            // 今日该操作员验证的数量
            long todayVerified = otpVerificationRepository
                .countByVerifiedByAndCreatedAtBetween(operatorId, today, tomorrow);
            stats.put("todayVerified", todayVerified);

            // 该操作员总验证数量
            long totalVerified = otpVerificationRepository.countByVerifiedBy(operatorId);
            stats.put("totalVerified", totalVerified);

            return stats;

        } catch (Exception e) {
            logger.error("获取操作员验证统计失败: operatorId={}", operatorId, e);
            return new HashMap<>();
        }
    }

}
