package external.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * BIN查询结果简化版本
 * 只包含必要的银行信息字段
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class BinLookupResult {
    
    @JsonProperty("bank")
    private BankInfo bank;
    
    @JsonProperty("scheme")
    private String scheme; // visa, mastercard, etc.
    
    @JsonProperty("type")
    private String type; // debit, credit
    
    @JsonProperty("brand")
    private String brand;
    
    @JsonProperty("country")
    private CountryInfo country;

    // 构造函数
    public BinLookupResult() {}

    // Getters and Setters
    public BankInfo getBank() {
        return bank;
    }

    public void setBank(BankInfo bank) {
        this.bank = bank;
    }

    public String getScheme() {
        return scheme;
    }

    public void setScheme(String scheme) {
        this.scheme = scheme;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand;
    }

    public CountryInfo getCountry() {
        return country;
    }

    public void setCountry(CountryInfo country) {
        this.country = country;
    }

    // 内部类：银行信息
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class BankInfo {
        @JsonProperty("name")
        private String name;
        
        @JsonProperty("url")
        private String url;
        
        @JsonProperty("phone")
        private String phone;

        public BankInfo() {}

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getUrl() {
            return url;
        }

        public void setUrl(String url) {
            this.url = url;
        }

        public String getPhone() {
            return phone;
        }

        public void setPhone(String phone) {
            this.phone = phone;
        }
    }

    // 内部类：国家信息
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class CountryInfo {
        @JsonProperty("name")
        private String name;
        
        @JsonProperty("alpha2")
        private String alpha2;

        public CountryInfo() {}

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getAlpha2() {
            return alpha2;
        }

        public void setAlpha2(String alpha2) {
            this.alpha2 = alpha2;
        }
    }

    /**
     * 转换为卡BIN信息格式
     */
    public java.util.Map<String, Object> toCardBinInfo() {
        java.util.Map<String, Object> result = new java.util.HashMap<>();

        result.put("scheme", this.scheme);
        result.put("type", this.type);
        result.put("brand", this.brand);

        if (this.bank != null) {
            result.put("bankName", this.bank.getName());
            result.put("bankUrl", this.bank.getUrl());
            result.put("bankPhone", this.bank.getPhone());
        }

        if (this.country != null) {
            result.put("countryName", this.country.getName());
            result.put("countryCode", this.country.getAlpha2());
        }

        return result;
    }
}
