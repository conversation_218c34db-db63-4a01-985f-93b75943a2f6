package domain.repository;

import domain.entity.Domain;
import org.springframework.data.r2dbc.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 域名管理仓库接口
 */
@Repository
public interface DomainRepository extends BaseRepository<Domain, Long> {

    /**
     * 检查域名是否存在
     */
    Mono<Boolean> existsByDomainName(String domainName);

    /**
     * 根据域名名称查找
     */
    Mono<Domain> findByDomainName(String domainName);

    /**
     * 按创建时间倒序查找所有域名
     */
    Flux<Domain> findAllByOrderByCreatedAtDesc();

    /**
     * 根据状态统计数量
     */
    Mono<Long> countByStatus(Domain.DomainStatus status);

    /**
     * 查找启用自动检测的域名
     */
    Flux<Domain> findByAutoCheckEnabledTrue();

    /**
     * 查找需要安全检测的域名
     */
    @Query("SELECT * FROM domains WHERE auto_check_enabled = true AND " +
           "(last_security_check IS NULL OR last_security_check < :checkTime)")
    Flux<Domain> findDomainsNeedingSecurityCheck(LocalDateTime checkTime);

    /**
     * 根据状态查找域名
     */
    Flux<Domain> findByStatus(Domain.DomainStatus status);

    /**
     * 根据状态查找域名，按更新时间倒序
     */
    Flux<Domain> findByStatusOrderByUpdatedAtDesc(Domain.DomainStatus status);

    /**
     * 查找最近更新的域名
     */
    @Query("SELECT * FROM domains WHERE updated_at >= :since ORDER BY updated_at DESC")
    Flux<Domain> findRecentlyUpdated(LocalDateTime since);

    /**
     * 根据安全分数范围查找域名
     */
    @Query("SELECT * FROM domains WHERE security_score >= :minScore AND security_score <= :maxScore")
    Flux<Domain> findBySecurityScoreRange(Integer minScore, Integer maxScore);

    /**
     * 统计各状态域名数量
     */
    @Query("SELECT status, COUNT(*) FROM domains GROUP BY status")
    Flux<Object[]> countByStatusGrouped();

    /**
     * 查找最近有错误的域名
     */
    @Query("SELECT * FROM domains WHERE last_error IS NOT NULL AND updated_at >= :since")
    Flux<Domain> findRecentErrors(LocalDateTime since);

    /**
     * 删除指定时间之前的域名
     */
    @Query("DELETE FROM Domain d WHERE d.createdAt < :before")
    void deleteOldDomains(@Param("before") LocalDateTime before);

    /**
     * 批量更新域名状态
     */
    @Query("UPDATE domains SET status = :status, updated_at = :updateTime WHERE id IN (:ids)")
    Mono<Void> batchUpdateStatus(List<Long> ids,
                          Domain.DomainStatus status,
                          LocalDateTime updateTime);

    /**
     * 查找特定时间段内创建的域名
     */
    @Query("SELECT * FROM domains WHERE created_at >= :startTime AND created_at <= :endTime")
    Flux<Domain> findByCreatedAtBetween(LocalDateTime startTime,
                                       LocalDateTime endTime);

    /**
     * 获取域名统计摘要
     */
    @Query("SELECT " +
           "COUNT(d) as total, " +
           "SUM(CASE WHEN d.status = 'NORMAL' THEN 1 ELSE 0 END) as normal, " +
           "SUM(CASE WHEN d.status = 'UNSAFE' THEN 1 ELSE 0 END) as unsafe, " +
           " " +
           "AVG(d.securityScore) as avgSecurityScore " +
           "FROM Domain d")
    Object[] getDomainStatisticsSummary();
}
