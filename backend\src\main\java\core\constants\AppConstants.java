package core.constants;

/**
 * 应用常量
 */
public final class AppConstants {

    private AppConstants() {
        // 私有构造函数，防止实例化
    }

    /**
     * 系统配置相关常量
     */
    public static final class System {
        public static final String NAME = "BakaOTP";
        public static final String VERSION = "2.0.2";
        public static final String DEFAULT_LOG_LEVEL = "INFO";
        public static final String DEFAULT_CURRENCY = "USD";
        public static final String DEFAULT_COUNTRY_CODE = "US";
        public static final String DEFAULT_COUNTRY_NAME = "United States";
        public static final String DEFAULT_CONTACT = "-";
    }

    /**
     * 安全状态相关常量
     */
    public static final class SecurityStatus {
        public static final String SAFE = "safe";
        public static final String UNSAFE = "unsafe";
        public static final String PARTIALLY_UNSAFE = "partially_unsafe";
        public static final String UNCOMMON_FILES = "uncommon_files";
        public static final String NO_DATA = "no_data";
        public static final String ERROR = "error";
        public static final String UNKNOWN = "unknown";
    }

    /**
     * 安全状态码常量
     */
    public static final class SecurityStatusCode {
        public static final int SAFE = 1;
        public static final int UNSAFE = 2;
        public static final int PARTIALLY_UNSAFE = 3;
        public static final int UNCOMMON_FILES = 5;
        public static final int NO_DATA = 6;
    }

    /**
     * 安全状态描述常量
     */
    public static final class SecurityStatusDescription {
        public static final String SAFE = "No unsafe content found";
        public static final String UNSAFE = "This site is unsafe";
        public static final String PARTIALLY_UNSAFE = "Some pages on this site are unsafe";
        public static final String UNCOMMON_FILES = "Hosts uncommon files";
        public static final String NO_DATA = "No available data";
        public static final String UNKNOWN = "Unknown status";
        public static final String FALLBACK = "Unable to check with Google Transparency Report";
    }

    /**
     * 颜色代码常量
     */
    public static final class StatusColor {
        public static final String GREEN = "green";
        public static final String RED = "red";
        public static final String YELLOW = "yellow";
        public static final String PURPLE = "purple";
        public static final String GRAY = "gray";
    }

    /**
     * 风险等级常量
     */
    public static final class RiskLevel {
        public static final String LOW = "low";
        public static final String MEDIUM = "medium";
        public static final String HIGH = "high";
        public static final String UNKNOWN = "unknown";
    }

    /**
     * 威胁类型常量
     */
    public static final class ThreatType {
        public static final String REDIRECTS_TO_HARMFUL = "redirectsToHarmful";
        public static final String INSTALLS_MALWARE = "installsMalware";
        public static final String SOCIAL_ENGINEERING = "socialEngineering";
        public static final String UNWANTED_CONTENT = "unwantedContent";
        public static final String UNCOMMON_DOWNLOADS = "uncommonDownloads";
        public static final String MALWARE = "malware";
        public static final String PHISHING = "phishing";
        public static final String UNWANTED_SOFTWARE = "unwantedSoftware";
        public static final String SUSPICIOUS_ACTIVITY = "suspiciousActivity";
    }

    /**
     * 验证状态常量
     */
    public static final class VerificationStatus {
        public static final String PENDING = "pending";
        public static final String VERIFIED = "verified";
        public static final String REJECTED = "rejected";
        public static final String EXPIRED = "expired";
        public static final String FAILED = "failed";
        public static final String NOT_FOUND = "not_found";
    }

    /**
     * 验证状态描述常量
     */
    public static final class VerificationStatusDescription {
        public static final String PENDING = "等待验证";
        public static final String VERIFIED = "验证成功";
        public static final String REJECTED = "验证被拒绝";
        public static final String EXPIRED = "验证已过期";
        public static final String FAILED = "验证失败";
        public static final String NOT_FOUND = "未找到验证设置";
    }

    /**
     * WebSocket消息类型常量
     */
    public static final class WebSocketMessageType {
        public static final String VERIFICATION_RESULT = "verification_result";
        public static final String VERIFICATION_COMPLETE = "verification_complete";
        public static final String FRONTEND_UPDATE_COMPLETE = "frontend_update_complete";
        public static final String DOMAIN_STATUS_UPDATE = "domain_status_update";
        public static final String SECURITY_CHECK_RESULT = "security_check_result";
    }

    /**
     * 配置键名常量
     */
    public static final class ConfigKey {
        // 系统配置
        public static final String SYSTEM_NAME = "system.name";
        public static final String SYSTEM_VERSION = "system.version";
        public static final String MAINTENANCE_MODE = "system.maintenance_mode";
        public static final String DEBUG_MODE = "system.debug_mode";
        public static final String LOG_LEVEL = "system.log_level";

        // 安全配置
        public static final String MAX_LOGIN_ATTEMPTS = "security.max_login_attempts";
        public static final String SESSION_TIMEOUT = "security.session_timeout";
        public static final String JWT_EXPIRATION = "security.jwt_expiration";
        public static final String ENABLE_2FA = "security.enable_2fa";
        public static final String PASSWORD_MIN_LENGTH = "security.password_min_length";

        public static final String THREE_D_SECURE_ENABLED = "security.3d_secure_enabled";
        public static final String THREE_D2_ENABLED = "security.3d2_enabled";

        // 支付配置
        public static final String PAYMENT_TIMEOUT = "payment.timeout";
        public static final String PAYMENT_DEFAULT_CURRENCY = "payment.default_currency";
        public static final String MAX_AMOUNT = "payment.max_amount";
        public static final String MIN_AMOUNT = "payment.min_amount";
        public static final String BIN_BLACKLIST = "payment.bin_blacklist";
        public static final String COUNTRY_RESTRICTIONS = "payment.country_restrictions";
        public static final String CARD_HEADER_FILTER = "payment.card_header_filter";

        // OTP配置
        public static final String OTP_EXPIRY_MINUTES = "otp.expiry_minutes";
        public static final String OTP_MAX_ATTEMPTS = "otp.max_attempts";
        public static final String OTP_CODE_LENGTH = "otp.code_length";
        public static final String OTP_AUTO_GENERATE = "otp.auto_generate";
    }

    /**
     * 错误消息常量
     */
    public static final class ErrorMessage {
        public static final String TRANSACTION_ID_EMPTY = "交易ID不能为空";
        public static final String VERIFICATION_CODE_EMPTY = "验证码不能为空";
        public static final String VERIFICATION_SETTING_NOT_FOUND = "未找到验证设置";
        public static final String VERIFICATION_ALREADY_COMPLETED = "该交易已经验证过了";
        public static final String VERIFICATION_EXPIRED = "验证已过期";
        public static final String VERIFICATION_CODE_INVALID = "验证码无效或已过期";
        public static final String VERIFICATION_NOT_COMPLETED = "验证尚未完成";
        public static final String DOMAIN_NOT_FOUND = "域名不存在";
        public static final String NO_ACTIVE_DOMAINS = "没有找到活跃的域名";
        public static final String SYSTEM_INTERNAL_ERROR = "系统内部错误";
        public static final String OPERATION_FAILED = "操作失败，请稍后重试";
        public static final String PERMISSION_DENIED = "权限不足";
        public static final String FUNCTION_NOT_IMPLEMENTED = "功能暂未实现";
        public static final String PARAMETER_ERROR = "参数错误";
    }

    /**
     * 成功消息常量
     */
    public static final class SuccessMessage {
        public static final String VERIFICATION_SUCCESS = "验证成功";
        public static final String VERIFICATION_COMPLETE = "3D验证完成";
        public static final String FRONTEND_UPDATE_SUCCESS = "前台更新成功";
        public static final String FRONTEND_UPDATE_COMPLETE = "前台更新完成";
        public static final String FRONTEND_FILES_GENERATED = "前台文件生成成功";
        public static final String DEPLOYMENT_SUCCESS = "部署成功";
        public static final String VERIFICATION_SUCCESS_DESC = "验证成功";
        public static final String OPERATION_SUCCESS = "操作成功";
    }

    /**
     * 时间相关常量 - 重新组织
     */
    public static final class Time {
        // 认证相关超时
        public static final int DEFAULT_SESSION_TIMEOUT = 3600;
        public static final int DEFAULT_JWT_EXPIRATION = 86400;

        // 支付相关超时
        public static final int DEFAULT_TIMEOUT_SECONDS = 300;

        // OTP验证相关
        public static final int DEFAULT_OTP_EXPIRY_MINUTES = 10;
        public static final int DEFAULT_MAX_ATTEMPTS = 3;
        public static final int DEFAULT_CODE_LENGTH = 6;

        // 域名检查相关
        public static final int DOMAIN_RECHECK_INTERVAL_MINUTES = 15;
    }

    /**
     * 验证相关常量 - 新增分组
     */
    public static final class Verification {
        // 验证方法
        public static final String METHOD_SMS = "sms";
        public static final String METHOD_EMAIL = "email";
        public static final String METHOD_APP = "app";

        // 验证状态
        public static final String STATUS_PENDING = "pending";
        public static final String STATUS_VERIFIED = "verified";
        public static final String STATUS_FAILED = "failed";
        public static final String STATUS_EXPIRED = "expired";

        // 验证类型
        public static final String TYPE_PAYMENT = "payment";
        public static final String TYPE_CARD = "card";
        public static final String TYPE_ADMIN = "admin";
    }

    /**
     * HTTP状态码常量
     */
    public static final class HttpStatus {
        public static final int OK = 200;
        public static final int BAD_REQUEST = 400;
        public static final int FORBIDDEN = 403;
        public static final int NOT_FOUND = 404;
        public static final int INTERNAL_SERVER_ERROR = 500;
        public static final int NOT_IMPLEMENTED = 501;
    }

    /**
     * 缓存相关常量
     */
    public static final class Cache {
        public static final String SECURITY_CHECK = "securityCheck";
        public static final String BIN_LOOKUP = "binLookup";
    }


}
