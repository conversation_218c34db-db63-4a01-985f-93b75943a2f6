/* BakaOTP Loading Screen - 参考3D安全模板 */
.g-loading-mask {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: #f8f9fa;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    transition: opacity 0.5s ease-out;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    pointer-events: none;
    opacity: 0;
}

.g-loading-mask.show {
    opacity: 1;
    pointer-events: initial;
}

.g-loading-mask.fade-out {
    opacity: 0;
    pointer-events: none;
}

.g-loading-mask .loading {
    width: 200px;
    height: 120px;
}

/* 加载动画卡片样式 - 参考3D安全模板 */
.loading {
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    animation: cardPulse 2s ease-in-out infinite;
    margin: 0 auto;
    transition: all 0.3s ease-in-out;
}

/* 卡片类型加载动画 */
.loading-visa {
    background: linear-gradient(45deg, #1A1F71, #0066CC);
    background-size: 400% 400%;
    animation: visaGradient 2s ease infinite;
    border-radius: 8px;
}

.loading-mastercard {
    background-image: url('../images/MASTERCARD.svg');
}

.loading-amex {
    background: linear-gradient(45deg, #006FCF, #0056b3);
    background-size: 400% 400%;
    animation: amexGradient 2s ease infinite;
    border-radius: 8px;
}

/* 通用卡片加载动画 */
.loading-generic {
    background: linear-gradient(45deg, #007cba, #005a87);
    background-size: 400% 400%;
    animation: genericGradient 2s ease infinite;
    border-radius: 8px;
}

/* Visa渐变动画 */
@keyframes visaGradient {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

/* 通用渐变动画 */
@keyframes genericGradient {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

/* 通用卡片脉冲动画 */
@keyframes cardPulse {
    0%, 100% {
        opacity: 0.6;
        transform: scale(1);
    }
    50% {
        opacity: 1;
        transform: scale(1.05);
    }
}

/* AMEX渐变动画 */
@keyframes amexGradient {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

/* 加载旋转器 */
.spinner {
    display: inline-block;
    width: 40px;
    height: 40px;
    border: 4px solid #e3e3e3;
    border-radius: 50%;
    border-top-color: #007cba;
    animation: spin 1s ease-in-out infinite;
    margin: 20px auto;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

.modal .loading {
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0
}

.modal .loading {
    background-color: #fff
}