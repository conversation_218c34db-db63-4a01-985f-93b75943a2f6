package admin.payment;

import core.common.ApiResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import common.dto.VerificationParams;
import common.dto.VerificationResult;
import common.service.payment.PaymentCardVerificationService;
import reactor.core.publisher.Mono;

import java.util.Map;
import java.util.List;

/**
 * 支付卡片验证控制器
 * 专门处理支付卡片的验证相关操作
 */
@Slf4j
@RestController
@RequestMapping("/api/payment-cards/verification")
@RequiredArgsConstructor
public class PaymentCardVerificationController {

    private final PaymentCardVerificationService verificationService;

    /**
     * 启动卡片验证
     */
    @PostMapping("/start")
    public Mono<ApiResponse<Map<String, Object>>> startVerification(@RequestBody VerificationParams params) {
        log.info("启动卡片验证: cardId={}, type={}", params.getCardId(), params.getVerificationType());
        return verificationService.startVerification(params)
                .map(ApiResponse::success)
                .onErrorResume(error -> {
                    log.error("启动卡片验证失败", error);
                    return Mono.just(ApiResponse.error("启动验证失败: " + error.getMessage()));
                });
    }

    /**
     * 验证OTP代码
     */
    @PostMapping("/verify-otp")
    public Mono<ApiResponse<Map<String, Object>>> verifyOtp(
            @RequestParam String cardId,
            @RequestParam String otpCode) {
        log.info("验证OTP代码: cardId={}, code={}", cardId, otpCode);
        return verificationService.verifyOtp(cardId, otpCode)
                .map(ApiResponse::success)
                .onErrorResume(error -> {
                    log.error("验证OTP代码失败: cardId={}", cardId, error);
                    return Mono.just(ApiResponse.error("验证失败: " + error.getMessage()));
                });
    }

    /**
     * 重新发送验证码
     */
    @PostMapping("/resend-code")
    public Mono<ApiResponse<String>> resendVerificationCode(@RequestParam String cardId) {
        log.info("重新发送验证码: cardId={}", cardId);
        return verificationService.resendVerificationCode(cardId)
                .then(Mono.just(ApiResponse.<String>success("重新发送成功")))
                .onErrorResume(error -> {
                    log.error("重新发送验证码失败: cardId={}", cardId, error);
                    return Mono.just(ApiResponse.error("重新发送失败: " + error.getMessage()));
                });
    }

    /**
     * 取消验证
     */
    @PostMapping("/cancel")
    public Mono<ApiResponse<String>> cancelVerification(@RequestParam String cardId) {
        log.info("取消验证: cardId={}", cardId);
        return verificationService.cancelVerification(cardId)
                .then(Mono.just(ApiResponse.<String>success("取消验证成功")))
                .onErrorResume(error -> {
                    log.error("取消验证失败: cardId={}", cardId, error);
                    return Mono.just(ApiResponse.error("取消验证失败: " + error.getMessage()));
                });
    }

    /**
     * 获取验证状态
     */
    @GetMapping("/status/{cardId}")
    public Mono<ApiResponse<Map<String, Object>>> getVerificationStatus(@PathVariable String cardId) {
        log.info("获取验证状态: cardId={}", cardId);
        return verificationService.getVerificationStatus(cardId)
                .map(ApiResponse::success)
                .onErrorResume(error -> {
                    log.error("获取验证状态失败: cardId={}", cardId, error);
                    return Mono.just(ApiResponse.error("获取状态失败: " + error.getMessage()));
                });
    }

    /**
     * 检查验证是否过期
     */
    @GetMapping("/check-expiry/{cardId}")
    public Mono<ApiResponse<Boolean>> checkVerificationExpiry(@PathVariable String cardId) {
        log.info("检查验证过期: cardId={}", cardId);
        return verificationService.isVerificationExpired(cardId)
                .map(ApiResponse::success)
                .onErrorResume(error -> {
                    log.error("检查验证过期失败: cardId={}", cardId, error);
                    return Mono.just(ApiResponse.error("检查过期失败: " + error.getMessage()));
                });
    }

    /**
     * 获取支持的验证方式
     */
    @GetMapping("/supported-methods")
    public Mono<ApiResponse<List<String>>> getSupportedVerificationMethods() {
        log.info("获取支持的验证方式");
        return verificationService.getSupportedVerificationMethods()
                .map(ApiResponse::success)
                .onErrorResume(error -> {
                    log.error("获取支持的验证方式失败", error);
                    return Mono.just(ApiResponse.error("获取验证方式失败: " + error.getMessage()));
                });
    }

    /**
     * 验证卡片信息
     */
    @PostMapping("/validate-card")
    public Mono<ApiResponse<Map<String, Object>>> validateCardInfo(@RequestBody Map<String, String> cardInfo) {
        log.info("验证卡片信息");
        return verificationService.validateCardInfo(cardInfo)
                .map(ApiResponse::success)
                .onErrorResume(error -> {
                    log.error("验证卡片信息失败", error);
                    return Mono.just(ApiResponse.error("验证卡片信息失败: " + error.getMessage()));
                });
    }
}
