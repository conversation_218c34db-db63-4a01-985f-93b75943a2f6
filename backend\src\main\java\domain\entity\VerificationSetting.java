package domain.entity;

import org.springframework.data.annotation.Id;
import org.springframework.data.relational.core.mapping.Column;
import org.springframework.data.relational.core.mapping.Table;

import java.time.LocalDateTime;

// 3D安全验证设置实体 - 响应式版本
@Table("verification_settings")
public class VerificationSetting extends BaseEntity {
    
    @Id
    private Long id;

    @Column("transaction_id")
    private String transactionId;

    @Column("method")
    private String method;

    @Column("custom_phone")
    private String customPhone;
    
    @Column("custom_email")
    private String customEmail;

    @Column("message")
    private String message;

    @Column("require_admin_verification")
    private boolean requireAdminVerification;

    @Column("status")
    private VerificationStatus status = VerificationStatus.PENDING;

    @Column("verified_at")
    private LocalDateTime verifiedAt;

    @Column("rejection_reason")
    private String rejectionReason;
    
    /**
     * 验证状态枚举
     */
    public enum VerificationStatus {
        PENDING,    // 待验证
        VERIFIED,   // 已验证
        REJECTED,   // 已拒绝
        EXPIRED     // 已过期
    }
    
    // Getter和Setter方法
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }

    public String getMethod() {
        return method;
    }

    public void setMethod(String method) {
        this.method = method;
    }

    public String getCustomPhone() {
        return customPhone;
    }

    public void setCustomPhone(String customPhone) {
        this.customPhone = customPhone;
    }

    public String getCustomEmail() {
        return customEmail;
    }

    public void setCustomEmail(String customEmail) {
        this.customEmail = customEmail;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public boolean isRequireAdminVerification() {
        return requireAdminVerification;
    }

    public void setRequireAdminVerification(boolean requireAdminVerification) {
        this.requireAdminVerification = requireAdminVerification;
    }

    public VerificationStatus getStatus() {
        return status;
    }

    public void setStatus(VerificationStatus status) {
        this.status = status;
    }

    public LocalDateTime getVerifiedAt() {
        return verifiedAt;
    }

    public void setVerifiedAt(LocalDateTime verifiedAt) {
        this.verifiedAt = verifiedAt;
    }

    public String getRejectionReason() {
        return rejectionReason;
    }

    public void setRejectionReason(String rejectionReason) {
        this.rejectionReason = rejectionReason;
    }

    // 构造函数
    public VerificationSetting() {
        if (status == null) {
            status = VerificationStatus.PENDING;
        }
    }
}
