package common.facade;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import admin.controller.PaymentCardCrudController;
import admin.payment.PaymentCardStatisticsController;
import admin.payment.PaymentCardVerificationController;
import common.dto.VerificationParams;
import common.dto.VerificationResult;
import domain.entity.PaymentCard;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.LocalDate;
import java.util.Map;

/**
 * 支付卡片门面服务
 * 协调CRUD、验证和统计功能，提供统一的业务接口
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PaymentCardFacade {

    private final PaymentCardCrudController crudController;
    private final PaymentCardVerificationController verificationController;
    private final PaymentCardStatisticsController statisticsController;

    // ==================== CRUD操作门面 ====================

    /**
     * 创建支付卡片
     */
    public Mono<PaymentCard> createCard(PaymentCard card) {
        log.info("门面服务：创建支付卡片");
        return crudController.createCard(card)
                .map(response -> response.getData())
                .doOnSuccess(result -> log.info("支付卡片创建成功"))
                .doOnError(error -> log.error("支付卡片创建失败", error));
    }

    /**
     * 获取支付卡片
     */
    public Mono<PaymentCard> getCard(String cardId) {
        log.info("门面服务：获取支付卡片 {}", cardId);
        return crudController.getCardById(cardId)
                .map(response -> response.getData())
                .doOnSuccess(result -> log.debug("支付卡片获取成功"))
                .doOnError(error -> log.error("支付卡片获取失败", error));
    }

    /**
     * 更新支付卡片
     */
    public Mono<PaymentCard> updateCard(String cardId, PaymentCard card) {
        log.info("门面服务：更新支付卡片 {}", cardId);
        return crudController.updateCard(Long.parseLong(cardId), card)
                .map(response -> response.getData())
                .doOnSuccess(result -> log.info("支付卡片更新成功"))
                .doOnError(error -> log.error("支付卡片更新失败", error));
    }

    /**
     * 删除支付卡片
     */
    public Mono<Void> deleteCard(String cardId) {
        log.info("门面服务：删除支付卡片 {}", cardId);
        return crudController.deleteCard(Long.parseLong(cardId))
                .then()
                .doOnSuccess(result -> log.info("支付卡片删除成功"))
                .doOnError(error -> log.error("支付卡片删除失败", error));
    }

    /**
     * 获取所有支付卡片
     */
    public Flux<PaymentCard> getAllCards() {
        log.info("门面服务：获取所有支付卡片");
        return crudController.getCardsPaged(1, Integer.MAX_VALUE, null, null)
                .map(response -> response.getData())
                .flatMapMany(data -> {
                    // 从分页响应中提取卡片列表
                    @SuppressWarnings("unchecked")
                    java.util.List<PaymentCard> cardList = (java.util.List<PaymentCard>) data.get("list");
                    return reactor.core.publisher.Flux.fromIterable(cardList != null ? cardList : java.util.List.of());
                })
                .doOnComplete(() -> log.debug("所有支付卡片获取完成"))
                .doOnError(error -> log.error("获取所有支付卡片失败", error));
    }

    // ==================== 验证操作门面 ====================

    /**
     * 启动卡片验证流程
     */
    public Mono<Map<String, Object>> startCardVerification(VerificationParams params) {
        log.info("门面服务：启动卡片验证流程");
        return verificationController.startVerification(params)
                .map(response -> response.getData())
                .doOnSuccess(result -> log.info("卡片验证流程启动成功"))
                .doOnError(error -> log.error("卡片验证流程启动失败", error));
    }

    /**
     * 验证OTP代码
     */
    public Mono<Map<String, Object>> verifyOtpCode(String cardId, String otpCode) {
        log.info("门面服务：验证OTP代码");
        return verificationController.verifyOtp(cardId, otpCode)
                .map(response -> response.getData())
                .doOnSuccess(result -> log.info("OTP代码验证完成"))
                .doOnError(error -> log.error("OTP代码验证失败", error));
    }

    /**
     * 获取验证状态
     */
    public Mono<Map<String, Object>> getVerificationStatus(String cardId) {
        log.info("门面服务：获取验证状态 {}", cardId);
        return verificationController.getVerificationStatus(cardId)
                .map(response -> response.getData())
                .doOnSuccess(result -> log.debug("验证状态获取成功"))
                .doOnError(error -> log.error("验证状态获取失败", error));
    }

    /**
     * 取消验证
     */
    public Mono<Void> cancelVerification(String cardId) {
        log.info("门面服务：取消验证 {}", cardId);
        return verificationController.cancelVerification(cardId)
                .then()
                .doOnSuccess(result -> log.info("验证取消成功"))
                .doOnError(error -> log.error("验证取消失败", error));
    }

    // ==================== 统计操作门面 ====================

    /**
     * 获取卡片总数
     */
    public Mono<Long> getTotalCardCount() {
        log.info("门面服务：获取卡片总数");
        return statisticsController.getTotalCardCount()
                .map(response -> response.getData())
                .doOnSuccess(count -> log.debug("卡片总数: {}", count))
                .doOnError(error -> log.error("获取卡片总数失败", error));
    }

    /**
     * 获取按类型统计
     */
    public Mono<Map<String, Long>> getCardStatsByType() {
        log.info("门面服务：获取按类型统计");
        return statisticsController.getCardCountByType()
                .map(response -> response.getData())
                .doOnSuccess(stats -> log.debug("按类型统计获取成功"))
                .doOnError(error -> log.error("获取按类型统计失败", error));
    }

    /**
     * 获取验证成功率
     */
    public Mono<Double> getVerificationSuccessRate(LocalDate startDate, LocalDate endDate) {
        log.info("门面服务：获取验证成功率");
        return statisticsController.getVerificationSuccessRate(startDate, endDate)
                .map(response -> response.getData())
                .doOnSuccess(rate -> log.debug("验证成功率: {}%", rate * 100))
                .doOnError(error -> log.error("获取验证成功率失败", error));
    }

    /**
     * 获取综合统计报告
     */
    public Mono<Map<String, Object>> getComprehensiveStats() {
        log.info("门面服务：获取综合统计报告");
        return statisticsController.getStatisticsSummary()
                .map(response -> response.getData())
                .doOnSuccess(stats -> log.debug("综合统计报告获取成功"))
                .doOnError(error -> log.error("获取综合统计报告失败", error));
    }

    // ==================== 组合业务操作 ====================

    /**
     * 创建卡片并启动验证
     */
    public Mono<Map<String, Object>> createCardAndStartVerification(PaymentCard card, VerificationParams params) {
        log.info("门面服务：创建卡片并启动验证");
        
        return createCard(card)
                .flatMap(createdCard -> {
                    params.setCardId(String.valueOf(createdCard.getId()));
                    return startCardVerification(params);
                })
                .doOnSuccess(result -> log.info("卡片创建和验证启动成功"))
                .doOnError(error -> log.error("卡片创建和验证启动失败", error));
    }

    /**
     * 获取卡片详情和验证状态
     */
    public Mono<Map<String, Object>> getCardWithVerificationStatus(String cardId) {
        log.info("门面服务：获取卡片详情和验证状态");
        
        return Mono.zip(
                getCard(cardId),
                getVerificationStatus(cardId).onErrorReturn(Map.of())
        )
        .map(tuple -> {
            PaymentCard card = tuple.getT1();
            Map<String, Object> verificationStatus = tuple.getT2();
            
            Map<String, Object> result = new java.util.HashMap<>();
            result.put("card", card);
            result.put("verificationStatus", verificationStatus);
            return result;
        })
        .doOnSuccess(result -> log.debug("卡片详情和验证状态获取成功"))
        .doOnError(error -> log.error("获取卡片详情和验证状态失败", error));
    }

    /**
     * 批量处理卡片验证
     */
    public Mono<Map<String, Object>> batchProcessVerifications(String[] cardIds) {
        log.info("门面服务：批量处理卡片验证，数量: {}", cardIds.length);
        
        return reactor.core.publisher.Flux.fromArray(cardIds)
                .flatMap(cardId -> 
                    getVerificationStatus(cardId)
                        .map(status -> Map.entry(cardId, status))
                        .onErrorReturn(Map.entry(cardId, Map.of("error", "获取状态失败")))
                )
                .collectMap(Map.Entry::getKey, Map.Entry::getValue)
                .map(results -> {
                    Map<String, Object> summary = new java.util.HashMap<>();
                    summary.put("total", cardIds.length);
                    summary.put("results", results);
                    summary.put("processed", results.size());
                    return summary;
                })
                .doOnSuccess(summary -> log.info("批量验证处理完成"))
                .doOnError(error -> log.error("批量验证处理失败", error));
    }

    /**
     * 健康检查
     */
    public Mono<Map<String, Object>> healthCheck() {
        log.debug("门面服务：健康检查");
        
        return Mono.fromCallable(() -> {
            Map<String, Object> health = new java.util.HashMap<>();
            health.put("status", "UP");
            health.put("timestamp", java.time.LocalDateTime.now());
            health.put("services", Map.of(
                "crud", "UP",
                "verification", "UP", 
                "statistics", "UP"
            ));
            return health;
        })
        .doOnSuccess(health -> log.debug("健康检查完成"))
        .doOnError(error -> log.error("健康检查失败", error));
    }
}
