package admin.controller;

import system.controller.BaseController;
import domain.entity.Order;
import domain.entity.PaymentTransaction;
import admin.service.DashboardService;
import core.common.ApiResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

import java.util.Map;

@RestController
@RequestMapping("/api/dashboard")
public class DashboardController extends BaseController {

    @Autowired
    private DashboardService dashboardService;

    @GetMapping("/stats")
    public Mono<ResponseEntity<ApiResponse<Object>>> getDashboardStats() {
        return dashboardService.getDashboardStats()
            .map(data -> success((Object) data, "获取仪表板统计成功"))
            .onErrorResume(Exception.class, e -> {
                logger.error("获取仪表板统计失败", e);
                return Mono.just(handleException(e, "获取仪表板统计"));
            });
    }

    @GetMapping("/orders")
    public ResponseEntity<ApiResponse<Object>> getOrders(
            @RequestParam(required = false) Order.OrderStatus status,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        Object data = dashboardService.getOrders(status, page, size);
        return success(data, "获取订单列表成功");
    }

    @GetMapping("/transactions")
    public ResponseEntity<ApiResponse<Object>> getTransactions(
            @RequestParam(required = false) PaymentTransaction.TransactionStatus status,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        Object data = dashboardService.getTransactions(status, page, size);
        return success(data, "获取交易列表成功");
    }

    @GetMapping("/blacklist")
    public Mono<ResponseEntity<ApiResponse<Object>>> getBlacklist(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        return dashboardService.getBlacklistedCards(page, size)
            .map(data -> success((Object) data, "获取黑名单成功"))
            .onErrorResume(Exception.class, e -> {
                logger.error("获取黑名单失败", e);
                return Mono.just(handleException(e, "获取黑名单"));
            });
    }

    @PostMapping("/blacklist")
    public Mono<ResponseEntity<ApiResponse<Object>>> addToBlacklist(@RequestBody Map<String, String> request) {
        return dashboardService.addCardToBlacklist(
                request.get("cardNumber"),
                request.get("reason")
            )
            .map(data -> success((Object) data, "添加到黑名单成功"))
            .onErrorResume(Exception.class, e -> {
                logger.error("添加到黑名单失败", e);
                return Mono.just(handleException(e, "添加到黑名单"));
            });
    }

    @DeleteMapping("/blacklist/{id}")
    public ResponseEntity<ApiResponse<Object>> removeFromBlacklist(@PathVariable String id) {
        dashboardService.removeCardFromBlacklist(id);
        return success(null, "从黑名单移除成功");
    }

    @GetMapping("/order-stats")
    public ResponseEntity<ApiResponse<Object>> getOrderStats() {
        Object data = dashboardService.getOrderStats();
        return success(data, "获取订单统计成功");
    }
}
