FROM azul/zulu-openjdk-alpine:17
WORKDIR /app

ENV TZ=Asia/Shanghai
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

COPY build-output/backend/baka-otp-1.0.0.jar app.jar

RUN mkdir -p /app/logs

EXPOSE 8080
ENV SPRING_PROFILES_ACTIVE=docker

# 健康检查配置
HEALTHCHECK --interval=30s --timeout=10s --start-period=90s --retries=3 \
  CMD wget --spider -q http://localhost:8080/actuator/health || exit 1

# 设置JVM参数
ENV JAVA_OPTS="-server \
    -Xms2g -Xmx4g \
    -XX:+UseG1GC \
    -XX:MaxGCPauseMillis=200 \
    -XX:G1HeapRegionSize=16m \
    -XX:+UseStringDeduplication \
    -XX:+HeapDumpOnOutOfMemoryError \
    -XX:HeapDumpPath=/app/logs/heapdump.hprof \
    -XX:+PrintGCDetails \
    -XX:+PrintGCTimeStamps \
    -XX:+PrintGCApplicationStoppedTime \
    -Xloggc:/app/logs/gc.log \
    -XX:+UseGCLogFileRotation \
    -XX:NumberOfGCLogFiles=5 \
    -XX:GCLogFileSize=10M \
    -Djava.security.egd=file:/dev/./urandom \
    -Dfile.encoding=UTF-8 \
    -Duser.timezone=Asia/Shanghai \
    -Djava.awt.headless=true \
    -Djava.net.preferIPv4Stack=true"

# 启动命令
ENTRYPOINT exec java $JAVA_OPTS -jar app.jar
