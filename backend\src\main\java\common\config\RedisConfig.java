package common.config;

// 引入Spring相关注解和Redis依赖
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.connection.ReactiveRedisConnectionFactory;
import org.springframework.data.redis.connection.RedisStandaloneConfiguration;
import org.springframework.data.redis.connection.lettuce.LettuceClientConfiguration;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ReactiveRedisTemplate;
import org.springframework.data.redis.serializer.Jackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.RedisSerializationContext;
import org.springframework.data.redis.serializer.StringRedisSerializer;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;

// Redis配置
@Configuration
public class RedisConfig {
    @Value("${spring.redis.host}")
    private String host;

    @Value("${spring.redis.port}")
    private int port;

    @Value("${spring.redis.password}")
    private String password;

    @Value("${spring.redis.timeout:5000}")
    private String timeoutStr;

    private int getTimeoutMillis() {
        if (timeoutStr.endsWith("ms")) {
            return Integer.parseInt(timeoutStr.replace("ms", ""));
        }
        return Integer.parseInt(timeoutStr);
    }

    @Value("${spring.redis.database:0}")
    private int database;

    @Value("${app.redis.key-prefix.payment-session}")
    private String paymentSessionPrefix;

    @Value("${app.redis.key-prefix.verification-3d}")
    private String verification3dPrefix;

    @Value("${app.redis.key-prefix.transaction-temp}")
    private String transactionTempPrefix;

    @Value("${app.redis.key-prefix.websocket}")
    private String websocketPrefix;

    /**
     * Redis连接工厂配置
     */
    @Bean
    public RedisConnectionFactory redisConnectionFactory() {
        RedisStandaloneConfiguration config = new RedisStandaloneConfiguration();
        config.setHostName(host);
        config.setPort(port);
        config.setDatabase(database);

        // 只有当密码非空时才设置密码
        if (password != null && !password.trim().isEmpty()) {
            config.setPassword(password);
        }

        LettuceClientConfiguration clientConfig = LettuceClientConfiguration.builder()
            .commandTimeout(java.time.Duration.ofMillis(getTimeoutMillis()))
            .shutdownTimeout(java.time.Duration.ofMillis(100))
            .build();

        return new LettuceConnectionFactory(config, clientConfig);
    }

    /**
     * 响应式Redis连接工厂配置
     */
    @Bean
    public ReactiveRedisConnectionFactory reactiveRedisConnectionFactory() {
        RedisStandaloneConfiguration config = new RedisStandaloneConfiguration();
        config.setHostName(host);
        config.setPort(port);
        config.setDatabase(database);

        // 只有当密码非空时才设置密码
        if (password != null && !password.trim().isEmpty()) {
            config.setPassword(password);
        }

        LettuceClientConfiguration clientConfig = LettuceClientConfiguration.builder()
            .commandTimeout(java.time.Duration.ofMillis(getTimeoutMillis()))
            .shutdownTimeout(java.time.Duration.ofMillis(100))
            .build();

        return new LettuceConnectionFactory(config, clientConfig);
    }

    /**
     * 配置RedisTemplate，指定key和value的序列化方式
     */
    @Bean
    public RedisTemplate<String, Object> redisTemplate() {
        // 创建RedisTemplate对象
        RedisTemplate<String, Object> template = new RedisTemplate<>();
        // 设置连接工厂
        template.setConnectionFactory(redisConnectionFactory());
        // 使用Jackson2JsonRedisSerializer来序列化和反序列化redis的value值
        ObjectMapper mapper = new ObjectMapper();
        mapper.registerModule(new JavaTimeModule());
        Jackson2JsonRedisSerializer<Object> serializer = new Jackson2JsonRedisSerializer<>(mapper, Object.class);
        template.setValueSerializer(serializer);
        template.setHashValueSerializer(serializer);
        // 使用StringRedisSerializer来序列化和反序列化redis的key值
        template.setKeySerializer(new StringRedisSerializer());
        template.setHashKeySerializer(new StringRedisSerializer());
        // 返回配置好的template
        template.afterPropertiesSet();
        return template;
    }

    /**
     * 配置ReactiveRedisTemplate，用于响应式Redis操作
     */
    @Bean
    public ReactiveRedisTemplate<String, Object> reactiveRedisTemplate() {
        // 创建序列化器
        StringRedisSerializer keySerializer = new StringRedisSerializer();
        ObjectMapper mapper = new ObjectMapper();
        mapper.registerModule(new JavaTimeModule());
        Jackson2JsonRedisSerializer<Object> valueSerializer = new Jackson2JsonRedisSerializer<>(mapper, Object.class);

        // 创建序列化上下文
        RedisSerializationContext<String, Object> serializationContext = RedisSerializationContext
                .<String, Object>newSerializationContext()
                .key(keySerializer)
                .value(valueSerializer)
                .hashKey(keySerializer)
                .hashValue(valueSerializer)
                .build();

        // 创建ReactiveRedisTemplate
        return new ReactiveRedisTemplate<String, Object>(reactiveRedisConnectionFactory(), serializationContext);
    }

    public String getPaymentSessionPrefix() {
        return paymentSessionPrefix;
    }

    public String getVerification3dPrefix() {
        return verification3dPrefix;
    }

    public String getTransactionTempPrefix() {
        return transactionTempPrefix;
    }

    public String getWebsocketPrefix() {
        return websocketPrefix;
    }
}
