package user.controller;

import system.controller.BaseController;
import domain.entity.PaymentTransaction;
import domain.entity.OtpVerification;
import common.service.payment.PaymentService;
import core.common.ApiResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Arrays;
import java.util.HashMap;
import reactor.core.publisher.Mono;

@RestController
@RequestMapping("/api/payments")
public class PaymentController extends BaseController {

    @Autowired
    private PaymentService paymentService;

    // 管理员不应该创建支付，只能管理现有支付
    // createPayment 接口已移除，符合用户隔离设计
    // 支付只能由用户通过 UserPaymentController 创建

    @PreAuthorize("hasRole('ADMIN')")
    @PostMapping("/process/{transactionId}")
    public Mono<ResponseEntity<ApiResponse<Object>>> processPayment(@PathVariable String transactionId) {
        return paymentService.processPayment(transactionId)
            .map(transaction -> success((Object) transaction, "处理完成"))
            .onErrorResume(e -> {
                logger.error("处理支付失败", e);
                return Mono.just(handleException((Exception) e, "处理支付"));
            });
    }



    @PreAuthorize("hasRole('ADMIN')")
    @PostMapping("/fail/{transactionId}")
    public Mono<ResponseEntity<ApiResponse<Object>>> failPayment(@PathVariable String transactionId) {
        return paymentService.failPayment(transactionId)
            .map(transaction -> success((Object) transaction, "处理完成"))
            .onErrorResume(e -> {
                logger.error("支付失败处理失败", e);
                return Mono.just(handleException((Exception) e, "支付失败处理"));
            });
    }

    @GetMapping("/{transactionId}")
    public Mono<ResponseEntity<ApiResponse<Object>>> getTransaction(@PathVariable String transactionId) {
        return paymentService.getTransaction(transactionId)
            .map(transaction -> success((Object) transaction, "获取成功"))
            .onErrorResume(e -> {
                logger.error("获取交易详情失败", e);
                return Mono.just(handleException((Exception) e, "获取交易详情"));
            });
    }

    @GetMapping("/list")
    public Mono<ResponseEntity<ApiResponse<Object>>> listAll() {
        return paymentService.getAllTransactions()
            .map(list -> {
                if (list == null) list = new java.util.ArrayList<>();
                return success((Object) list, "获取交易列表成功");
            })
            .onErrorResume(e -> {
                logger.error("获取交易列表失败", e);
                return Mono.just(handleException((Exception) e, "获取交易列表"));
            });
    }

    @GetMapping("/page")
    public Mono<ResponseEntity<ApiResponse<Object>>> page(@RequestParam int page, @RequestParam int size) {
        return paymentService.getTransactionsPaged(page, size)
            .map(list -> success((Object) list, "分页查询成功"))
            .onErrorResume(e -> {
                logger.error("分页查询交易失败", e);
                return Mono.just(handleException((Exception) e, "分页查询交易"));
            });
    }

    @GetMapping("/status/{status}")
    public Mono<ResponseEntity<ApiResponse<Object>>> byStatus(@PathVariable String status) {
        return paymentService.getTransactionsByStatus(PaymentTransaction.TransactionStatus.valueOf(status))
            .map(list -> success((Object) list, "按状态查询成功"))
            .onErrorResume(e -> {
                logger.error("按状态查询交易失败", e);
                return Mono.just(handleException((Exception) e, "按状态查询交易"));
            });
    }
}
