{"prefix": "lucide", "total": 1614, "title": "Lucide", "uncategorized": ["a-arrow-down", "a-arrow-up", "a-large-small", "accessibility", "activity", "air-vent", "airplay", "alarm-clock", "alarm-clock-check", "alarm-clock-minus", "alarm-clock-off", "alarm-clock-plus", "alarm-smoke", "album", "align-center", "align-center-horizontal", "align-center-vertical", "align-end-horizontal", "align-end-vertical", "align-horizontal-distribute-center", "align-horizontal-distribute-end", "align-horizontal-distribute-start", "align-horizontal-justify-center", "align-horizontal-justify-end", "align-horizontal-justify-start", "align-horizontal-space-around", "align-horizontal-space-between", "align-justify", "align-left", "align-right", "align-start-horizontal", "align-start-vertical", "align-vertical-distribute-center", "align-vertical-distribute-end", "align-vertical-distribute-start", "align-vertical-justify-center", "align-vertical-justify-end", "align-vertical-justify-start", "align-vertical-space-around", "align-vertical-space-between", "ambulance", "ampersand", "ampersands", "amphora", "anchor", "angry", "annoyed", "antenna", "anvil", "aperture", "app-window", "app-window-mac", "apple", "archive", "archive-restore", "archive-x", "armchair", "arrow-big-down", "arrow-big-down-dash", "arrow-big-left", "arrow-big-left-dash", "arrow-big-right", "arrow-big-right-dash", "arrow-big-up", "arrow-big-up-dash", "arrow-down", "arrow-down-0-1", "arrow-down-1-0", "arrow-down-a-z", "arrow-down-from-line", "arrow-down-left", "arrow-down-narrow-wide", "arrow-down-right", "arrow-down-to-dot", "arrow-down-to-line", "arrow-down-up", "arrow-down-wide-narrow", "arrow-down-z-a", "arrow-left", "arrow-left-from-line", "arrow-left-right", "arrow-left-to-line", "arrow-right", "arrow-right-from-line", "arrow-right-left", "arrow-right-to-line", "arrow-up", "arrow-up-0-1", "arrow-up-1-0", "arrow-up-a-z", "arrow-up-down", "arrow-up-from-dot", "arrow-up-from-line", "arrow-up-left", "arrow-up-narrow-wide", "arrow-up-right", "arrow-up-to-line", "arrow-up-wide-narrow", "arrow-up-z-a", "arrows-up-from-line", "asterisk", "at-sign", "atom", "audio-lines", "audio-waveform", "award", "axe", "axis-3d", "baby", "backpack", "badge", "badge-alert", "badge-cent", "badge-check", "badge-dollar-sign", "badge-euro", "badge-indian-rupee", "badge-info", "badge-japanese-yen", "badge-minus", "badge-percent", "badge-plus", "badge-pound-sterling", "badge-question-mark", "badge-russian-ruble", "badge-swiss-franc", "badge-turkish-lira", "badge-x", "baggage-claim", "ban", "banana", "bandage", "banknote", "banknote-arrow-down", "banknote-arrow-up", "banknote-x", "barcode", "barrel", "baseline", "bath", "battery", "battery-charging", "battery-full", "battery-low", "battery-medium", "battery-plus", "battery-warning", "beaker", "bean", "bean-off", "bed", "bed-double", "bed-single", "beef", "beer", "beer-off", "bell", "bell-dot", "bell-electric", "bell-minus", "bell-off", "bell-plus", "bell-ring", "between-horizontal-end", "between-horizontal-start", "between-vertical-end", "between-vertical-start", "biceps-flexed", "bike", "binary", "binoculars", "biohazard", "bird", "bitcoin", "blend", "blinds", "blocks", "bluetooth", "bluetooth-connected", "bluetooth-off", "bluetooth-searching", "bold", "bolt", "bomb", "bone", "book", "book-a", "book-alert", "book-audio", "book-check", "book-copy", "book-dashed", "book-down", "book-headphones", "book-heart", "book-image", "book-key", "book-lock", "book-marked", "book-minus", "book-open", "book-open-check", "book-open-text", "book-plus", "book-text", "book-type", "book-up", "book-up-2", "book-user", "book-x", "bookmark", "bookmark-check", "bookmark-minus", "bookmark-plus", "bookmark-x", "boom-box", "bot", "bot-message-square", "bot-off", "bottle-wine", "bow-arrow", "box", "boxes", "braces", "brackets", "brain", "brain-circuit", "brain-cog", "brick-wall", "brick-wall-fire", "briefcase", "briefcase-business", "briefcase-conveyor-belt", "briefcase-medical", "bring-to-front", "brush", "brush-cleaning", "bubbles", "bug", "bug-off", "bug-play", "building", "building-2", "bus", "bus-front", "cable", "cable-car", "cake", "cake-slice", "calculator", "calendar", "calendar-1", "calendar-arrow-down", "calendar-arrow-up", "calendar-check", "calendar-check-2", "calendar-clock", "calendar-cog", "calendar-days", "calendar-fold", "calendar-heart", "calendar-minus", "calendar-minus-2", "calendar-off", "calendar-plus", "calendar-plus-2", "calendar-range", "calendar-search", "calendar-sync", "calendar-x", "calendar-x-2", "camera", "camera-off", "candy", "candy-cane", "candy-off", "cannabis", "captions", "captions-off", "car", "car-front", "car-taxi-front", "caravan", "card-sim", "carrot", "case-lower", "case-sensitive", "case-upper", "cassette-tape", "cast", "castle", "cat", "cctv", "chart-area", "chart-bar", "chart-bar-big", "chart-bar-decreasing", "chart-bar-increasing", "chart-bar-stacked", "chart-candlestick", "chart-column", "chart-column-big", "chart-column-decreasing", "chart-column-increasing", "chart-column-stacked", "chart-gantt", "chart-line", "chart-network", "chart-no-axes-column", "chart-no-axes-column-decreasing", "chart-no-axes-column-increasing", "chart-no-axes-combined", "chart-no-axes-gantt", "chart-pie", "chart-scatter", "chart-spline", "check", "check-check", "check-line", "chef-hat", "cherry", "chevron-down", "chevron-first", "chevron-last", "chevron-left", "chevron-right", "chevron-up", "chevrons-down", "chevrons-down-up", "chevrons-left", "chevrons-left-right", "chevrons-left-right-ellipsis", "chevrons-right", "chevrons-right-left", "chevrons-up", "chevrons-up-down", "chrome", "church", "cigarette", "cigarette-off", "circle", "circle-alert", "circle-arrow-down", "circle-arrow-left", "circle-arrow-out-down-left", "circle-arrow-out-down-right", "circle-arrow-out-up-left", "circle-arrow-out-up-right", "circle-arrow-right", "circle-arrow-up", "circle-check", "circle-check-big", "circle-chevron-down", "circle-chevron-left", "circle-chevron-right", "circle-chevron-up", "circle-dashed", "circle-divide", "circle-dollar-sign", "circle-dot", "circle-dot-dashed", "circle-ellipsis", "circle-equal", "circle-fading-arrow-up", "circle-fading-plus", "circle-gauge", "circle-minus", "circle-off", "circle-parking", "circle-parking-off", "circle-pause", "circle-percent", "circle-play", "circle-plus", "circle-pound-sterling", "circle-power", "circle-question-mark", "circle-slash", "circle-slash-2", "circle-small", "circle-stop", "circle-user", "circle-user-round", "circle-x", "circuit-board", "citrus", "clapperboard", "clipboard", "clipboard-check", "clipboard-copy", "clipboard-list", "clipboard-minus", "clipboard-paste", "clipboard-pen", "clipboard-pen-line", "clipboard-plus", "clipboard-type", "clipboard-x", "clock", "clock-1", "clock-10", "clock-11", "clock-12", "clock-2", "clock-3", "clock-4", "clock-5", "clock-6", "clock-7", "clock-8", "clock-9", "clock-alert", "clock-arrow-down", "clock-arrow-up", "clock-fading", "clock-plus", "cloud", "cloud-alert", "cloud-check", "cloud-cog", "cloud-download", "cloud-drizzle", "cloud-fog", "cloud-hail", "cloud-lightning", "cloud-moon", "cloud-moon-rain", "cloud-off", "cloud-rain", "cloud-rain-wind", "cloud-snow", "cloud-sun", "cloud-sun-rain", "cloud-upload", "cloudy", "clover", "club", "code", "code-xml", "codepen", "codesandbox", "coffee", "cog", "coins", "columns-2", "columns-3", "columns-3-cog", "columns-4", "combine", "command", "compass", "component", "computer", "concierge-bell", "cone", "construction", "contact", "contact-round", "container", "contrast", "cookie", "cooking-pot", "copy", "copy-check", "copy-minus", "copy-plus", "copy-slash", "copy-x", "copyleft", "copyright", "corner-down-left", "corner-down-right", "corner-left-down", "corner-left-up", "corner-right-down", "corner-right-up", "corner-up-left", "corner-up-right", "cpu", "creative-commons", "credit-card", "croissant", "crop", "cross", "crosshair", "crown", "cuboid", "cup-soda", "currency", "cylinder", "dam", "database", "database-backup", "database-zap", "decimals-arrow-left", "decimals-arrow-right", "delete", "dessert", "diameter", "diamond", "diamond-minus", "diamond-percent", "diamond-plus", "dice-1", "dice-2", "dice-3", "dice-4", "dice-5", "dice-6", "dices", "diff", "disc", "disc-2", "disc-3", "disc-album", "divide", "dna", "dna-off", "dock", "dog", "dollar-sign", "donut", "door-closed", "door-closed-locked", "door-open", "dot", "download", "drafting-compass", "drama", "dribbble", "drill", "drone", "droplet", "droplet-off", "droplets", "drum", "drumstick", "dumbbell", "ear", "ear-off", "earth", "earth-lock", "eclipse", "egg", "egg-fried", "egg-off", "ellipsis", "ellipsis-vertical", "equal", "equal-approximately", "equal-not", "eraser", "ethernet-port", "euro", "expand", "external-link", "eye", "eye-closed", "eye-off", "facebook", "factory", "fan", "fast-forward", "feather", "fence", "ferris-wheel", "figma", "file", "file-archive", "file-audio", "file-audio-2", "file-axis-3d", "file-badge", "file-badge-2", "file-box", "file-chart-column", "file-chart-column-increasing", "file-chart-line", "file-chart-pie", "file-check", "file-check-2", "file-clock", "file-code", "file-code-2", "file-cog", "file-diff", "file-digit", "file-down", "file-heart", "file-image", "file-input", "file-json", "file-json-2", "file-key", "file-key-2", "file-lock", "file-lock-2", "file-minus", "file-minus-2", "file-music", "file-output", "file-pen", "file-pen-line", "file-plus", "file-plus-2", "file-question-mark", "file-scan", "file-search", "file-search-2", "file-sliders", "file-spreadsheet", "file-stack", "file-symlink", "file-terminal", "file-text", "file-type", "file-type-2", "file-up", "file-user", "file-video", "file-video-2", "file-volume", "file-volume-2", "file-warning", "file-x", "file-x-2", "files", "film", "fingerprint", "fire-extinguisher", "fish", "fish-off", "fish-symbol", "flag", "flag-off", "flag-triangle-left", "flag-triangle-right", "flame", "flame-kindling", "flashlight", "flashlight-off", "flask-conical", "flask-conical-off", "flask-round", "flip-horizontal", "flip-horizontal-2", "flip-vertical", "flip-vertical-2", "flower", "flower-2", "focus", "fold-horizontal", "fold-vertical", "folder", "folder-archive", "folder-check", "folder-clock", "folder-closed", "folder-code", "folder-cog", "folder-dot", "folder-down", "folder-git", "folder-git-2", "folder-heart", "folder-input", "folder-kanban", "folder-key", "folder-lock", "folder-minus", "folder-open", "folder-open-dot", "folder-output", "folder-pen", "folder-plus", "folder-root", "folder-search", "folder-search-2", "folder-symlink", "folder-sync", "folder-tree", "folder-up", "folder-x", "folders", "footprints", "forklift", "forward", "frame", "framer", "frown", "fuel", "fullscreen", "funnel", "funnel-plus", "funnel-x", "gallery-horizontal", "gallery-horizontal-end", "gallery-thumbnails", "gallery-vertical", "gallery-vertical-end", "gamepad", "gamepad-2", "gauge", "gavel", "gem", "georgian-lari", "ghost", "gift", "git-branch", "git-branch-plus", "git-commit-horizontal", "git-commit-vertical", "git-compare", "git-compare-arrows", "git-fork", "git-graph", "git-merge", "git-pull-request", "git-pull-request-arrow", "git-pull-request-closed", "git-pull-request-create", "git-pull-request-create-arrow", "git-pull-request-draft", "github", "gitlab", "glass-water", "glasses", "globe", "globe-lock", "goal", "gpu", "grab", "graduation-cap", "grape", "grid-2x2", "grid-2x2-check", "grid-2x2-plus", "grid-2x2-x", "grid-3x2", "grid-3x3", "grip", "grip-horizontal", "grip-vertical", "group", "guitar", "ham", "hamburger", "hammer", "hand", "hand-coins", "hand-heart", "hand-helping", "hand-metal", "hand-platter", "handshake", "hard-drive", "hard-drive-download", "hard-drive-upload", "hard-hat", "hash", "haze", "hdmi-port", "heading", "heading-1", "heading-2", "heading-3", "heading-4", "heading-5", "heading-6", "headphone-off", "headphones", "headset", "heart", "heart-crack", "heart-handshake", "heart-minus", "heart-off", "heart-plus", "heart-pulse", "heater", "hexagon", "highlighter", "history", "hop", "hop-off", "hospital", "hotel", "hourglass", "house", "house-plug", "house-plus", "house-wifi", "ice-cream-bowl", "ice-cream-cone", "id-card", "id-card-lanyard", "image", "image-down", "image-minus", "image-off", "image-play", "image-plus", "image-up", "image-upscale", "images", "import", "inbox", "indent-decrease", "indent-increase", "indian-rupee", "infinity", "info", "inspection-panel", "instagram", "italic", "iteration-ccw", "iteration-cw", "japanese-yen", "joystick", "kanban", "key", "key-round", "key-square", "keyboard", "keyboard-music", "keyboard-off", "lamp", "lamp-ceiling", "lamp-desk", "lamp-floor", "lamp-wall-down", "lamp-wall-up", "land-plot", "landmark", "languages", "laptop", "laptop-minimal", "laptop-minimal-check", "lasso", "lasso-select", "laugh", "layers", "layers-2", "layout-dashboard", "layout-grid", "layout-list", "layout-panel-left", "layout-panel-top", "layout-template", "leaf", "leafy-green", "lectern", "letter-text", "library", "library-big", "life-buoy", "ligature", "lightbulb", "lightbulb-off", "line-squiggle", "link", "link-2", "link-2-off", "linkedin", "list", "list-check", "list-checks", "list-collapse", "list-end", "list-filter", "list-filter-plus", "list-minus", "list-music", "list-ordered", "list-plus", "list-restart", "list-start", "list-todo", "list-tree", "list-video", "list-x", "loader", "loader-circle", "loader-pinwheel", "locate", "locate-fixed", "locate-off", "location-edit", "lock", "lock-keyhole", "lock-keyhole-open", "lock-open", "log-in", "log-out", "logs", "lollipop", "luggage", "magnet", "mail", "mail-check", "mail-minus", "mail-open", "mail-plus", "mail-question-mark", "mail-search", "mail-warning", "mail-x", "mailbox", "mails", "map", "map-pin", "map-pin-check", "map-pin-check-inside", "map-pin-house", "map-pin-minus", "map-pin-minus-inside", "map-pin-off", "map-pin-plus", "map-pin-plus-inside", "map-pin-x", "map-pin-x-inside", "map-pinned", "map-plus", "mars", "mars-stroke", "martini", "maximize", "maximize-2", "medal", "megaphone", "megaphone-off", "meh", "memory-stick", "menu", "merge", "message-circle", "message-circle-code", "message-circle-dashed", "message-circle-heart", "message-circle-more", "message-circle-off", "message-circle-plus", "message-circle-question-mark", "message-circle-reply", "message-circle-warning", "message-circle-x", "message-square", "message-square-code", "message-square-dashed", "message-square-diff", "message-square-dot", "message-square-heart", "message-square-lock", "message-square-more", "message-square-off", "message-square-plus", "message-square-quote", "message-square-reply", "message-square-share", "message-square-text", "message-square-warning", "message-square-x", "messages-square", "mic", "mic-off", "mic-vocal", "microchip", "microscope", "microwave", "milestone", "milk", "milk-off", "minimize", "minimize-2", "minus", "monitor", "monitor-check", "monitor-cog", "monitor-dot", "monitor-down", "monitor-off", "monitor-pause", "monitor-play", "monitor-smartphone", "monitor-speaker", "monitor-stop", "monitor-up", "monitor-x", "moon", "moon-star", "mountain", "mountain-snow", "mouse", "mouse-off", "mouse-pointer", "mouse-pointer-2", "mouse-pointer-ban", "mouse-pointer-click", "move", "move-3d", "move-diagonal", "move-diagonal-2", "move-down", "move-down-left", "move-down-right", "move-horizontal", "move-left", "move-right", "move-up", "move-up-left", "move-up-right", "move-vertical", "music", "music-2", "music-3", "music-4", "navigation", "navigation-2", "navigation-2-off", "navigation-off", "network", "newspaper", "nfc", "non-binary", "notebook", "notebook-pen", "notebook-tabs", "notebook-text", "notepad-text", "notepad-text-dashed", "nut", "nut-off", "octagon", "octagon-alert", "octagon-minus", "octagon-pause", "octagon-x", "omega", "option", "orbit", "origami", "package", "package-2", "package-check", "package-minus", "package-open", "package-plus", "package-search", "package-x", "paint-bucket", "paint-roller", "paintbrush", "paintbrush-vertical", "palette", "panda", "panel-bottom", "panel-bottom-close", "panel-bottom-dashed", "panel-bottom-open", "panel-left", "panel-left-close", "panel-left-dashed", "panel-left-open", "panel-right", "panel-right-close", "panel-right-dashed", "panel-right-open", "panel-top", "panel-top-close", "panel-top-dashed", "panel-top-open", "panels-left-bottom", "panels-right-bottom", "panels-top-left", "paperclip", "parentheses", "parking-meter", "party-popper", "pause", "paw-print", "pc-case", "pen", "pen-line", "pen-off", "pen-tool", "pencil", "pencil-line", "pencil-off", "pencil-ruler", "pentagon", "percent", "person-standing", "philippine-peso", "phone", "phone-call", "phone-forwarded", "phone-incoming", "phone-missed", "phone-off", "phone-outgoing", "pi", "piano", "pickaxe", "picture-in-picture", "picture-in-picture-2", "piggy-bank", "pilcrow", "pilcrow-left", "pilcrow-right", "pill", "pill-bottle", "pin", "pin-off", "pipette", "pizza", "plane", "plane-landing", "plane-takeoff", "play", "plug", "plug-2", "plug-zap", "plus", "pocket", "pocket-knife", "podcast", "pointer", "pointer-off", "popcorn", "popsicle", "pound-sterling", "power", "power-off", "presentation", "printer", "printer-check", "projector", "proportions", "puzzle", "pyramid", "qr-code", "quote", "rabbit", "radar", "radiation", "radical", "radio", "radio-receiver", "radio-tower", "radius", "rail-symbol", "rainbow", "rat", "ratio", "receipt", "receipt-cent", "receipt-euro", "receipt-indian-rupee", "receipt-japanese-yen", "receipt-pound-sterling", "receipt-russian-ruble", "receipt-swiss-franc", "receipt-text", "receipt-turkish-lira", "rectangle-circle", "rectangle-ellipsis", "rectangle-goggles", "rectangle-horizontal", "rectangle-vertical", "recycle", "redo", "redo-2", "redo-dot", "refresh-ccw", "refresh-ccw-dot", "refresh-cw", "refresh-cw-off", "refrigerator", "regex", "remove-formatting", "repeat", "repeat-1", "repeat-2", "replace", "replace-all", "reply", "reply-all", "rewind", "ribbon", "rocket", "rocking-chair", "roller-coaster", "rotate-3d", "rotate-ccw", "rotate-ccw-key", "rotate-ccw-square", "rotate-cw", "rotate-cw-square", "route", "route-off", "router", "rows-2", "rows-3", "rows-4", "rss", "ruler", "ruler-dimension-line", "russian-ruble", "sailboat", "salad", "sandwich", "satellite", "satellite-dish", "saudi-riyal", "save", "save-all", "save-off", "scale", "scale-3d", "scaling", "scan", "scan-barcode", "scan-eye", "scan-face", "scan-heart", "scan-line", "scan-qr-code", "scan-search", "scan-text", "school", "scissors", "scissors-line-dashed", "screen-share", "screen-share-off", "scroll", "scroll-text", "search", "search-check", "search-code", "search-slash", "search-x", "section", "send", "send-horizontal", "send-to-back", "separator-horizontal", "separator-vertical", "server", "server-cog", "server-crash", "server-off", "settings", "settings-2", "shapes", "share", "share-2", "sheet", "shell", "shield", "shield-alert", "shield-ban", "shield-check", "shield-ellipsis", "shield-half", "shield-minus", "shield-off", "shield-plus", "shield-question-mark", "shield-user", "shield-x", "ship", "ship-wheel", "shirt", "shopping-bag", "shopping-basket", "shopping-cart", "shovel", "shower-head", "shredder", "shrimp", "shrink", "shrub", "shuffle", "sigma", "signal", "signal-high", "signal-low", "signal-medium", "signal-zero", "signature", "signpost", "signpost-big", "siren", "skip-back", "skip-forward", "skull", "slack", "slash", "slice", "sliders-horizontal", "sliders-vertical", "smartphone", "smartphone-charging", "smartphone-nfc", "smile", "smile-plus", "snail", "snowflake", "soap-dispenser-droplet", "sofa", "soup", "space", "spade", "sparkle", "sparkles", "speaker", "speech", "spell-check", "spell-check-2", "spline", "spline-pointer", "split", "spool", "spray-can", "sprout", "square", "square-activity", "square-arrow-down", "square-arrow-down-left", "square-arrow-down-right", "square-arrow-left", "square-arrow-out-down-left", "square-arrow-out-down-right", "square-arrow-out-up-left", "square-arrow-out-up-right", "square-arrow-right", "square-arrow-up", "square-arrow-up-left", "square-arrow-up-right", "square-asterisk", "square-bottom-dashed-scissors", "square-chart-gantt", "square-check", "square-check-big", "square-chevron-down", "square-chevron-left", "square-chevron-right", "square-chevron-up", "square-code", "square-dashed", "square-dashed-bottom", "square-dashed-bottom-code", "square-dashed-kanban", "square-dashed-mouse-pointer", "square-dashed-top-solid", "square-divide", "square-dot", "square-equal", "square-function", "square-kanban", "square-library", "square-m", "square-menu", "square-minus", "square-mouse-pointer", "square-parking", "square-parking-off", "square-pen", "square-percent", "square-pi", "square-pilcrow", "square-play", "square-plus", "square-power", "square-radical", "square-round-corner", "square-scissors", "square-sigma", "square-slash", "square-split-horizontal", "square-split-vertical", "square-square", "square-stack", "square-terminal", "square-user", "square-user-round", "square-x", "squares-exclude", "squares-intersect", "squares-subtract", "squares-unite", "squircle", "squircle-dashed", "squirrel", "stamp", "star", "star-half", "star-off", "step-back", "step-forward", "stethoscope", "sticker", "sticky-note", "store", "stretch-horizontal", "stretch-vertical", "strikethrough", "subscript", "sun", "sun-dim", "sun-medium", "sun-moon", "sun-snow", "sunrise", "sunset", "superscript", "swatch-book", "swiss-franc", "switch-camera", "sword", "swords", "syringe", "table", "table-2", "table-cells-merge", "table-cells-split", "table-columns-split", "table-of-contents", "table-properties", "table-rows-split", "tablet", "tablet-smartphone", "tablets", "tag", "tags", "tally-1", "tally-2", "tally-3", "tally-4", "tally-5", "tangent", "target", "telescope", "tent", "tent-tree", "terminal", "test-tube", "test-tube-diagonal", "test-tubes", "text", "text-cursor", "text-cursor-input", "text-quote", "text-search", "text-select", "theater", "thermometer", "thermometer-snowflake", "thermometer-sun", "thumbs-down", "thumbs-up", "ticket", "ticket-check", "ticket-minus", "ticket-percent", "ticket-plus", "ticket-slash", "ticket-x", "tickets", "tickets-plane", "timer", "timer-off", "timer-reset", "toggle-left", "toggle-right", "toilet", "tool-case", "tornado", "torus", "touchpad", "touchpad-off", "tower-control", "toy-brick", "tractor", "traffic-cone", "train-front", "train-front-tunnel", "train-track", "tram-front", "transgender", "trash", "trash-2", "tree-deciduous", "tree-palm", "tree-pine", "trees", "trello", "trending-down", "trending-up", "trending-up-down", "triangle", "triangle-alert", "triangle-dashed", "triangle-right", "trophy", "truck", "truck-electric", "turkish-lira", "turtle", "tv", "tv-minimal", "tv-minimal-play", "twitch", "twitter", "type", "type-outline", "umbrella", "umbrella-off", "underline", "undo", "undo-2", "undo-dot", "unfold-horizontal", "unfold-vertical", "ungroup", "university", "unlink", "unlink-2", "unplug", "upload", "usb", "user", "user-check", "user-cog", "user-lock", "user-minus", "user-pen", "user-plus", "user-round", "user-round-check", "user-round-cog", "user-round-minus", "user-round-pen", "user-round-plus", "user-round-search", "user-round-x", "user-search", "user-x", "users", "users-round", "utensils", "utensils-crossed", "utility-pole", "variable", "vault", "vector-square", "vegan", "venetian-mask", "venus", "venus-and-mars", "vibrate", "vibrate-off", "video", "video-off", "videotape", "view", "voicemail", "volleyball", "volume", "volume-1", "volume-2", "volume-off", "volume-x", "vote", "wallet", "wallet-cards", "wallet-minimal", "wallpaper", "wand", "wand-sparkles", "warehouse", "washing-machine", "watch", "waves", "waves-ladder", "waypoints", "webcam", "webhook", "webhook-off", "weight", "wheat", "wheat-off", "whole-word", "wifi", "wifi-cog", "wifi-high", "wifi-low", "wifi-off", "wifi-pen", "wifi-zero", "wind", "wind-arrow-down", "wine", "wine-off", "workflow", "worm", "wrap-text", "wrench", "x", "youtube", "zap", "zap-off", "zoom-in", "zoom-out"], "hidden": ["area-chart", "bar-chart-3", "bar-chart-4", "bar-chart-big", "bar-chart-horizontal", "bar-chart-horizontal-big", "candlestick-chart", "file-pie-chart", "filter", "filter-x", "layers-3", "line-chart", "pie-chart", "scatter-chart", "search-large"], "aliases": {"activity-square": "square-activity", "alarm-check": "alarm-clock-check", "alarm-minus": "alarm-clock-minus", "alarm-plus": "alarm-clock-plus", "alert-circle": "circle-alert", "alert-octagon": "octagon-alert", "alert-triangle": "triangle-alert", "align-horizonal-distribute-center": "align-horizontal-distribute-center", "align-horizonal-distribute-end": "align-horizontal-distribute-end", "align-horizonal-distribute-start": "align-horizontal-distribute-start", "arrow-down-01": "arrow-down-0-1", "arrow-down-10": "arrow-down-1-0", "arrow-down-az": "arrow-down-a-z", "arrow-down-circle": "circle-arrow-down", "arrow-down-left-from-circle": "circle-arrow-out-down-left", "arrow-down-left-from-square": "square-arrow-out-down-left", "arrow-down-left-square": "square-arrow-down-left", "arrow-down-right-from-circle": "circle-arrow-out-down-right", "arrow-down-right-from-square": "square-arrow-out-down-right", "arrow-down-right-square": "square-arrow-down-right", "arrow-down-square": "square-arrow-down", "arrow-down-za": "arrow-down-z-a", "arrow-left-circle": "circle-arrow-left", "arrow-left-square": "square-arrow-left", "arrow-right-circle": "circle-arrow-right", "arrow-right-square": "square-arrow-right", "arrow-up-01": "arrow-up-0-1", "arrow-up-10": "arrow-up-1-0", "arrow-up-az": "arrow-up-a-z", "arrow-up-circle": "circle-arrow-up", "arrow-up-left-from-circle": "circle-arrow-out-up-left", "arrow-up-left-from-square": "square-arrow-out-up-left", "arrow-up-left-square": "square-arrow-up-left", "arrow-up-right-from-circle": "circle-arrow-out-up-right", "arrow-up-right-from-square": "square-arrow-out-up-right", "arrow-up-right-square": "square-arrow-up-right", "arrow-up-square": "square-arrow-up", "arrow-up-za": "arrow-up-z-a", "asterisk-square": "square-asterisk", "axis-3-d": "axis-3d", "badge-help": "badge-question-mark", "bar-chart": "chart-no-axes-column-increasing", "bar-chart-2": "chart-no-axes-column", "between-horizonal-end": "between-horizontal-end", "between-horizonal-start": "between-horizontal-start", "book-template": "book-dashed", "box-select": "square-dashed", "check-circle": "circle-check-big", "check-circle-2": "circle-check", "check-square": "square-check-big", "check-square-2": "square-check", "chevron-down-circle": "circle-chevron-down", "chevron-down-square": "square-chevron-down", "chevron-left-circle": "circle-chevron-left", "chevron-left-square": "square-chevron-left", "chevron-right-circle": "circle-chevron-right", "chevron-right-square": "square-chevron-right", "chevron-up-circle": "circle-chevron-up", "chevron-up-square": "square-chevron-up", "circle-help": "circle-question-mark", "circle-slashed": "circle-slash-2", "clipboard-edit": "clipboard-pen", "clipboard-signature": "clipboard-pen-line", "code-2": "code-xml", "code-square": "square-code", "columns": "columns-2", "columns-settings": "columns-3-cog", "contact-2": "contact-round", "curly-braces": "braces", "divide-circle": "circle-divide", "divide-square": "square-divide", "dot-square": "square-dot", "download-cloud": "cloud-download", "edit": "square-pen", "edit-2": "pen", "edit-3": "pen-line", "equal-square": "square-equal", "file-axis-3-d": "file-axis-3d", "file-bar-chart": "file-chart-column-increasing", "file-bar-chart-2": "file-chart-column", "file-cog-2": "file-cog", "file-edit": "file-pen", "file-line-chart": "file-chart-line", "file-question": "file-question-mark", "file-signature": "file-pen-line", "folder-cog-2": "folder-cog", "folder-edit": "folder-pen", "fork-knife": "utensils", "fork-knife-crossed": "utensils-crossed", "form-input": "rectangle-ellipsis", "function-square": "square-function", "gantt-chart": "chart-no-axes-gantt", "gantt-chart-square": "square-chart-gantt", "gauge-circle": "circle-gauge", "git-commit": "git-commit-horizontal", "globe-2": "earth", "grid": "grid-3x3", "grid-2-x-2": "grid-2x2", "grid-2-x-2-check": "grid-2x2-check", "grid-2-x-2-plus": "grid-2x2-plus", "grid-2-x-2-x": "grid-2x2-x", "grid-3-x-3": "grid-3x3", "help-circle": "circle-question-mark", "helping-hand": "hand-helping", "home": "house", "ice-cream": "ice-cream-cone", "ice-cream-2": "ice-cream-bowl", "indent": "indent-increase", "inspect": "square-mouse-pointer", "jersey-pound": "japanese-yen", "kanban-square": "square-kanban", "kanban-square-dashed": "square-dashed-kanban", "laptop-2": "laptop-minimal", "layout": "panels-top-left", "library-square": "square-library", "loader-2": "loader-circle", "m-square": "square-m", "mail-question": "mail-question-mark", "menu-square": "square-menu", "message-circle-question": "message-circle-question-mark", "mic-2": "mic-vocal", "minus-circle": "circle-minus", "minus-square": "square-minus", "more-horizontal": "ellipsis", "more-vertical": "ellipsis-vertical", "mouse-pointer-square": "square-mouse-pointer", "mouse-pointer-square-dashed": "square-dashed-mouse-pointer", "move-3-d": "move-3d", "outdent": "indent-decrease", "paintbrush-2": "paintbrush-vertical", "palmtree": "tree-palm", "panel-bottom-inactive": "panel-bottom-dashed", "panel-left-inactive": "panel-left-dashed", "panel-right-inactive": "panel-right-dashed", "panel-top-inactive": "panel-top-dashed", "panels-left-right": "columns-3", "panels-top-bottom": "rows-3", "parking-circle": "circle-parking", "parking-circle-off": "circle-parking-off", "parking-square": "square-parking", "parking-square-off": "square-parking-off", "pause-circle": "circle-pause", "pause-octagon": "octagon-pause", "pen-box": "square-pen", "pen-square": "square-pen", "percent-circle": "circle-percent", "percent-diamond": "diamond-percent", "percent-square": "square-percent", "pi-square": "square-pi", "pilcrow-square": "square-pilcrow", "play-circle": "circle-play", "play-square": "square-play", "plug-zap-2": "plug-zap", "plus-circle": "circle-plus", "plus-square": "square-plus", "power-circle": "circle-power", "power-square": "square-power", "rotate-3-d": "rotate-3d", "rows": "rows-2", "scale-3-d": "scale-3d", "school-2": "university", "scissors-square": "square-scissors", "scissors-square-dashed-bottom": "square-bottom-dashed-scissors", "send-horizonal": "send-horizontal", "shield-close": "shield-x", "shield-question": "shield-question-mark", "sidebar": "panel-left", "sidebar-close": "panel-left-close", "sidebar-open": "panel-left-open", "sigma-square": "square-sigma", "slash-square": "square-slash", "sliders": "sliders-vertical", "sort-asc": "arrow-up-narrow-wide", "sort-desc": "arrow-down-wide-narrow", "split-square-horizontal": "square-split-horizontal", "split-square-vertical": "square-split-vertical", "square-gantt": "square-chart-gantt", "square-gantt-chart": "square-chart-gantt", "square-kanban-dashed": "square-dashed-kanban", "stars": "sparkles", "stop-circle": "circle-stop", "subtitles": "captions", "table-config": "columns-3-cog", "terminal-square": "square-terminal", "test-tube-2": "test-tube-diagonal", "text-selection": "text-select", "train": "tram-front", "tv-2": "tv-minimal", "unlock": "lock-open", "unlock-keyhole": "lock-keyhole-open", "upload-cloud": "cloud-upload", "user-2": "user-round", "user-check-2": "user-round-check", "user-circle": "circle-user", "user-circle-2": "circle-user-round", "user-cog-2": "user-round-cog", "user-minus-2": "user-round-minus", "user-plus-2": "user-round-plus", "user-square": "square-user", "user-square-2": "square-user-round", "user-x-2": "user-round-x", "users-2": "users-round", "verified": "badge-check", "wallet-2": "wallet-minimal", "wand-2": "wand-sparkles", "x-circle": "circle-x", "x-octagon": "octagon-x", "x-square": "square-x"}}