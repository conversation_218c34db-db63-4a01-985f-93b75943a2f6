.container[data-v-fbbf461c] {
    margin: 10px;
    padding: 10px;
    border: 1px solid #808080
}

.container .title[data-v-fbbf461c] {
    background-color: #486ec1;
    color: #fff;
    padding: 5px 10px;
    text-transform: uppercase;
    font-weight: 700
}

.container div.input input[data-v-fbbf461c] {
    padding: 5px;
    width: 100%;
    border: 1px solid rgba(128, 128, 128, .5019607843);
    outline: none;
    border-radius: 5px
}

.container div.button[data-v-fbbf461c] {
    text-align: center;
    background-color: transparent
}

.container div.button button[data-v-fbbf461c] {
    background-color: #36538d;
    color: #fff;
    width: 100%;
    padding: 8px;
    border-radius: 5px;
    border: none;
}

.error[data-v-fbbf461c] {
    font-size: 10px;
    color: red;
    margin: 5px 0
}

.counter[data-v-fbbf461c] {
    -webkit-text-decoration: underline;
    text-decoration: underline;
    color: #000
}