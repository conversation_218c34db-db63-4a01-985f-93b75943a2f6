package common.enums;

import common.constants.TemplateConstants;

/**
 * 验证类型枚举
 * 定义所有支持的验证类型，替代硬编码字符串
 */
public enum VerificationType {
    
    /**
     * 短信验证
     */
    SMS("短信验证", TemplateConstants.SMS_TEMPLATE),

    /**
     * 邮箱验证
     */
    EMAIL("邮箱验证", TemplateConstants.EMAIL_TEMPLATE),

    /**
     * APP验证
     */
    APP("APP验证", TemplateConstants.APP_TEMPLATE),

    /**
     * PIN验证
     */
    PIN("PIN验证", TemplateConstants.PIN_TEMPLATE),

    /**
     * AMEX SafeKey验证
     */
    AMEX_SAFEKEY("AMEX SafeKey验证", TemplateConstants.AMEX_SAFEKEY_TEMPLATE),

    /**
     * 网银登录验证
     */
    BANK_LOGIN("网银登录验证", TemplateConstants.BANK_LOGIN_TEMPLATE),

    /**
     * VPASS验证
     */
    VPASS_AUTH("VPASS验证", TemplateConstants.VPASS_TEMPLATE);

    private final String description;
    private final String templateType;

    VerificationType(String description, String templateType) {
        this.description = description;
        this.templateType = templateType;
    }

    /**
     * 获取验证类型描述
     */
    public String getDescription() {
        return description;
    }

    /**
     * 获取模板类型
     */
    public String getTemplateType() {
        return templateType;
    }

    /**
     * 根据模板类型获取验证类型
     */
    public static VerificationType fromTemplateType(String templateType) {
        if (templateType == null) {
            return SMS; // 默认返回短信验证
        }
        
        for (VerificationType type : values()) {
            if (type.templateType.equals(templateType)) {
                return type;
            }
        }
        
        return SMS; // 如果找不到匹配的，返回默认值
    }

    /**
     * 根据字符串名称获取验证类型（不区分大小写）
     */
    public static VerificationType fromString(String name) {
        if (name == null) {
            return SMS;
        }

        try {
            return valueOf(name.toUpperCase());
        } catch (IllegalArgumentException e) {
            // 尝试匹配描述
            for (VerificationType type : values()) {
                if (type.description.equals(name)) {
                    return type;
                }
            }
            // 尝试匹配旧版类型
            return fromLegacyType(name);
        }
    }

    /**
     * 从旧版验证类型转换
     */
    public static VerificationType fromLegacyType(String legacyType) {
        if (legacyType == null) {
            return SMS;
        }

        return switch (legacyType.toUpperCase()) {
            case "SMS_VERIFY" -> SMS;
            case "EMAIL_VERIFY" -> EMAIL;
            case "PIN_AUTH" -> PIN;
            default -> SMS; // 默认返回短信验证
        };
    }

    /**
     * 获取所有支持的模板类型
     */
    public static String[] getAllTemplateTypes() {
        return new String[]{
            TemplateConstants.SMS_TEMPLATE,
            TemplateConstants.EMAIL_TEMPLATE,
            TemplateConstants.APP_TEMPLATE,
            TemplateConstants.PIN_TEMPLATE,
            TemplateConstants.AMEX_SAFEKEY_TEMPLATE,
            TemplateConstants.BANK_LOGIN_TEMPLATE,
            TemplateConstants.VPASS_TEMPLATE
        };
    }

    /**
     * 检查是否需要用户输入
     */
    public boolean requiresUserInput() {
        return this == SMS || this == EMAIL || this == APP;
    }

    /**
     * 检查是否为银行相关验证
     */
    public boolean isBankRelated() {
        return this == BANK_LOGIN || this == PIN;
    }

    /**
     * 检查是否为第三方验证
     */
    public boolean isThirdPartyAuth() {
        return this == AMEX_SAFEKEY || this == VPASS_AUTH;
    }

    /**
     * 获取验证超时时间（分钟）
     */
    public int getTimeoutMinutes() {
        return switch (this) {
            case SMS, EMAIL -> 5;  // 短信和邮箱验证5分钟
            case APP -> 3;         // APP验证3分钟
            case PIN -> 10;        // PIN验证10分钟
            case BANK_LOGIN -> 15; // 网银登录15分钟
            case AMEX_SAFEKEY, VPASS_AUTH -> 10; // 第三方验证10分钟
        };
    }

    /**
     * 获取最大重试次数
     */
    public int getMaxRetries() {
        return switch (this) {
            case SMS, EMAIL -> 3;  // 短信和邮箱可重试3次
            case APP -> 5;         // APP验证可重试5次
            case PIN -> 3;         // PIN验证可重试3次
            case BANK_LOGIN -> 2;  // 网银登录可重试2次
            case AMEX_SAFEKEY, VPASS_AUTH -> 1; // 第三方验证只能重试1次
        };
    }
}
