package admin.controller;

import domain.entity.SystemLog;
import core.common.ApiResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 系统日志管理控制器
 */
@RestController
@RequestMapping("/api/system/logs")
@PreAuthorize("hasRole('ADMIN')")
public class SystemLogController {

    private static final Logger logger = LoggerFactory.getLogger(SystemLogController.class);
    private static final String LOG_KEY_PREFIX = "log:";
    private static final String LOG_ID_SET_KEY = "log:ids";

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    /**
     * 获取操作日志列表
     */
    @GetMapping
    public ResponseEntity<ApiResponse<Object>> getLogList(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int pageSize,
            @RequestParam(required = false) String username,
            @RequestParam(required = false) String operation,
            @RequestParam(required = false) String level,
            @RequestParam(required = false) String startTime,
            @RequestParam(required = false) String endTime) {
        try {
            List<SystemLog> allLogs = getAllLogs();

            // 过滤
            List<SystemLog> filteredLogs = allLogs.stream()
                .filter(log -> username == null || (log.getUsername() != null && log.getUsername().contains(username)))
                .filter(log -> operation == null || (log.getCategory() != null && log.getCategory().contains(operation)))
                .filter(log -> level == null || level.equals(log.getLevel()))
                .filter(log -> {
                    if (startTime == null) return true;
                    try {
                        LocalDateTime start = LocalDateTime.parse(startTime, DateTimeFormatter.ISO_LOCAL_DATE_TIME);
                        return log.getCreateTime().isAfter(start) || log.getCreateTime().isEqual(start);
                    } catch (Exception e) {
                        return true;
                    }
                })
                .filter(log -> {
                    if (endTime == null) return true;
                    try {
                        LocalDateTime end = LocalDateTime.parse(endTime, DateTimeFormatter.ISO_LOCAL_DATE_TIME);
                        return log.getCreateTime().isBefore(end) || log.getCreateTime().isEqual(end);
                    } catch (Exception e) {
                        return true;
                    }
                })
                .sorted((a, b) -> b.getCreateTime().compareTo(a.getCreateTime())) // 按时间倒序
                .collect(Collectors.toList());

            // 分页
            int total = filteredLogs.size();
            int start = (page - 1) * pageSize;
            int end = Math.min(start + pageSize, total);
            List<SystemLog> pagedLogs = filteredLogs.subList(start, end);

            // 转换为DTO
            List<Map<String, Object>> logDTOs = pagedLogs.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());

            Map<String, Object> result = new HashMap<>();
            result.put("list", logDTOs);
            result.put("total", total);
            result.put("page", page);
            result.put("pageSize", pageSize);
            result.put("totalPages", (total + pageSize - 1) / pageSize);

            return ResponseEntity.ok(new ApiResponse<>(200, "success", result, true));
        } catch (Exception e) {
            logger.error("获取日志列表失败", e);
            return ResponseEntity.badRequest().body(new ApiResponse<>(400, "获取日志列表失败: " + e.getMessage(), null, false));
        }
    }

    /**
     * 记录操作日志
     */
    @PostMapping
    public ResponseEntity<ApiResponse<Object>> addLog(@RequestBody Map<String, Object> request) {
        try {
            SystemLog log = new SystemLog();
            log.setUsername(request.get("username") != null ? request.get("username").toString() : "system");
            log.setCategory(request.get("operation").toString());
            log.setMessage(request.get("method") != null ? request.get("method").toString() : "");
            log.setDetails(request.get("params") != null ? request.get("params").toString() : "");
            log.setIpAddress(request.get("ip") != null ? request.get("ip").toString() : "");
            log.setLevel(request.get("level") != null ? request.get("level").toString() : "INFO");

            redisTemplate.opsForValue().set(LOG_KEY_PREFIX + log.getId(), log);
            redisTemplate.opsForSet().add(LOG_ID_SET_KEY, log.getId());

            return ResponseEntity.ok(new ApiResponse<>(200, "记录成功", convertToDTO(log), true));
        } catch (Exception e) {
            logger.error("记录日志失败", e);
            return ResponseEntity.badRequest().body(new ApiResponse<>(400, "记录日志失败: " + e.getMessage(), null, false));
        }
    }

    /**
     * 获取日志统计信息
     */
    @GetMapping("/stats")
    public ResponseEntity<ApiResponse<Object>> getLogStats() {
        try {
            List<SystemLog> allLogs = getAllLogs();

            // 今日日志
            LocalDateTime todayStart = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0).withNano(0);
            List<SystemLog> todayLogs = allLogs.stream()
                .filter(log -> log.getCreatedAt().isAfter(todayStart))
                .collect(Collectors.toList());

            Map<String, Object> stats = new HashMap<>();
            stats.put("totalLogs", allLogs.size());
            stats.put("todayLogs", todayLogs.size());
            stats.put("errorLogs", allLogs.stream().filter(log -> "ERROR".equals(log.getLevel())).count());
            stats.put("warnLogs", allLogs.stream().filter(log -> "WARN".equals(log.getLevel())).count());
            stats.put("infoLogs", allLogs.stream().filter(log -> "INFO".equals(log.getLevel())).count());

            // 按小时统计今日日志
            Map<Integer, Long> hourlyStats = todayLogs.stream()
                .collect(Collectors.groupingBy(
                    log -> log.getCreatedAt().getHour(),
                    Collectors.counting()
                ));
            stats.put("hourlyStats", hourlyStats);

            // 操作类型统计
            Map<String, Long> operationStats = allLogs.stream()
                .collect(Collectors.groupingBy(
                    SystemLog::getCategory,
                    Collectors.counting()
                ));
            stats.put("operationStats", operationStats);

            return ResponseEntity.ok(new ApiResponse<>(200, "success", stats, true));
        } catch (Exception e) {
            logger.error("获取日志统计失败", e);
            return ResponseEntity.badRequest().body(new ApiResponse<>(400, "获取日志统计失败: " + e.getMessage(), null, false));
        }
    }

    /**
     * 清理日志
     */
    @DeleteMapping("/cleanup")
    public ResponseEntity<ApiResponse<Object>> cleanupLogs(@RequestParam(defaultValue = "30") int days) {
        try {
            LocalDateTime cutoffTime = LocalDateTime.now().minusDays(days);
            List<SystemLog> allLogs = getAllLogs();

            int deletedCount = 0;
            for (SystemLog log : allLogs) {
                if (log.getCreatedAt().isBefore(cutoffTime)) {
                    redisTemplate.delete(LOG_KEY_PREFIX + log.getId());
                    redisTemplate.opsForSet().remove(LOG_ID_SET_KEY, log.getId().toString());
                    deletedCount++;
                }
            }

            Map<String, Object> result = new HashMap<>();
            result.put("deletedCount", deletedCount);
            result.put("message", "清理完成，删除了 " + deletedCount + " 条日志");

            return ResponseEntity.ok(new ApiResponse<>(200, "success", result, true));
        } catch (Exception e) {
            logger.error("清理日志失败", e);
            return ResponseEntity.badRequest().body(new ApiResponse<>(400, "清理日志失败: " + e.getMessage(), null, false));
        }
    }

    /**
     * 导出日志
     */
    @GetMapping("/export")
    public ResponseEntity<ApiResponse<Object>> exportLogs(
            @RequestParam(required = false) String startTime,
            @RequestParam(required = false) String endTime) {
        try {
            List<SystemLog> allLogs = getAllLogs();

            // 过滤时间范围
            List<SystemLog> filteredLogs = allLogs.stream()
                .filter(log -> {
                    if (startTime == null) return true;
                    try {
                        LocalDateTime start = LocalDateTime.parse(startTime, DateTimeFormatter.ISO_LOCAL_DATE_TIME);
                        return log.getCreatedAt().isAfter(start) || log.getCreatedAt().isEqual(start);
                    } catch (Exception e) {
                        return true;
                    }
                })
                .filter(log -> {
                    if (endTime == null) return true;
                    try {
                        LocalDateTime end = LocalDateTime.parse(endTime, DateTimeFormatter.ISO_LOCAL_DATE_TIME);
                        return log.getCreatedAt().isBefore(end) || log.getCreatedAt().isEqual(end);
                    } catch (Exception e) {
                        return true;
                    }
                })
                .sorted((a, b) -> b.getCreatedAt().compareTo(a.getCreatedAt()))
                .collect(Collectors.toList());

            // 转换为CSV格式
            StringBuilder csv = new StringBuilder();
            csv.append("时间,用户,操作,方法,参数,IP,级别,结果\n");

            for (SystemLog log : filteredLogs) {
                csv.append(log.getCreatedAt()).append(",")
                   .append(log.getUsername()).append(",")
                   .append(log.getCategory()).append(",")
                   .append(log.getMessage()).append(",")
                   .append(log.getDetails()).append(",")
                   .append(log.getIpAddress()).append(",")
                   .append(log.getLevel()).append(",")
                   .append("SUCCESS").append("\n");
            }

            Map<String, Object> result = new HashMap<>();
            result.put("csvData", csv.toString());
            result.put("filename", "system_logs_" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss")) + ".csv");
            result.put("count", filteredLogs.size());

            return ResponseEntity.ok(new ApiResponse<>(200, "success", result, true));
        } catch (Exception e) {
            logger.error("导出日志失败", e);
            return ResponseEntity.badRequest().body(new ApiResponse<>(400, "导出日志失败: " + e.getMessage(), null, false));
        }
    }

    // 辅助方法
    private List<SystemLog> getAllLogs() {
        List<SystemLog> logs = new ArrayList<>();
        Set<Object> ids = redisTemplate.opsForSet().members(LOG_ID_SET_KEY);
        if (ids != null) {
            for (Object id : ids) {
                Object obj = redisTemplate.opsForValue().get(LOG_KEY_PREFIX + id);
                if (obj instanceof SystemLog) {
                    logs.add((SystemLog) obj);
                }
            }
        }
        return logs;
    }

    private Map<String, Object> convertToDTO(SystemLog log) {
        Map<String, Object> dto = new HashMap<>();
        dto.put("id", log.getId().toString());
        dto.put("username", log.getUsername());
        dto.put("operation", log.getCategory());
        dto.put("method", log.getMessage());
        dto.put("params", log.getDetails());
        dto.put("ip", log.getIpAddress());
        dto.put("level", log.getLevel());
        dto.put("result", "SUCCESS");
        dto.put("createTime", log.getCreatedAt() != null ? log.getCreatedAt().toString() : null);
        return dto;
    }
}
