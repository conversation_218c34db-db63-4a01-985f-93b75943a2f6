package admin.controller;

import system.controller.BaseController;
import domain.entity.User;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;
import core.common.ApiResponse;

import java.util.*;

@RestController
@RequestMapping("/api/menu")
public class MenuController extends BaseController {

    @GetMapping("/all")
    public ResponseEntity<ApiResponse<Object>> getAllMenus(@AuthenticationPrincipal User user) {
        try {
            List<Map<String, Object>> menus = new ArrayList<>();

            // 仪表盘菜单
            Map<String, Object> dashboard = new HashMap<>();
            dashboard.put("component", "BasicLayout");
            dashboard.put("meta", Map.of(
                "icon", "lucide:layout-dashboard",
                "order", -1,
                "title", "仪表盘"
            ));
            dashboard.put("name", "Dashboard");
            dashboard.put("path", "/dashboard");

            List<Map<String, Object>> dashboardChildren = new ArrayList<>();
            dashboardChildren.add(Map.of(
                "name", "Analytics",
                "path", "/dashboard/analytics",
                "component", "/views/dashboard/analytics/index.vue",
                "meta", Map.of(
                    "affixTab", true,
                    "icon", "lucide:area-chart",
                    "title", "分析页"
                )
            ));
            dashboard.put("children", dashboardChildren);
            menus.add(dashboard);

            // 支付管理菜单（根据用户角色显示）
            if (user != null && ("ADMIN".equals(user.getRole()) || "USER".equals(user.getRole()))) {
                Map<String, Object> payment = new HashMap<>();
                payment.put("component", "BasicLayout");
                payment.put("meta", Map.of(
                    "icon", "lucide:credit-card",
                    "order", 1,
                    "title", "支付管理"
                ));
                payment.put("name", "Payment");
                payment.put("path", "/payment");

                List<Map<String, Object>> paymentChildren = new ArrayList<>();
                paymentChildren.add(Map.of(
                    "name", "PaymentCardManager",
                    "path", "/payment/payment-cards",
                    "component", "/views/payment/PaymentCardManager.vue",
                    "meta", Map.of(
                        "icon", "lucide:credit-card",
                        "title", "支付卡管理"
                    )
                ));
                paymentChildren.add(Map.of(
                    "name", "PaymentTransactions",
                    "path", "/payment/transactions",
                    "component", "/views/payment/PaymentTransactions.vue",
                    "meta", Map.of(
                        "icon", "lucide:receipt",
                        "title", "交易记录"
                    )
                ));

                // 只有管理员才能看到支付设置
                if ("ADMIN".equals(user.getRole())) {
                    paymentChildren.add(Map.of(
                        "name", "PaymentSettings",
                        "path", "/payment/payment-settings",
                        "component", "/views/payment/PaymentSettings.vue",
                        "meta", Map.of(
                            "icon", "lucide:settings",
                            "title", "支付设置"
                        )
                    ));
                }

                payment.put("children", paymentChildren);
                menus.add(payment);
            }

            // 系统管理菜单（仅管理员可见）
            if (user != null && "ADMIN".equals(user.getRole())) {
                Map<String, Object> system = new HashMap<>();
                system.put("component", "BasicLayout");
                system.put("meta", Map.of(
                    "icon", "lucide:settings",
                    "order", 2,
                    "title", "系统管理"
                ));
                system.put("name", "System");
                system.put("path", "/system");

                List<Map<String, Object>> systemChildren = new ArrayList<>();
                systemChildren.add(Map.of(
                    "name", "UserManagement",
                    "path", "/system/users",
                    "component", "/views/system/UserManagement.vue",
                    "meta", Map.of(
                        "icon", "lucide:users",
                        "title", "用户管理"
                    )
                ));
                systemChildren.add(Map.of(
                    "name", "SystemLogs",
                    "path", "/system/logs",
                    "component", "/views/system/SystemLogs.vue",
                    "meta", Map.of(
                        "icon", "lucide:file-text",
                        "title", "系统日志"
                    )
                ));
                systemChildren.add(Map.of(
                    "name", "SystemSettings",
                    "path", "/system/settings",
                    "component", "/views/system/SystemSettings.vue",
                    "meta", Map.of(
                        "icon", "lucide:cog",
                        "title", "系统设置"
                    )
                ));

                system.put("children", systemChildren);
                menus.add(system);
            }

            logger.info("菜单获取成功，用户: {}, 菜单数量: {}",
                user != null ? user.getUsername() : "anonymous", menus.size());

            return success(menus);

        } catch (Exception e) {
            logger.error("获取菜单失败", e);
            return handleException(e, "获取菜单");
        }
    }

    @GetMapping("/user")
    public ResponseEntity<ApiResponse<Object>> getUserMenus(@AuthenticationPrincipal User user) {
        try {
            // 这个接口返回用户特定的菜单，可以根据权限进行过滤
            return getAllMenus(user);
        } catch (Exception e) {
            logger.error("获取用户菜单失败", e);
            return ResponseEntity.status(500).body(new ApiResponse<>(500, "获取用户菜单失败: " + e.getMessage(), null, false));
        }
    }
}
