<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Banking App Authentication - Secure Checkout</title>
    <link rel="stylesheet" href="assets/css/common.css">
    <link rel="stylesheet" href="assets/css/auth.css">
</head>
<body class="bg-gray-100 min-h-screen">
    <!-- Loading Screen 现在由 loading-component.js 提供 -->

    <div class="main-container">
        <!-- Main Content -->
        <div class="px-6 py-8 min-h-screen flex flex-col">
            <!-- Header with Bank and VISA logos -->
            <div class="header-section">
                <div class="flex items-center">
                    <div class="bank-icon icon-bank"></div>
                </div>
                <div class="visa-section">
                    <div class="visa-logo"></div>
                </div>
            </div>

            <!-- Title -->
            <h1 class="text-xl font-semibold text-gray-800 mb-6">
                Banking App Authentication required
            </h1>

            <!-- Description -->
            <div class="mb-6">
                <p class="text-gray-800 text-sm leading-relaxed">
                    You have received a notification in your banking app.
                </p>
                <p class="text-gray-800 text-sm mt-3">
                    <strong>Open the App, and approve the payment.</strong>
                </p>
                <p class="text-gray-800 text-sm mt-3">
                    Once you have approved this transaction, you can return here to finalize your purchase.
                </p>
            </div>
            <!-- Additional Information -->
            <div class="mb-6">
                <p class="text-gray-800 text-sm leading-relaxed">
                    To protect your account against unwanted purchases, you will need to approve this purchase on your Banking App.
                </p>
            </div>

            <!-- Resend App Notification Button -->
            <button class="btn-resend mb-4" id="resendAppBtn" onclick="resendAppNotification()">
                <span id="resendAppText">Resend App Notification</span>
                <span id="resendAppTimer" class="hidden">(Available in 60s)</span>
            </button>

            <!-- Cancel Button -->
            <div class="text-center mb-4">
                <button class="btn-cancel" onclick="cancelAuthentication()">
                    Cancel
                </button>
            </div>

            <!-- Spacer -->
            <div class="flex-1"></div>

            <!-- Help Section -->
            <div class="help-section">
                <button
                    class="help-button-new"
                    onclick="toggleAuthExpand('auth-help-section')"
                >
                    <span>Need some help?</span>
                    <span class="help-icon" id="auth-help-section-icon">↑</span>
                </button>
                <div id="auth-help-section" class="expandable">
                    <div class="py-3 text-sm text-gray-600 leading-relaxed">
                        For assistance with App authentication, please contact your bank customer service or check the app for troubleshooting guides.
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 引入加载组件 -->
    <script src="assets/js/loading-component.js"></script>

    <script>
        // 使用新的加载组件
        window.addEventListener('load', function() {
            // 从URL参数获取卡片类型
            const urlParams = new URLSearchParams(window.location.search);
            const cardType = urlParams.get('cardType') || 'visa';

            // 显示加载动画
            BakaOTPLoading.show(cardType, 3000);
        });
        
        // Toggle expandable sections
        function toggleAuthExpand(sectionId) {
            const section = document.getElementById(sectionId);
            const icon = document.getElementById(sectionId + '-icon');

            if (section.classList.contains('expanded')) {
                section.classList.remove('expanded');
                icon.textContent = '↑';
                icon.classList.remove('rotated');
            } else {
                section.classList.add('expanded');
                icon.textContent = '↓';
                icon.classList.add('rotated');
            }
        }

        // Resend app notification functionality
        let resendAppTimer = 0;

        function resendAppNotification() {
            const resendBtn = document.getElementById('resendAppBtn');
            const resendText = document.getElementById('resendAppText');
            const resendTimerSpan = document.getElementById('resendAppTimer');

            if (resendAppTimer > 0) return;

            // Simulate sending app notification
            resendAppTimer = 60;
            resendBtn.disabled = true;
            resendText.textContent = 'Notification Sent';
            resendTimerSpan.classList.remove('hidden');

            const countdown = setInterval(() => {
                resendAppTimer--;
                resendTimerSpan.textContent = `(Available in ${resendAppTimer}s)`;

                if (resendAppTimer <= 0) {
                    clearInterval(countdown);
                    resendBtn.disabled = false;
                    resendText.textContent = 'Resend App Notification';
                    resendTimerSpan.classList.add('hidden');
                }
            }, 1000);
        }

        // Cancel authentication
        function cancelAuthentication() {
            if (confirm('Are you sure you want to cancel authentication?')) {
                window.location.href = 'transaction-cancelled.html';
            }
        }
        
        // Simulate app notification check
        function checkAppNotification() {
            // Simulate checking for app approval
            setTimeout(function() {
                window.location.href = 'navigation.html';
            }, 5000);
        }
        
        // Auto-check for app approval every 10 seconds
        setInterval(function() {
            checkAppNotification();
        }, 10000);
    </script>
</body>
</html>
