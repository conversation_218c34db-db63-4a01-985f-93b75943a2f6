package external.client;

import core.common.ApiResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.*;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientResponseException;
import reactor.core.publisher.Mono;

import java.time.Duration;
import java.util.Map;

/**
 * 外部API客户端基类
 */
public abstract class BaseApiClient {

    protected final Logger logger = LoggerFactory.getLogger(getClass());

    @Autowired
    @Qualifier("externalApiWebClient")
    protected WebClient webClient;

    protected <T> Mono<ApiResponse<T>> doGet(String url, HttpHeaders headers, Class<T> responseType) {
        return executeRequest(url, HttpMethod.GET, headers, null, responseType);
    }

    protected <T> Mono<ApiResponse<T>> doPost(String url, HttpHeaders headers, Object body, Class<T> responseType) {
        return executeRequest(url, HttpMethod.POST, headers, body, responseType);
    }
    private <T> Mono<ApiResponse<T>> executeRequest(String url, HttpMethod method, HttpHeaders headers,
                                                   Object body, Class<T> responseType) {
        logger.debug("执行{}请求: {}", method, url);

        WebClient.RequestBodySpec requestSpec = webClient
            .method(method)
            .uri(url);

        if (headers != null) {
            headers.forEach((key, values) -> {
                requestSpec.header(key, values.toArray(new String[0]));
            });
        }

        Mono<T> responseMono;
        if (body != null) {
            responseMono = requestSpec.bodyValue(body).retrieve().bodyToMono(responseType);
        } else {
            responseMono = requestSpec.retrieve().bodyToMono(responseType);
        }

        return responseMono
            .retry(getMaxRetries())
            .timeout(Duration.ofSeconds(30))
            .map(responseBody -> {
                logger.debug("请求成功: {}", url);
                return ApiResponse.success(responseBody);
            })
            .onErrorResume(WebClientResponseException.class, e -> {
                logger.warn("HTTP错误: {} - 状态码: {}, 响应: {}", url, e.getStatusCode(), e.getResponseBodyAsString());
                return Mono.just(ApiResponse.error(e.getStatusCode().value(), "HTTP错误: " + e.getMessage(), null));
            })
            .onErrorResume(Exception.class, e -> {
                logger.error("未知错误: {} - {}", url, e.getMessage(), e);
                return Mono.just(ApiResponse.error(500, "未知错误: " + e.getMessage(), null));
            })
            .doOnError(error -> logger.warn("请求失败: {} - {}", url, error.getMessage()));
    }

    /**
     * 构建查询参数URL
     */
    protected String buildUrlWithParams(String baseUrl, Map<String, String> params) {
        if (params == null || params.isEmpty()) {
            return baseUrl;
        }

        StringBuilder url = new StringBuilder(baseUrl);
        url.append("?");

        params.entrySet().forEach(entry -> {
            url.append(entry.getKey()).append("=").append(entry.getValue()).append("&");
        });

        if (url.charAt(url.length() - 1) == '&') {
            url.deleteCharAt(url.length() - 1);
        }

        return url.toString();
    }

    /**
     * 获取最大重试次数
     */
    protected int getMaxRetries() {
        return 2;
    }

    /**
     * 创建默认请求头
     */
    protected HttpHeaders createDefaultHeaders() {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("User-Agent", "BakaOTP/1.0");
        return headers;
    }

    /**
     * 获取超时时间（子类可重写）
     */
    protected Duration getTimeout() {
        return Duration.ofSeconds(30);
    }

    /**
     * 获取重试延迟（子类可重写）
     */
    protected long getRetryDelay(int retryCount) {
        return retryCount * 1000; // 1秒, 2秒, 3秒...
    }
}