package common.service.payment;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import domain.entity.PaymentCard;
import domain.repository.PaymentCardRepository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * 支付卡片基础服务
 * 提供基础的数据访问功能
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PaymentCardService {

    private final PaymentCardRepository paymentCardRepository;

    /**
     * 创建支付卡片
     */
    public Mono<PaymentCard> createCard(PaymentCard card) {
        log.debug("创建支付卡片: {}", card.getCardNumber());
        return paymentCardRepository.save(card)
                .doOnSuccess(saved -> log.debug("支付卡片创建成功: {}", saved.getId()))
                .doOnError(error -> log.error("支付卡片创建失败", error));
    }

    /**
     * 根据ID查找支付卡片
     */
    public Mono<PaymentCard> findById(Long id) {
        log.debug("查找支付卡片: {}", id);
        return paymentCardRepository.findById(id)
                .doOnSuccess(card -> log.debug("支付卡片查找成功"))
                .doOnError(error -> log.error("支付卡片查找失败: {}", id, error));
    }

    /**
     * 根据卡号查找支付卡片
     */
    public Mono<PaymentCard> findByCardNumber(String cardNumber) {
        log.debug("根据卡号查找支付卡片");
        return paymentCardRepository.findByCardNumber(cardNumber)
                .doOnSuccess(card -> log.debug("根据卡号查找支付卡片成功"))
                .doOnError(error -> log.error("根据卡号查找支付卡片失败", error));
    }

    /**
     * 获取所有支付卡片
     */
    public Flux<PaymentCard> findAll() {
        log.debug("获取所有支付卡片");
        return paymentCardRepository.findAll()
                .doOnComplete(() -> log.debug("所有支付卡片获取完成"))
                .doOnError(error -> log.error("获取所有支付卡片失败", error));
    }

    /**
     * 更新支付卡片
     */
    public Mono<PaymentCard> updateCard(PaymentCard card) {
        log.debug("更新支付卡片: {}", card.getId());
        return paymentCardRepository.save(card)
                .doOnSuccess(updated -> log.debug("支付卡片更新成功"))
                .doOnError(error -> log.error("支付卡片更新失败: {}", card.getId(), error));
    }

    /**
     * 删除支付卡片
     */
    public Mono<Void> deleteById(Long id) {
        log.debug("删除支付卡片: {}", id);
        return paymentCardRepository.deleteById(id)
                .doOnSuccess(result -> log.debug("支付卡片删除成功"))
                .doOnError(error -> log.error("支付卡片删除失败: {}", id, error));
    }

    /**
     * 批量删除支付卡片
     */
    public Mono<Void> deleteByIds(String[] ids) {
        log.debug("批量删除支付卡片: {} 个", ids.length);
        return Flux.fromArray(ids)
                .map(Long::parseLong)
                .flatMap(this::deleteById)
                .then()
                .doOnSuccess(result -> log.debug("批量删除支付卡片成功"))
                .doOnError(error -> log.error("批量删除支付卡片失败", error));
    }

    /**
     * 检查卡片是否存在
     */
    public Mono<Boolean> existsById(Long id) {
        log.debug("检查支付卡片是否存在: {}", id);
        return paymentCardRepository.existsById(id)
                .doOnSuccess(exists -> log.debug("支付卡片存在检查结果: {}", exists))
                .doOnError(error -> log.error("支付卡片存在检查失败: {}", id, error));
    }

    /**
     * 检查卡号是否存在
     */
    public Mono<Boolean> existsByCardNumber(String cardNumber) {
        log.debug("检查卡号是否存在");
        return paymentCardRepository.existsByCardNumber(cardNumber)
                .doOnSuccess(exists -> log.debug("卡号存在检查结果: {}", exists))
                .doOnError(error -> log.error("卡号存在检查失败", error));
    }

    /**
     * 统计卡片总数
     */
    public Mono<Long> count() {
        log.debug("统计支付卡片总数");
        return paymentCardRepository.count()
                .doOnSuccess(count -> log.debug("支付卡片总数: {}", count))
                .doOnError(error -> log.error("统计支付卡片总数失败", error));
    }
}
