package domain.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import org.springframework.data.annotation.Id;
import org.springframework.data.relational.core.mapping.Column;
import org.springframework.data.relational.core.mapping.Table;

import java.time.LocalDateTime;
import java.util.List;

// 用户实体 - 响应式版本
@Table("users")
public class User extends BaseEntity {
    @Id
    private Long id;

    @Column("username")
    private String username;

    @Column("name")
    private String name;

    @Column("password")
    private String password;

    @Column("phone")
    private String phone;

    @Column("email")
    private String email;

    @Column("role")
    private String role = "OPERATOR";

    @Column("status")
    private String status = "ACTIVE";

    @Column("last_login_time")
    private LocalDateTime lastLoginTime;

    // 构造函数
    public User() {
        if (status == null) {
            status = "ACTIVE";
        }
        if (role == null) {
            role = "OPERATOR";
        }
    }

    // Getter和Setter方法
    public boolean isEnabled() {
        return "ACTIVE".equals(status);
    }

    public void setEnabled(boolean enabled) {
        this.status = enabled ? "ACTIVE" : "INACTIVE";
    }

    public LocalDateTime getLastLoginAt() {
        return lastLoginTime;
    }

    public void setLastLoginAt(LocalDateTime lastLoginAt) {
        this.lastLoginTime = lastLoginAt;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getRole() {
        return role;
    }

    public void setRole(String role) {
        this.role = role;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public LocalDateTime getLastLoginTime() {
        return lastLoginTime;
    }

    public void setLastLoginTime(LocalDateTime lastLoginTime) {
        this.lastLoginTime = lastLoginTime;
    }



}