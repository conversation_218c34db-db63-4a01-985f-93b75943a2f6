package common.service.cache;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.ReactiveRedisTemplate;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;
import reactor.util.retry.Retry;

import java.time.Duration;
import java.util.Map;

/**
 * 响应式缓存服务
 * 提供事务性缓存操作，确保数据一致性
 */
@Service
public class ReactiveCacheService {

    private static final Logger logger = LoggerFactory.getLogger(ReactiveCacheService.class);

    @Autowired
    private ReactiveRedisTemplate<String, Object> reactiveRedisTemplate;

    /**
     * 设置缓存值
     */
    public <T> Mono<Boolean> set(String key, T value, Duration ttl) {
        return reactiveRedisTemplate.opsForValue()
                .set(key, value, ttl)
                .doOnSuccess(result -> {
                    if (result) {
                        logger.debug("缓存设置成功: key={}, ttl={}", key, ttl);
                    } else {
                        logger.warn("缓存设置失败: key={}", key);
                    }
                })
                .doOnError(error -> logger.error("缓存设置异常: key={}", key, error))
                .retryWhen(Retry.backoff(3, Duration.ofMillis(100)));
    }

    /**
     * 获取缓存值
     */
    @SuppressWarnings("unchecked")
    public <T> Mono<T> get(String key, Class<T> type) {
        return reactiveRedisTemplate.opsForValue()
                .get(key)
                .cast(type)
                .doOnNext(value -> logger.debug("缓存命中: key={}", key))
                .doOnError(error -> logger.error("缓存获取异常: key={}", key, error))
                .onErrorReturn(null);
    }

    /**
     * 删除缓存
     */
    public Mono<Boolean> delete(String key) {
        return reactiveRedisTemplate.delete(key)
                .map(count -> count > 0)
                .doOnSuccess(result -> {
                    if (result) {
                        logger.debug("缓存删除成功: key={}", key);
                    } else {
                        logger.debug("缓存删除失败或key不存在: key={}", key);
                    }
                })
                .doOnError(error -> logger.error("缓存删除异常: key={}", key, error));
    }

    /**
     * 设置Hash缓存
     */
    public Mono<Boolean> setHash(String key, Map<String, Object> hash, Duration ttl) {
        return reactiveRedisTemplate.opsForHash()
                .putAll(key, hash)
                .then(reactiveRedisTemplate.expire(key, ttl))
                .doOnSuccess(result -> {
                    if (result) {
                        logger.debug("Hash缓存设置成功: key={}, fields={}", key, hash.keySet());
                    }
                })
                .doOnError(error -> logger.error("Hash缓存设置异常: key={}", key, error));
    }

    /**
     * 获取Hash缓存
     */
    public Mono<Map<Object, Object>> getHash(String key) {
        return reactiveRedisTemplate.opsForHash()
                .entries(key)
                .collectMap(Map.Entry::getKey, Map.Entry::getValue)
                .doOnNext(hash -> logger.debug("Hash缓存命中: key={}, size={}", key, hash.size()))
                .doOnError(error -> logger.error("Hash缓存获取异常: key={}", key, error));
    }

    /**
     * 检查key是否存在
     */
    public Mono<Boolean> exists(String key) {
        return reactiveRedisTemplate.hasKey(key)
                .doOnNext(exists -> logger.debug("缓存存在检查: key={}, exists={}", key, exists))
                .doOnError(error -> logger.error("缓存存在检查异常: key={}", key, error));
    }

    /**
     * 设置过期时间
     */
    public Mono<Boolean> expire(String key, Duration ttl) {
        return reactiveRedisTemplate.expire(key, ttl)
                .doOnSuccess(result -> {
                    if (result) {
                        logger.debug("缓存过期时间设置成功: key={}, ttl={}", key, ttl);
                    }
                })
                .doOnError(error -> logger.error("缓存过期时间设置异常: key={}", key, error));
    }

    /**
     * 原子性递增
     */
    public Mono<Long> increment(String key, long delta, Duration ttl) {
        return reactiveRedisTemplate.opsForValue()
                .increment(key, delta)
                .flatMap(value -> {
                    if (value == delta) {
                        // 首次设置，需要设置过期时间
                        return reactiveRedisTemplate.expire(key, ttl)
                                .thenReturn(value);
                    }
                    return Mono.just(value);
                })
                .doOnNext(value -> logger.debug("缓存递增: key={}, delta={}, result={}", key, delta, value))
                .doOnError(error -> logger.error("缓存递增异常: key={}", key, error));
    }

    /**
     * 获取剩余过期时间
     */
    public Mono<Duration> getExpire(String key) {
        return reactiveRedisTemplate.getExpire(key)
                .doOnNext(ttl -> logger.debug("缓存剩余时间: key={}, ttl={}", key, ttl))
                .doOnError(error -> logger.error("获取缓存剩余时间异常: key={}", key, error));
    }
}
