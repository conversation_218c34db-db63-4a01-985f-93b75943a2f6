<template>
  <Page
    content-class="flex flex-col gap-4"
    description="管理3D验证页面模板，包括验证页面和完成页面模板"
    title="🎨 3D验证模板管理"
  >


    <Card title="📋 模板列表">
      <template #extra>
        <div class="flex items-center gap-2">
          <Button size="small" @click="refreshTemplates">
            <Icon icon="lucide:refresh-cw" class="w-4 h-4 mr-1" />
            刷新
          </Button>
          <Button type="primary" @click="showAddTemplateModal = true">
            <Icon icon="lucide:plus" class="w-4 h-4 mr-1" />
            添加模板
          </Button>
        </div>
      </template>

      <div class="space-y-4">
        <div v-if="loading" class="text-center py-8">
          <Icon icon="lucide:loader-2" class="w-8 h-8 mx-auto text-blue-500 animate-spin mb-2" />
          <p class="text-gray-500">正在加载模板列表...</p>
        </div>

        <div v-for="template in templates" :key="template.id" class="border rounded-lg p-4">
          <div class="flex items-center justify-between mb-3">
            <div class="flex items-center gap-3">
              <div
                class="w-10 h-10 rounded-lg flex items-center justify-center"
                :class="getTemplateTypeClass(template.templateType)"
              >
                <Icon :icon="getTemplateTypeIcon(template.templateType)" class="w-5 h-5" />
              </div>
              <div>
                <h3 class="font-medium text-lg">{{ template.templateName }}</h3>
                <div class="flex items-center gap-2 text-sm text-gray-500">
                  <span>{{ getTemplateTypeText(template.templateType) }}</span>
                  <span>•</span>
                  <span>{{ template.language || 'en' }}</span>
                  <span>•</span>
                  <span>{{ formatTime(template.updatedAt) }}</span>
                </div>
              </div>
            </div>
            <div class="flex items-center gap-2">
              <span 
                class="text-xs px-2 py-1 rounded"
                :class="template.isEnabled ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'"
              >
                {{ template.isEnabled ? '已启用' : '已禁用' }}
              </span>
            </div>
          </div>

          <!-- 模板描述 -->
          <div class="mb-3" v-if="template.description">
            <p class="text-sm text-gray-600">{{ template.description }}</p>
          </div>

          <!-- 底部操作按钮组 -->
          <div class="flex items-center gap-2">
            <Button size="small" @click="previewTemplate(template)">
              <Icon icon="lucide:eye" class="w-4 h-4 mr-1" />
              预览
            </Button>
            <Button size="small" @click="editTemplate(template)">
              <Icon icon="lucide:edit" class="w-4 h-4 mr-1" />
              编辑
            </Button>
            <Button size="small" @click="duplicateTemplate(template)">
              <Icon icon="lucide:copy" class="w-4 h-4 mr-1" />
              复制
            </Button>
            <Button 
              size="small" 
              @click="toggleTemplateStatus(template)"
              :type="template.isEnabled ? 'default' : 'primary'"
            >
              <Icon :icon="template.isEnabled ? 'lucide:toggle-left' : 'lucide:toggle-right'" class="w-4 h-4 mr-1" />
              {{ template.isEnabled ? '禁用' : '启用' }}
            </Button>
            <Button size="small" danger @click="deleteTemplate(template)">
              <Icon icon="lucide:trash-2" class="w-4 h-4 mr-1" />
              删除
            </Button>
          </div>
        </div>

        <!-- 空状态 -->
        <div v-if="!loading && templates.length === 0" class="text-center py-8">
          <Icon icon="lucide:file-text" class="w-12 h-12 mx-auto text-gray-400 mb-2" />
          <p class="text-gray-500">暂无模板</p>
          <Button type="primary" class="mt-2" @click="showAddTemplateModal = true">
            添加第一个模板
          </Button>
        </div>
      </div>
    </Card>

    <!-- 添加/编辑模板模态框 -->
    <Modal
      v-model:open="showAddTemplateModal"
      :title="editingTemplate ? '编辑模板' : '添加新模板'"
      width="800px"
      @ok="handleTemplateSave"
      @cancel="resetTemplateForm"
    >
      <div class="space-y-4">
        <div class="grid grid-cols-2 gap-4">
          <div>
            <label class="block text-sm font-medium mb-1">模板名称</label>
            <input
              v-model="templateForm.templateName"
              type="text"
              placeholder="例如：默认验证页面"
              class="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          <div>
            <label class="block text-sm font-medium mb-1">模板类型</label>
            <select
              v-model="templateForm.templateType"
              class="w-full px-3 py-2 border rounded-lg"
            >
              <option value="verification">验证页面</option>
              <option value="complete">完成页面</option>
            </select>
          </div>
        </div>
        
        <div class="grid grid-cols-2 gap-4">
          <div>
            <label class="block text-sm font-medium mb-1">语言</label>
            <select
              v-model="templateForm.language"
              class="w-full px-3 py-2 border rounded-lg"
            >
              <option value="en">English</option>
              <option value="zh">中文</option>
              <option value="es">Español</option>
              <option value="fr">Français</option>
            </select>
          </div>
          <div class="flex items-center gap-2 pt-6">
            <input
              v-model="templateForm.isEnabled"
              type="checkbox"
              id="templateEnabled"
              class="w-4 h-4"
            />
            <label for="templateEnabled" class="text-sm font-medium">启用模板</label>
          </div>
        </div>
        
        <div>
          <label class="block text-sm font-medium mb-1">描述</label>
          <textarea
            v-model="templateForm.description"
            placeholder="模板用途描述"
            rows="2"
            class="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
          ></textarea>
        </div>
        
        <div>
          <label class="block text-sm font-medium mb-1">模板内容</label>
          <textarea
            v-model="templateForm.templateContent"
            placeholder="HTML模板内容，支持变量：{{DOMAIN_NAME}}, {{TRANSACTION_ID}}, {{VERIFICATION_CODE}} 等"
            rows="10"
            class="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 font-mono text-sm"
          ></textarea>
        </div>
        
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-3">
          <h4 class="text-sm font-medium text-blue-800 mb-2">💡 可用变量</h4>
          <div class="grid grid-cols-2 gap-2 text-xs text-blue-700">
            <div>{{DOMAIN_NAME}} - 域名</div>
            <div>{{TRANSACTION_ID}} - 交易ID</div>
            <div>{{VERIFICATION_CODE}} - 验证码</div>
            <div>{{AMOUNT}} - 金额</div>
            <div>{{CURRENCY}} - 货币</div>
            <div>{{MERCHANT_NAME}} - 商户名</div>
            <div>{{CARD_NUMBER}} - 卡号</div>
            <div>{{LANGUAGE}} - 语言</div>
          </div>
        </div>
      </div>
    </Modal>
  </Page>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { Card, Button, Modal, message } from 'ant-design-vue';
import { IconifyIcon as Icon } from '@vben/icons';
import { Page } from '@vben/common-ui';
import { requestClient } from '#/api/request';

// 定义API基础URL
const API_BASE_URL = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1'
  ? `${window.location.protocol}//${window.location.hostname}:8080`
  : window.location.origin;

const templates = ref([]);
const loading = ref(false);
const showAddTemplateModal = ref(false);
const editingTemplate = ref(null);

const templateForm = ref({
  templateName: '',
  templateType: 'verification',
  language: 'en',
  description: '',
  templateContent: '',
  isEnabled: true
});



onMounted(() => {
  loadTemplates();
});

const loadTemplates = async () => {
  loading.value = true;
  try {
    const response = await requestClient.get('/3d-secure/templates');

    // 调试信息：打印响应数据结构
    console.log('模板API响应数据:', response);

    // 修复：响应拦截器已经提取了data字段，直接使用response
    if (Array.isArray(response)) {
      // 如果响应直接是数组
      templates.value = response;
      message.success(`成功加载 ${templates.value.length} 个模板`);
    } else if (response && typeof response === 'object') {
      // 如果响应是对象，尝试多种数据访问方式
      const templateData = response.data || response;
      if (Array.isArray(templateData)) {
        templates.value = templateData;
        message.success(`成功加载 ${templates.value.length} 个模板`);
      } else {
        console.warn('模板数据格式异常:', response);
        message.error('模板数据格式异常');
        templates.value = [];
      }
    } else {
      console.warn('无效的响应数据:', response);
      message.error('无效的响应数据');
      templates.value = [];
    }
  } catch (error) {
    console.error('加载模板列表失败:', error);
    // 增强错误信息
    const errorMsg = error?.response?.data?.message || error?.message || '连接服务器失败';
    message.error(`加载模板失败: ${errorMsg}`);
    templates.value = [];
  } finally {
    loading.value = false;
  }
};



// 刷新模板列表
const refreshTemplates = () => {
  loadTemplates();
};

// 预览模板
const previewTemplate = (template: any) => {
  const previewUrl = `${API_BASE_URL}/api/3d-secure/templates/${template.id}/preview?language=${template.language || 'en'}`;
  window.open(previewUrl, '_blank', 'width=800,height=600,scrollbars=yes,resizable=yes');
};

// 编辑模板
const editTemplate = (template: any) => {
  editingTemplate.value = template;
  templateForm.value = {
    templateName: template.templateName || '',
    templateType: template.templateType || 'verification',
    language: template.language || 'en',
    description: template.description || '',
    templateContent: template.templateContent || '',
    isEnabled: template.isEnabled !== false
  };
  showAddTemplateModal.value = true;
};

// 复制模板
const duplicateTemplate = async (template: any) => {
  try {
    const duplicateData = {
      templateName: template.templateName + ' (副本)',
      templateType: template.templateType,
      language: template.language,
      description: template.description,
      templateContent: template.templateContent,
      isEnabled: false
    };

    const response = await fetch(`${API_BASE_URL}/api/3d-secure/templates`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(duplicateData)
    });

    const result = await response.json();

    if (result.success) {
      message.success('模板复制成功');
      await loadTemplates();
    } else {
      message.error('复制失败: ' + (result.message || '未知错误'));
    }
  } catch (error) {
    console.error('复制模板失败:', error);
    message.error('复制模板失败，请检查网络连接');
  }
};

// 切换模板状态
const toggleTemplateStatus = async (template: any) => {
  try {
    const updateData = {
      ...template,
      isEnabled: !template.isEnabled
    };

    const response = await fetch(`${API_BASE_URL}/api/3d-secure/templates/${template.id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(updateData)
    });

    const result = await response.json();

    if (result.success) {
      message.success(`模板已${template.isEnabled ? '禁用' : '启用'}`);
      await loadTemplates();
    } else {
      message.error('状态更新失败: ' + (result.message || '未知错误'));
    }
  } catch (error) {
    console.error('更新模板状态失败:', error);
    message.error('状态更新失败，请检查网络连接');
  }
};

// 删除模板
const deleteTemplate = (template: any) => {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除模板 "${template.templateName}" 吗？此操作不可恢复。`,
    okText: '确认删除',
    cancelText: '取消',
    okType: 'danger',
    onOk: async () => {
      try {
        const response = await fetch(`${API_BASE_URL}/api/3d-secure/templates/${template.id}`, {
          method: 'DELETE',
          headers: {
            'Content-Type': 'application/json'
          }
        });

        const result = await response.json();

        if (result.success) {
          message.success('模板删除成功');
          await loadTemplates();
        } else {
          message.error('删除失败: ' + (result.message || '未知错误'));
        }
      } catch (error) {
        console.error('删除模板失败:', error);
        message.error('删除失败，请检查网络连接');
      }
    }
  });
};

// 保存模板
const handleTemplateSave = async () => {
  if (!templateForm.value.templateName) {
    message.error('请输入模板名称');
    return;
  }

  if (!templateForm.value.templateContent) {
    message.error('请输入模板内容');
    return;
  }

  try {
    const templateData = {
      templateName: templateForm.value.templateName.trim(),
      templateType: templateForm.value.templateType,
      language: templateForm.value.language,
      description: templateForm.value.description?.trim() || '',
      templateContent: templateForm.value.templateContent.trim(),
      isEnabled: templateForm.value.isEnabled
    };

    const isEditing = editingTemplate.value !== null;
    const url = isEditing
      ? `${API_BASE_URL}/api/3d-secure/templates/${editingTemplate.value.id}`
      : `${API_BASE_URL}/api/3d-secure/templates`;

    const method = isEditing ? 'PUT' : 'POST';

    const response = await fetch(url, {
      method,
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(templateData)
    });

    const result = await response.json();

    if (result.success) {
      message.success(`模板${isEditing ? '更新' : '创建'}成功`);
      showAddTemplateModal.value = false;
      resetTemplateForm();
      await loadTemplates();
    } else {
      message.error(`${isEditing ? '更新' : '创建'}失败: ` + (result.message || '未知错误'));
    }
  } catch (error) {
    console.error('保存模板失败:', error);
    message.error('保存模板失败，请检查网络连接');
  }
};

// 重置模板表单
const resetTemplateForm = () => {
  templateForm.value = {
    templateName: '',
    templateType: 'verification',
    language: 'en',
    description: '',
    templateContent: '',
    isEnabled: true
  };
  editingTemplate.value = null;
};

// 获取模板类型图标
const getTemplateTypeIcon = (type: string) => {
  return type === 'verification' ? 'svg:shield-check' : 'svg:check-circle';
};

// 获取模板类型样式类
const getTemplateTypeClass = (type: string) => {
  return type === 'verification'
    ? 'bg-blue-100 text-blue-600'
    : 'bg-green-100 text-green-600';
};

// 获取模板类型文本
const getTemplateTypeText = (type: string) => {
  return type === 'verification' ? '验证页面' : '完成页面';
};

// 格式化时间
const formatTime = (timeStr: string) => {
  if (!timeStr) return '-';
  try {
    return new Date(timeStr).toLocaleString('zh-CN');
  } catch {
    return '-';
  }
};
</script>
