import type { RouteRecordRaw } from 'vue-router';

const routes: RouteRecordRaw[] = [
  {
    meta: {
      icon: 'svg:download',
      keepAlive: true,
      order: 1000,
      title: '开发参考',
    },
    name: 'DevReference',
    path: '/dev-reference',
    children: [
      // 表单示例
      {
        name: 'FormExample',
        path: 'form',
        meta: {
          icon: 'svg:form-select',
          title: '表单组件',
        },
        children: [
          {
            name: 'FormBasicExample',
            path: 'basic',
            component: () => import('#/views/examples/form/basic.vue'),
            meta: {
              title: '基础表单',
            },
          },
          {
            name: 'FormQueryExample',
            path: 'query',
            component: () => import('#/views/examples/form/query.vue'),
            meta: {
              title: '查询表单',
            },
          },
          {
            name: 'FormRulesExample',
            path: 'rules',
            component: () => import('#/views/examples/form/rules.vue'),
            meta: {
              title: '表单验证',
            },
          },
          {
            name: 'FormDynamicExample',
            path: 'dynamic',
            component: () => import('#/views/examples/form/dynamic.vue'),
            meta: {
              title: '动态表单',
            },
          },
          {
            name: 'FormLayoutExample',
            path: 'custom-layout',
            component: () => import('#/views/examples/form/custom-layout.vue'),
            meta: {
              title: '自定义布局',
            },
          },
          {
            name: 'FormCustomExample',
            path: 'custom',
            component: () => import('#/views/examples/form/custom.vue'),
            meta: {
              title: '自定义组件',
            },
          },
          {
            name: 'FormApiExample',
            path: 'api',
            component: () => import('#/views/examples/form/api.vue'),
            meta: {
              title: 'API表单',
            },
          },
          {
            name: 'FormMergeExample',
            path: 'merge',
            component: () => import('#/views/examples/form/merge.vue'),
            meta: {
              title: '表单合并',
            },
          },
        ],
      },
      // 表格示例
      {
        name: 'VxeTableExample',
        path: 'vxe-table',
        meta: {
          icon: 'svg:table',
          title: '表格组件',
        },
        children: [
          {
            name: 'VxeTableBasicExample',
            path: 'basic',
            component: () => import('#/views/examples/vxe-table/basic.vue'),
            meta: {
              title: '基础表格',
            },
          },
          {
            name: 'VxeTableRemoteExample',
            path: 'remote',
            component: () => import('#/views/examples/vxe-table/remote.vue'),
            meta: {
              title: '远程数据',
            },
          },
          {
            name: 'VxeTableTreeExample',
            path: 'tree',
            component: () => import('#/views/examples/vxe-table/tree.vue'),
            meta: {
              title: '树形表格',
            },
          },
          {
            name: 'VxeTableFixedExample',
            path: 'fixed',
            component: () => import('#/views/examples/vxe-table/fixed.vue'),
            meta: {
              title: '固定列',
            },
          },
          {
            name: 'VxeTableCustomCellExample',
            path: 'custom-cell',
            component: () =>
              import('#/views/examples/vxe-table/custom-cell.vue'),
            meta: {
              title: '自定义单元格',
            },
          },
          {
            name: 'VxeTableFormExample',
            path: 'form',
            component: () => import('#/views/examples/vxe-table/form.vue'),
            meta: {
              title: '表格表单',
            },
          },
          {
            name: 'VxeTableEditCellExample',
            path: 'edit-cell',
            component: () => import('#/views/examples/vxe-table/edit-cell.vue'),
            meta: {
              title: '单元格编辑',
            },
          },
          {
            name: 'VxeTableEditRowExample',
            path: 'edit-row',
            component: () => import('#/views/examples/vxe-table/edit-row.vue'),
            meta: {
              title: '行编辑',
            },
          },
          {
            name: 'VxeTableVirtualExample',
            path: 'virtual',
            component: () => import('#/views/examples/vxe-table/virtual.vue'),
            meta: {
              title: '虚拟滚动',
            },
          },
        ],
      },
      // 权限控制
      {
        meta: {
          icon: 'svg:shield-key-outline',
          title: '权限控制',
        },
        name: 'AccessDemos',
        path: 'access',
        children: [
          {
            name: 'AccessPageControlDemo',
            path: 'page-control',
            component: () => import('#/views/demos/access/index.vue'),
            meta: {
              icon: 'svg:page-previous-outline',
              title: '页面权限',
            },
          },
          {
            name: 'AccessButtonControlDemo',
            path: 'button-control',
            component: () => import('#/views/demos/access/button-control.vue'),
            meta: {
              icon: 'svg:button-cursor',
              title: '按钮权限',
            },
          },
          {
            name: 'AccessMenuVisible403Demo',
            path: 'menu-visible-403',
            component: () =>
              import('#/views/demos/access/menu-visible-403.vue'),
            meta: {
              authority: ['no-body'],
              icon: 'svg:bell',
              menuVisibleWithForbidden: true,
              title: '菜单可见403',
            },
          },
          {
            name: 'AccessSuperVisibleDemo',
            path: 'super-visible',
            component: () => import('#/views/demos/access/super-visible.vue'),
            meta: {
              authority: ['super'],
              icon: 'svg:bell',
              title: '超级管理员可见',
            },
          },
          {
            name: 'AccessAdminVisibleDemo',
            path: 'admin-visible',
            component: () => import('#/views/demos/access/admin-visible.vue'),
            meta: {
              authority: ['admin'],
              icon: 'svg:bell',
              title: '管理员可见',
            },
          },
          {
            name: 'AccessUserVisibleDemo',
            path: 'user-visible',
            component: () => import('#/views/demos/access/user-visible.vue'),
            meta: {
              authority: ['user'],
              icon: 'svg:bell',
              title: '用户可见',
            },
          },
        ],
      },
      // 验证码组件
      {
        name: 'CaptchaExample',
        path: 'captcha',
        meta: {
          icon: 'svg:shield-check',
          title: '验证码组件',
        },
        children: [
          {
            name: 'DragVerifyExample',
            path: 'slider',
            component: () =>
              import('#/views/examples/captcha/slider-captcha.vue'),
            meta: {
              title: '滑动验证',
            },
          },
          {
            name: 'RotateVerifyExample',
            path: 'slider-rotate',
            component: () =>
              import('#/views/examples/captcha/slider-rotate-captcha.vue'),
            meta: {
              title: '旋转验证',
            },
          },
          {
            name: 'CaptchaPointSelectionExample',
            path: 'point-selection',
            component: () =>
              import('#/views/examples/captcha/point-selection-captcha.vue'),
            meta: {
              title: '点选验证',
            },
          },
        ],
      },
      // 弹窗组件
      {
        name: 'ModalExample',
        path: 'modal',
        component: () => import('#/views/examples/modal/index.vue'),
        meta: {
          icon: 'svg:app-window',
          keepAlive: true,
          title: '弹窗组件',
        },
      },
      {
        name: 'DrawerExample',
        path: 'drawer',
        component: () => import('#/views/examples/drawer/index.vue'),
        meta: {
          icon: 'svg:toggle-right',
          keepAlive: true,
          title: '抽屉组件',
        },
      },
      // 其他组件
      {
        name: 'EllipsisExample',
        path: 'ellipsis',
        component: () => import('#/views/examples/ellipsis/index.vue'),
        meta: {
          icon: 'svg:plus',
          title: '文本省略',
        },
      },
      {
        name: 'VueResizeDemo',
        path: 'resize',
        component: () => import('#/views/examples/resize/basic.vue'),
        meta: {
          icon: 'svg:ring-resize',
          title: '尺寸调整',
        },
      },
      {
        name: 'ColPageDemo',
        path: 'col-page',
        component: () => import('#/views/examples/layout/col-page.vue'),
        meta: {
          badge: 'Alpha',
          badgeVariants: 'destructive',
          icon: 'svg:bars-scale',
          title: '列布局页面',
        },
      },
      {
        name: 'TippyDemo',
        path: 'tippy',
        component: () => import('#/views/examples/tippy/index.vue'),
        meta: {
          icon: 'svg:message-settings-outline',
          title: '提示组件',
        },
      },
      {
        name: 'JsonViewer',
        path: 'json-viewer',
        component: () => import('#/views/examples/json-viewer/index.vue'),
        meta: {
          icon: 'svg:json',
          title: 'JSON查看器',
        },
      },
      {
        name: 'Motion',
        path: 'motion',
        component: () => import('#/views/examples/motion/index.vue'),
        meta: {
          icon: 'svg:animation-play',
          title: '动画组件',
        },
      },
      {
        name: 'CountTo',
        path: 'count-to',
        component: () => import('#/views/examples/count-to/index.vue'),
        meta: {
          icon: 'svg:animation-play',
          title: '数字动画',
        },
      },
      {
        name: 'Loading',
        path: 'loading',
        component: () => import('#/views/examples/loading/index.vue'),
        meta: {
          icon: 'svg:circle-double',
          title: '加载组件',
        },
      },
      {
        name: 'ButtonGroup',
        path: 'button-group',
        component: () => import('#/views/examples/button-group/index.vue'),
        meta: {
          icon: 'svg:check-circle',
          title: '按钮组',
        },
      },
      // 功能演示
      {
        meta: {
          icon: 'svg:feature-highlight',
          title: '功能演示',
        },
        name: 'FeaturesDemos',
        path: 'features',
        children: [
          {
            name: 'LoginExpiredDemo',
            path: 'login-expired',
            component: () =>
              import('#/views/demos/features/login-expired/index.vue'),
            meta: {
              icon: 'svg:encryption-expiration',
              title: '登录过期',
            },
          },
          {
            name: 'IconsDemo',
            path: 'icons',
            component: () => import('#/views/demos/features/icons/index.vue'),
            meta: {
              icon: 'svg:annoyed',
              title: '图标库',
            },
          },
          {
            name: 'WatermarkDemo',
            path: 'watermark',
            component: () =>
              import('#/views/demos/features/watermark/index.vue'),
            meta: {
              icon: 'svg:tags',
              title: '水印功能',
            },
          },
          {
            name: 'FeatureTabsDemo',
            path: 'tabs',
            component: () => import('#/views/demos/features/tabs/index.vue'),
            meta: {
              icon: 'svg:app-window',
              title: '标签页',
            },
          },
          {
            name: 'FeatureTabDetailDemo',
            path: 'tabs/detail/:id',
            component: () =>
              import('#/views/demos/features/tabs/tab-detail.vue'),
            meta: {
              activePath: '/dev-reference/features/tabs',
              hideInMenu: true,
              maxNumOfOpenTab: 3,
              title: '标签详情',
            },
          },
          {
            name: 'FullScreenDemo',
            path: 'full-screen',
            component: () =>
              import('#/views/demos/features/full-screen/index.vue'),
            meta: {
              icon: 'lucide:fullscreen',
              title: '全屏功能',
            },
          },
          {
            name: 'FileDownloadDemo',
            path: 'file-download',
            component: () =>
              import('#/views/demos/features/file-download/index.vue'),
            meta: {
              icon: 'lucide:hard-drive-download',
              title: '文件下载',
            },
          },
          {
            name: 'ClipboardDemo',
            path: 'clipboard',
            component: () =>
              import('#/views/demos/features/clipboard/index.vue'),
            meta: {
              icon: 'lucide:copy',
              title: '剪贴板',
            },
          },
          {
            name: 'VueQueryDemo',
            path: 'vue-query',
            component: () =>
              import('#/views/demos/features/vue-query/index.vue'),
            meta: {
              icon: 'lucide:git-pull-request-arrow',
              title: 'Tanstack Query',
            },
          },
          {
            name: 'BigIntDemo',
            path: 'json-bigint',
            component: () =>
              import('#/views/demos/features/json-bigint/index.vue'),
            meta: {
              icon: 'lucide:grape',
              title: 'JSON BigInt',
            },
          },
        ],
      },
    ],
  },
];

export default routes;
