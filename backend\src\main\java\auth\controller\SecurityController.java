package auth.controller;

import core.common.ApiResponse;
import system.controller.BaseController;
import security.service.SecurityCheckService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

import java.util.HashMap;
import java.util.Map;

/**
 * 安全检查控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/security")
public class SecurityController extends BaseController {

    @Autowired
    private SecurityCheckService securityCheckService;

    @Autowired
    private core.service.SystemConfigService systemConfigService;

    @PostMapping("/check")
    public Mono<ResponseEntity<ApiResponse<Map<String, Object>>>> checkDomainSecurity(@RequestParam String domain) {
        if (domain == null || domain.trim().isEmpty()) {
            return Mono.just(error("域名不能为空"));
        }

        return securityCheckService.checkDomainSecurity(domain.trim())
            .map(result -> success(result, "域名安全检查完成"))
            .onErrorResume(Exception.class, e -> {
                logger.error("域名安全检查失败", e);
                return Mono.just(handleException(e, "域名安全检查"));
            });
    }

    /**
     * 批量检查域名安全状态
     */
    @PostMapping("/batch-check")
    public Mono<ResponseEntity<ApiResponse<Map<String, Map<String, Object>>>>> batchCheckDomainSecurity(
            @RequestBody String[] domains) {
        try {
            if (domains == null || domains.length == 0) {
                return Mono.just(error("域名列表不能为空"));
            }

            return securityCheckService.batchSecurityCheck(domains)
                .map(results -> success(results, "批量域名安全检查完成"))
                .onErrorResume(Exception.class, e -> {
                    logger.error("批量域名安全检查失败", e);
                    return Mono.just(handleException(e, "批量域名安全检查"));
                });
        } catch (Exception e) {
            return Mono.just(handleException(e, "批量域名安全检查"));
        }
    }

    /**
     * 异步检查域名安全状态
     */
    @PostMapping("/check-async")
    public ResponseEntity<ApiResponse<String>> checkDomainSecurityAsync(@RequestParam String domain) {
        try {
            if (domain == null || domain.trim().isEmpty()) {
                return error("域名不能为空");
            }

            securityCheckService.checkDomainSecurityAsync(domain.trim());
            return success("异步安全检查已启动，结果将通过WebSocket推送");
        } catch (Exception e) {
            return handleException(e, "异步域名安全检查");
        }
    }

    /**
     * 获取安全状态颜色代码
     */
    @GetMapping("/status-color")
    public ResponseEntity<ApiResponse<String>> getStatusColor(@RequestParam String status) {
        try {
            Map<String, Object> mockResult = Map.of("status", status);
            String color = securityCheckService.getStatusColor(mockResult);
            return success(color, "获取状态颜色成功");
        } catch (Exception e) {
            return handleException(e, "获取状态颜色");
        }
    }

    /**
     * 测试Google Transparency Report API连接（响应式）
     */
    @GetMapping("/test-api")
    public Mono<ResponseEntity<ApiResponse<Map<String, Object>>>> testGoogleTransparencyApi() {
        // 使用一个已知的测试域名
        String testDomain = "google.com";

        return securityCheckService.checkDomainSecurity(testDomain)
            .map(result -> {
                Map<String, Object> testResult = Map.of(
                    "testDomain", testDomain,
                    "apiWorking", result.get("source") != null,
                    "result", result
                );
                return success(testResult, "Google Transparency Report API测试完成");
            })
            .onErrorResume(Exception.class, e -> {
                logger.error("API测试失败", e);
                return Mono.just(handleException(e, "API测试"));
            });
    }

    /**
     * 获取安全检查统计信息
     */
    @GetMapping("/statistics")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getSecurityStatistics() {
        try {
            // 这里可以添加安全检查的统计信息
            Map<String, Object> statistics = Map.of(
                "totalChecks", 0,
                "safeCount", 0,
                "unsafeCount", 0,
                "partiallyUnsafeCount", 0,
                "noDataCount", 0,
                "errorCount", 0
            );

            return success(statistics, "获取安全检查统计信息成功");
        } catch (Exception e) {
            return handleException(e, "获取安全检查统计信息");
        }
    }

    /**
     * 获取安全配置
     */
    @GetMapping("/config")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getSecurityConfig() {
        try {
            Map<String, Object> config = new HashMap<>();

            // 从SystemConfigService获取安全配置
            var securityConfig = systemConfigService.getSecurity();
            var paymentSecurity = securityConfig.getPaymentSecurity();

            config.put("3d_secure_enabled", paymentSecurity.isThreeDSecureEnabled());
            config.put("3d2_enabled", paymentSecurity.isThreeD2Enabled());
            config.put("enable_network_type_check", paymentSecurity.isEnableNetworkTypeCheck());
            config.put("filter_crawler_user_agent", paymentSecurity.isFilterCrawlerUserAgent());

            return success(config, "获取安全配置成功");
        } catch (Exception e) {
            logger.error("获取安全配置失败", e);
            return handleException(e, "获取安全配置");
        }
    }

    /**
     * 更新安全配置
     */
    @PostMapping("/config")
    public Mono<ResponseEntity<ApiResponse<String>>> updateSecurityConfig(@RequestBody Map<String, Object> request) {
        logger.info("收到安全配置更新请求: {}", request);

        return systemConfigService.updateSecurityConfig(request)
            .map(unused -> success("配置更新成功并已持久化", "配置更新成功"))
            .onErrorResume(Exception.class, e -> {
                logger.error("更新安全配置失败", e);
                return Mono.just(handleException(e, "更新安全配置"));
            });
    }
}
