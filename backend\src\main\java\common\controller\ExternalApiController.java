package common.controller;

import system.controller.BaseController;
import core.common.ApiResponse;

import external.dto.BinLookupResult;
import external.service.ExternalApiService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

import java.util.HashMap;
import java.util.Map;

/**
 * 外部API管理控制器
 */
@RestController
@RequestMapping("/api/external")
@PreAuthorize("hasRole('ADMIN')")
public class ExternalApiController extends BaseController {

    @Autowired
    private ExternalApiService externalApiService;

    /**
     * 查询BIN码信息
     */
    @GetMapping("/bin-lookup/{bin}")
    public Mono<ResponseEntity<ApiResponse<Object>>> lookupBin(@PathVariable String bin) {
        if (!externalApiService.isValidBin(bin)) {
            return Mono.just(error("无效的BIN码格式，请提供6-8位数字"));
        }

        return externalApiService.lookupBin(bin)
            .map(response -> {
                if (response.isSuccess()) {
                    // 转换为业务对象
                    BinLookupResult binData = response.getData();
                    Object result = binData; // 直接返回简化的结果
                    return success(result, "BIN查询成功");
                } else {
                    return error(response.getStatusCode(), response.getMessage());
                }
            })
            .onErrorResume(Exception.class, e -> {
                logger.error("BIN查询失败", e);
                return Mono.just(handleException(e, "BIN查询"));
            });
    }

    /**
     * 批量查询BIN码信息
     * 
     * @param request 包含BIN码数组的请求
     * @return 批量查询结果
     */
    @PostMapping("/bin-lookup/batch")
    public Mono<ResponseEntity<ApiResponse<Map<String, Object>>>> batchLookupBin(@RequestBody Map<String, Object> request) {
        try {
            Object binsObj = request.get("bins");
            if (!(binsObj instanceof String[])) {
                return Mono.just(error("请提供有效的BIN码数组"));
            }
            
            String[] bins = (String[]) binsObj;
            if (bins.length == 0) {
                return Mono.just(error("BIN码数组不能为空"));
            }

            if (bins.length > 50) {
                return Mono.just(error("批量查询最多支持50个BIN码"));
            }

            // 验证所有BIN码格式
            for (String bin : bins) {
                if (!externalApiService.isValidBin(bin)) {
                    return Mono.just(error("无效的BIN码格式: " + bin));
                }
            }
            
            return externalApiService.batchLookupBin(bins)
                .map(results -> {
                    // 转换结果格式
                    Map<String, Object> formattedResults = new HashMap<>();
                    int successCount = 0;

                    for (Map.Entry<String, BinLookupResult> entry : results.entrySet()) {
                        String bin = entry.getKey();
                        BinLookupResult result = entry.getValue();

                        Map<String, Object> binResult = new HashMap<>();
                        if (result != null) {
                            binResult.put("success", true);
                            binResult.put("statusCode", 200);
                            binResult.put("message", "查询成功");
                            binResult.put("data", result.toCardBinInfo());
                            successCount++;
                        } else {
                            binResult.put("success", false);
                            binResult.put("statusCode", 404);
                            binResult.put("message", "查询失败");
                            binResult.put("data", null);
                        }

                        formattedResults.put(bin, binResult);
                    }

                    Map<String, Object> finalResult = new HashMap<>();
                    finalResult.put("results", formattedResults);
                    finalResult.put("total", bins.length);
                    finalResult.put("success", successCount);
                    finalResult.put("failed", bins.length - successCount);

                    return success(finalResult, "批量BIN查询完成");
                })
                .onErrorResume(e -> Mono.just(handleException((Exception) e, "批量BIN查询")));

        } catch (Exception e) {
            return Mono.just(handleException(e, "批量BIN查询"));
        }
    }

    /**
     * 获取外部API服务状态
     * 
     * @return 服务状态信息
     */
    @GetMapping("/status")
    public Mono<ResponseEntity<ApiResponse<Map<String, Object>>>> getExternalApiStatus() {
        try {
            return externalApiService.getAllExternalApiStatus()
                .map(status -> success(status, "获取服务状态成功"))
                .onErrorResume(e -> Mono.just(handleException((Exception) e, "获取外部API服务状态")));
        } catch (Exception e) {
            return Mono.just(handleException(e, "获取外部API服务状态"));
        }
    }

    /**
     * 获取BIN查询服务状态
     * 
     * @return BIN查询服务状态
     */
    @GetMapping("/bin-lookup/status")
    public ResponseEntity<ApiResponse<Object>> getBinLookupStatus() {
        try {
            Map<String, Object> status = externalApiService.getBinLookupServiceStatus();
            return success((Object) status, "获取BIN查询服务状态成功");
        } catch (Exception e) {
            logger.error("获取BIN查询服务状态失败", e);
            return handleException(e, "获取BIN查询服务状态");
        }
    }

    /**
     * 健康检查
     * 
     * @return 健康状态
     */
    @GetMapping("/health")
    public ResponseEntity<ApiResponse<Object>> healthCheck() {
        try {
            boolean healthy = externalApiService.isHealthy();
            Map<String, Object> health = new HashMap<>();
            health.put("healthy", healthy);
            health.put("status", healthy ? "UP" : "DOWN");
            health.put("timestamp", System.currentTimeMillis());
            
            if (healthy) {
                return success(health, "外部API服务健康");
            } else {
                return error(503, "外部API服务不健康", health);
            }
        } catch (Exception e) {
            return handleException(e, "外部API健康检查");
        }
    }

    // BIN查询缓存由Redis自动管理，无需手动缓存操作端点

    /**
     * 获取缓存统计信息
     * 
     * @return 缓存统计
     */
    @GetMapping("/cache/statistics")
    public Mono<ResponseEntity<ApiResponse<Map<String, Object>>>> getCacheStatistics() {
        try {
            return externalApiService.getCacheStatistics()
                .map(stats -> success(stats, "获取缓存统计成功"))
                .onErrorResume(e -> Mono.just(handleException((Exception) e, "获取缓存统计")));
        } catch (Exception e) {
            return Mono.just(handleException(e, "获取缓存统计"));
        }
    }

    /**
     * 测试BIN查询服务连接（响应式）
     *
     * @return 测试结果的Mono
     */
    @PostMapping("/bin-lookup/test")
    public Mono<ResponseEntity<ApiResponse<Object>>> testBinLookupConnection() {
        // 使用测试BIN码进行连接测试
        String testBin = "411111";

        return externalApiService.lookupBin(testBin)
            .map(response -> {
                Map<String, Object> testResult = new HashMap<>();
                testResult.put("testBin", testBin);
                testResult.put("success", response.isSuccess());
                testResult.put("statusCode", response.getStatusCode());
                testResult.put("message", response.getMessage());
                testResult.put("responseTime", System.currentTimeMillis());

                if (response.isSuccess()) {
                    testResult.put("hasData", response.getData() != null);
                }

                return success((Object) testResult, "BIN查询服务连接测试完成");
            })
            .onErrorResume(Exception.class, e -> {
                logger.error("BIN查询服务连接测试失败", e);
                ResponseEntity<ApiResponse<Object>> errorResponse = handleException(e, "BIN查询服务连接测试");
                return Mono.just(errorResponse);
            });
    }

    /**
     * 获取外部API配置信息
     * 
     * @return 配置信息
     */
    @GetMapping("/config")
    public ResponseEntity<ApiResponse<Object>> getExternalApiConfig() {
        try {
            Map<String, Object> binLookupStatus = externalApiService.getBinLookupServiceStatus();
            Map<String, Object> config = new HashMap<>();
            // 可以在这里添加其他外部API的配置信息
            return success((Object) config, "获取外部API配置成功");
        } catch (Exception e) {
            logger.error("获取外部API配置失败", e);
            return handleException(e, "获取外部API配置");
        }
    }
}
