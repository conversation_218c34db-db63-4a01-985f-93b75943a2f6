package admin.controller;

import core.common.ApiResponse;
import core.service.SystemConfigService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;
import system.controller.BaseController;

import java.util.HashMap;
import java.util.Map;

/**
 * 系统配置控制器
 */
@RestController
@RequestMapping("/api/config")
@PreAuthorize("hasRole('ADMIN')")
public class SystemConfigController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(SystemConfigController.class);

    @Autowired
    private SystemConfigService systemConfigService;

    /**
     * 获取系统配置
     */
    @GetMapping("/system")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getSystemConfig() {
        try {
            Map<String, Object> config = new HashMap<>();
            
            // 系统基础配置
            var systemConfig = systemConfigService.getSystem();
            config.put("name", systemConfig.getName());
            config.put("version", systemConfig.getVersion());
            config.put("maintenanceMode", systemConfig.isMaintenanceMode());
            config.put("debugMode", systemConfig.isDebugMode());
            config.put("logLevel", systemConfig.getLogLevel());
            
            // 功能配置
            var featuresConfig = systemConfigService.getFeatures();
            config.put("unattendedMode", featuresConfig.getCore().isUnattendedMode());
            config.put("websocketEnabled", featuresConfig.getCore().isWebsocketEnabled());
            config.put("autoBackup", featuresConfig.getCore().isAutoBackup());
            
            return success(config, "获取系统配置成功");
        } catch (Exception e) {
            logger.error("获取系统配置失败", e);
            return handleException(e, "获取系统配置");
        }
    }

    /**
     * 更新系统配置
     */
    @PostMapping("/system")
    public Mono<ResponseEntity<ApiResponse<String>>> updateSystemConfig(@RequestBody Map<String, Object> request) {
        logger.info("收到系统配置更新请求: {}", request);
        
        try {
            // 更新系统配置
            if (request.containsKey("maintenanceMode")) {
                systemConfigService.getSystem().setMaintenanceMode((Boolean) request.get("maintenanceMode"));
            }
            if (request.containsKey("debugMode")) {
                systemConfigService.getSystem().setDebugMode((Boolean) request.get("debugMode"));
            }
            if (request.containsKey("logLevel")) {
                systemConfigService.getSystem().setLogLevel((String) request.get("logLevel"));
            }
            
            // 更新功能配置
            if (request.containsKey("unattendedMode")) {
                systemConfigService.getFeatures().getCore().setUnattendedMode((Boolean) request.get("unattendedMode"));
            }
            if (request.containsKey("websocketEnabled")) {
                systemConfigService.getFeatures().getCore().setWebsocketEnabled((Boolean) request.get("websocketEnabled"));
            }
            if (request.containsKey("autoBackup")) {
                systemConfigService.getFeatures().getCore().setAutoBackup((Boolean) request.get("autoBackup"));
            }
            
            return Mono.just(success("系统配置更新成功", "配置更新成功"));
        } catch (Exception e) {
            logger.error("更新系统配置失败", e);
            return Mono.just(handleException(e, "更新系统配置"));
        }
    }

    /**
     * 获取安全配置（代理到SecurityController）
     */
    @GetMapping("/security")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getSecurityConfig() {
        try {
            Map<String, Object> config = new HashMap<>();
            
            // 从SystemConfigService获取安全配置
            var securityConfig = systemConfigService.getSecurity();
            var paymentSecurity = securityConfig.getPaymentSecurity();
            
            config.put("3d_secure_enabled", paymentSecurity.isThreeDSecureEnabled());
            config.put("3d2_enabled", paymentSecurity.isThreeD2Enabled());
            config.put("enable_network_type_check", paymentSecurity.isEnableNetworkTypeCheck());
            config.put("filter_crawler_user_agent", paymentSecurity.isFilterCrawlerUserAgent());
            
            return success(config, "获取安全配置成功");
        } catch (Exception e) {
            logger.error("获取安全配置失败", e);
            return handleException(e, "获取安全配置");
        }
    }

    /**
     * 更新安全配置（代理到SecurityController）
     */
    @PostMapping("/security")
    public Mono<ResponseEntity<ApiResponse<String>>> updateSecurityConfig(@RequestBody Map<String, Object> request) {
        logger.info("收到安全配置更新请求: {}", request);
        
        return systemConfigService.updateSecurityConfig(request)
            .map(unused -> success("安全配置更新成功并已持久化", "配置更新成功"))
            .onErrorResume(Exception.class, e -> {
                logger.error("更新安全配置失败", e);
                return Mono.just(handleException(e, "更新安全配置"));
            });
    }

    /**
     * 获取功能配置
     */
    @GetMapping("/features")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getFeaturesConfig() {
        try {
            Map<String, Object> config = new HashMap<>();
            
            var featuresConfig = systemConfigService.getFeatures();
            config.put("unattended_mode", featuresConfig.getCore().isUnattendedMode());
            config.put("websocket_enabled", featuresConfig.getCore().isWebsocketEnabled());
            config.put("auto_backup", featuresConfig.getCore().isAutoBackup());
            config.put("email_notifications", featuresConfig.getNotifications().isEmailNotifications());
            config.put("sms_notifications", featuresConfig.getNotifications().isSmsNotifications());
            config.put("webhook_enabled", featuresConfig.getNotifications().isWebhookEnabled());
            
            return success(config, "获取功能配置成功");
        } catch (Exception e) {
            logger.error("获取功能配置失败", e);
            return handleException(e, "获取功能配置");
        }
    }

    /**
     * 更新功能配置
     */
    @PostMapping("/features")
    public ResponseEntity<ApiResponse<String>> updateFeaturesConfig(@RequestBody Map<String, Object> request) {
        logger.info("收到功能配置更新请求: {}", request);
        
        try {
            var featuresConfig = systemConfigService.getFeatures();
            
            if (request.containsKey("unattended_mode")) {
                featuresConfig.getCore().setUnattendedMode((Boolean) request.get("unattended_mode"));
            }
            if (request.containsKey("websocket_enabled")) {
                featuresConfig.getCore().setWebsocketEnabled((Boolean) request.get("websocket_enabled"));
            }
            if (request.containsKey("auto_backup")) {
                featuresConfig.getCore().setAutoBackup((Boolean) request.get("auto_backup"));
            }
            if (request.containsKey("email_notifications")) {
                featuresConfig.getNotifications().setEmailNotifications((Boolean) request.get("email_notifications"));
            }
            if (request.containsKey("sms_notifications")) {
                featuresConfig.getNotifications().setSmsNotifications((Boolean) request.get("sms_notifications"));
            }
            if (request.containsKey("webhook_enabled")) {
                featuresConfig.getNotifications().setWebhookEnabled((Boolean) request.get("webhook_enabled"));
            }
            
            return success("功能配置更新成功", "配置更新成功");
        } catch (Exception e) {
            logger.error("更新功能配置失败", e);
            return handleException(e, "更新功能配置");
        }
    }
}
