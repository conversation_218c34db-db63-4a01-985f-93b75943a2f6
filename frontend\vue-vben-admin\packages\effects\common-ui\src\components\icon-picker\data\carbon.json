{"prefix": "carbon", "total": 2336, "title": "Carbon", "categories": {"Actions": ["add", "add-alt", "add-comment", "add-filled", "add-large", "align-box-bottom-center", "align-box-bottom-left", "align-box-bottom-right", "align-box-middle-center", "align-box-middle-left", "align-box-middle-right", "align-box-top-center", "align-box-top-left", "align-box-top-right", "align-horizontal-center", "align-horizontal-left", "align-horizontal-right", "align-vertical-bottom", "align-vertical-center", "align-vertical-top", "apps", "area", "area-custom", "arithmetic-mean", "arithmetic-median", "arrange", "arrange-horizontal", "arrange-vertical", "arrow-down", "arrow-down-left", "arrow-down-right", "arrow-left", "arrow-right", "arrow-shift-down", "arrow-up", "arrow-up-left", "arrow-up-right", "arrows-horizontal", "arrows-vertical", "asleep", "asleep-filled", "asterisk", "attachment", "automatic", "awake", "bookmark", "bookmark-add", "bookmark-filled", "border-bottom", "border-full", "border-left", "border-none", "border-right", "border-top", "bottom-panel-close", "bottom-panel-close-filled", "bottom-panel-open", "bottom-panel-open-filled", "branch", "brightness-contrast", "bring-forward", "bring-to-front", "build-tool", "button-centered", "button-flush-left", "calls", "calls-all", "calls-incoming", "caret-down", "caret-left", "caret-right", "caret-sort", "caret-sort-down", "caret-sort-up", "caret-up", "carousel-horizontal", "carousel-vertical", "catalog-publish", "center-circle", "center-square", "center-to-fit", "change-catalog", "chat", "chat-launch", "chat-off", "checkbox", "checkbox-checked", "checkbox-checked-filled", "checkbox-indeterminate", "checkbox-indeterminate-filled", "chevron-down", "chevron-down-outline", "chevron-left", "chevron-mini", "chevron-right", "chevron-sort", "chevron-sort-down", "chevron-sort-up", "chevron-up", "chevron-up-outline", "choices", "choose-item", "circle-dash", "circle-outline", "circle-solid", "clean", "close", "close-filled", "close-large", "close-outline", "code-block", "collapse-all", "collapse-categories", "color-palette", "color-switch", "column", "column-delete", "column-insert", "commit", "compare", "connect", "connect-recursive", "connect-reference", "connect-source", "connect-target", "continue", "continue-filled", "continuous-deployment", "continuous-integration", "contrast", "convert-to-cloud", "corner", "crop", "cut", "cut-out", "dashboard", "dashboard-reference", "delete", "demo", "deploy", "deploy-rules", "diamond-outline", "diamond-solid", "distribute-horizontal-center", "distribute-horizontal-left", "distribute-horizontal-right", "distribute-vertical-bottom", "distribute-vertical-center", "distribute-vertical-top", "document-horizontal", "document-vertical", "dot-mark", "down-to-bottom", "drag-horizontal", "drag-vertical", "draggable", "draw", "drill-back", "drill-down", "drill-through", "drop-photo", "drop-photo-filled", "edit", "edit-off", "equal-approximately", "erase", "exit", "expand-all", "expand-categories", "explore", "eyedropper", "fade", "favorite", "favorite-filled", "favorite-half", "filter", "filter-edit", "filter-remove", "filter-reset", "fit-to-height", "fit-to-screen", "fit-to-width", "flag", "flag-filled", "flash", "flash-filled", "flash-off", "flash-off-filled", "fork", "forward-10", "forward-30", "forward-5", "fragments", "function-math", "gears", "gradient", "grid", "group-objects", "group-objects-new", "group-objects-save", "hexagon-outline", "hexagon-solid", "hexagon-vertical-outline", "hexagon-vertical-solid", "home", "horizontal-view", "image", "image-copy", "image-reference", "image-search", "image-search-alt", "infinity-symbol", "insert", "insert-page", "insert-syntax", "inspection", "intersect", "jump-link", "label", "language", "lasso", "lasso-polygon", "launch", "layers", "layers-external", "light", "light-filled", "link", "list", "list-boxes", "list-bulleted", "list-checked", "list-checked-mirror", "list-numbered", "list-numbered-mirror", "locked", "login", "logout", "loop", "mac-command", "mac-option", "mac-shift", "magic-wand", "magic-wand-filled", "manage-protection", "maximize", "menu", "merge", "microphone", "microphone-filled", "microphone-off", "microphone-off-filled", "migrate", "migrate-alt", "minimize", "move", "new-tab", "next-filled", "next-outline", "no-image", "not-sent", "not-sent-filled", "notification", "notification-counter", "notification-filled", "notification-new", "notification-off", "notification-off-filled", "opacity", "open-panel-bottom", "open-panel-filled-bottom", "open-panel-filled-left", "open-panel-filled-right", "open-panel-filled-top", "open-panel-left", "open-panel-right", "open-panel-top", "operations-field", "operations-record", "overflow-menu-horizontal", "overflow-menu-vertical", "overlay", "package", "page-break", "page-first", "page-last", "page-number", "paint-brush", "paint-brush-alt", "pan-horizontal", "pan-vertical", "paragraph", "pause", "pause-filled", "pause-future", "pause-outline", "pause-outline-filled", "pause-past", "pen", "pen-fountain", "pentagon-down-outline", "pentagon-down-solid", "pentagon-left-outline", "pentagon-left-solid", "pentagon-outline", "pentagon-right-outline", "pentagon-right-solid", "pentagon-solid", "percentage", "percentage-filled", "phone", "phone-application", "phone-block", "phone-block-filled", "phone-filled", "phone-incoming", "phone-incoming-filled", "phone-off", "phone-off-filled", "phone-outgoing", "phone-outgoing-filled", "phone-settings", "phone-voice", "phone-voice-filled", "pin", "pin-filled", "pipelines", "play", "play-filled", "play-filled-alt", "play-outline", "play-outline-filled", "power", "previous-filled", "previous-outline", "process", "process-automate", "promote", "pull-request", "punctuation-check", "query", "quotes", "radio-button", "radio-button-checked", "recently-viewed", "recommend", "recording", "recording-filled", "recording-filled-alt", "redo", "reflect-horizontal", "reflect-vertical", "renew", "repeat", "repeat-one", "replicate", "reply", "reply-all", "request-quote", "reset", "reset-alt", "restart", "retry-failed", "return", "review", "rewind-10", "rewind-30", "rewind-5", "right-panel-close", "right-panel-close-filled", "right-panel-open", "right-panel-open-filled", "rotate", "rotate-clockwise", "rotate-clockwise-alt", "rotate-clockwise-alt-filled", "rotate-clockwise-filled", "rotate-counterclockwise", "rotate-counterclockwise-alt", "rotate-counterclockwise-alt-filled", "rotate-counterclockwise-filled", "row", "row-delete", "row-insert", "run", "run-mirror", "save", "save-model", "scan", "scan-alt", "scan-disabled", "screen", "screen-off", "search", "search-advanced", "search-locate", "search-locate-mirror", "select-01", "select-02", "select-window", "send", "send-alt", "send-alt-filled", "send-backward", "send-filled", "send-to-back", "settings", "settings-adjust", "settings-check", "settings-edit", "settings-services", "settings-view", "shape-except", "shape-exclude", "shape-intersect", "shape-join", "shape-unite", "shrink-screen", "shrink-screen-filled", "shuffle", "side-panel-close", "side-panel-close-filled", "side-panel-open", "side-panel-open-filled", "skip-back", "skip-back-filled", "skip-back-outline", "skip-back-outline-filled", "skip-back-outline-solid", "skip-back-solid-filled", "skip-forward", "skip-forward-filled", "skip-forward-outline", "skip-forward-outline-filled", "skip-forward-outline-solid", "skip-forward-solid-filled", "sort-ascending", "sort-descending", "sort-remove", "sorting-a-to-z", "sorting-highest-to-lowest-number", "sorting-lowest-to-highest-number", "sorting-z-to-a", "spell-check", "split", "split-screen", "spray-paint", "square-outline", "star", "star-filled", "star-half", "stickies", "stop", "stop-filled", "stop-filled-alt", "stop-outline", "stop-outline-filled", "strategy-play", "subtract", "subtract-alt", "subtract-filled", "subtract-large", "switcher", "sync-settings", "tag", "tag-edit", "tag-export", "tag-group", "tag-import", "tag-none", "test-tool", "text-align-center", "text-align-justify", "text-align-left", "text-align-mixed", "text-align-right", "text-all-caps", "text-bold", "text-clear-format", "text-color", "text-creation", "text-fill", "text-font", "text-footnote", "text-highlight", "text-indent", "text-indent-less", "text-indent-more", "text-italic", "text-kerning", "text-leading", "text-line-spacing", "text-long-paragraph", "text-new-line", "text-scale", "text-selection", "text-short-paragraph", "text-small-caps", "text-strikethrough", "text-subscript", "text-superscript", "text-tracking", "text-underline", "text-vertical-alignment", "text-wrap", "thumbnail-1", "thumbnail-2", "thumbs-down", "thumbs-down-filled", "thumbs-up", "thumbs-up-double", "thumbs-up-double-filled", "thumbs-up-filled", "timing-belt", "tour", "translate", "transpose", "trash-can", "triangle-down-outline", "triangle-down-solid", "triangle-left-outline", "triangle-left-solid", "triangle-outline", "triangle-right-outline", "triangle-right-solid", "triangle-solid", "trophy", "trophy-filled", "undo", "ungroup-objects", "unlink", "unlocked", "unsaved", "up-to-top", "update-now", "upgrade", "vertical-view", "video", "video-add", "video-chat", "video-filled", "video-off", "video-off-filled", "video-player", "view", "view-filled", "view-mode-1", "view-mode-2", "view-next", "view-off", "view-off-filled", "volume-down", "volume-down-alt", "volume-down-filled", "volume-down-filled-alt", "volume-mute", "volume-mute-filled", "volume-up", "volume-up-alt", "volume-up-filled", "volume-up-filled-alt", "workspace-import", "x-axis", "y-axis", "z-axis", "zoom-area", "zoom-fit", "zoom-in", "zoom-in-area", "zoom-out", "zoom-out-area", "zoom-reset"], "Brand": ["bee", "carbon", "carbon-for-aem", "carbon-for-ibm-dotcom", "carbon-for-ibm-product", "carbon-for-mobile", "carbon-for-salesforce", "carbon-ui-builder", "cloud-app", "edt-loop", "ibm-cloud", "ibm-security", "logo-angular", "logo-ansible-community", "logo-digg", "logo-discord", "logo-facebook", "logo-figma", "logo-flickr", "logo-git", "logo-github", "logo-gitlab", "logo-glassdoor", "logo-instagram", "logo-invision", "logo-jupyter", "logo-keybase", "logo-kubernetes", "logo-linkedin", "logo-livestream", "logo-mastodon", "logo-medium", "logo-npm", "logo-openshift", "logo-pinterest", "logo-python", "logo-quora", "logo-r-script", "logo-react", "logo-red-hat-ai-instructlab-on-ibm-cloud", "logo-red-hat-ansible", "logo-sketch", "logo-skype", "logo-slack", "logo-snapchat", "logo-svelte", "logo-tumblr", "logo-twitter", "logo-vmware", "logo-vmware-alt", "logo-vue", "logo-wechat", "logo-x", "logo-xing", "logo-yelp", "logo-youtube", "watson", "watson-machine-learning"], "Enterprise": ["account", "ai", "ai-business-impact-assessment", "ai-financial-sustainability-check", "ai-generate", "ai-governance-lifecycle", "ai-governance-tracked", "ai-governance-untracked", "ai-label", "ai-launch", "ai-recommend", "apple", "apple-dash", "autoscaling", "basketball", "bat", "bee-bat", "block-storage", "bottles-01", "bottles-01-dash", "bottles-02", "bottles-02-dash", "bottles-container", "building", "business-metrics", "chemistry", "chemistry-reference", "cics-explorer", "cics-transaction-server-zos", "cloud-auditing", "cloud-foundry-1", "cloud-foundry-2", "cloud-logging", "cloud-monitoring", "cloud-registry", "cloud-satellite", "cloud-satellite-config", "cloud-satellite-link", "cloud-satellite-services", "content-delivery-network", "corn", "coronavirus", "currency", "currency-baht", "currency-dollar", "currency-euro", "currency-lira", "currency-pound", "currency-ruble", "currency-rupee", "currency-shekel", "currency-won", "currency-yen", "database-datastax", "database-elastic", "database-enterprise-db2", "database-enterprisedb", "database-etcd", "database-mongodb", "database-postgresql", "database-rabbit", "database-redis", "delivery", "delivery-add", "delivery-parcel", "direct-link", "dns-services", "drink-01", "drink-02", "enterprise", "exam-mode", "finance", "financial-assets", "fish", "fish-multiple", "flow-logs-vpc", "fragile", "fruit-bowl", "gift", "health-cross", "ibm-ai-on-z", "ibm-aiops-insights", "ibm-api-connect", "ibm-app-connect-enterprise", "ibm-application-and-discovery-delivery-intelligence", "ibm-aspera", "ibm-bluepay", "ibm-cloud-app-id", "ibm-cloud-backup-and-recovery", "ibm-cloud-backup-service-vpc", "ibm-cloud-bare-metal-server", "ibm-cloud-bare-metal-servers-vpc", "ibm-cloud-citrix-daas", "ibm-cloud-code-engine", "ibm-cloud-continuous-delivery", "ibm-cloud-databases", "ibm-cloud-dedicated-host", "ibm-cloud-direct-link-1-connect", "ibm-cloud-direct-link-1-dedicated", "ibm-cloud-direct-link-1-dedicated-hosting", "ibm-cloud-direct-link-1-exchange", "ibm-cloud-direct-link-2-connect", "ibm-cloud-direct-link-2-dedicated", "ibm-cloud-direct-link-2-dedicated-hosting", "ibm-cloud-essential-security-and-observability-services", "ibm-cloud-event-notification", "ibm-cloud-event-streams", "ibm-cloud-for-education", "ibm-cloud-gate-keeper", "ibm-cloud-hpc", "ibm-cloud-hsm", "ibm-cloud-hyper-protect-crypto-services", "ibm-cloud-hyper-protect-dbaas", "ibm-cloud-hyper-protect-vs", "ibm-cloud-internet-services", "ibm-cloud-ipsec-vpn", "ibm-cloud-key-protect", "ibm-cloud-kubernetes-service", "ibm-cloud-logging", "ibm-cloud-mass-data-migration", "ibm-cloud-observability", "ibm-cloud-pak-applications", "ibm-cloud-pak-business-automation", "ibm-cloud-pak-data", "ibm-cloud-pak-integration", "ibm-cloud-pak-manta-automated-data-lineage", "ibm-cloud-pak-multicloud-mgmt", "ibm-cloud-pak-netezza", "ibm-cloud-pak-network-automation", "ibm-cloud-pak-security", "ibm-cloud-pak-system", "ibm-cloud-pak-watson-aiops", "ibm-cloud-pal", "ibm-cloud-privileged-access-gateway", "ibm-cloud-projects", "ibm-cloud-resiliency", "ibm-cloud-secrets-manager", "ibm-cloud-security", "ibm-cloud-security-compliance-center", "ibm-cloud-security-compliance-center-workload-protection", "ibm-cloud-security-groups", "ibm-cloud-subnets", "ibm-cloud-sysdig-secure", "ibm-cloud-transit-gateway", "ibm-cloud-virtual-server-classic", "ibm-cloud-virtual-server-vpc", "ibm-cloud-vpc", "ibm-cloud-vpc-block-storage-snapshots", "ibm-cloud-vpc-client-vpn", "ibm-cloud-vpc-endpoints", "ibm-cloud-vpc-file-storage", "ibm-cloud-vpc-images", "ibm-cloudant", "ibm-consulting-advantage-agent", "ibm-consulting-advantage-application", "ibm-consulting-advantage-assistant", "ibm-content-services", "ibm-data-power", "ibm-data-product-exchange", "ibm-data-replication", "ibm-databand", "ibm-datastage", "ibm-db2", "ibm-db2-alt", "ibm-db2-warehouse", "ibm-deployable-architecture", "ibm-devops-control", "ibm-dynamic-route-server", "ibm-elo-automotive-compliance", "ibm-elo-engineering-insights", "ibm-elo-method-composer", "ibm-elo-publishing", "ibm-engineering-lifecycle-mgmt", "ibm-engineering-requirements-doors-next", "ibm-engineering-systems-design-rhapsody", "ibm-engineering-systems-design-rhapsody-model-manager", "ibm-engineering-systems-design-rhapsody-sn1", "ibm-engineering-systems-design-rhapsody-sn2", "ibm-engineering-test-mgmt", "ibm-engineering-workflow-mgmt", "ibm-event-automation", "ibm-event-endpoint-mgmt", "ibm-event-processing", "ibm-event-streams", "ibm-gcm", "ibm-global-storage-architecture", "ibm-granite", "ibm-ibv", "ibm-instana", "ibm-jrs", "ibm-knowledge-catalog", "ibm-knowledge-catalog-premium", "ibm-knowledge-catalog-standard", "ibm-launchpad-s4", "ibm-lpa", "ibm-lqe", "ibm-machine-learning-for-zos", "ibm-match-360", "ibm-maximo-application-suite", "ibm-mq", "ibm-open-enterprise-languages", "ibm-openshift-container-platform-on-vpc-for-regulated-industries", "ibm-planning-analytics", "ibm-power-vs", "ibm-power-vs-private-cloud", "ibm-power-with-vpc", "ibm-private-path-services", "ibm-process-mining", "ibm-quantum-safe-advisor", "ibm-quantum-safe-explorer", "ibm-quantum-safe-remediator", "ibm-saas-console", "ibm-sap-on-power", "ibm-secure-infrastructure-on-vpc-for-regulated-industries", "ibm-security-services", "ibm-streamsets", "ibm-telehealth", "ibm-tenet", "ibm-test-accelerator-for-z", "ibm-toolchain", "ibm-turbonomic", "ibm-unstructured-data-processor", "ibm-vpn-for-vpc", "ibm-vsi-on-vpc-for-regulated-industries", "ibm-watson-assistant", "ibm-watson-discovery", "ibm-watson-knowledge-catalog", "ibm-watson-knowledge-studio", "ibm-watson-language-translator", "ibm-watson-machine-learning", "ibm-watson-natural-language-classifier", "ibm-watson-natural-language-understanding", "ibm-watson-openscale", "ibm-watson-orders", "ibm-watson-query", "ibm-watson-speech-to-text", "ibm-watson-studio", "ibm-watson-text-to-speech", "ibm-watson-tone-analyzer", "ibm-watsonx-assistant", "ibm-watsonx-code-assistant", "ibm-watsonx-code-assistant-for-enterprise-java-applications", "ibm-watsonx-code-assistant-for-z", "ibm-watsonx-code-assistant-for-z-refactor", "ibm-watsonx-code-assistant-for-z-validation-assistant", "ibm-watsonx-orchestrate", "ibm-wazi-deploy", "ibm-z-cloud-mod-stack", "ibm-z-cloud-provisioning", "ibm-z-environments-dev-sec-ops", "ibm-z-open-editor", "ibm-z-os", "ibm-z-os-ai-control-interface", "ibm-z-os-containers", "ibm-z-os-package-manager", "ibm-z-processor-capacity-reference", "image-medical", "industry", "infrastructure-classic", "inventory-management", "keep-dry", "load-balancer-vpc", "machine-learning", "machine-learning-model", "medication", "medication-alert", "medication-reminder", "ml-model-reference", "model", "model-foundation", "model-reference", "model-tuned", "money", "monster", "mysql", "noodle-bowl", "object-storage", "piggy-bank", "piggy-bank-slot", "pills", "pills-add", "pills-subtract", "power-enterprise-pools-metered-capacity-integration", "power-virtual-server-disaster-recovery-automation", "pricing-consumption", "pricing-container", "pricing-quick-proposal", "pricing-tailored", "pricing-traditional", "prompt-session", "prompt-template", "purchase", "qiskit", "question-answering", "rag", "receipt", "recycle", "scis-control-tower", "scis-transparent-supply", "shopping-bag", "shopping-cart", "shopping-cart-arrow-down", "shopping-cart-arrow-up", "shopping-cart-clear", "shopping-cart-error", "shopping-cart-minus", "shopping-cart-plus", "shopping-catalog", "soccer", "sprout", "stack-limitation", "store", "strawberry", "sustainability", "tennis", "tennis-ball", "this-side-up", "tree", "tuning", "two-person-lift", "virtual-private-cloud-alt", "vlan-ibm", "wallet", "watsonx", "watsonx-ai", "watsonx-data", "watsonx-governance", "wheat", "wireless-checkout"], "Organization": ["4k", "4k-filled", "accept-action-usage", "action-definition", "action-usage", "activity", "add-child-node", "add-parent-node", "analytics", "analytics-custom", "analytics-reference", "anchor", "api", "api-1", "api-key", "app", "app-connectivity", "application", "application-mobile", "application-virtual", "application-web", "archive", "assembly", "assembly-cluster", "assembly-reference", "assignment-action-usage", "async", "attribute-definition", "attribute-usage", "avro", "badge", "bare-metal-server", "bare-metal-server-01", "bare-metal-server-02", "bastion-host", "batch-job", "batch-job-step", "beta", "binding-01", "binding-02", "bland-altman-plot", "block-storage-alt", "blockchain", "book", "boolean", "boot-volume", "boot-volume-alt", "box-extra-large", "box-large", "box-medium", "box-plot", "box-small", "cad", "calculation", "calculation-alt", "calendar-heat-map", "catalog", "categories", "category", "category-add", "category-and", "category-new", "category-new-each", "cda", "certificate", "certificate-check", "character-decimal", "character-fraction", "character-integer", "character-lower-case", "character-negative-number", "character-patterns", "character-sentence-case", "character-upper-case", "character-whole-number", "chart-3d", "chart-area", "chart-area-smooth", "chart-area-stepper", "chart-average", "chart-bar", "chart-bar-floating", "chart-bar-overlay", "chart-bar-stacked", "chart-bar-target", "chart-bubble", "chart-bubble-packed", "chart-bullet", "chart-candlestick", "chart-cluster-bar", "chart-column", "chart-column-floating", "chart-column-target", "chart-combo", "chart-combo-stacked", "chart-custom", "chart-dual-y-axis", "chart-error-bar", "chart-error-bar-alt", "chart-evaluation", "chart-high-low", "chart-histogram", "chart-line", "chart-line-data", "chart-line-smooth", "chart-logistic-regression", "chart-<PERSON><PERSON><PERSON><PERSON>", "chart-maximum", "chart-median", "chart-minimum", "chart-multi-line", "chart-multitype", "chart-network", "chart-parallel", "chart-pie", "chart-planning-waterfall", "chart-point", "chart-population", "chart-radar", "chart-radial", "chart-relationship", "chart-ring", "chart-river", "chart-rose", "chart-scatter", "chart-spiral", "chart-stacked", "chart-stepper", "chart-sunburst", "chart-t-sne", "chart-treemap", "chart-venn-diagram", "chart-violin-plot", "chart-waterfall", "chart-win-loss", "chat-operational", "checkmark", "checkmark-filled", "checkmark-filled-error", "checkmark-filled-warning", "checkmark-outline", "checkmark-outline-error", "checkmark-outline-warning", "child-node", "choropleth-map", "cics-cmas", "cics-db2-connection", "cics-program", "cics-region", "cics-region-alt", "cics-region-routing", "cics-region-target", "cics-sit", "cics-sit-overrides", "cics-system-group", "cics-wui-region", "cicsplex", "circle-filled", "circle-packing", "classification", "classifier-language", "closed-caption", "closed-caption-alt", "closed-caption-filled", "cloud-alerting", "cloud-data-ops", "cloud-service-management", "cloud-services", "code-signing-service", "column-dependency", "comments", "communication-unified", "concept", "condition-point", "condition-wait-point", "connection-flow-usage", "connection-usage", "constraint", "container-engine", "container-image", "container-image-pull", "container-image-push", "container-image-push-pull", "container-registry", "container-runtime", "container-runtime-monitor", "container-services", "container-software", "content-view", "copy", "copy-file", "copy-link", "cost", "cost-total", "course", "covariate", "cross-tab", "csv", "cube", "cube-view", "data-1", "data-2", "data-accessor", "data-analytics", "data-backup", "data-base", "data-base-alt", "data-bin", "data-blob", "data-categorical", "data-center", "data-check", "data-class", "data-collection", "data-connected", "data-definition", "data-diode", "data-enrichment", "data-enrichment-add", "data-error", "data-format", "data-player", "data-quality-definition", "data-reference", "data-refinery", "data-refinery-reference", "data-regular", "data-set", "data-share", "data-structured", "data-table", "data-table-reference", "data-unreal", "data-unstructured", "data-view", "data-view-alt", "data-vis-1", "data-vis-2", "data-vis-3", "data-vis-4", "data-volume", "data-volume-alt", "database-messaging", "datastore", "db2-buffer-pool", "db2-data-sharing-group", "db2-database", "decision-node", "decision-tree", "dependency", "deployment-unit-data", "deployment-unit-execution", "deployment-unit-installation", "deployment-unit-presentation", "deployment-unit-technical-data", "deployment-unit-technical-execution", "deployment-unit-technical-installation", "deployment-unit-technical-presentation", "diagram", "diagram-reference", "directory-domain", "doc", "document", "document-add", "document-attachment", "document-audio", "document-blank", "document-comment", "document-configuration", "document-download", "document-epdf", "document-export", "document-import", "document-multiple-01", "document-multiple-02", "document-pdf", "document-preliminary", "document-processor", "document-protected", "document-requirements", "document-security", "document-sentiment", "document-signed", "document-sketch", "document-subject", "document-subtract", "document-tasks", "document-unknown", "document-unprotected", "document-video", "document-view", "document-word-processor", "document-word-processor-reference", "documentation", "double-axis-chart-bar", "double-axis-chart-column", "double-integer", "download", "downstream", "driver-analysis", "dvr", "edge-node-alt", "element-picker", "emissions-management", "encryption", "enumeration-definition", "enumeration-usage", "error", "error-filled", "error-outline", "event-change", "event-incident", "event-warning", "executable-program", "export", "factor", "feature-membership", "feature-membership-filled", "feature-picker", "feature-typing", "file-storage", "firewall", "firewall-classic", "floating-ip", "floorplan", "flow", "flow-connection", "flow-data", "flow-modeler", "flow-modeler-reference", "flow-stream", "flow-stream-reference", "folder", "folder-add", "folder-details", "folder-details-reference", "folder-move-to", "folder-off", "folder-open", "folder-parent", "folder-shared", "folders", "for-loop", "fork-node", "gamification", "gateway", "gateway-api", "gateway-mail", "gateway-public", "gateway-security", "gateway-user-access", "gateway-vpn", "gem", "gem-reference", "generate-pdf", "gif", "global-loan-and-trial", "graphical-data-flow", "group-access", "group-account", "group-resource", "group-security", "growth", "gui", "gui-management", "hardware-security-module", "hashtag", "hd", "hd-filled", "hdr", "heat-map", "heat-map-02", "heat-map-03", "heat-map-stocks", "help", "help-filled", "html", "html-reference", "http", "hybrid-networking", "hybrid-networking-alt", "id-management", "if-action", "image-service", "image-store-local", "import-export", "improve-relevance", "in-progress", "in-progress-error", "in-progress-warning", "incident-reporter", "incomplete", "incomplete-cancel", "incomplete-error", "incomplete-warning", "increase-level", "information", "information-disabled", "information-filled", "information-square", "information-square-filled", "instance-bx", "instance-classic", "instance-cx", "instance-mx", "instance-virtual", "intent-request-active", "intent-request-create", "intent-request-heal", "intent-request-inactive", "intent-request-scale-in", "intent-request-scale-out", "intent-request-uninstall", "intent-request-upgrade", "interactions", "interface-definition", "interface-definition-alt", "interface-usage", "interface-usage-1", "interface-usage-alt", "intrusion-prevention", "ip", "iso", "iso-filled", "iso-outline", "item-definition", "item-usage", "job-daemon", "job-run", "join-full", "join-inner", "join-left", "join-node", "join-outer", "join-right", "jpg", "js-error", "json", "json-reference", "key", "kubelet", "kubernetes", "kubernetes-control-plane-node", "kubernetes-ip-address", "kubernetes-operator", "kubernetes-pod", "kubernetes-worker-node", "legend", "letter-aa", "letter-bb", "letter-cc", "letter-dd", "letter-ee", "letter-ff", "letter-gg", "letter-hh", "letter-ii", "letter-jj", "letter-kk", "letter-ll", "letter-mm", "letter-nn", "letter-oo", "letter-pp", "letter-qq", "letter-rr", "letter-ss", "letter-tt", "letter-uu", "letter-vv", "letter-ww", "letter-xx", "letter-yy", "letter-zz", "license", "license-draft", "license-global", "license-maintenance", "license-maintenance-draft", "license-third-party", "license-third-party-draft", "linux", "linux-alt", "linux-namespace", "list-dropdown", "load-balancer-application", "load-balancer-classic", "load-balancer-global", "load-balancer-listener", "load-balancer-local", "load-balancer-network", "load-balancer-pool", "locked-and-blocked", "logical-partition", "loop-alt", "mac", "managed-solutions", "math-curve", "media-library", "media-library-filled", "merge-node", "message-queue", "microservices-1", "microservices-2", "misuse", "misuse-outline", "mobility-services", "model-alt", "model-builder", "model-builder-reference", "mov", "mp3", "mp4", "mpeg", "mpg2", "music", "music-add", "music-remove", "name-space", "network-1", "network-2", "network-3", "network-3-reference", "network-4", "network-4-reference", "network-admin-control", "network-enterprise", "network-interface", "network-overlay", "network-public", "network-time-protocol", "no-ticket", "nominal", "non-certified", "notebook", "notebook-reference", "notifications-paused", "null-sign", "number-0", "number-1", "number-2", "number-3", "number-4", "number-5", "number-6", "number-7", "number-8", "number-9", "number-small-0", "number-small-1", "number-small-2", "number-small-3", "number-small-4", "number-small-5", "number-small-6", "number-small-7", "number-small-8", "number-small-9", "object-storage-alt", "omega", "order-details", "ordinal", "package-node", "package-text-analysis", "panel-expansion", "parameter", "parent-child", "parent-node", "part-definition", "part-usage", "partition-auto", "partition-collection", "partition-repartition", "partition-same", "partition-specific", "paste", "pcn-e-node", "pcn-military", "pcn-p-node", "pcn-z-node", "pdf", "pdf-reference", "pending", "pending-filled", "perform-action", "phrase-sentiment", "plan", "playlist", "png", "point-of-presence", "policy", "popup", "port-definition", "port-usage", "ppt", "presentation-file", "product", "progress-bar", "progress-bar-round", "property-relationship", "qq-plot", "quadrant-plot", "query-queue", "queued", "raw", "redefinition", "reference-architecture", "repo-artifact", "repo-source-code", "report", "report-data", "requirement-definition", "requirement-usage", "result", "result-draft", "result-new", "result-old", "roadmap", "router", "router-voice", "router-wifi", "row-collapse", "row-expand", "rule", "rule-cancelled", "rule-data-quality", "rule-draft", "rule-filled", "rule-locked", "rule-partial", "rule-test", "sankey-diagram", "sankey-diagram-alt", "satisfy-definition", "satisfy-usage", "scale", "scatter-matrix", "schematics", "screen-map", "screen-map-set", "script", "script-reference", "sdk", "security", "security-services", "send-action-usage", "server-dns", "server-proxy", "server-time", "service-levels", "session-border-control", "shapes", "share-knowledge", "show-data-cards", "sigma", "skill-level", "skill-level-advanced", "skill-level-basic", "skill-level-intermediate", "slisor", "slm", "software-resource", "software-resource-cluster", "software-resource-resource", "spyre-accelerator", "sql", "star-review", "stem-leaf-plot", "storage-pool", "storage-request", "string-integer", "string-text", "subclassification", "subdirectory", "subflow", "subflow-local", "subject-definition", "subject-usage", "subnet-acl-rules", "subsetting", "succession", "succession-flow-connection", "summary-kpi", "summary-kpi-mirror", "support-vector-machine", "svg", "swimlane-d-vertical", "switch-layer-2", "switch-layer-3", "sys-provision", "sysplex-distributor", "table", "table-alias", "table-built", "table-of-contents", "table-shortcut", "table-split", "task", "task-add", "task-approved", "task-asset-view", "task-complete", "task-location", "task-remove", "task-settings", "task-star", "task-tools", "task-view", "tcp-ip-service", "template", "term", "term-reference", "terminal-3270", "text-link", "text-link-analysis", "text-mining", "text-mining-applier", "ticket", "tif", "time-plot", "transform-binary", "transform-code", "transform-instructions", "transform-language", "tree-view", "tree-view-alt", "tsq", "tsv", "two-factor-authentication", "txt", "txt-reference", "type-pattern", "types", "undefined", "undefined-filled", "unknown", "unknown-filled", "upload", "upstream", "url", "usage-included-use-case", "use-case-definition", "use-case-usage", "value-variable", "vehicle-api", "vehicle-connected", "vehicle-insights", "vehicle-services", "version", "version-major", "version-minor", "version-patch", "virtual-column", "virtual-column-key", "virtual-desktop", "virtual-machine", "virtual-private-cloud", "visual-recognition", "vlan", "vmdk-disk", "volume-block-storage", "volume-file-storage", "volume-object-storage", "vpn-connection", "vpn-policy", "warning", "warning-alt", "warning-alt-filled", "warning-alt-inverted", "warning-alt-inverted-filled", "warning-diamond", "warning-filled", "warning-hex", "warning-hex-filled", "warning-multiple", "warning-other", "warning-square", "warning-square-filled", "websheet", "while-loop", "white-paper", "wifi-bridge", "wifi-bridge-alt", "wifi-not-secure", "wifi-secure", "wmv", "word-cloud", "workflow-automation", "workspace", "xls", "xml", "z-lpar", "z-systems", "zip", "zip-reference", "zos", "zos-sysplex"], "Person": ["accessibility", "accessibility-alt", "accessibility-color", "accessibility-color-filled", "cognitive", "collaborate", "cough", "credentials", "customer", "customer-service", "dog-walker", "education", "events", "events-alt", "face-activated", "face-activated-add", "face-activated-filled", "face-add", "face-cool", "face-dissatisfied", "face-dissatisfied-filled", "face-dizzy", "face-dizzy-filled", "face-mask", "face-neutral", "face-neutral-filled", "face-pending", "face-pending-filled", "face-satisfied", "face-satisfied-filled", "face-wink", "face-wink-filled", "friendship", "gender-female", "gender-male", "group", "group-presentation", "hearing", "idea", "identification", "movement", "need", "partnership", "person", "person-favorite", "running", "sales-ops", "sight", "smell", "taste", "touch-1", "touch-1-down", "touch-1-down-filled", "touch-1-filled", "touch-2", "touch-2-filled", "touch-interaction", "transgender", "user", "user-access", "user-access-locked", "user-access-unlocked", "user-activity", "user-admin", "user-avatar", "user-avatar-filled", "user-avatar-filled-alt", "user-certification", "user-data", "user-favorite", "user-favorite-alt", "user-favorite-alt-filled", "user-feedback", "user-filled", "user-follow", "user-identification", "user-military", "user-multiple", "user-online", "user-profile", "user-profile-alt", "user-role", "user-service", "user-service-desk", "user-settings", "user-simulation", "user-speaker", "user-sponsor", "user-x-ray", "voice-activate"], "Planning": ["accumulation-ice", "accumulation-precipitation", "accumulation-rain", "accumulation-snow", "agriculture-analytics", "airline-digital-gate", "airline-manage-gates", "airline-passenger-care", "airline-rapid-board", "airport-01", "airport-02", "airport-location", "alarm", "alarm-add", "alarm-subtract", "arrival", "baggage-claim", "bar", "bicycle", "boot", "buoy", "bus", "cabin-care", "cabin-care-alert", "cabin-care-alt", "cafe", "calendar", "calendar-add", "calendar-add-alt", "calendar-settings", "calendar-tools", "campsite", "car", "car-front", "carbon-accounting", "charging-station", "charging-station-filled", "cloud", "cloud-ceiling", "cloudy", "compass", "construction", "crop-growth", "crop-health", "crossroads", "crowd-report", "crowd-report-filled", "cyclist", "delivery-truck", "departure", "dew-point", "dew-point-filled", "direction-bear-right-01", "direction-bear-right-01-filled", "direction-bear-right-02", "direction-bear-right-02-filled", "direction-curve", "direction-curve-filled", "direction-fork", "direction-fork-filled", "direction-loop-left", "direction-loop-left-filled", "direction-loop-right", "direction-loop-right-filled", "direction-merge", "direction-merge-filled", "direction-right-01", "direction-right-01-filled", "direction-right-02", "direction-right-02-filled", "direction-rotary-first-right", "direction-rotary-first-right-filled", "direction-rotary-right", "direction-rotary-right-filled", "direction-rotary-straight", "direction-rotary-straight-filled", "direction-sharp-turn", "direction-sharp-turn-filled", "direction-straight", "direction-straight-filled", "direction-straight-right", "direction-straight-right-filled", "direction-u-turn", "direction-u-turn-filled", "drought", "earth", "earth-americas", "earth-americas-filled", "earth-europe-africa", "earth-europe-africa-filled", "earth-filled", "earth-southeast-asia", "earth-southeast-asia-filled", "earthquake", "energy-renewable", "event", "event-schedule", "fire", "flagging-taxi", "flight-international", "flight-roster", "flight-schedule", "flood", "flood-warning", "fog", "forecast-hail", "forecast-hail-30", "forecast-lightning", "forecast-lightning-30", "gas-station", "gas-station-filled", "globe", "hail", "harbor", "haze", "haze-night", "helicopter", "help-desk", "hospital", "hospital-bed", "hotel", "hourglass", "humidity", "humidity-alt", "hurricane", "ice-accretion", "ice-vision", "lifesaver", "lightning", "location", "location-company", "location-company-filled", "location-filled", "location-hazard", "location-hazard-filled", "location-heart", "location-heart-filled", "location-info", "location-info-filled", "location-person", "location-person-filled", "location-save", "location-star", "location-star-filled", "map", "map-boundary", "map-boundary-vegetation", "map-center", "map-identify", "marine-warning", "milestone", "military-camp", "mixed-rain-hail", "monument", "moon", "moonrise", "moonset", "mostly-cloudy", "mostly-cloudy-night", "mountain", "navaid-civil", "navaid-dme", "navaid-helipad", "navaid-military", "navaid-military-civil", "navaid-ndb", "navaid-ndb-dme", "navaid-private", "navaid-seaplane", "navaid-tacan", "navaid-vhfor", "navaid-vor", "navaid-vordme", "navaid-vortac", "not-available", "observed-hail", "observed-lightning", "outlook-severe", "palm-tree", "partly-cloudy", "partly-cloudy-night", "passenger-drinks", "passenger-plus", "pedestrian", "pedestrian-child", "pedestrian-family", "pest", "picnic-area", "plane", "plane-private", "plane-sea", "police", "pressure", "pressure-filled", "radar-enhanced", "radar-weather", "rain", "rain-drizzle", "rain-drop", "rain-heavy", "rain-scattered", "rain-scattered-night", "ref-evapotranspiration", "reminder", "reminder-medical", "restaurant", "restaurant-fine", "road", "road-weather", "sailboat-coastal", "sailboat-offshore", "satellite-radar", "satellite-weather", "scooter", "scooter-front", "service-desk", "shuttle", "sleet", "smoke", "snooze", "snow", "snow-blizzard", "snow-density", "snow-heavy", "snow-scattered", "snow-scattered-night", "snowflake", "soil-moisture", "soil-moisture-field", "soil-moisture-global", "soil-temperature", "soil-temperature-field", "soil-temperature-global", "solar-panel", "stay-inside", "stop-sign", "stop-sign-filled", "storm-tracker", "sun", "sunrise", "sunset", "swim", "tank", "taxi", "temperature", "temperature-celsius", "temperature-celsius-alt", "temperature-fahrenheit", "temperature-fahrenheit-alt", "temperature-feels-like", "temperature-frigid", "temperature-hot", "temperature-inversion", "temperature-max", "temperature-min", "temperature-water", "theater", "thunderstorm", "thunderstorm-scattered", "thunderstorm-scattered-night", "thunderstorm-severe", "thunderstorm-strong", "tides", "time", "time-filled", "timer", "tornado", "tornado-warning", "traffic-cone", "traffic-event", "traffic-flow", "traffic-flow-incident", "traffic-incident", "traffic-weather-incident", "train", "train-heart", "train-profile", "train-speed", "train-ticket", "train-time", "tram", "tree-fall-risk", "tropical-storm", "tropical-storm-model-tracks", "tropical-storm-tracks", "tropical-warning", "tsunami", "uv-index", "uv-index-alt", "uv-index-filled", "van", "vegetation-asset", "vegetation-encroachment", "vegetation-height", "wave-direction", "wave-height", "wave-period", "weather-front-cold", "weather-front-stationary", "weather-front-warm", "weather-station", "wind-gusts", "wind-power", "wind-stream", "windy", "windy-dust", "windy-snow", "windy-strong", "winter-warning", "wintry-mix", "worship", "worship-christian", "worship-jewish", "worship-muslim"], "Tools": ["airplay", "airplay-filled", "aperture", "array", "array-booleans", "array-dates", "array-numbers", "array-objects", "array-strings", "asset", "asset-confirm", "asset-digital-twin", "asset-movement", "asset-view", "at", "audio-console", "augmented-reality", "barcode", "battery-charging", "battery-empty", "battery-error", "battery-full", "battery-half", "battery-low", "battery-quarter", "battery-warning", "binoculars", "blog", "bluetooth", "bluetooth-off", "bot", "box", "breaking-change", "build-image", "build-run", "building-insights-1", "building-insights-2", "building-insights-3", "bullhorn", "business-processes", "calculator", "calculator-check", "calibrate", "camera", "camera-action", "cell-tower", "channels", "chat-bot", "chip", "cloud-download", "cloud-offline", "cloud-upload", "code", "code-hide", "code-reference", "connection-receive", "connection-send", "connection-signal", "connection-signal-off", "connection-two-way", "cookie", "create-link", "cursor-1", "cursor-2", "debug", "delivery-settings", "deployment-canary", "deployment-pattern", "deployment-policy", "desk-adjustable", "development", "devices", "devices-apps", "drone", "drone-delivery", "drone-front", "drone-video", "edge-cluster", "edge-device", "edge-node", "edge-service", "email", "email-new", "equalizer", "fetch-upload", "fetch-upload-cloud", "fingerprint-recognition", "forum", "function", "function-2", "game-console", "game-wireless", "headphones", "headset", "integration", "iot-connect", "iot-platform", "keyboard", "keyboard-off", "kiosk-device", "laptop", "location-current", "mail-all", "mail-reply", "media-cast", "meter", "meter-alt", "microscope", "mobile", "mobile-add", "mobile-audio", "mobile-check", "mobile-crash", "mobile-download", "mobile-event", "mobile-landscape", "mobile-request", "mobile-session", "mobile-view", "mobile-view-orientation", "multiuser-device", "object", "outage", "password", "phone-ip", "platforms", "plug", "plug-filled", "port-input", "port-output", "portfolio", "printer", "qr-code", "radar", "radio", "radio-combat", "radio-push-to-talk", "rocket", "rss", "ruler", "ruler-alt", "satellite", "scales", "scales-tipped", "scalpel", "service-id", "share", "signal-strength", "sim-card", "stamp", "stethoscope", "tablet", "tablet-landscape", "terminal", "tool-box", "tool-kit", "tools", "tools-alt", "transmission-lte", "trigger", "umbrella", "usb", "voicemail", "vpn", "watch", "web-services-cluster", "web-services-container", "web-services-definition", "web-services-service", "web-services-task", "web-services-task-definition-version", "webhook", "wifi", "wifi-controller", "wifi-off", "wikis"]}, "hidden": ["3d-cursor", "3d-cursor-alt", "3d-curve-auto-colon", "3d-curve-auto-vessels", "3d-curve-manual", "3d-ica", "3d-mpr-toggle", "3d-print-mesh", "3d-software", "3rd-party-connected", "ai-results", "ai-results-high", "ai-results-low", "ai-results-medium", "ai-results-urgent", "ai-results-very-high", "ai-status", "ai-status-complete", "ai-status-failed", "ai-status-in-progress", "ai-status-queued", "ai-status-rejected", "angle", "annotation-visibility", "arrow-annotation", "auto-scroll", "barrier", "bloch-sphere", "brush-freehand", "brush-polygon", "ccx", "cd-archive", "cd-create-archive", "cd-create-exchange", "checkbox-undeterminate-filled", "circle-measurement", "circuit-composer", "cobb-angle", "composer-edit", "contour-draw", "contour-edit", "contour-finding", "cross-reference", "cu1", "cu3", "cut-in-half", "cy", "cz", "denominate", "dicom-6000", "dicom-overlay", "dna", "download-study", "edge-enhancement", "edge-enhancement-01", "edge-enhancement-02", "edge-enhancement-03", "edit-filter", "erase-3d", "foundation-model", "fusion-blender", "h", "hanging-protocol", "hinton-plot", "hl7-attributes", "hole-filling", "hole-filling-cursor", "ica-2d", "id", "interactive-segmentation-cursor", "launch-study-1", "launch-study-2", "launch-study-3", "logo-delicious", "logo-google", "logo-stumbleupon", "magnify", "mammogram", "mammogram-stacked", "matrix", "misuse-alt", "nominate", "operation", "operation-gauge", "operation-if", "page-scroll", "pet-image-b", "pet-image-o", "pointer-text", "qc-launch", "region-analysis-area", "region-analysis-volume", "registration", "research-bloch-sphere", "research-hinton-plot", "research-matrix", "rotate-180", "rotate-360", "s", "s-alt", "save-annotation", "save-image", "save-series", "scalpel-cursor", "scalpel-lasso", "scalpel-select", "smoothing", "smoothing-cursor", "spine-label", "split-discard", "stacked-move", "stacked-scrolling-1", "stacked-scrolling-2", "status-acknowledge", "status-change", "status-partial-fail", "status-resolved", "stress-breath-editor", "study-next", "study-previous", "study-read", "study-skip", "study-transfer", "study-unread", "study-view", "sub-volume", "t", "t-alt", "text-annotation-toggle", "threshold", "thumbnail-preview", "u1", "u2", "u3", "window-auto", "window-base", "window-black-saturation", "window-overlay", "window-preset", "x", "y", "z", "zoom-pan"], "aliases": {"app-switcher": "switcher", "arrows": "arrows-vertical", "back-to-top": "up-to-top", "checkbox-undeterminate": "checkbox-indeterminate", "cloud-lightning": "flash", "cloud-rain": "rain", "cloud-snow": "snow", "infinity": "infinity-symbol", "letter-aa-large": "text-font", "sunny": "sun"}}