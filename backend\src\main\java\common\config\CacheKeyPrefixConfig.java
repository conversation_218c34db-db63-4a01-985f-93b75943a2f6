package common.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 缓存键前缀配置
 */
@Configuration
@ConfigurationProperties(prefix = "app.redis.key-prefix")
public class CacheKeyPrefixConfig {

    // ==================== 业务缓存前缀 ====================
    
    /**
     * 支付会话缓存前缀
     */
    private String paymentSession = "cache:payment:session:";

    /**
     * 3D验证状态缓存前缀
     */
    private String verification3d = "cache:verification:3d:";

    /**
     * 支付交易临时缓存前缀
     */
    private String transactionTemp = "cache:transaction:temp:";

    /**
     * WebSocket连接状态缓存前缀
     */
    private String websocket = "cache:websocket:";

    /**
     * OTP信息缓存前缀
     */
    private String otpInfo = "cache:otp:info:";

    /**
     * OTP验证码缓存前缀
     */
    private String otpCode = "cache:otp:code:";

    /**
     * 用户会话缓存前缀
     */
    private String userSession = "cache:user:session:";

    // ==================== 系统缓存前缀 ====================

    /**
     * 系统配置缓存前缀
     */
    private String systemConfig = "cache:system:config:";

    /**
     * 黑名单缓存前缀
     */
    private String blacklist = "cache:blacklist:";

    /**
     * BIN查询缓存前缀
     */
    private String binLookup = "cache:bin:lookup:";

    /**
     * API限流缓存前缀
     */
    private String rateLimiting = "cache:rate:limit:";

    /**
     * 统计数据缓存前缀
     */
    private String statistics = "cache:stats:";

    /**
     * 文件上传缓存前缀
     */
    private String fileUpload = "cache:file:upload:";

    /**
     * 域名缓存前缀
     */
    private String domain = "cache:domain:";

    /**
     * 用户信息缓存前缀
     */
    private String user = "cache:user:";

    /**
     * 验证模板缓存前缀
     */
    private String template = "cache:template:";

    /**
     * 卡片信息缓存前缀
     */
    private String card = "cache:card:";

    // ==================== 工具方法 ====================

    /**
     * 构建完整的缓存键
     */
    public String buildKey(String prefix, String key) {
        return prefix + key;
    }

    /**
     * 构建支付会话缓存键
     */
    public String buildPaymentSessionKey(String sessionId) {
        return buildKey(paymentSession, sessionId);
    }

    /**
     * 构建3D验证缓存键
     */
    public String buildVerification3dKey(String transactionId) {
        return buildKey(verification3d, transactionId);
    }

    /**
     * 构建交易临时缓存键
     */
    public String buildTransactionTempKey(String transactionId) {
        return buildKey(transactionTemp, transactionId);
    }

    /**
     * 构建WebSocket缓存键
     */
    public String buildWebsocketKey(String connectionId) {
        return buildKey(websocket, connectionId);
    }

    /**
     * 构建OTP信息缓存键
     */
    public String buildOtpInfoKey(String otpId) {
        return buildKey(otpInfo, otpId);
    }

    /**
     * 构建OTP验证码缓存键
     */
    public String buildOtpCodeKey(String phone) {
        return buildKey(otpCode, phone);
    }

    /**
     * 构建用户会话缓存键
     */
    public String buildUserSessionKey(String userId) {
        return buildKey(userSession, userId);
    }

    /**
     * 构建系统配置缓存键
     */
    public String buildSystemConfigKey(String configKey) {
        return buildKey(systemConfig, configKey);
    }

    /**
     * 构建黑名单缓存键
     */
    public String buildBlacklistKey(String type, String value) {
        return buildKey(blacklist, type + ":" + value);
    }

    /**
     * 构建BIN查询缓存键
     */
    public String buildBinLookupKey(String bin) {
        return buildKey(binLookup, bin);
    }

    /**
     * 构建API限流缓存键
     */
    public String buildRateLimitingKey(String api, String identifier) {
        return buildKey(rateLimiting, api + ":" + identifier);
    }

    /**
     * 构建统计数据缓存键
     */
    public String buildStatisticsKey(String type, String period) {
        return buildKey(statistics, type + ":" + period);
    }

    /**
     * 构建文件上传缓存键
     */
    public String buildFileUploadKey(String uploadId) {
        return buildKey(fileUpload, uploadId);
    }

    /**
     * 构建域名缓存键
     */
    public String buildDomainKey(String domainName) {
        return buildKey(domain, domainName);
    }

    /**
     * 构建用户信息缓存键
     */
    public String buildUserKey(String userId) {
        return buildKey(user, userId);
    }

    /**
     * 构建验证模板缓存键
     */
    public String buildTemplateKey(String templateType) {
        return buildKey(template, templateType);
    }

    /**
     * 构建卡片信息缓存键
     */
    public String buildCardKey(String cardId) {
        return buildKey(card, cardId);
    }

    // ==================== Getters and Setters ====================

    public String getPaymentSession() {
        return paymentSession;
    }

    public void setPaymentSession(String paymentSession) {
        this.paymentSession = paymentSession;
    }

    public String getVerification3d() {
        return verification3d;
    }

    public void setVerification3d(String verification3d) {
        this.verification3d = verification3d;
    }

    public String getTransactionTemp() {
        return transactionTemp;
    }

    public void setTransactionTemp(String transactionTemp) {
        this.transactionTemp = transactionTemp;
    }

    public String getWebsocket() {
        return websocket;
    }

    public void setWebsocket(String websocket) {
        this.websocket = websocket;
    }

    public String getOtpInfo() {
        return otpInfo;
    }

    public void setOtpInfo(String otpInfo) {
        this.otpInfo = otpInfo;
    }

    public String getOtpCode() {
        return otpCode;
    }

    public void setOtpCode(String otpCode) {
        this.otpCode = otpCode;
    }

    public String getUserSession() {
        return userSession;
    }

    public void setUserSession(String userSession) {
        this.userSession = userSession;
    }

    public String getSystemConfig() {
        return systemConfig;
    }

    public void setSystemConfig(String systemConfig) {
        this.systemConfig = systemConfig;
    }

    public String getBlacklist() {
        return blacklist;
    }

    public void setBlacklist(String blacklist) {
        this.blacklist = blacklist;
    }

    public String getBinLookup() {
        return binLookup;
    }

    public void setBinLookup(String binLookup) {
        this.binLookup = binLookup;
    }

    public String getRateLimiting() {
        return rateLimiting;
    }

    public void setRateLimiting(String rateLimiting) {
        this.rateLimiting = rateLimiting;
    }

    public String getStatistics() {
        return statistics;
    }

    public void setStatistics(String statistics) {
        this.statistics = statistics;
    }

    public String getFileUpload() {
        return fileUpload;
    }

    public void setFileUpload(String fileUpload) {
        this.fileUpload = fileUpload;
    }
}
