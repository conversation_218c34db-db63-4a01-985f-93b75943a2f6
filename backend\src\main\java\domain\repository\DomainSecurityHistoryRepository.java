package domain.repository;

import domain.entity.DomainSecurityHistory;
import org.springframework.data.domain.Pageable;
import org.springframework.data.r2dbc.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 域名安全检测历史Repository - 响应式版本
 */
@Repository
public interface DomainSecurityHistoryRepository extends BaseRepository<DomainSecurityHistory, Long> {

    /**
     * 根据域名ID查询安全检测历史，按检测时间倒序
     */
    Flux<DomainSecurityHistory> findByDomainIdOrderByCheckTimeDesc(Long domainId);

    /**
     * 根据域名ID分页查询安全检测历史，按检测时间倒序
     */
    Flux<DomainSecurityHistory> findByDomainIdOrderByCheckTimeDesc(Long domainId, Pageable pageable);

    /**
     * 查询域名最近一次安全检测记录
     */
    Mono<DomainSecurityHistory> findFirstByDomainIdOrderByCheckTimeDesc(Long domainId);

    /**
     * 根据安全状态查询历史记录
     */
    List<DomainSecurityHistory> findBySecurityStatusOrderByCheckTimeDesc(String securityStatus);

    /**
     * 根据风险等级查询历史记录
     */
    List<DomainSecurityHistory> findByRiskLevelOrderByCheckTimeDesc(String riskLevel);

    /**
     * 查询指定时间范围内的安全检测记录
     */
    List<DomainSecurityHistory> findByCheckTimeBetweenOrderByCheckTimeDesc(
            LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 查询域名在指定时间范围内的安全检测记录
     */
    List<DomainSecurityHistory> findByDomainIdAndCheckTimeBetweenOrderByCheckTimeDesc(
            Long domainId, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 查询包含威胁的检测记录
     */
    @Query("SELECT h FROM DomainSecurityHistory h WHERE " +
           "h.hasMalware = true OR h.hasPhishing = true OR " +
           "h.hasUnwantedSoftware = true OR h.hasSuspiciousActivity = true " +
           "ORDER BY h.checkTime DESC")
    List<DomainSecurityHistory> findRecordsWithThreats();

    /**
     * 查询域名包含威胁的检测记录
     */
    @Query("SELECT h FROM DomainSecurityHistory h WHERE h.domainId = :domainId AND " +
           "(h.hasMalware = true OR h.hasPhishing = true OR " +
           "h.hasUnwantedSoftware = true OR h.hasSuspiciousActivity = true) " +
           "ORDER BY h.checkTime DESC")
    List<DomainSecurityHistory> findDomainRecordsWithThreats(@Param("domainId") Long domainId);

    /**
     * 查询检测失败的记录
     */
    @Query("SELECT h FROM DomainSecurityHistory h WHERE " +
           "h.errorMessage IS NOT NULL AND h.errorMessage != '' " +
           "ORDER BY h.checkTime DESC")
    List<DomainSecurityHistory> findFailedChecks();

    /**
     * 统计域名的安全检测次数
     */
    long countByDomainId(Long domainId);

    /**
     * 统计指定时间范围内的检测次数
     */
    long countByCheckTimeBetween(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 统计域名在指定时间范围内的检测次数
     */
    long countByDomainIdAndCheckTimeBetween(Long domainId, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 统计各安全状态的记录数量
     */
    @Query("SELECT h.securityStatus, COUNT(h) FROM DomainSecurityHistory h " +
           "GROUP BY h.securityStatus")
    List<Object[]> countBySecurityStatus();

    /**
     * 统计各风险等级的记录数量
     */
    @Query("SELECT h.riskLevel, COUNT(h) FROM DomainSecurityHistory h " +
           "WHERE h.riskLevel IS NOT NULL " +
           "GROUP BY h.riskLevel")
    List<Object[]> countByRiskLevel();

    /**
     * 查询最近N天的安全检测记录
     */
    @Query("SELECT h FROM DomainSecurityHistory h WHERE " +
           "h.checkTime >= :startTime " +
           "ORDER BY h.checkTime DESC")
    List<DomainSecurityHistory> findRecentRecords(@Param("startTime") LocalDateTime startTime);

    /**
     * 查询域名最近N天的安全检测记录
     */
    @Query("SELECT h FROM DomainSecurityHistory h WHERE " +
           "h.domainId = :domainId AND h.checkTime >= :startTime " +
           "ORDER BY h.checkTime DESC")
    List<DomainSecurityHistory> findDomainRecentRecords(
            @Param("domainId") Long domainId, 
            @Param("startTime") LocalDateTime startTime);

    /**
     * 删除指定时间之前的历史记录（用于清理过期数据）
     */
    void deleteByCheckTimeBefore(LocalDateTime expireTime);

    /**
     * 查询域名的安全状态变化记录
     */
    @Query("SELECT h FROM DomainSecurityHistory h WHERE h.domainId = :domainId " +
           "AND h.securityStatus != :currentStatus " +
           "ORDER BY h.checkTime DESC")
    List<DomainSecurityHistory> findStatusChangeRecords(
            @Param("domainId") Long domainId, 
            @Param("currentStatus") String currentStatus);

    /**
     * 查询最近一次成功的安全检测记录
     */
    @Query("SELECT h FROM DomainSecurityHistory h WHERE h.domainId = :domainId " +
           "AND (h.errorMessage IS NULL OR h.errorMessage = '') " +
           "ORDER BY h.checkTime DESC")
    Optional<DomainSecurityHistory> findLastSuccessfulCheck(@Param("domainId") Long domainId);

    /**
     * 查询检测来源统计
     */
    @Query("SELECT h.checkSource, COUNT(h) FROM DomainSecurityHistory h " +
           "GROUP BY h.checkSource")
    List<Object[]> countByCheckSource();

    /**
     * 查询域名安全趋势（最近30天每天的安全状态）
     */
    @Query("SELECT DATE(h.checkTime) as checkDate, h.securityStatus, COUNT(h) " +
           "FROM DomainSecurityHistory h WHERE h.domainId = :domainId " +
           "AND h.checkTime >= :startTime " +
           "GROUP BY DATE(h.checkTime), h.securityStatus " +
           "ORDER BY checkDate DESC")
    List<Object[]> findDomainSecurityTrend(
            @Param("domainId") Long domainId, 
            @Param("startTime") LocalDateTime startTime);
}
