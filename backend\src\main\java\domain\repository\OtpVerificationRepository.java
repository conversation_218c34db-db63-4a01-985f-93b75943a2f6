package domain.repository;

import domain.entity.OtpVerification;
import org.springframework.data.r2dbc.repository.Modifying;
import org.springframework.data.r2dbc.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * OTP验证仓库接口 - 响应式版本
 */
@Repository
public interface OtpVerificationRepository extends BaseRepository<OtpVerification, Long> {

    /**
     * 根据邮箱和验证码查找有效的OTP
     */
    @Query("SELECT * FROM otp_verifications WHERE email = :email AND otp_code = :otpCode AND used = false AND expires_at > :now")
    Mono<OtpVerification> findValidOtp(@Param("email") String email, @Param("otpCode") String otpCode, @Param("now") LocalDateTime now);

    /**
     * 根据邮箱查找所有OTP记录
     */
    @Query("SELECT * FROM otp_verifications WHERE email = :email ORDER BY created_at DESC")
    Flux<OtpVerification> findByEmailOrderByCreatedAtDesc(@Param("email") String email);

    /**
     * 根据类型查找OTP记录
     */
    @Query("SELECT * FROM otp_verifications WHERE type = :type ORDER BY created_at DESC")
    Flux<OtpVerification> findByTypeOrderByCreatedAtDesc(@Param("type") OtpVerification.OtpType type);

    /**
     * 根据关联ID查找OTP记录
     */
    @Query("SELECT * FROM otp_verifications WHERE related_id = :relatedId ORDER BY created_at DESC")
    Flux<OtpVerification> findByRelatedIdOrderByCreatedAtDesc(@Param("relatedId") String relatedId);
    
    /**
     * 查找指定时间范围内的OTP记录
     */
    @Query("SELECT * FROM otp_verifications WHERE created_at BETWEEN :startTime AND :endTime ORDER BY created_at DESC")
    Flux<OtpVerification> findByCreatedAtBetween(LocalDateTime startTime,
                                                LocalDateTime endTime);

    @Query("SELECT * FROM otp_verifications WHERE used = false AND expires_at < :now")
    Flux<OtpVerification> findExpiredOtps(@Param("now") LocalDateTime now);

    @Query("SELECT * FROM otp_verifications WHERE used = false AND expires_at > :now")
    Flux<OtpVerification> findActiveOtps(@Param("now") LocalDateTime now);

    @Query("SELECT COUNT(*) FROM otp_verifications WHERE email = :email AND created_at >= :todayStart")
    Mono<Long> countTodayOtpsByEmail(@Param("email") String email, @Param("todayStart") LocalDateTime todayStart);

    @Query("SELECT COUNT(*) FROM otp_verifications WHERE type = :type")
    Mono<Long> countByType(@Param("type") OtpVerification.OtpType type);

    @Query("SELECT COUNT(*) FROM otp_verifications WHERE used = true")
    Mono<Long> countByUsedTrue();

    /**
     * 统计今日生成的OTP数量
     */
    @Query("SELECT COUNT(*) FROM otp_verifications WHERE created_at >= :todayStart")
    Mono<Long> countTodayOtps(@Param("todayStart") LocalDateTime todayStart);

    /**
     * 统计今日验证成功的OTP数量
     */
    @Query("SELECT COUNT(*) FROM otp_verifications WHERE used = true AND created_at >= :todayStart")
    Mono<Long> countTodayVerifiedOtps(@Param("todayStart") LocalDateTime todayStart);

    // ==================== 新增方法：支持统一存储策略 ====================

    /**
     * 查找过期的OTP记录
     */
    List<OtpVerification> findByExpiresAtBefore(LocalDateTime dateTime);

    /**
     * 删除过期的OTP记录
     */
    @Modifying
    @Query("DELETE FROM OtpVerification o WHERE o.expiresAt < :now")
    void deleteByExpiresAtBefore(@Param("now") LocalDateTime now);

    /**
     * 查找最新的有效OTP（按标识符、方法、卡片ID）
     */
    @Query("SELECT o FROM OtpVerification o WHERE o.identifier = :identifier " +
           "AND o.method = :method AND o.cardId = :cardId " +
           "AND o.used = false AND o.expiresAt > :now " +
           "ORDER BY o.createdAt DESC")
    Optional<OtpVerification> findLatestValidOtp(@Param("identifier") String identifier,
                                                @Param("method") String method,
                                                @Param("cardId") String cardId,
                                                @Param("now") LocalDateTime now);

    /**
     * 根据方法和标识符查找OTP记录
     */
    List<OtpVerification> findByMethodAndIdentifierOrderByCreatedAtDesc(String method, String identifier);

    /**
     * 根据卡片ID查找OTP记录
     */
    List<OtpVerification> findByCardIdOrderByCreatedAtDesc(String cardId);

    /**
     * 查找指定标识符的有效OTP
     */
    @Query("SELECT o FROM OtpVerification o WHERE o.identifier = :identifier " +
           "AND o.used = false AND o.expiresAt > :now " +
           "ORDER BY o.createdAt DESC")
    List<OtpVerification> findValidOtpsByIdentifier(@Param("identifier") String identifier,
                                                   @Param("now") LocalDateTime now);

    // ==================== OtpVerificationService 需要的方法 ====================

    /**
     * 根据标识符、方法、卡片ID查找未使用的OTP记录（按创建时间倒序）
     */
    List<OtpVerification> findByIdentifierAndMethodAndCardIdAndUsedFalseOrderByCreatedAtDesc(
            String identifier, String method, String cardId);

    /**
     * 查找指定时间之前创建且已使用的OTP记录
     */
    List<OtpVerification> findByCreatedAtBeforeAndUsedTrue(LocalDateTime dateTime);

    /**
     * 统计指定时间范围内创建的OTP数量
     */
    Mono<Long> countByCreatedAtBetween(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 统计指定时间范围内创建且已使用的OTP数量
     */
    long countByCreatedAtBetweenAndUsedTrue(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 统计未使用且未过期的OTP数量
     */
    long countByUsedFalseAndExpiresAtAfter(LocalDateTime now);

    /**
     * 根据标识符、方法、卡片ID、验证码查找已使用的OTP记录（按创建时间倒序）
     */
    List<OtpVerification> findByIdentifierAndMethodAndCardIdAndOtpCodeAndUsedTrueOrderByCreatedAtDesc(
            String identifier, String method, String cardId, String otpCode);

    /**
     * 根据标识符、方法、卡片ID查找所有OTP记录（按创建时间倒序）
     */
    List<OtpVerification> findByIdentifierAndMethodAndCardIdOrderByCreatedAtDesc(
            String identifier, String method, String cardId);

    /**
     * 统计指定操作员在指定时间范围内验证的OTP数量
     */
    long countByVerifiedByAndCreatedAtBetween(String verifiedBy, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 统计指定操作员验证的OTP总数量
     */
    long countByVerifiedBy(String verifiedBy);
}
