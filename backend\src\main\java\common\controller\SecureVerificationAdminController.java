package common.controller;

import domain.entity.OtpVerification;
import domain.entity.PaymentTransaction;
import domain.entity.VerificationSetting;
import common.service.payment.OtpVerificationService;
import common.service.payment.PaymentService;
import common.service.payment.VerificationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.access.prepost.PreAuthorize;

import reactor.core.publisher.Mono;
import reactor.core.publisher.Flux;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;

import core.common.ApiResponse;

/**
 * 3D安全验证管理控制器
 * 用于管理3D Secure验证流程
 */
@RestController
@RequestMapping("/api/payment/3d-secure/admin")
@PreAuthorize("hasRole('ADMIN')")
public class SecureVerificationAdminController {
    private static final Logger logger = LoggerFactory.getLogger(SecureVerificationAdminController.class);

    @Autowired
    private PaymentService paymentService;
    @Autowired
    private OtpVerificationService otpVerificationService;

    @Autowired
    private VerificationService verificationService;
      /**
     * 获取待处理的验证请求列表
     */
    @GetMapping("/requests")
    public Mono<ResponseEntity<ApiResponse<Object>>> getVerificationRequests(
            @RequestParam(value = "page", defaultValue = "0") int page,
            @RequestParam(value = "size", defaultValue = "10") int size,
            @RequestParam(value = "query", required = false) String query,
            @RequestParam(value = "status", required = false) String status,
            @RequestParam(value = "startTime", required = false) String startTime,
            @RequestParam(value = "endTime", required = false) String endTime) {

        try {
            // 解析时间参数
            LocalDateTime startDateTime = startTime != null ?
                LocalDateTime.parse(startTime, DateTimeFormatter.ISO_DATE_TIME) : null;

            LocalDateTime endDateTime = endTime != null ?
                LocalDateTime.parse(endTime, DateTimeFormatter.ISO_DATE_TIME) : null;

            // 获取分页数据 - 修复：改为响应式处理
            return verificationService.findVerifications(page, size, query, status, startDateTime, endDateTime)
                .flatMap(verificationPage -> {
                    List<Mono<Map<String, Object>>> verificationMonos = new ArrayList<>();

                    // 转换为前端所需格式
                    for (VerificationSetting setting : verificationPage.getContent()) {
                        Mono<Map<String, Object>> verificationMono = paymentService.getTransaction(setting.getTransactionId())
                            .map(transaction -> {
                                Map<String, Object> verificationRequest = new HashMap<>();
                                verificationRequest.put("transactionId", setting.getTransactionId());
                                verificationRequest.put("cardNumber", transaction.getCardNumber());
                                verificationRequest.put("amount", transaction.getAmount().toString());
                                verificationRequest.put("merchantName", transaction.getMerchantName());
                                verificationRequest.put("customerName", "Customer"); // 默认客户姓名
                                verificationRequest.put("customerEmail", "<EMAIL>"); // 默认客户邮箱
                                verificationRequest.put("customerPhone", ""); // 默认客户电话
                                verificationRequest.put("requestTime", setting.getCreatedAt().toString());
                                verificationRequest.put("status", setting.getStatus().name());
                                // 添加验证设置信息
                                verificationRequest.put("verificationSettings", verificationService.convertToMap(setting));
                                return verificationRequest;
                            })
                            .onErrorResume(e -> {
                                logger.error("获取交易 {} 的详情失败", setting.getTransactionId(), e);
                                return Mono.empty();
                            });
                        verificationMonos.add(verificationMono);
                    }

                    return Flux.fromIterable(verificationMonos)
                        .flatMap(mono -> mono)
                        .collectList()
                        .map(verificationRequests -> {
                            Map<String, Object> response = new HashMap<>();
                            Map<String, Object> pagination = new HashMap<>();
                            pagination.put("page", page);
                            pagination.put("size", size);
                            pagination.put("total", verificationPage.getTotalElements());

                            response.put("data", verificationRequests);
                            response.put("pagination", pagination);

                            return ResponseEntity.ok(new ApiResponse<>(200, "success", (Object) response, true));
                        });
                })
                .onErrorResume(Exception.class, e -> {
                    logger.error("获取验证请求列表失败", e);
                    return Mono.just(ResponseEntity.badRequest().body(new ApiResponse<>(400, "获取验证请求列表失败: " + e.getMessage(), (Object) null, false)));
                });
        } catch (Exception e) {
            logger.error("获取验证请求列表失败", e);
            return Mono.just(ResponseEntity.badRequest().body(new ApiResponse<>(400, "获取验证请求列表失败: " + e.getMessage(), (Object) null, false)));
        }
    }
      /**
     * 获取验证请求详情
     */
    @GetMapping("/transactions/{transactionId}")
    public Mono<ResponseEntity<ApiResponse<Object>>> getVerificationDetail(@PathVariable String transactionId) {
        return verificationService.getVerificationSetting(transactionId)
            .switchIfEmpty(Mono.error(new RuntimeException("未找到该交易的验证设置")))
            .flatMap(setting ->
                paymentService.getTransaction(transactionId)
                    .switchIfEmpty(Mono.error(new RuntimeException("未找到该交易的信息")))
                    .map(transaction -> {
                        Map<String, Object> verification = new HashMap<>();
                        verification.put("transactionId", transactionId);
                        verification.put("cardNumber", transaction.getCardNumber());
                        verification.put("amount", transaction.getAmount().toString());
                        verification.put("merchantName", transaction.getMerchantName());
                        verification.put("customerName", "Customer"); // 默认客户姓名
                        verification.put("customerEmail", "<EMAIL>"); // 默认客户邮箱
                        verification.put("customerPhone", ""); // 默认客户电话
                        verification.put("requestTime", setting.getCreatedAt().toString());
                        verification.put("status", setting.getStatus().name());

                        // 添加验证设置信息
                        verification.put("verificationSettings", verificationService.convertToMap(setting));

                        return ResponseEntity.ok(new ApiResponse<>(200, "success", (Object) verification, true));
                    })
            )
            .onErrorResume(Exception.class, e -> {
                logger.error("获取验证请求详情失败", e);
                return Mono.just(ResponseEntity.badRequest().body(new ApiResponse<>(400, "获取验证请求详情失败: " + e.getMessage(), (Object) null, false)));
            });
    }
      /**
     * 更新验证方法设置
     */
    @PutMapping("/transactions/{transactionId}/method")
    public Mono<ResponseEntity<Object>> updateVerificationMethod(
            @PathVariable String transactionId,
            @RequestBody Map<String, Object> settings) {

        try {
            String method = (String) settings.get("method");
            if (method == null) {
                return Mono.just(ResponseEntity.badRequest().body(createErrorResponse("验证方式不能为空")));
            }

            String customPhone = (String) settings.get("customPhone");
            String customEmail = (String) settings.get("customEmail");
            String message = (String) settings.get("message");
            boolean requireAdminVerification = (boolean) settings.getOrDefault("requireAdminVerification", true);

            // 保存验证设置到数据库
            return verificationService.saveVerificationSetting(
                transactionId, method, customPhone, customEmail, message, requireAdminVerification)
                .map(setting -> {
                    logger.info("更新验证方式: transactionId={}, method={}, settings={}",
                            transactionId, method, settings);

                    Map<String, Object> response = new HashMap<>();
                    response.put("success", true);
                    response.put("message", "验证方式设置成功");

                    return ResponseEntity.ok((Object) response);
                })
                .onErrorResume(e -> {
                    logger.error("更新验证方式失败: {}", transactionId, e);
                    return Mono.just(ResponseEntity.badRequest().body(createErrorResponse("更新验证方式失败: " + e.getMessage())));
                });
        } catch (Exception e) {
            logger.error("更新验证方式失败: {}", transactionId, e);
            return Mono.just(ResponseEntity.badRequest().body(createErrorResponse("更新验证方式失败: " + e.getMessage())));
        }
    }
      /**
     * 手动验证交易
     */
    @PostMapping("/transactions/{transactionId}/verify")
    public Mono<ResponseEntity<Object>> manualVerifyTransaction(
            @PathVariable String transactionId,
            @RequestBody Map<String, Object> verificationData) {

        boolean success = (boolean) verificationData.getOrDefault("success", false);
        String message = (String) verificationData.get("message");

        // 更新验证状态
        return verificationService.manualVerify(transactionId, success, message)
            .switchIfEmpty(Mono.error(new RuntimeException("未找到该交易的验证设置")))
            .flatMap(setting -> {
                logger.info("手动验证交易: transactionId={}, success={}, message={}",
                        transactionId, success, message);

                Mono<PaymentTransaction> paymentMono;
                if (success) {
                    // 如果验证成功，调用支付服务进行交易处理
                    paymentMono = paymentService.processPayment(transactionId);
                } else {
                    // 如果验证失败，调用支付服务取消交易
                    paymentMono = paymentService.failPayment(transactionId, "验证被拒绝: " + message);
                }

                return paymentMono.map(transaction -> {
                    Map<String, Object> response = new HashMap<>();
                    response.put("success", true);
                    response.put("message", success ? "验证已通过" : "验证已拒绝");
                    return ResponseEntity.ok((Object) response);
                });
            })
            .onErrorResume(e -> {
                logger.error("手动验证交易失败: {}", transactionId, e);
                return Mono.just(
                    ResponseEntity.badRequest().body((Object) createErrorResponse("手动验证交易失败: " + e.getMessage()))
                );
            });
    }
      /**
     * 重新发送验证请求
     */
    @PostMapping("/transactions/{transactionId}/resend")
    public Mono<ResponseEntity<ApiResponse<Object>>> resendVerification(
            @PathVariable String transactionId,
            @RequestBody(required = false) Map<String, Object> requestBody) {

        // 修复：改为响应式处理
        return paymentService.getTransaction(transactionId)
            .switchIfEmpty(Mono.error(new RuntimeException("未找到该交易")))
            .flatMap(transaction ->
                verificationService.getVerificationSetting(transactionId)
                    .switchIfEmpty(Mono.error(new RuntimeException("未找到该交易的验证设置")))
                    .map(setting -> {
                        // 管理员需要自己设定验证码

                        // 生成适合当前验证方法的OTP
                        OtpVerification.OtpType otpType = OtpVerification.OtpType.PAYMENT_VERIFICATION;
                        String recipient;

                        switch (setting.getMethod()) {
                            case "SMS_USER":
                                recipient = ""; // 默认电话号码
                                break;
                            case "SMS_CUSTOM":
                                recipient = setting.getCustomPhone();
                                break;
                            case "EMAIL_USER":
                                recipient = "<EMAIL>"; // 默认客户邮箱
                                break;
                            case "EMAIL_CUSTOM":
                                recipient = setting.getCustomEmail();
                                break;
                            case "APP":
                            case "PIN":
                            case "AMEX_SECURE":
                            case "DIRECT":
                            default:
                                recipient = "<EMAIL>"; // 默认使用邮箱
                        }

                        // 管理员需要手动创建和发送验证码

                        logger.info("重新发送验证: transactionId={}, method={}, recipient={}",
                            transactionId, setting.getMethod(), recipient);

                        Map<String, Object> response = new HashMap<>();
                        response.put("success", true);
                        response.put("message", "已请求重新填写验证码");
                        response.put("recipient", recipient); // 返回接收者信息
                        response.put("method", setting.getMethod()); // 返回验证方式

                        return ResponseEntity.ok(new ApiResponse<>(200, "success", (Object) response, true));
                    })
            )
            .onErrorResume(Exception.class, e -> {
                logger.error("重新发送验证请求失败: {}", transactionId, e);
                return Mono.just(ResponseEntity.badRequest().body(new ApiResponse<>(400, "重新发送验证请求失败: " + e.getMessage(), (Object) null, false)));
            });
    }
      /**
     * 获取验证统计信息
     */
    @GetMapping("/stats")
    public Mono<ResponseEntity<ApiResponse<Object>>> getVerificationStats() {
        // 从服务获取实际统计数据
        return verificationService.getVerificationStats()
            .map(stats -> ResponseEntity.ok(new ApiResponse<>(200, "success", (Object) stats, true)))
            .onErrorResume(Exception.class, e -> {
                logger.error("获取验证统计信息失败", e);
                return Mono.just(ResponseEntity.badRequest().body(new ApiResponse<>(400, "获取验证统计信息失败: " + e.getMessage(), (Object) null, false)));
            });
    }


    /**
     * 创建错误响应
     */
    private Map<String, Object> createErrorResponse(String errorMessage) {
        Map<String, Object> response = new HashMap<>();
        response.put("success", false);
        response.put("error", errorMessage);
        return response;
    }



    /**
     * 转换验证方法类型
     */
    private String getMethodType(String method) {
        switch (method) {
            case "SMS_USER":
            case "SMS_CUSTOM":
                return "sms";
            case "EMAIL_USER":
            case "EMAIL_CUSTOM":
                return "email";
            case "APP":
                return "app";
            default:
                return "email"; // 默认使用邮箱
        }
    }
}
