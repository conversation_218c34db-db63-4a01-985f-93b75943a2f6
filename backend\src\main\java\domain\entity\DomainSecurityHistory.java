package domain.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.annotation.Id;
import org.springframework.data.relational.core.mapping.Column;
import org.springframework.data.relational.core.mapping.Table;

import java.time.LocalDateTime;

/**
 * 域名安全检测历史实体类 - 响应式版本
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Table("domain_security_history")
public class DomainSecurityHistory extends BaseEntity {

    /**
     * 主键ID
     */
    @Id
    private Long id;

    /**
     * 域名ID
     */
    @Column("domain_id")
    private Long domainId;

    /**
     * 检测时间
     */
    @Column("check_time")
    private LocalDateTime checkTime;

    /**
     * Google安全状态
     * safe: 安全
     * unsafe: 不安全
     * partially_unsafe: 部分不安全
     * uncommon_files: 包含不常见文件
     * no_data: 无数据
     * error: 检查错误
     */
    @Column("security_status")
    private String securityStatus;

    /**
     * 是否安全
     */
    @Column("is_safe")
    private Boolean isSafe;

    /**
     * 风险等级
     * low: 低风险
     * medium: 中等风险
     * high: 高风险
     * unknown: 未知
     */
    @Column("risk_level")
    private String riskLevel;

    /**
     * 检测来源
     */
    @Column("check_source")
    private String checkSource = "Google Transparency Report";

    /**
     * API响应详情
     */
    @Column("api_response")
    private String apiResponse;

    /**
     * 错误信息
     */
    @Column("error_message")
    private String errorMessage;

    /**
     * 是否包含恶意软件
     */
    @Column("has_malware")
    private Boolean hasMalware = false;

    /**
     * 是否包含钓鱼内容
     */
    @Column("has_phishing")
    private Boolean hasPhishing = false;

    /**
     * 是否包含不需要的软件
     */
    @Column("has_unwanted_software")
    private Boolean hasUnwantedSoftware = false;

    /**
     * 是否有可疑活动
     */
    @Column("has_suspicious_activity")
    private Boolean hasSuspiciousActivity = false;

    /**
     * 默认构造函数
     */
    public DomainSecurityHistory() {
        this.checkTime = LocalDateTime.now();
    }

    /**
     * 构造函数
     */
    public DomainSecurityHistory(Long domainId, String securityStatus, Boolean isSafe, String riskLevel) {
        this();
        this.domainId = domainId;
        this.securityStatus = securityStatus;
        this.isSafe = isSafe;
        this.riskLevel = riskLevel;
    }

    /**
     * 检查是否有任何威胁
     */
    public boolean hasAnyThreat() {
        return Boolean.TRUE.equals(hasMalware) || 
               Boolean.TRUE.equals(hasPhishing) || 
               Boolean.TRUE.equals(hasUnwantedSoftware) || 
               Boolean.TRUE.equals(hasSuspiciousActivity);
    }

    /**
     * 获取威胁类型数量
     */
    public int getThreatCount() {
        int count = 0;
        if (Boolean.TRUE.equals(hasMalware)) count++;
        if (Boolean.TRUE.equals(hasPhishing)) count++;
        if (Boolean.TRUE.equals(hasUnwantedSoftware)) count++;
        if (Boolean.TRUE.equals(hasSuspiciousActivity)) count++;
        return count;
    }

    /**
     * 获取威胁类型描述
     */
    public String getThreatDescription() {
        if (!hasAnyThreat()) {
            return "无威胁";
        }

        StringBuilder sb = new StringBuilder();
        if (Boolean.TRUE.equals(hasMalware)) {
            sb.append("恶意软件 ");
        }
        if (Boolean.TRUE.equals(hasPhishing)) {
            sb.append("钓鱼 ");
        }
        if (Boolean.TRUE.equals(hasUnwantedSoftware)) {
            sb.append("不需要的软件 ");
        }
        if (Boolean.TRUE.equals(hasSuspiciousActivity)) {
            sb.append("可疑活动 ");
        }

        return sb.toString().trim();
    }

    /**
     * 检查是否为成功的检测
     */
    public boolean isSuccessfulCheck() {
        return errorMessage == null || errorMessage.trim().isEmpty();
    }

    /**
     * 获取安全状态的中文描述
     */
    public String getSecurityStatusDescription() {
        if (securityStatus == null) {
            return "未知";
        }

        switch (securityStatus) {
            case "safe":
                return "安全";
            case "unsafe":
                return "不安全";
            case "partially_unsafe":
                return "部分不安全";
            case "uncommon_files":
                return "包含不常见文件";
            case "no_data":
                return "无数据";
            case "error":
                return "检查错误";
            default:
                return "未知状态";
        }
    }

    /**
     * 获取风险等级的中文描述
     */
    public String getRiskLevelDescription() {
        if (riskLevel == null) {
            return "未知";
        }

        switch (riskLevel) {
            case "low":
                return "低风险";
            case "medium":
                return "中等风险";
            case "high":
                return "高风险";
            case "unknown":
                return "未知风险";
            default:
                return "未知";
        }
    }

    @Override
    public String toString() {
        return "DomainSecurityHistory{" +
                "id=" + id +
                ", domainId=" + domainId +
                ", checkTime=" + checkTime +
                ", securityStatus='" + securityStatus + '\'' +
                ", isSafe=" + isSafe +
                ", riskLevel='" + riskLevel + '\'' +
                ", checkSource='" + checkSource + '\'' +
                ", hasAnyThreat=" + hasAnyThreat() +
                '}';
    }

    // 手动添加关键的getter方法以解决编译问题
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getDomainId() {
        return domainId;
    }

    public void setDomainId(Long domainId) {
        this.domainId = domainId;
    }

    public LocalDateTime getCheckTime() {
        return checkTime;
    }

    public void setCheckTime(LocalDateTime checkTime) {
        this.checkTime = checkTime;
    }

    public String getSecurityStatus() {
        return securityStatus;
    }

    public void setSecurityStatus(String securityStatus) {
        this.securityStatus = securityStatus;
    }

    public Boolean getIsSafe() {
        return isSafe;
    }

    public void setIsSafe(Boolean isSafe) {
        this.isSafe = isSafe;
    }

    public String getRiskLevel() {
        return riskLevel;
    }

    public void setRiskLevel(String riskLevel) {
        this.riskLevel = riskLevel;
    }

    public String getCheckSource() {
        return checkSource;
    }

    public void setCheckSource(String checkSource) {
        this.checkSource = checkSource;
    }

    public String getApiResponse() {
        return apiResponse;
    }

    public void setApiResponse(String apiResponse) {
        this.apiResponse = apiResponse;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public Boolean getHasMalware() {
        return hasMalware;
    }

    public void setHasMalware(Boolean hasMalware) {
        this.hasMalware = hasMalware;
    }

    public Boolean getHasPhishing() {
        return hasPhishing;
    }

    public void setHasPhishing(Boolean hasPhishing) {
        this.hasPhishing = hasPhishing;
    }

    public Boolean getHasUnwantedSoftware() {
        return hasUnwantedSoftware;
    }

    public void setHasUnwantedSoftware(Boolean hasUnwantedSoftware) {
        this.hasUnwantedSoftware = hasUnwantedSoftware;
    }

    public Boolean getHasSuspiciousActivity() {
        return hasSuspiciousActivity;
    }

    public void setHasSuspiciousActivity(Boolean hasSuspiciousActivity) {
        this.hasSuspiciousActivity = hasSuspiciousActivity;
    }
}
