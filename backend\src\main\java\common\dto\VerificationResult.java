package common.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 验证结果DTO
 * 封装验证操作的结果信息
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class VerificationResult {
    
    /**
     * 验证ID
     */
    private String verificationId;
    
    /**
     * 卡片ID
     */
    private String cardId;
    
    /**
     * 验证类型
     */
    private String verificationType;
    
    /**
     * 验证状态
     */
    private VerificationStatus status;
    
    /**
     * 验证消息
     */
    private String message;
    
    /**
     * 验证URL（如果需要跳转）
     */
    private String verificationUrl;
    
    /**
     * 验证码（如果适用）
     */
    private String verificationCode;
    
    /**
     * 过期时间
     */
    private LocalDateTime expiresAt;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 额外数据
     */
    private Map<String, Object> additionalData;
    
    /**
     * 验证状态枚举
     */
    public enum VerificationStatus {
        PENDING("待验证"),
        SUCCESS("验证成功"),
        FAILED("验证失败"),
        EXPIRED("已过期"),
        CANCELLED("已取消");
        
        private final String description;
        
        VerificationStatus(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 创建成功结果
     */
    public static VerificationResult success(String verificationId, String cardId, String message) {
        return VerificationResult.builder()
                .verificationId(verificationId)
                .cardId(cardId)
                .status(VerificationStatus.SUCCESS)
                .message(message)
                .createdAt(LocalDateTime.now())
                .build();
    }
    
    /**
     * 创建失败结果
     */
    public static VerificationResult failed(String verificationId, String cardId, String message) {
        return VerificationResult.builder()
                .verificationId(verificationId)
                .cardId(cardId)
                .status(VerificationStatus.FAILED)
                .message(message)
                .createdAt(LocalDateTime.now())
                .build();
    }
    
    /**
     * 创建待验证结果
     */
    public static VerificationResult pending(String verificationId, String cardId, String verificationType, 
                                           String verificationUrl, LocalDateTime expiresAt) {
        return VerificationResult.builder()
                .verificationId(verificationId)
                .cardId(cardId)
                .verificationType(verificationType)
                .status(VerificationStatus.PENDING)
                .message("验证已启动，请完成验证")
                .verificationUrl(verificationUrl)
                .expiresAt(expiresAt)
                .createdAt(LocalDateTime.now())
                .build();
    }
    
    /**
     * 检查是否成功
     */
    public boolean isSuccess() {
        return status == VerificationStatus.SUCCESS;
    }
    
    /**
     * 检查是否失败
     */
    public boolean isFailed() {
        return status == VerificationStatus.FAILED;
    }
    
    /**
     * 检查是否待验证
     */
    public boolean isPending() {
        return status == VerificationStatus.PENDING;
    }
    
    /**
     * 检查是否已过期
     */
    public boolean isExpired() {
        return status == VerificationStatus.EXPIRED || 
               (expiresAt != null && LocalDateTime.now().isAfter(expiresAt));
    }
}
