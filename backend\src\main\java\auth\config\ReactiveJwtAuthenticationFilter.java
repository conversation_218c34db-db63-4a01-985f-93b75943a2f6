package auth.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.ReactiveSecurityContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.server.ServerWebExchange;
import org.springframework.web.server.WebFilter;
import org.springframework.web.server.WebFilterChain;
import reactor.core.publisher.Mono;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;


import java.util.Collections;
import java.util.Map;
import java.util.List;
import java.util.ArrayList;

import domain.entity.User;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.security.core.authority.SimpleGrantedAuthority;

@Component
@Order(Ordered.HIGHEST_PRECEDENCE)
public class ReactiveJwtAuthenticationFilter implements WebFilter {

    private static final Logger logger = LoggerFactory.getLogger(ReactiveJwtAuthenticationFilter.class);

    @Autowired
    private JwtUtil jwtUtil;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, WebFilterChain chain) {
        String path = exchange.getRequest().getPath().value();
        logger.debug("JWT Filter processing request: {}", path);

        String token = getTokenFromRequest(exchange);
        logger.debug("Extracted token: {}", token != null ? "present" : "null");

        if (StringUtils.hasText(token)) {
            boolean isValid = jwtUtil.validateToken(token);
            logger.debug("Token validation result: {}", isValid);

            if (isValid) {
                return authenticateToken(token)
                    .flatMap(authentication -> {
                        logger.debug("Authentication successful for token");
                        return chain.filter(exchange)
                            .contextWrite(ReactiveSecurityContextHolder.withAuthentication(authentication));
                    })
                    .switchIfEmpty(Mono.defer(() -> {
                        logger.debug("Authentication failed - no user found");
                        return chain.filter(exchange);
                    }))
                    .onErrorResume(ex -> {
                        logger.error("JWT authentication error", ex);
                        return chain.filter(exchange);
                    });
            }
        }

        logger.debug("No valid token, proceeding without authentication");
        return chain.filter(exchange);
    }

    private Mono<UsernamePasswordAuthenticationToken> authenticateToken(String token) {
        return Mono.fromCallable(() -> {
            String userId = jwtUtil.extractUserId(token);
            String key = "user:" + userId;
            logger.debug("Authenticating token for userId: {}, key: {}", userId, key);

            if (redisTemplate.hasKey(key)) {
                logger.debug("User found in Redis for key: {}", key);
                // 从Redis哈希中获取用户数据
                Map<Object, Object> userMap = redisTemplate.opsForHash().entries(key);
                logger.debug("User data from Redis: {}", userMap);

                // 构建User对象
                User user = new User();
                user.setId(Long.valueOf((String) userMap.get("id")));
                user.setName((String) userMap.get("name"));
                user.setUsername((String) userMap.get("username"));
                user.setRole((String) userMap.get("role"));

                logger.debug("Created user object: id={}, username={}, role={}", user.getId(), user.getUsername(), user.getRole());

                // 创建权限列表
                List<SimpleGrantedAuthority> authorities = new ArrayList<>();
                authorities.add(new SimpleGrantedAuthority("ROLE_" + user.getRole()));
                logger.debug("Created authorities: {}", authorities);

                // 返回认证信息
                return new UsernamePasswordAuthenticationToken(
                    user, null, authorities
                );
            } else {
                logger.debug("User not found in Redis for key: {}", key);
            }
            return null;
        })
        .onErrorResume(ex -> {
            logger.error("Error in authenticateToken", ex);
            return Mono.empty();
        });
    }

    private String getTokenFromRequest(ServerWebExchange exchange) {
        String bearerToken = exchange.getRequest().getHeaders().getFirst("Authorization");
        if (StringUtils.hasText(bearerToken) && bearerToken.startsWith("Bearer ")) {
            return bearerToken.substring(7);
        }
        return null;
    }
}
