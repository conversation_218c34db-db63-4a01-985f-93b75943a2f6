[mysqld]
user = mysql
port = 3306
bind-address = 0.0.0.0
default-authentication-plugin = mysql_native_password

character-set-server = utf8mb4
collation-server = utf8mb4_unicode_ci
init-connect = 'SET NAMES utf8mb4'

sql_mode = STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_AUTO_CREATE_USER
local_infile = 0
secure_file_priv = /var/lib/mysql-files/

table_open_cache = 2000
table_definition_cache = 1000
table_open_cache_instances = 16

# InnoDB存储引擎
innodb_file_per_table = 1
innodb_flush_log_at_trx_commit = 2
innodb_flush_method = O_DIRECT
innodb_log_file_size = 256M
innodb_log_files_in_group = 2
innodb_io_capacity = 1000
innodb_io_capacity_max = 2000
innodb_read_io_threads = 4
innodb_write_io_threads = 4
innodb_thread_concurrency = 0
innodb_lock_wait_timeout = 50
innodb_rollback_on_timeout = 1
innodb_purge_threads = 4
innodb_page_cleaners = 4
innodb_adaptive_hash_index = 1
innodb_change_buffering = all
innodb_old_blocks_time = 1000
innodb_stats_on_metadata = 0

# 二进制日志
log-bin = mysql-bin
binlog_format = ROW
binlog_cache_size = 2M
max_binlog_cache_size = 128M
max_binlog_size = 512M
expire_logs_days = 7
sync_binlog = 0
binlog_rows_query_log_events = 1

# 慢查询日志
slow_query_log = 1
slow_query_log_file = /var/lib/mysql/mysql-slow.log
long_query_time = 2
log_queries_not_using_indexes = 0
log_slow_admin_statements = 1
log_slow_slave_statements = 1

log-error = /var/lib/mysql/mysql-error.log
log_error_verbosity = 2

max_allowed_packet = 64M
net_buffer_length = 32K
net_read_timeout = 30
net_write_timeout = 60
wait_timeout = 120
interactive_timeout = 120

skip-name-resolve = 1
skip-external-locking = 1

concurrent_insert = 2
delay_key_write = ON
bulk_insert_buffer_size = 64M
myisam_sort_buffer_size = 128M
myisam_max_sort_file_size = 2G
myisam_repair_threads = 1

tmpdir = /tmp

# 性能监控
performance_schema = ON
performance_schema_consumer_events_statements_current = ON
performance_schema_consumer_events_statements_history = ON
performance_schema_consumer_events_statements_history_long = ON
performance_schema_consumer_events_waits_current = ON
performance_schema_consumer_events_waits_history = ON
performance_schema_consumer_events_waits_history_long = ON
performance_schema_max_statement_classes = 200
performance_schema_max_statement_stack = 10

# 查询优化器
optimizer_switch = 'index_merge=on,index_merge_union=on,index_merge_sort_union=on,index_merge_intersection=on,engine_condition_pushdown=on,index_condition_pushdown=on,mrr=on,mrr_cost_based=on,block_nested_loop=on,batched_key_access=off,materialization=on,semijoin=on,loosescan=on,firstmatch=on,duplicateweedout=on,subquery_materialization_cost_based=on,use_index_extensions=on,condition_fanout_filter=on,derived_merge=on'

max_connections = 5000
max_user_connections = 4500

[mysql]
default-character-set = utf8mb4

[client]
default-character-set = utf8mb4
port = 3306

[mysqldump]
quick
quote-names
max_allowed_packet = 64M
