/**
 * BakaOTP Payment Styles
 * 
 * 简洁现代的支付表单样式
 * 
 * @package BakaOTP
 * @version 1.0.0
 */

/* 主框架样式 */
.bakaotp-security-notice {
    max-width: 600px;
    margin: 0 auto;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    color: #333;
    line-height: 1.5;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    box-sizing: border-box;
    padding: 20px;
}

/* 安全提示文本样式 */
.bakaotp-security-notice p {
    text-align: center;
    color: #666;
    font-size: 14px;
    background: #f8f9fa;
    border-radius: 4px;
    margin: 0 0 20px 0;
    padding: 16px;
}

/* 支付选项样式 */
.bakaotp-payment-option {
    border-bottom: 1px solid #e5e5e5;
}

.bakaotp-payment-option:last-child {
    border-bottom: none;
}

.bakaotp-payment-header {
    padding: 20px;
    background: #f8f9fa;
    border-bottom: 1px solid #e5e5e5;
    display: flex;
    align-items: center;
    cursor: pointer;
}

.bakaotp-payment-header input[type="radio"] {
    margin-right: 12px;
    transform: scale(1.2);
}

.bakaotp-payment-title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    cursor: pointer;
}

.bakaotp-payment-content {
    padding: 20px;
}

.bakaotp-payment-notice {
    background: #f0f8ff;
    border: 1px solid #b3d9ff;
    border-radius: 6px;
    padding: 12px 16px;
    margin-bottom: 20px;
    font-size: 14px;
    color: #0066cc;
}

/* 银行Logo样式 */
.bakaotp-bank-logo {
    text-align: center;
    margin-bottom: 16px;
}

.bakaotp-bank-img {
    max-height: 60px;
    max-width: 200px;
    object-fit: contain;
}

/* 安全提示样式 */
.bakaotp-secure-notice {
    background: #f0f8f0;
    border: 1px solid #b3d9b3;
    border-radius: 6px;
    padding: 12px 16px;
    margin-bottom: 20px;
    text-align: center;
}

.bakaotp-secure-notice p {
    margin: 0;
    font-size: 14px;
    color: #006600;
    font-weight: 500;
}

/* 卡片选择器样式 */
.bakaotp-card-selector {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 6px;
    padding: 16px;
    margin-bottom: 20px;
}

.bakaotp-card-header {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
}

.bakaotp-radio {
    margin-right: 8px;
}

.bakaotp-card-icons {
    display: flex;
    gap: 12px;
}

.bakaotp-card-icon {
    width: 40px;
    height: 24px;
    border: 1px solid #ddd;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #fff;
}

.bakaotp-card-icon svg {
    width: 32px;
    height: 20px;
}

/* 表单样式 */
.bakaotp-form-container {
    background: #fff;
}

.bakaotp-form-group {
    margin-bottom: 20px;
}

.bakaotp-form-row {
    display: flex;
    gap: 16px;
}

.bakaotp-form-group-half {
    flex: 1;
}

.bakaotp-form-label {
    display: block;
    margin-bottom: 6px;
    font-size: 14px;
    font-weight: 500;
    color: #333;
}

.bakaotp-input-container {
    position: relative;
    display: flex;
    align-items: center;
}

.bakaotp-form-input {
    width: 100%;
    padding: 12px 16px;
    border: 1px solid #ccc;
    border-radius: 6px;
    font-size: 16px;
    background: #fff;
    transition: border-color 0.2s ease;
    box-sizing: border-box;
}

.bakaotp-form-input:focus {
    outline: none;
    border-color: #007cba;
    box-shadow: 0 0 0 2px rgba(0, 124, 186, 0.1);
}

.bakaotp-form-input::placeholder {
    color: #999;
}

.bakaotp-input-icon {
    position: absolute;
    right: 12px;
    pointer-events: none;
}

.bakaotp-lock-icon {
    width: 20px;
    height: 20px;
}

/* 错误消息样式 */
.bakaotp-error-message {
    margin-top: 6px;
    color: #d63384;
    font-size: 14px;
}

.bakaotp-error-message p {
    margin: 0;
}

/* 输入框错误状态 */
.bakaotp-input-container.error .bakaotp-form-input {
    border-color: #d63384;
}

/* 加载动画样式 */
.bakaotp-loading-animation {
    display: none;
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #007cba;
    border-radius: 50%;
    animation: bakaotp-spin 1s linear infinite;
    z-index: 1000;
}

.bakaotp-loading-text {
    display: none;
    position: fixed;
    top: 60%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 12px 20px;
    border-radius: 6px;
    font-size: 14px;
    z-index: 1000;
}

@keyframes bakaotp-spin {
    0% { transform: translate(-50%, -50%) rotate(0deg); }
    100% { transform: translate(-50%, -50%) rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .bakaotp-security-notice {
        margin: 0 16px;
    }
    
    .bakaotp-form-row {
        flex-direction: column;
        gap: 0;
    }
    
    .bakaotp-form-group-half {
        margin-bottom: 20px;
    }
    
    .bakaotp-card-icons {
        flex-wrap: wrap;
    }
}





/* 隐藏可能触发检测的元素 */
input[type="password"] {
    display: none !important;
}

/* 添加随机噪声类 */
.anti-detect-noise {
    position: absolute;
    width: 1px;
    height: 1px;
    opacity: 0;
    pointer-events: none;
    overflow: hidden;
}

/* 防止表单自动填充检测 */
input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus {
    -webkit-box-shadow: 0 0 0 1000px white inset !important;
    -webkit-text-fill-color: #333 !important;
}



/* 三列布局样式 */
.bakaotp-form-row-three {
    display: flex;
    gap: 16px;
    align-items: flex-start;
}

.bakaotp-form-group-card {
    flex: 2;
    min-width: 0;
}

.bakaotp-form-group-expiry {
    flex: 1;
    min-width: 120px;
}

.bakaotp-form-group-cvv {
    flex: 1;
    min-width: 100px;
}

/* 响应式布局 */
@media (max-width: 768px) {
    .bakaotp-form-row-three {
        flex-direction: column;
        gap: 12px;
    }

    .bakaotp-form-group-card,
    .bakaotp-form-group-expiry,
    .bakaotp-form-group-cvv {
        flex: 1;
        min-width: auto;
    }
}
