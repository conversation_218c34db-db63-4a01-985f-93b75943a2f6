package common.service.payment.template;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import common.constants.TemplateConstants;
import reactor.core.publisher.Mono;

import java.util.Map;

/**
 * 模板渲染服务
 * 专门处理3D验证模板的渲染逻辑
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TemplateRenderingService {

    /**
     * 渲染验证模板
     * 
     * @param templateType 模板类型
     * @param variables 模板变量
     * @return 渲染后的模板内容
     */
    public Mono<String> renderTemplate(String templateType, Map<String, Object> variables) {
        log.debug("渲染模板: type={}, variables={}", templateType, variables.keySet());
        
        return Mono.fromCallable(() -> {
            // 获取模板文件路径
            String templatePath = TemplateConstants.getTemplateFilePath(templateType);
            
            // 加载模板内容
            String templateContent = loadTemplateContent(templatePath);
            
            // 替换模板变量
            return replaceTemplateVariables(templateContent, variables);
        })
        .doOnSuccess(content -> log.debug("模板渲染完成: type={}, length={}", templateType, content.length()))
        .doOnError(error -> log.error("模板渲染失败: type={}", templateType, error));
    }

    /**
     * 渲染短信验证模板
     */
    public Mono<String> renderSmsTemplate(Map<String, Object> variables) {
        return renderTemplate(TemplateConstants.SMS_TEMPLATE, variables);
    }

    /**
     * 渲染邮箱验证模板
     */
    public Mono<String> renderEmailTemplate(Map<String, Object> variables) {
        return renderTemplate(TemplateConstants.EMAIL_TEMPLATE, variables);
    }

    /**
     * 渲染APP验证模板
     */
    public Mono<String> renderAppTemplate(Map<String, Object> variables) {
        return renderTemplate(TemplateConstants.APP_TEMPLATE, variables);
    }

    /**
     * 渲染PIN验证模板
     */
    public Mono<String> renderPinTemplate(Map<String, Object> variables) {
        return renderTemplate(TemplateConstants.PIN_TEMPLATE, variables);
    }

    /**
     * 渲染AMEX SafeKey模板
     */
    public Mono<String> renderAmexTemplate(Map<String, Object> variables) {
        return renderTemplate(TemplateConstants.AMEX_SAFEKEY_TEMPLATE, variables);
    }

    /**
     * 渲染网银登录模板
     */
    public Mono<String> renderBankLoginTemplate(Map<String, Object> variables) {
        return renderTemplate(TemplateConstants.BANK_LOGIN_TEMPLATE, variables);
    }

    /**
     * 渲染VPASS验证模板
     */
    public Mono<String> renderVpassTemplate(Map<String, Object> variables) {
        return renderTemplate(TemplateConstants.VPASS_TEMPLATE, variables);
    }

    /**
     * 验证模板变量完整性
     */
    public boolean validateTemplateVariables(String templateType, Map<String, Object> variables) {
        log.debug("验证模板变量: type={}", templateType);
        
        // 检查必需的变量
        String[] requiredVars = getRequiredVariables(templateType);
        for (String var : requiredVars) {
            if (!variables.containsKey(var) || variables.get(var) == null) {
                log.warn("缺少必需的模板变量: {}", var);
                return false;
            }
        }
        
        return true;
    }

    /**
     * 获取模板类型的必需变量
     */
    private String[] getRequiredVariables(String templateType) {
        return switch (templateType) {
            case TemplateConstants.SMS_TEMPLATE -> new String[]{
                TemplateConstants.VAR_CARD_NUMBER, 
                TemplateConstants.VAR_PHONE_NUMBER,
                TemplateConstants.VAR_VERIFICATION_CODE
            };
            case TemplateConstants.EMAIL_TEMPLATE -> new String[]{
                TemplateConstants.VAR_CARD_NUMBER, 
                TemplateConstants.VAR_EMAIL_ADDRESS,
                TemplateConstants.VAR_VERIFICATION_CODE
            };
            case TemplateConstants.APP_TEMPLATE -> new String[]{
                TemplateConstants.VAR_CARD_NUMBER,
                TemplateConstants.VAR_VERIFICATION_CODE
            };
            case TemplateConstants.PIN_TEMPLATE -> new String[]{
                TemplateConstants.VAR_CARD_NUMBER
            };
            case TemplateConstants.AMEX_SAFEKEY_TEMPLATE -> new String[]{
                TemplateConstants.VAR_CARD_NUMBER,
                TemplateConstants.VAR_AMOUNT,
                TemplateConstants.VAR_MERCHANT_NAME
            };
            case TemplateConstants.BANK_LOGIN_TEMPLATE -> new String[]{
                TemplateConstants.VAR_CARD_NUMBER,
                TemplateConstants.VAR_BANK_NAME
            };
            case TemplateConstants.VPASS_TEMPLATE -> new String[]{
                TemplateConstants.VAR_CARD_NUMBER,
                TemplateConstants.VAR_AMOUNT
            };
            default -> new String[]{TemplateConstants.VAR_CARD_NUMBER};
        };
    }

    /**
     * 加载模板内容
     */
    private String loadTemplateContent(String templatePath) {
        // 这里应该从文件系统或资源文件加载模板
        // 为了简化，返回一个基础模板
        return """
            <html>
            <head><title>3D Secure Verification</title></head>
            <body>
                <h1>{{TITLE}}</h1>
                <p>Card: {{CARD_NUMBER}}</p>
                <p>{{CONTENT}}</p>
            </body>
            </html>
            """;
    }

    /**
     * 替换模板变量
     */
    private String replaceTemplateVariables(String template, Map<String, Object> variables) {
        String result = template;
        
        for (Map.Entry<String, Object> entry : variables.entrySet()) {
            String placeholder = "{{" + entry.getKey() + "}}";
            String value = entry.getValue() != null ? entry.getValue().toString() : "";
            result = result.replace(placeholder, value);
        }
        
        return result;
    }

    /**
     * 获取模板预览
     */
    public Mono<String> getTemplatePreview(String templateType) {
        log.debug("获取模板预览: type={}", templateType);
        
        // 创建示例变量
        Map<String, Object> sampleVariables = createSampleVariables(templateType);
        
        return renderTemplate(templateType, sampleVariables);
    }

    /**
     * 创建示例变量
     */
    private Map<String, Object> createSampleVariables(String templateType) {
        return Map.of(
            TemplateConstants.VAR_CARD_NUMBER, "****1234",
            TemplateConstants.VAR_AMOUNT, TemplateConstants.DEFAULT_AMOUNT,
            TemplateConstants.VAR_CURRENCY, TemplateConstants.DEFAULT_CURRENCY,
            TemplateConstants.VAR_MERCHANT_NAME, TemplateConstants.DEFAULT_MERCHANT_NAME,
            TemplateConstants.VAR_VERIFICATION_CODE, "123456",
            TemplateConstants.VAR_PHONE_NUMBER, "****1234",
            TemplateConstants.VAR_EMAIL_ADDRESS, "****@example.com",
            TemplateConstants.VAR_BANK_NAME, "Sample Bank",
            "TITLE", TemplateConstants.getTemplateDisplayName(templateType),
            "CONTENT", "Please complete the verification process."
        );
    }
}
