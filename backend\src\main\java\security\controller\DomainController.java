package security.controller;

import core.common.ApiResponse;
import domain.entity.Domain;
import security.service.DomainService;
import security.service.SecurityCheckService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

import java.util.List;
import java.util.Map;

//域名管理控制器
@RestController
@RequestMapping("/api/domains")
@CrossOrigin(origins = "*")
public class DomainController extends system.controller.BaseController {

    @Autowired
    private DomainService domainService;

    @Autowired
    private SecurityCheckService securityCheckService;

    //获取域名列表
    @GetMapping
    public Mono<ResponseEntity<ApiResponse<Object>>> getDomains() {
        return domainService.getAllDomains()
            .map(domains -> success((Object) domains, "获取域名列表成功"))
            .onErrorResume(e -> Mono.just(handleException((Exception) e, "获取域名列表")));
    }

    //获取域名详情
    @GetMapping("/{id}")
    public Mono<ResponseEntity<ApiResponse<Object>>> getDomain(@PathVariable Long id) {
        return domainService.getDomainById(id)
            .map(domain -> success((Object) domain, "获取域名详情成功"))
            .switchIfEmpty(Mono.just(error(404, "域名不存在")))
            .onErrorResume(e -> Mono.just(handleException((Exception) e, "获取域名详情")));
    }

    //创建新域名
    @PostMapping
    public Mono<ResponseEntity<ApiResponse<Object>>> createDomain(@RequestBody Domain domain) {
        // 验证域名格式
        if (domain.getDomainName() == null || domain.getDomainName().trim().isEmpty()) {
            return Mono.just(error(400, "域名不能为空"));
        }

        return domainService.existsByDomainName(domain.getDomainName())
            .flatMap(exists -> {
                if (exists) {
                    return Mono.just(error(400, "域名已存在"));
                }
                return domainService.createDomain(domain)
                    .map(createdDomain -> success((Object) createdDomain, "域名创建成功"));
            })
            .onErrorResume(e -> Mono.just(handleException((Exception) e, "创建域名")));
    }

    //更新域名
    @PutMapping("/{id}")
    public Mono<ResponseEntity<ApiResponse<Domain>>> updateDomain(@PathVariable Long id, @RequestBody Domain domain) {
        domain.setId(id);
        return domainService.updateDomain(domain)
            .map(updatedDomain -> success(updatedDomain, "域名更新成功"))
            .switchIfEmpty(Mono.just(error(404, "域名不存在")))
            .onErrorResume(Exception.class, e -> {
                logger.error("更新域名失败", e);
                return Mono.just(handleException(e, "更新域名"));
            });
    }

    //删除域名
    @DeleteMapping("/{id}")
    public Mono<ResponseEntity<ApiResponse<Object>>> deleteDomain(@PathVariable Long id) {
        return domainService.deleteDomain(id)
            .map(deleted -> {
                if (deleted) {
                    return success(null, "域名删除成功");
                } else {
                    return error(404, "域名不存在");
                }
            })
            .onErrorResume(Exception.class, e -> {
                logger.error("删除域名失败", e);
                return Mono.just(handleException(e, "删除域名"));
            });
    }

    //手动安全检测
    @PostMapping("/{id}/security-check")
    public Mono<ResponseEntity<ApiResponse<Object>>> performSecurityCheck(@PathVariable Long id) {
        return domainService.performSecurityCheck(id)
            .map(result -> success((Object) result, "安全检测完成"))
            .onErrorResume(e -> Mono.just(handleException((Exception) e, "安全检测")));
    }

    //批量安全检测
    @PostMapping("/batch-security-check")
    public Mono<ResponseEntity<ApiResponse<Object>>> batchSecurityCheck() {
        return domainService.batchSecurityCheck()
            .map(result -> success((Object) result, "批量安全检测完成"))
            .onErrorResume(e -> Mono.just(handleException((Exception) e, "批量安全检测")));
    }

    //获取域名统计信息
    @GetMapping("/statistics")
    public Mono<ResponseEntity<ApiResponse<Object>>> getDomainStatistics() {
        return domainService.getDomainStatistics()
            .map(statistics -> success((Object) statistics, "获取域名统计成功"))
            .onErrorResume(e -> Mono.just(handleException((Exception) e, "获取域名统计")));
    }

    //获取安全检测历史
    @GetMapping("/{id}/security-history")
    public Mono<ResponseEntity<ApiResponse<Object>>> getSecurityHistory(@PathVariable Long id) {
        return domainService.getSecurityHistory(id)
            .map(history -> success((Object) history, "获取安全检测历史成功"))
            .onErrorResume(e -> Mono.just(handleException((Exception) e, "获取安全检测历史")));
    }

    //手动触发域名安全检查
    @PostMapping("/security/check/{id}")
    public ResponseEntity<ApiResponse<Map<String, Object>>> checkDomainSecurity(@PathVariable Long id) {
        try {
            ResponseEntity<ApiResponse<Map<String, Object>>> validation = validateId(id, "域名ID");
            if (validation != null) return validation;

            Map<String, Object> result = securityCheckService.checkDomainSecurityAsync(domainService.getDomainById(id).block().getDomainName()).get();
            return success(result, "域名安全检查完成");
        } catch (Exception e) {
            return handleException(e, "域名安全检查");
        }
    }

    //批量触发安全检查
    @PostMapping("/security/check-all")
    public ResponseEntity<ApiResponse<String>> checkAllDomainsSecurity() {
        try {
            securityCheckService.scheduledSecurityCheck();
            return success("批量安全检查已启动");
        } catch (Exception e) {
            return handleException(e, "批量安全检查");
        }
    }

    //获取域名安全历史记录
    @GetMapping("/{id}/security/history")
    public Mono<ResponseEntity<ApiResponse<List<Map<String, Object>>>>> getDomainSecurityHistory(@PathVariable Long id) {
        ResponseEntity<ApiResponse<List<Map<String, Object>>>> validation = validateId(id, "域名ID");
        if (validation != null) return Mono.just(validation);

        return domainService.getSecurityHistory(id)
            .map(history -> success(history, "获取域名安全历史成功"))
            .onErrorResume(e -> Mono.just(handleException((Exception) e, "获取域名安全历史")));
    }
}
