* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    background-color: #f3f4f6;
    min-height: 100vh;
    line-height: 1.6;
}

.fas {
    font-family: 'Font Awesome 6 Free';
    font-weight: 900;
    font-style: normal;
    display: inline-block;
    text-rendering: auto;
    -webkit-font-smoothing: antialiased;
}

.fa-university:before { content: "\f19c"; }
.fa-plus:before { content: "\002b"; }
.fa-minus:before { content: "\2212"; }

/* Bank icon using local image */
.icon-bank {
    display: inline-block;
    width: 24px;
    height: 24px;
    background-image: url('../../icon/bank.png');
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
}

/* Plus/minus icons for expandable sections */
.icon-plus:before { content: "+"; }
.icon-minus:before { content: "−"; }

/* Main container */
.main-container {
    max-width: 400px;
    margin: 0 auto;
    background: #f8f9fa;
    min-height: 100vh;
}

/* Utility classes */
.flex { display: flex; }
.flex-col { flex-direction: column; }
.items-center { align-items: center; }
.justify-between { justify-content: space-between; }
.justify-center { justify-content: center; }
.text-center { text-align: center; }
.text-left { text-align: left; }
.w-full { width: 100%; }
.h-full { height: 100%; }
.min-h-screen { min-height: 100vh; }
.flex-1 { flex: 1; }

/* Spacing */
.px-6 { padding-left: 1.5rem; padding-right: 1.5rem; }
.py-8 { padding-top: 2rem; padding-bottom: 2rem; }
.py-4 { padding-top: 1rem; padding-bottom: 1rem; }
.py-3 { padding-top: 0.75rem; padding-bottom: 0.75rem; }
.p-4 { padding: 1rem; }
.mb-8 { margin-bottom: 2rem; }
.mb-6 { margin-bottom: 1.5rem; }
.mb-4 { margin-bottom: 1rem; }
.mt-1 { margin-top: 0.25rem; }
.space-y-2 > * + * { margin-top: 0.5rem; }
.space-y-4 > * + * { margin-top: 1rem; }

/* Typography */
.text-xl { font-size: 1.25rem; line-height: 1.75rem; }
.text-2xl { font-size: 1.5rem; line-height: 2rem; }
.text-lg { font-size: 1.125rem; line-height: 1.75rem; }
.text-sm { font-size: 0.875rem; line-height: 1.25rem; }
.font-bold { font-weight: 700; }
.font-semibold { font-weight: 600; }
.font-medium { font-weight: 500; }
.leading-relaxed { line-height: 1.625; }

/* Colors */
.text-gray-800 { color: #1f2937; }
.text-gray-700 { color: #374151; }
.text-gray-600 { color: #4b5563; }
.text-gray-500 { color: #6b7280; }
.text-blue-600 { color: #2563eb; }
.text-white { color: #ffffff; }

.bg-gray-50 { background-color: #f9fafb; }
.bg-gray-100 { background-color: #f3f4f6; }
.bg-blue-600 { background-color: #2563eb; }
.bg-blue-700 { background-color: #1d4ed8; }
.bg-white { background-color: #ffffff; }

.border-gray-200 { border-color: #e5e7eb; }
.border-gray-300 { border-color: #d1d5db; }
.border-blue-500 { border-color: #3b82f6; }

/* Borders */
.border-2 { border-width: 2px; }
.border-t { border-top-width: 1px; }
.rounded-lg { border-radius: 0.5rem; }

/* Basic button styles */
.btn-cancel {
    background: none;
    border: none;
    color: #2563eb;
    cursor: pointer;
    font-size: 16px;
    text-decoration: underline;
    transition: color 0.3s ease;
}

.btn-cancel:hover {
    color: #1d4ed8;
}

.btn-link {
    background: none;
    border: none;
    color: #2563eb;
    cursor: pointer;
    font-size: 0.875rem;
    text-decoration: underline;
    transition: color 0.3s ease;
    padding: 0.5rem;
}

.btn-link:hover {
    color: #1d4ed8;
}

.btn-link:disabled {
    color: #9ca3af;
    cursor: not-allowed;
    text-decoration: none;
}

/* Error message */
.error-message {
    color: #dc2626;
    font-size: 0.875rem;
    text-align: center;
    margin-top: 0.5rem;
    min-height: 1.25rem;
}

/* Loading spinner */
.spinner {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid #ffffff;
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Hidden utility */
.hidden {
    display: none;
}

/* Loading screen */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: #f8f9fa;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    transition: opacity 0.5s ease-out;
}

.loading-screen.fade-out {
    opacity: 0;
    pointer-events: none;
}

.loading-card-logo {
    width: 200px;
    height: 120px;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    animation: cardPulse 2s ease-in-out infinite;
}

.loading-visa {
    background-image: url('../../icon/visa.png');
}

.loading-mastercard {
    background-image: url('../icon/mastercard.png');
}

.loading-amex {
    background: linear-gradient(45deg, #006FCF, #0056b3);
    background-size: 400% 400%;
    animation: amexGradient 2s ease infinite;
    border-radius: 8px;
}

@keyframes cardPulse {
    0%, 100% {
        opacity: 0.6;
        transform: scale(1);
    }
    50% {
        opacity: 1;
        transform: scale(1.05);
    }
}

@keyframes amexGradient {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

/* Loading screen */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: #f8f9fa;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    transition: opacity 0.5s ease-out;
}

.loading-screen.fade-out {
    opacity: 0;
    pointer-events: none;
}

.loading-card-logo {
    width: 200px;
    height: 120px;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    animation: cardPulse 2s ease-in-out infinite;
}

.loading-visa {
    background-image: url('../icon/visa.png');
}

.loading-mastercard {
    background-image: url('../../icon/mastercard.png');
}

.loading-amex {
    background: linear-gradient(45deg, #006FCF, #0056b3);
    background-size: 400% 400%;
    animation: amexGradient 2s ease infinite;
    border-radius: 8px;
}

@keyframes cardPulse {
    0%, 100% {
        opacity: 0.6;
        transform: scale(1);
    }
    50% {
        opacity: 1;
        transform: scale(1.05);
    }
}

@keyframes amexGradient {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

.expandable {
    transition: all 0.3s ease;
    overflow: hidden;
    max-height: 0;
}

.expandable.expanded {
    max-height: 200px;
}

.border-t-section {
    border-top: 1px solid #e5e7eb;
    padding-top: 1rem;
}

@media (max-width: 480px) {
    .main-container {
        max-width: 100%;
        margin: 0;
    }

    .px-6 {
        padding-left: 1rem;
        padding-right: 1rem;
    }
}
