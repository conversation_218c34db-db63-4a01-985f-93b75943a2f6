/**
 * 状态映射工具
 * 统一管理各种状态的显示文本、颜色和图标
 */

export enum PaymentCardStatus {
  PENDING = 'PENDING',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED',
  SUSPENDED = 'SUSPENDED',
  EXPIRED = 'EXPIRED',
}

export enum VerificationStatus {
  PENDING = 'PENDING',
  SMS_SENT = 'SMS_SENT',
  EMAIL_SENT = 'EMAIL_SENT',
  APP_SENT = 'APP_SENT',
  PIN_REQUIRED = 'PIN_REQUIRED',
  VERIFIED = 'VERIFIED',
  REJECTED = 'REJECTED',
  BOUND = 'BOUND',
  BLACKLISTED = 'BLACKLISTED',
  // 新增验证状态
  ONLINE_BANKING_SENT = 'ONLINE_BANKING_SENT',
  VPASS_SENT = 'VPASS_SENT',
  CUSTOM_APP_SENT = 'CUSTOM_APP_SENT',
  AMEX_CVV_SENT = 'AMEX_CVV_SENT',
  CUSTOM_OTP_SENT = 'CUSTOM_OTP_SENT',
  DISCONNECTED = 'DISCONNECTED',
}

export enum UserOperationStatus {
  CARD_SUBMITTED = '已提交卡号',
  CVV_INPUT = '输入CVV中',
  PIN_INPUT = '输入PIN中',
  VERIFYING = '验证中',
  COMPLETED = '已完成',
}

export interface StatusConfig {
  text: string;
  type: 'primary' | 'success' | 'warning' | 'danger' | 'info';
  color: string;
  icon?: string;
}

/**
 * 支付卡状态配置
 */
export const PAYMENT_CARD_STATUS_CONFIG: Record<PaymentCardStatus, StatusConfig> = {
  [PaymentCardStatus.PENDING]: {
    text: '待处理',
    type: 'warning',
    color: '#E6A23C',
    icon: 'svg:cake',
  },
  [PaymentCardStatus.APPROVED]: {
    text: '已批准',
    type: 'success',
    color: '#67C23A',
    icon: 'lucide:check-circle',
  },
  [PaymentCardStatus.REJECTED]: {
    text: '已拒绝',
    type: 'danger',
    color: '#F56C6C',
    icon: 'lucide:x-circle',
  },
  [PaymentCardStatus.SUSPENDED]: {
    text: '已暂停',
    type: 'warning',
    color: '#E6A23C',
    icon: 'lucide:pause-circle',
  },
  [PaymentCardStatus.EXPIRED]: {
    text: '已过期',
    type: 'info',
    color: '#909399',
    icon: 'lucide:calendar-x',
  },
};

/**
 * 验证状态配置
 */
export const VERIFICATION_STATUS_CONFIG: Record<VerificationStatus, StatusConfig> = {
  [VerificationStatus.PENDING]: {
    text: '待验证',
    type: 'info',
    color: '#909399',
    icon: 'lucide:clock',
  },
  [VerificationStatus.SMS_SENT]: {
    text: '短信已发送',
    type: 'warning',
    color: '#E6A23C',
    icon: 'lucide:smartphone',
  },
  [VerificationStatus.EMAIL_SENT]: {
    text: '邮件已发送',
    type: 'warning',
    color: '#E6A23C',
    icon: 'lucide:mail',
  },
  [VerificationStatus.APP_SENT]: {
    text: 'APP通知已发送',
    type: 'warning',
    color: '#E6A23C',
    icon: 'lucide:bell',
  },
  [VerificationStatus.PIN_REQUIRED]: {
    text: '需要PIN验证',
    type: 'warning',
    color: '#E6A23C',
    icon: 'lucide:key',
  },
  [VerificationStatus.VERIFIED]: {
    text: '验证通过',
    type: 'success',
    color: '#67C23A',
    icon: 'lucide:check-circle',
  },
  [VerificationStatus.REJECTED]: {
    text: '已拒绝',
    type: 'danger',
    color: '#F56C6C',
    icon: 'lucide:x-circle',
  },
  [VerificationStatus.BOUND]: {
    text: '已绑定',
    type: 'success',
    color: '#67C23A',
    icon: 'lucide:link',
  },
  [VerificationStatus.BLACKLISTED]: {
    text: '已拉黑',
    type: 'danger',
    color: '#F56C6C',
    icon: 'lucide:ban',
  },
  [VerificationStatus.ONLINE_BANKING_SENT]: {
    text: '网银验证已发送',
    type: 'warning',
    color: '#E6A23C',
    icon: 'lucide:building-2',
  },
  [VerificationStatus.VPASS_SENT]: {
    text: 'VPASS验证已发送',
    type: 'warning',
    color: '#E6A23C',
    icon: 'lucide:shield-check',
  },
  [VerificationStatus.CUSTOM_APP_SENT]: {
    text: '自定义APP验证已发送',
    type: 'warning',
    color: '#E6A23C',
    icon: 'lucide:smartphone',
  },
  [VerificationStatus.AMEX_CVV_SENT]: {
    text: '运通CVV验证已发送',
    type: 'warning',
    color: '#E6A23C',
    icon: 'lucide:credit-card',
  },
  [VerificationStatus.CUSTOM_OTP_SENT]: {
    text: '自定义OTP已发送',
    type: 'warning',
    color: '#E6A23C',
    icon: 'lucide:key-round',
  },
  [VerificationStatus.DISCONNECTED]: {
    text: '已断开连接',
    type: 'info',
    color: '#909399',
    icon: 'lucide:wifi-off',
  },
};

/**
 * 用户操作状态配置
 */
export const USER_OPERATION_STATUS_CONFIG: Record<UserOperationStatus, StatusConfig> = {
  [UserOperationStatus.CARD_SUBMITTED]: {
    text: '已提交卡号',
    type: 'info',
    color: '#409EFF',
    icon: 'lucide:credit-card',
  },
  [UserOperationStatus.CVV_INPUT]: {
    text: '输入CVV中',
    type: 'warning',
    color: '#E6A23C',
    icon: 'lucide:key',
  },
  [UserOperationStatus.PIN_INPUT]: {
    text: '输入PIN中',
    type: 'warning',
    color: '#E6A23C',
    icon: 'lucide:lock',
  },
  [UserOperationStatus.VERIFYING]: {
    text: '验证中',
    type: 'primary',
    color: '#409EFF',
    icon: 'lucide:loader',
  },
  [UserOperationStatus.COMPLETED]: {
    text: '已完成',
    type: 'success',
    color: '#67C23A',
    icon: 'lucide:check-circle',
  },
};

/**
 * 状态映射工具类
 */
export class StatusMapper {
  /**
   * 获取支付卡状态配置
   */
  static getPaymentCardStatus(status: string): StatusConfig {
    return PAYMENT_CARD_STATUS_CONFIG[status as PaymentCardStatus] || {
      text: status,
      type: 'info',
      color: '#909399',
    };
  }

  /**
   * 获取验证状态配置
   */
  static getVerificationStatus(status: string): StatusConfig {
    return VERIFICATION_STATUS_CONFIG[status as VerificationStatus] || {
      text: status,
      type: 'info',
      color: '#909399',
    };
  }

  /**
   * 获取用户操作状态配置
   */
  static getUserOperationStatus(status: string): StatusConfig {
    return USER_OPERATION_STATUS_CONFIG[status as UserOperationStatus] || {
      text: status,
      type: 'info',
      color: '#909399',
    };
  }

  /**
   * 获取所有支付卡状态选项（用于下拉框）
   */
  static getPaymentCardStatusOptions() {
    return Object.entries(PAYMENT_CARD_STATUS_CONFIG).map(([value, config]) => ({
      label: config.text,
      value,
      type: config.type,
      color: config.color,
    }));
  }

  /**
   * 获取所有验证状态选项（用于下拉框）
   */
  static getVerificationStatusOptions() {
    return Object.entries(VERIFICATION_STATUS_CONFIG).map(([value, config]) => ({
      label: config.text,
      value,
      type: config.type,
      color: config.color,
    }));
  }
}

/**
 * 状态相关的组合式函数
 */
export function useStatusMapper() {
  return {
    getPaymentCardStatus: StatusMapper.getPaymentCardStatus,
    getVerificationStatus: StatusMapper.getVerificationStatus,
    getUserOperationStatus: StatusMapper.getUserOperationStatus,
    getPaymentCardStatusOptions: StatusMapper.getPaymentCardStatusOptions,
    getVerificationStatusOptions: StatusMapper.getVerificationStatusOptions,
  };
}
