package domain.repository;

import domain.entity.SystemSetting;
import org.springframework.data.r2dbc.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * 系统配置仓库接口
 */
@Repository
public interface SystemSettingRepository extends BaseRepository<SystemSetting, Long> {

    /**
     * 根据配置键查找配置
     */
    @Query("SELECT * FROM system_settings WHERE config_key = :configKey")
    Mono<SystemSetting> findByConfigKey(@Param("configKey") String configKey);

    /**
     * 根据分类查找配置
     */
    @Query("SELECT * FROM system_settings WHERE category = :category")
    Flux<SystemSetting> findByCategory(@Param("category") String category);

    /**
     * 根据配置键检查是否存在
     */
    @Query("SELECT COUNT(*) > 0 FROM system_settings WHERE config_key = :configKey")
    Mono<Boolean> existsByConfigKey(@Param("configKey") String configKey);

    /**
     * 更新配置值
     */
    @Query("UPDATE system_settings SET config_value = :configValue, updated_at = NOW() WHERE config_key = :configKey")
    Mono<Integer> updateConfigValue(@Param("configKey") String configKey, @Param("configValue") String configValue);
}
