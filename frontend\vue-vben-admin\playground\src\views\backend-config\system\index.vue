<template>
  <Page
    content-class="flex flex-col gap-4"
    description="系统配置管理"
    title="⚙️ 系统配置"
  >
    <!-- 无人值守配置 -->
    <Card title="🤖 无人值守">
      <template #extra>
        <Button type="primary" :loading="saving" @click="saveConfig">
          保存配置
        </Button>
      </template>
      <p class="text-gray-600 mb-6">无人值守模式配置</p>

      <div class="space-y-4">
        <!-- 同步控制 -->
        <div>
          <label class="block font-medium mb-2">同步控制 (2D/3D验证)</label>
          <div class="relative">
            <select
              v-model="config.unattended.mode"
              class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 appearance-none cursor-pointer"
            >
              <option value="fixed_3d">开启3D验证</option>
              <option value="fixed_unattended">无人值守</option>
            </select>
            <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
              <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
              </svg>
            </div>
          </div>
        </div>




      </div>
    </Card>
  </Page>
</template>

<script setup lang="ts">
import { reactive, ref, onMounted } from 'vue'
import { Page } from '@vben/common-ui'
import { Card, Button, message } from 'ant-design-vue'
import { requestClient } from '#/api/request'

const saving = ref(false)
const loading = ref(false)

// 配置数据
const config = reactive({
  // 无人值守配置
  unattended: {
    mode: 'fixed_3d' as 'fixed_3d' | 'fixed_unattended'
  },
  // 系统配置
  system: {
  },
  // 安全配置
  security: {
  }
})

// 加载配置
const loadConfig = async () => {
  loading.value = true
  try {
    // 加载系统配置
    const systemData = await requestClient.get('/config/system')
    if (systemData) {
      Object.assign(config.system, systemData)
    }

    // 加载安全配置
    const securityData = await requestClient.get('/config/security')
    if (securityData) {
      Object.assign(config.security, securityData)
    }

    // 设置无人值守模式 - 默认为固定3D模式
    config.unattended.mode = 'fixed_3d'

  } catch (error) {
    console.error('加载配置失败:', error)
    message.error('加载配置失败')
  } finally {
    loading.value = false
  }
}

// 保存配置
const saveConfig = async () => {
  saving.value = true
  try {
    // 准备配置数据
    const systemData = {
    }

    const securityData = {
    }

    const featuresData = {
      unattended_mode: config.unattended.mode === 'fixed_unattended'
    }

    // 批量保存配置
    const promises = [
      requestClient.post('/config/system', systemData),
      requestClient.post('/config/security', securityData),
      requestClient.post('/config/features', featuresData)
    ]

    await Promise.all(promises)

    // 响应拦截器已经处理了成功的情况，如果到这里说明都成功了
    message.success('配置保存成功')

  } catch (error) {
    console.error('保存配置失败:', error)
    message.error('保存配置失败')
  } finally {
    saving.value = false
  }
}

// 页面加载时获取配置
onMounted(() => {
  loadConfig()
})
</script>
