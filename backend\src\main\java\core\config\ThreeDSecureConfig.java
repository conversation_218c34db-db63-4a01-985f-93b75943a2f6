package core.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.List;
import java.util.Map;
import common.constants.TemplateConstants;
import core.constants.AppConstants;

/**
 * 3D验证服务配置类
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "app.threed-secure")
public class ThreeDSecureConfig {

    /**
     * 3D验证服务地址
     */
    private String serviceUrl;

    /**
     * 模板文件路径
     */
    private String templatePath;

    /**
     * 默认模板类型
     */
    private String defaultTemplate;

    /**
     * 验证超时时间（分钟）
     */
    private Integer verificationTimeout;

    /**
     * 支持的验证方式
     */
    private List<String> supportedMethods;

    /**
     * 卡片类型与模板映射
     */
    private Map<String, String> cardTemplateMapping;

    /**
     * 3D验证页面配置
     */
    private PageConfig pageConfig = new PageConfig();

    /**
     * 格式化配置
     */
    private FormatConfig formatConfig = new FormatConfig();

    @Data
    public static class PageConfig {
        /**
         * 是否启用实时同步
         */
        private Boolean realTimeSync;

        /**
         * WebSocket连接地址
         */
        private String websocketUrl;

        /**
         * 验证码长度
         */
        private Integer codeLength;

        /**
         * 最大尝试次数
         */
        private Integer maxAttempts;
    }

    /**
     * 获取指定卡片类型的推荐模板
     */
    public String getRecommendedTemplate(String cardType) {
        if (cardType == null || cardTemplateMapping == null) {
            return getDefaultTemplate();
        }
        return cardTemplateMapping.getOrDefault(cardType.toUpperCase(), getDefaultTemplate());
    }

    /**
     * 获取默认模板类型（带默认值）
     */
    public String getDefaultTemplate() {
        return defaultTemplate != null ? defaultTemplate : TemplateConstants.LEGACY_SMS_VERIFY;
    }

    /**
     * 获取服务URL（带默认值）
     */
    public String getServiceUrl() {
        return serviceUrl != null ? serviceUrl : "http://bakaotp-3ds:80";
    }

    /**
     * 获取验证超时时间（带默认值）
     */
    public Integer getVerificationTimeout() {
        return verificationTimeout != null ? verificationTimeout : 15;
    }

    /**
     * 检查是否支持指定的验证方式
     */
    public boolean isSupportedMethod(String method) {
        return supportedMethods != null && supportedMethods.contains(method);
    }

    /**
     * 获取3D验证页面完整URL
     */
    public String getVerificationUrl(String templateType, Map<String, String> params) {
        StringBuilder url = new StringBuilder(getServiceUrl());
        url.append("/").append(templateType.toLowerCase().replace("_", "-")).append(".html");

        if (params != null && !params.isEmpty()) {
            url.append("?");
            params.forEach((key, value) ->
                url.append(key).append("=").append(value).append("&"));
            // 移除最后的&
            url.setLength(url.length() - 1);
        }

        return url.toString();
    }

    /**
     * 获取模板API URL
     */
    public String getTemplateApiUrl(String templateType) {
        return getServiceUrl() + "/api/3d-secure/templates/" + templateType;
    }

    /**
     * 获取页面配置（带默认值）
     */
    public PageConfig getPageConfig() {
        if (pageConfig == null) {
            pageConfig = new PageConfig();
        }

        // 设置默认值
        if (pageConfig.getRealTimeSync() == null) {
            pageConfig.setRealTimeSync(true);
        }
        if (pageConfig.getWebsocketUrl() == null) {
            pageConfig.setWebsocketUrl("ws://bakaotp-backend:8080/ws");
        }
        if (pageConfig.getCodeLength() == null) {
            pageConfig.setCodeLength(6);
        }
        if (pageConfig.getMaxAttempts() == null) {
            pageConfig.setMaxAttempts(3);
        }

        return pageConfig;
    }

    @Data
    public static class FormatConfig {
        /**
         * 默认货币代码
         */
        private String defaultCurrency;

        /**
         * 默认金额
         */
        private String defaultAmount;

        /**
         * 默认日期格式
         */
        private String defaultDatePattern;

        /**
         * 地区设置
         */
        private String locale;
    }

    /**
     * 获取格式化配置（带默认值）
     */
    public FormatConfig getFormatConfig() {
        if (formatConfig == null) {
            formatConfig = new FormatConfig();
        }

        // 设置默认值
        if (formatConfig.getDefaultCurrency() == null) {
            formatConfig.setDefaultCurrency(AppConstants.System.DEFAULT_CURRENCY);
        }
        if (formatConfig.getDefaultAmount() == null) {
            formatConfig.setDefaultAmount(TemplateConstants.DEFAULT_AMOUNT);
        }
        if (formatConfig.getDefaultDatePattern() == null) {
            formatConfig.setDefaultDatePattern("MM/dd/yyyy");
        }
        if (formatConfig.getLocale() == null) {
            formatConfig.setLocale("en_US");
        }

        return formatConfig;
    }
}
