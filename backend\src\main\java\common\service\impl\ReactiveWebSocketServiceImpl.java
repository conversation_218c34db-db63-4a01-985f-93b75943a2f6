package common.service.impl;

import common.service.WebSocketService;
import domain.entity.Order;
import domain.entity.PaymentTransaction;
import domain.entity.Domain;
import common.websocket.ReactivePaymentWebSocketHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

//WebSocket服务实现类

@Service
public class ReactiveWebSocketServiceImpl implements WebSocketService {

    private static final Logger logger = LoggerFactory.getLogger(ReactiveWebSocketServiceImpl.class);

    @Autowired
    private ReactivePaymentWebSocketHandler webSocketHandler;

    @Override
    public void notifyNewOrder(Order order) {
        Map<String, Object> message = new HashMap<>();
        message.put("type", "new-order");
        message.put("data", order);
        message.put("timestamp", LocalDateTime.now().toString());
        
        webSocketHandler.broadcastJson(message);
        logger.info("广播新订单通知: orderId={}", order.getId());
    }

    @Override
    public void notifyOrderStatusChange(Order order) {
        Map<String, Object> message = new HashMap<>();
        message.put("type", "order-status-change");
        message.put("data", order);
        message.put("timestamp", LocalDateTime.now().toString());
        
        webSocketHandler.broadcastJson(message);
        logger.info("广播订单状态变更: orderId={}, status={}", order.getId(), order.getStatus());
    }

    @Override
    public void notifyNewTransaction(PaymentTransaction transaction) {
        Map<String, Object> message = new HashMap<>();
        message.put("type", "new-transaction");
        message.put("data", transaction);
        message.put("timestamp", LocalDateTime.now().toString());
        
        webSocketHandler.broadcastJson(message);
        logger.info("广播新交易通知: transactionId={}", transaction.getId());
    }

    @Override
    public void notifyTransactionStatusChange(PaymentTransaction transaction) {
        Map<String, Object> message = new HashMap<>();
        message.put("type", "transaction-status-change");
        message.put("data", transaction);
        message.put("timestamp", LocalDateTime.now().toString());
        
        webSocketHandler.broadcastJson(message);
        logger.info("广播交易状态变更: transactionId={}, status={}", transaction.getId(), transaction.getStatus());
    }

    @Override
    public void notifyOtpVerification(String email, String otpCode) {
        Map<String, Object> message = new HashMap<>();
        message.put("type", "otp-verification");
        message.put("email", email);
        message.put("otpCode", otpCode);
        message.put("timestamp", LocalDateTime.now().toString());
        
        webSocketHandler.broadcastJson(message);
        logger.info("广播OTP验证码: email={}", email);
    }

    @Override
    public void broadcastMessage(Map<String, Object> message) {
        message.put("timestamp", LocalDateTime.now().toString());
        webSocketHandler.broadcastJson(message);
        logger.info("广播自定义消息: type={}", message.get("type"));
    }

    @Override
    public void broadcastDomainUpdate(Domain domain) {
        Map<String, Object> message = new HashMap<>();
        message.put("type", "domain-update");
        message.put("data", domain);
        message.put("timestamp", LocalDateTime.now().toString());
        
        webSocketHandler.broadcastJson(message);
        logger.info("广播域名更新: domainId={}, domain={}", domain.getId(), domain.getDomainName());
    }

    @Override
    public void broadcastDomainDelete(Long domainId) {
        Map<String, Object> message = new HashMap<>();
        message.put("type", "domain-delete");
        message.put("domainId", domainId);
        message.put("timestamp", LocalDateTime.now().toString());

        webSocketHandler.broadcastJson(message);
        logger.info("广播域名删除: domainId={}", domainId);
    }


}
