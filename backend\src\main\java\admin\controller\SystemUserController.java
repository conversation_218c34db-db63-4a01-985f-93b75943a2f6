package admin.controller;

import system.controller.BaseController;
import domain.entity.User;
import core.common.ApiResponse;
import domain.repository.UserRepository;
import core.util.ReactiveExceptionHandler;
import core.util.ReactiveOperationUtils;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.*;
import java.util.ArrayList;
import java.util.function.Function;
import java.util.stream.Collectors;
import reactor.core.publisher.Mono;

/**
 * 系统用户管理控制器
 */
@RestController
@RequestMapping("/api/system/users")
@PreAuthorize("hasRole('ADMIN')")
public class SystemUserController extends BaseController {

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private PasswordEncoder passwordEncoder;

    // 字段转换器
    private static final Map<String, Function<Object, Object>> USER_FIELD_TRANSFORMERS = Map.of(
        "email", value -> value != null ? value : "" // 默认邮箱
    );

    /**
     * 获取用户列表（分页）
     */
    @GetMapping
    public Mono<ResponseEntity<ApiResponse<Object>>> getUserList(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int pageSize,
            @RequestParam(required = false) String username,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String role) {

        // 使用响应式查询
        Pageable pageable = createPageable(page, pageSize);
        return userRepository.findAll()
            .collectList()
            .map(users -> {
                if (users == null) users = new ArrayList<>();

                // 过滤（如果有查询条件）
                if (username != null || status != null || role != null) {
                    users = users.stream()
                        .filter(user -> username == null || user.getUsername().contains(username))
                        .filter(user -> status == null || user.getStatus().equals(status))
                        .filter(user -> role == null || user.getRole().equals(role))
                        .collect(Collectors.toList());
                }

                // 创建分页响应
                Map<String, Object> result = new HashMap<>();
                result.put("content", users);
                result.put("totalElements", users.size());
                result.put("totalPages", (users.size() + pageSize - 1) / pageSize);
                result.put("size", pageSize);
                result.put("number", page);
                result.put("first", page == 0);
                result.put("last", page >= ((users.size() + pageSize - 1) / pageSize) - 1);

                return success((Object) result);
            })
            .onErrorResume(ReactiveExceptionHandler.handleError("获取用户列表"));
    }

    /**
     * 获取用户详情
     */
    @GetMapping("/{id}")
    public Mono<ResponseEntity<ApiResponse<Object>>> getUserDetail(@PathVariable Long id) {
        return ReactiveOperationUtils.executeWithNotFoundHandling(
            userRepository.findById(id).cast(Object.class),
            "用户不存在",
            "获取用户详情成功"
        ).onErrorResume(ReactiveExceptionHandler.handleError("获取用户详情"));
    }

    /**
     * 添加用户
     */
    @PostMapping
    public Mono<ResponseEntity<ApiResponse<Object>>> addUser(@RequestBody Map<String, Object> request) {
        User user = new User();

        // 手动设置字段
        if (request.containsKey("username")) {
            user.setUsername((String) request.get("username"));
        }
        if (request.containsKey("name")) {
            user.setName((String) request.get("name"));
        }
        // email和phone字段在User实体中不存在，已移除
        if (request.containsKey("role")) {
            user.setRole((String) request.get("role"));
        }
        if (request.containsKey("status")) {
            user.setStatus((String) request.get("status"));
        }

        // 用户不需要密码字段

        // 设置默认值
        if (user.getRole() == null) user.setRole("USER");
        if (user.getStatus() == null) user.setStatus("ACTIVE");

        // 保存到数据库
        return userRepository.save(user)
            .map(savedUser -> success((Object) savedUser, "添加成功"))
            .onErrorResume(ReactiveExceptionHandler.handleError("添加用户"));
    }

    /**
     * 更新用户
     */
    @PutMapping("/{id}")
    public Mono<ResponseEntity<ApiResponse<Object>>> updateUser(@PathVariable Long id, @RequestBody Map<String, Object> request) {
        return userRepository.findById(id)
            .flatMap(user -> {
                // 手动更新字段，排除不可更新的字段
                if (request.containsKey("name")) {
                    user.setName((String) request.get("name"));
                }
                // email和phone字段在User实体中不存在，已移除
                if (request.containsKey("role")) {
                    user.setRole((String) request.get("role"));
                }
                if (request.containsKey("status")) {
                    user.setStatus((String) request.get("status"));
                }

                // 特殊处理密码字段
                if (request.containsKey("password") && request.get("password") != null) {
                    String password = request.get("password").toString();
                    if (!password.trim().isEmpty()) {
                        user.setPassword(passwordEncoder.encode(password));
                    }
                }

                // 保存到数据库
                return userRepository.save(user)
                    .map(updatedUser -> success((Object) updatedUser, "更新成功"));
            })
            .switchIfEmpty(Mono.just(ReactiveOperationUtils.createErrorResponse("用户不存在")))
            .onErrorResume(ReactiveExceptionHandler.handleError("更新用户"));
    }

    /**
     * 删除用户
     */
    @DeleteMapping("/{id}")
    public Mono<ResponseEntity<ApiResponse<Object>>> deleteUser(@PathVariable Long id) {
        return ReactiveOperationUtils.executeWithExistenceCheck(
            userRepository.existsById(id),
            () -> userRepository.deleteById(id)
                .then(Mono.just(ReactiveOperationUtils.createSuccessResponse(null, "删除成功"))),
            "用户不存在"
        ).onErrorResume(ReactiveExceptionHandler.handleError("删除用户"));
    }

    /**
     * 重置密码
     */
    @PostMapping("/{id}/reset-password")
    public Mono<ResponseEntity<ApiResponse<Object>>> resetPassword(@PathVariable String id) {
        try {
            Long userId = Long.parseLong(id);
            return userRepository.findById(userId)
                .flatMap(user -> {
                    String newPassword = "123456"; // 默认密码
                    user.setPassword(passwordEncoder.encode(newPassword));
                    user.setUpdateTime(LocalDateTime.now());

                    return userRepository.save(user)
                        .map(savedUser -> {
                            Map<String, Object> result = new HashMap<>();
                            result.put("newPassword", newPassword);
                            result.put("message", "密码重置成功");
                            return success((Object) result, "密码重置成功");
                        });
                })
                .switchIfEmpty(Mono.just(error("用户不存在")))
                .onErrorResume(Exception.class, e -> {
                    logger.error("重置密码失败", e);
                    return Mono.just(handleException(e, "重置密码"));
                });
        } catch (NumberFormatException e) {
            logger.error("用户ID格式错误: {}", id, e);
            return Mono.just(error("用户ID格式错误"));
        }
    }

    /**
     * 获取用户统计信息
     */
    @GetMapping("/stats")
    public Mono<ResponseEntity<ApiResponse<Object>>> getUserStats() {
        return userRepository.findAll()
            .collectList()
            .map(allUsers -> {
                if (allUsers == null) allUsers = new ArrayList<>();
                List<User> adminUsers = allUsers.stream().filter(u -> "ADMIN".equals(u.getRole())).collect(Collectors.toList());

                Map<String, Object> stats = new HashMap<>();
                // 总体统计
                stats.put("totalUsers", allUsers.size());
                stats.put("activeUsers", allUsers.stream().filter(u -> "ACTIVE".equals(u.getStatus())).count());
                stats.put("inactiveUsers", allUsers.stream().filter(u -> "INACTIVE".equals(u.getStatus())).count());
                stats.put("adminUsers", adminUsers.size());
                stats.put("normalUsers", allUsers.stream().filter(u -> "USER".equals(u.getRole())).count());

                // 管理员专用统计
                stats.put("totalAdmins", adminUsers.size());
                stats.put("activeAdmins", adminUsers.stream().filter(u -> "ACTIVE".equals(u.getStatus())).count());
                stats.put("inactiveAdmins", adminUsers.stream().filter(u -> "INACTIVE".equals(u.getStatus())).count());
                stats.put("superAdmins", adminUsers.stream().filter(u -> "SUPER_ADMIN".equals(u.getRole())).count());
                stats.put("normalAdmins", adminUsers.stream().filter(u -> "ADMIN".equals(u.getRole())).count());

                return success((Object) stats);
            })
            .onErrorResume(Exception.class, e -> {
                logger.error("获取用户统计失败", e);
                return Mono.just(handleException(e, "获取用户统计"));
            });
    }


}
