package domain.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.annotation.Id;
import org.springframework.data.relational.core.mapping.Column;
import org.springframework.data.relational.core.mapping.Table;

/**
 * 系统配置实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Table("system_settings")
public class SystemSetting extends BaseEntity {

    @Id
    private Long id;

    /**
     * 配置键
     */
    @Column("config_key")
    private String configKey;

    /**
     * 配置值
     */
    @Column("config_value")
    private String configValue;

    /**
     * 配置类型
     */
    @Column("config_type")
    private ConfigType configType = ConfigType.STRING;

    /**
     * 配置描述
     */
    @Column("description")
    private String description;

    /**
     * 配置分类
     */
    @Column("category")
    private String category = "GENERAL";

    /**
     * 配置类型枚举
     */
    public enum ConfigType {
        STRING, BOOLEAN, INTEGER, JSON
    }

    /**
     * 获取布尔值
     */
    public Boolean getBooleanValue() {
        if (configType == ConfigType.BOOLEAN) {
            return Boolean.parseBoolean(configValue);
        }
        return null;
    }

    /**
     * 获取整数值
     */
    public Integer getIntegerValue() {
        if (configType == ConfigType.INTEGER) {
            try {
                return Integer.parseInt(configValue);
            } catch (NumberFormatException e) {
                return null;
            }
        }
        return null;
    }

    /**
     * 设置布尔值
     */
    public void setBooleanValue(Boolean value) {
        this.configType = ConfigType.BOOLEAN;
        this.configValue = value != null ? value.toString() : "false";
    }

    /**
     * 设置整数值
     */
    public void setIntegerValue(Integer value) {
        this.configType = ConfigType.INTEGER;
        this.configValue = value != null ? value.toString() : "0";
    }
}
