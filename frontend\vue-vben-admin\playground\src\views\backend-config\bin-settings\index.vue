<template>
  <Page
    content-class="flex flex-col gap-4"
    description="卡头管理配置"
    title="💳 卡头设置"
  >
    <div class="flex flex-col gap-6">
      <!-- 自动拒绝的卡头 -->
      <Card title="自动拒绝的卡头">
        <template #extra>
          <span class="px-2 py-1 bg-destructive/10 text-destructive rounded text-xs">风控配置</span>
        </template>



        <!-- 卡头输入区域 -->
        <div class="bg-muted rounded-lg p-4 mb-4">
          <div class="flex items-center justify-between mb-2">
            <label class="block text-muted-foreground text-sm">输入卡头前缀，多个用|分隔</label>
            <span class="text-xs text-muted-foreground bg-muted-foreground/10 px-2 py-1 rounded">
              拒绝的卡头会显示红色
            </span>
          </div>
          <textarea
            v-model="rejectPrefixes"
            class="w-full bg-background border border-border rounded-lg p-3 h-32 resize-none text-foreground focus:outline-none focus:ring-2 focus:ring-primary"
            placeholder="例如：440393|491637|544064"
          ></textarea>
        </div>

        <!-- 操作按钮 -->
        <div class="flex gap-3">
          <VbenButton
            @click="saveRejectPrefixes"
            :loading="saving"
            variant="primary"
          >
            保存
          </VbenButton>
          <VbenButton
            @click="resetRejectPrefixes"
            variant="outline"
          >
            重置
          </VbenButton>
        </div>
      </Card>

      <!-- 卡头管理表格 -->
      <Card title="卡头管理">
        <template #extra>
          <div class="flex items-center gap-3">
            <select
              v-model="pageSize"
              class="bg-background border border-border rounded px-3 py-1 text-sm text-foreground"
            >
              <option value="10">10</option>
              <option value="20">20</option>
              <option value="50">50</option>
            </select>
            <div class="flex gap-2">
              <VbenButton
                @click="showAddNoteModal = true"
                variant="primary"
                size="sm"
              >
                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"/>
                </svg>
                新增卡头
              </VbenButton>
              <VbenButton
                @click="showBatchImportModal = true"
                variant="outline"
                size="sm"
              >
                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"/>
                </svg>
                批量导入
              </VbenButton>
              <VbenButton
                @click="exportNotes"
                variant="outline"
                size="sm"
              >
                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"/>
                </svg>
                导出
              </VbenButton>
              <VbenButton
                @click="confirmClearAll"
                variant="destructive"
                size="sm"
              >
                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                </svg>
                清空全部
              </VbenButton>
            </div>
          </div>
        </template>

        <!-- 搜索框 -->
        <div class="mb-4">
          <input
            v-model="searchQuery"
            type="text"
            placeholder="Search BIN"
            class="w-full bg-background border border-border rounded-lg px-3 py-2 text-foreground focus:outline-none focus:ring-2 focus:ring-primary"
          />
        </div>



        <!-- 表格 -->
        <div class="bg-card border border-border rounded-lg overflow-hidden">
          <table class="w-full">
            <thead class="bg-muted">
              <tr>
                <th class="text-left p-3 text-muted-foreground text-sm">卡头</th>
                <th class="text-left p-3 text-muted-foreground text-sm">备注</th>
                <th class="text-left p-3 text-muted-foreground text-sm">国家</th>
                <th class="text-left p-3 text-muted-foreground text-sm">创建时间</th>
                <th class="text-left p-3 text-muted-foreground text-sm">更新时间</th>
                <th class="text-left p-3 text-muted-foreground text-sm">操作</th>
              </tr>
            </thead>
            <tbody>
              <tr
                v-if="paginatedNotes.length === 0"
                class="border-b border-border"
              >
                <td colspan="6" class="p-12 text-center">
                  <div class="flex flex-col items-center gap-4">
                    <svg class="w-16 h-16 text-muted-foreground/50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                    </svg>
                    <div class="text-center">
                      <h3 class="text-lg font-medium text-muted-foreground mb-2">暂无卡头数据</h3>
                      <p class="text-sm text-muted-foreground mb-4">开始添加您的第一个卡头备注</p>
                      <VbenButton
                        @click="showAddNoteModal = true"
                        variant="primary"
                        size="sm"
                      >
                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"/>
                        </svg>
                        新增卡头
                      </VbenButton>
                    </div>
                  </div>
                </td>
              </tr>
              <tr
                v-for="note in paginatedNotes"
                v-else
                :key="note.id"
                class="border-b border-border hover:bg-muted/50"
              >
                <td class="p-3 text-foreground font-mono">{{ note.prefix }}</td>
                <td class="p-3 text-foreground">{{ note.note }}</td>
                <td class="p-3 text-foreground">{{ note.country }}</td>
                <td class="p-3 text-muted-foreground text-sm">{{ note.createdAt }}</td>
                <td class="p-3 text-muted-foreground text-sm">{{ note.updatedAt }}</td>
                <td class="p-3">
                  <div class="flex gap-1">
                    <VbenButton
                      @click="editNote(note)"
                      variant="ghost"
                      size="sm"
                      title="编辑"
                    >
                      <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
                      </svg>
                    </VbenButton>
                    <VbenButton
                      @click="deleteNote(note)"
                      variant="ghost"
                      size="sm"
                      title="删除"
                    >
                      <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                      </svg>
                    </VbenButton>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>

          <!-- 分页 -->
          <div class="bg-muted px-4 py-3 flex justify-between items-center">
            <div class="text-muted-foreground text-sm">
              Showing {{ startIndex }} to {{ endIndex }} of {{ filteredNotes.length }} entries
            </div>
            <div class="flex gap-2">
              <VbenButton
                @click="prevPage"
                :disabled="currentPage === 1"
                variant="outline"
                size="sm"
              >
                上一页
              </VbenButton>
              <span class="px-3 py-1 bg-primary text-primary-foreground rounded">
                {{ currentPage }}
              </span>
              <VbenButton
                @click="nextPage"
                :disabled="currentPage === totalPages"
                variant="outline"
                size="sm"
              >
                下一页
              </VbenButton>
            </div>
          </div>
        </div>
      </Card>
    </div>

    <!-- 新增卡头模态框 -->
    <div v-if="showAddNoteModal" class="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <Card class="w-96">
        <template #header>
          <h3 class="text-lg font-semibold">新增卡头备注</h3>
        </template>

        <AddForm />

        <div class="flex gap-3 mt-6">
          <VbenButton
            @click="addFormApi.submitForm()"
            variant="primary"
          >
            确认添加
          </VbenButton>
          <VbenButton
            @click="cancelAdd"
            variant="outline"
          >
            取消
          </VbenButton>
        </div>
      </Card>
    </div>

    <!-- 批量导入模态框 -->
    <div v-if="showBatchImportModal" class="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <Card class="w-96">
        <template #header>
          <h3 class="text-lg font-semibold">批量导入卡头</h3>
        </template>

        <div class="space-y-4">
          <div>
            <label class="block text-sm font-medium mb-2">批量数据</label>
            <textarea
              v-model="batchImportData"
              class="w-full bg-background border border-border rounded px-3 py-2 h-32 resize-none text-foreground focus:outline-none focus:ring-2 focus:ring-primary"
              placeholder="每行一个卡头，格式：卡头|备注|国家|颜色"
            ></textarea>
            <div class="text-muted-foreground text-xs mt-1">
              示例：440393|工商银行|中国|红色
            </div>
          </div>
        </div>

        <div class="flex gap-3 mt-6">
          <VbenButton
            @click="batchImport"
            variant="primary"
          >
            确认导入
          </VbenButton>
          <VbenButton
            @click="showBatchImportModal = false"
            variant="outline"
          >
            取消
          </VbenButton>
        </div>
      </Card>
    </div>

    <!-- 编辑卡头模态框 -->
    <div v-if="showEditNoteModal" class="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <Card class="w-96">
        <template #header>
          <h3 class="text-lg font-semibold">编辑卡头备注</h3>
        </template>

        <EditForm />

        <div class="flex gap-3 mt-6">
          <VbenButton
            @click="editFormApi.submitForm()"
            variant="primary"
          >
            确认修改
          </VbenButton>
          <VbenButton
            @click="cancelEdit"
            variant="outline"
          >
            取消
          </VbenButton>
        </div>
      </Card>
    </div>
  </Page>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { Page, VbenButton } from '@vben/common-ui'
import { Card } from 'ant-design-vue'
import { useVbenForm } from '#/adapter/form'

// 拒绝卡头数据
const rejectPrefixes = ref('')
const saving = ref(false)

// 卡头备注数据
interface BinNote {
  id: number
  prefix: string
  note: string
  country: string
  createdAt: string
  updatedAt: string
}

const noteList = ref<BinNote[]>([])

// 搜索和分页
const searchQuery = ref('')
const currentPage = ref(1)
const pageSize = ref(10)
const showAddNoteModal = ref(false)
const showBatchImportModal = ref(false)
const showEditNoteModal = ref(false)



// 编辑中的备注
const editingNote = ref<BinNote | null>(null)

// 批量导入数据
const batchImportData = ref('')

// 编辑表单配置
const [EditForm, editFormApi] = useVbenForm({
  commonConfig: {
    componentProps: {
      class: 'w-full',
    },
  },
  layout: 'vertical',
  schema: [
    {
      component: 'VbenInput',
      fieldName: 'prefix',
      label: '卡头前缀',
      rules: 'required',
      componentProps: {
        placeholder: '例如：440393',
      },
    },
    {
      component: 'VbenInput',
      fieldName: 'note',
      label: '备注',
      componentProps: {
        placeholder: '输入备注信息',
      },
    },
    {
      component: 'VbenInput',
      fieldName: 'country',
      label: '国家',
      componentProps: {
        placeholder: '国家名称',
      },
    },
  ],
  handleSubmit: async (values) => {
    if (editingNote.value) {
      // 更新现有记录
      const index = noteList.value.findIndex(n => n.id === editingNote.value!.id)
      if (index > -1) {
        noteList.value[index] = {
          ...editingNote.value,
          ...values,
          updatedAt: new Date().toLocaleString('zh-CN')
        }
      }
      showEditNoteModal.value = false
      editingNote.value = null
    }
  },
})

// 新增表单配置
const [AddForm, addFormApi] = useVbenForm({
  commonConfig: {
    componentProps: {
      class: 'w-full',
    },
  },
  layout: 'vertical',
  schema: [
    {
      component: 'VbenInput',
      fieldName: 'prefix',
      label: '卡头前缀',
      rules: 'required',
      componentProps: {
        placeholder: '例如：440393',
      },
    },
    {
      component: 'VbenInput',
      fieldName: 'note',
      label: '备注',
      componentProps: {
        placeholder: '输入备注信息',
      },
    },
    {
      component: 'VbenInput',
      fieldName: 'country',
      label: '国家',
      componentProps: {
        placeholder: '国家名称',
      },
    },
  ],
  handleSubmit: async (values) => {
    const note: BinNote = {
      id: Date.now(),
      prefix: values.prefix.trim(),
      note: values.note.trim(),
      country: values.country.trim(),
      createdAt: new Date().toLocaleString('zh-CN'),
      updatedAt: new Date().toLocaleString('zh-CN')
    }

    noteList.value.unshift(note)
    showAddNoteModal.value = false
    addFormApi.resetForm()
  },
})

// 计算属性
const filteredNotes = computed(() => {
  if (!searchQuery.value) return noteList.value
  return noteList.value.filter(note => 
    note.prefix.includes(searchQuery.value) ||
    note.note.includes(searchQuery.value)
  )
})

const totalPages = computed(() => {
  return Math.ceil(filteredNotes.value.length / pageSize.value)
})

const paginatedNotes = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return filteredNotes.value.slice(start, end)
})

const startIndex = computed(() => {
  return (currentPage.value - 1) * pageSize.value + 1
})

const endIndex = computed(() => {
  return Math.min(currentPage.value * pageSize.value, filteredNotes.value.length)
})

// 方法
const saveRejectPrefixes = async () => {
  saving.value = true
  try {
    // 模拟保存
    await new Promise(resolve => setTimeout(resolve, 1000))
    console.log('保存成功:', rejectPrefixes.value)
  } finally {
    saving.value = false
  }
}

const resetRejectPrefixes = () => {
  rejectPrefixes.value = ''
}

const editNote = (note: BinNote) => {
  editingNote.value = note
  editFormApi.setValues({
    prefix: note.prefix,
    note: note.note,
    country: note.country,
  })
  showEditNoteModal.value = true
}

const cancelEdit = () => {
  showEditNoteModal.value = false
  editingNote.value = null
  editFormApi.resetForm()
}

const cancelAdd = () => {
  showAddNoteModal.value = false
  addFormApi.resetForm()
}

const deleteNote = (note: BinNote) => {
  if (confirm(`确定要删除卡头 ${note.prefix} 的备注吗？`)) {
    const index = noteList.value.findIndex(n => n.id === note.id)
    if (index > -1) {
      noteList.value.splice(index, 1)
    }
  }
}

const confirmClearAll = () => {
  if (confirm('确定要清空所有卡头备注吗？此操作不可恢复！')) {
    noteList.value = []
  }
}



const exportNotes = () => {
  console.log('导出备注')
}

const prevPage = () => {
  if (currentPage.value > 1) {
    currentPage.value--
  }
}

const nextPage = () => {
  if (currentPage.value < totalPages.value) {
    currentPage.value++
  }
}



const batchImport = () => {
  if (!batchImportData.value.trim()) {
    alert('请输入批量数据')
    return
  }

  const lines = batchImportData.value.trim().split('\n')
  let addedCount = 0

  for (const line of lines) {
    const parts = line.trim().split('|')
    if (parts.length >= 1 && parts[0]) {
      const note: BinNote = {
        id: Date.now() + addedCount,
        prefix: parts[0].trim(),
        note: parts[1]?.trim() || '',
        country: parts[2]?.trim() || '',
        createdAt: new Date().toLocaleString('zh-CN'),
        updatedAt: new Date().toLocaleString('zh-CN')
      }

      noteList.value.unshift(note)
      addedCount++
    }
  }

  batchImportData.value = ''
  showBatchImportModal.value = false

  alert(`成功导入 ${addedCount} 条记录`)
}
</script>
