package domain.repository;

import domain.entity.PaymentUser;
import org.springframework.data.r2dbc.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 支付用户数据访问层 - 响应式版本
 */
@Repository
public interface PaymentUserRepository extends BaseRepository<PaymentUser, Long> {

    /**
     * 根据邮箱查找支付用户
     */
    Mono<PaymentUser> findByEmail(String email);

    /**
     * 根据手机号查找支付用户
     */
    Mono<PaymentUser> findByPhone(String phone);

    /**
     * 根据状态查找支付用户
     */
    Flux<PaymentUser> findByStatus(String status);

    /**
     * 统计指定状态的支付用户数量
     */
    Mono<Long> countByStatus(String status);

    /**
     * 根据邮箱和手机号查找支付用户（用于检查重复）
     */
    @Query("SELECT * FROM payment_users WHERE email = :email OR phone = :phone")
    Flux<PaymentUser> findByEmailOrPhone(@Param("email") String email, @Param("phone") String phone);

    /**
     * 根据多个条件查找支付用户
     */
    @Query("SELECT * FROM payment_users WHERE " +
           "(:email IS NULL OR email LIKE CONCAT('%', :email, '%')) AND " +
           "(:name IS NULL OR name LIKE CONCAT('%', :name, '%')) AND " +
           "(:phone IS NULL OR phone LIKE CONCAT('%', :phone, '%')) AND " +
           "(:status IS NULL OR status = :status)")
    Flux<PaymentUser> findByConditions(@Param("email") String email,
                                     @Param("name") String name,
                                     @Param("phone") String phone,
                                     @Param("status") String status);

    /**
     * 查找今日注册的支付用户
     */
    @Query("SELECT * FROM payment_users WHERE DATE(created_at) = CURRENT_DATE")
    Flux<PaymentUser> findTodayRegistered();

    /**
     * 统计今日注册的支付用户数量
     */
    @Query("SELECT COUNT(*) FROM payment_users WHERE DATE(created_at) = CURRENT_DATE")
    Mono<Long> countTodayRegistered();

    /**
     * 查找最近活跃的支付用户
     */
    @Query("SELECT * FROM payment_users ORDER BY updated_at DESC")
    Flux<PaymentUser> findRecentlyActive();

    /**
     * 查找指定时间之后更新的支付用户
     */
    @Query("SELECT * FROM payment_users WHERE updated_at > :since ORDER BY updated_at DESC")
    Flux<PaymentUser> findUpdatedSince(@Param("since") LocalDateTime since);

    /**
     * 查找指定时间之前创建的支付用户（用于清理过期数据）
     */
    @Query("SELECT * FROM payment_users WHERE created_at < :expireTime")
    Flux<PaymentUser> findExpiredByCreatedAt(@Param("expireTime") LocalDateTime expireTime);

    /**
     * 检查邮箱是否已存在
     */
    Mono<Boolean> existsByEmail(String email);

    /**
     * 检查手机号是否已存在
     */
    Mono<Boolean> existsByPhone(String phone);

    /**
     * 根据创建时间范围查找支付用户
     */
    Flux<PaymentUser> findByCreatedAtBetween(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 统计指定时间范围内创建的支付用户数量
     */
    @Query("SELECT COUNT(*) FROM payment_users WHERE created_at BETWEEN :startTime AND :endTime")
    Mono<Long> countByCreatedAtBetween(@Param("startTime") LocalDateTime startTime,
                               @Param("endTime") LocalDateTime endTime);

    /**
     * 按创建时间倒序查询（最新的在前）
     */
    @Query("SELECT u FROM PaymentUser u ORDER BY u.createdAt DESC")
    Flux<PaymentUser> findAllOrderByCreatedAtDesc();

    /**
     * 删除指定时间之前的支付用户
     */
    void deleteByCreatedAtBefore(LocalDateTime expireTime);
}
