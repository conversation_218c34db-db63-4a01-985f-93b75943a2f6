package external.service;

import core.common.ApiResponse;
import core.util.ValidationUtil;
import external.client.IPRegistryApiClient;
import external.config.IPDetectionConfig;
import external.dto.IPRegistryResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.util.HashMap;
import java.util.Map;

/**
 * IP检测服务
 * 提供IP地址类型检测和威胁分析功能
 */
@Slf4j
@Service
public class IPDetectionService {

    @Autowired
    private IPRegistryApiClient ipRegistryApiClient;

    @Autowired
    private IPDetectionConfig ipDetectionConfig;

    /**
     * 检测IP地址
     *
     * @param ip IP地址
     * @return 检测结果
     */
    @Cacheable(value = "ipDetection", key = "#ip", condition = "#ip != null")
    public Mono<Map<String, Object>> detectIP(String ip) {
        if (!ipDetectionConfig.isEnabled()) {
            log.debug("IP检测功能未启用");
            return Mono.just(createDefaultResult(ip, "IP检测功能未启用"));
        }

        if (!ValidationUtil.isValidIP(ip)) {
            log.warn("无效的IP地址: {}", ip);
            return Mono.just(createErrorResult(ip, "无效的IP地址"));
        }

        log.info("开始检测IP地址: {}", ip);

        return ipRegistryApiClient.queryIP(ip)
                .map(response -> {
                    if (response.isSuccess()) {
                        return processDetectionResult(ip, response.getData());
                    } else {
                        log.warn("IP检测失败: {} - {}", ip, response.getMessage());
                        return createErrorResult(ip, response.getMessage());
                    }
                })
                .onErrorResume(Exception.class, e -> {
                    log.error("IP检测异常: {} - {}", ip, e.getMessage(), e);
                    return Mono.just(createErrorResult(ip, "检测异常: " + e.getMessage()));
                });
    }

    /**
     * 批量检测IP地址
     *
     * @param ips IP地址列表
     * @return 批量检测结果
     */
    public Mono<Map<String, Map<String, Object>>> batchDetectIP(String[] ips) {
        if (!ipDetectionConfig.isEnabled()) {
            log.debug("IP检测功能未启用");
            return Mono.just(new HashMap<>());
        }

        if (ips == null || ips.length == 0) {
            log.warn("IP地址列表为空");
            return Mono.just(new HashMap<>());
        }

        log.info("开始批量检测IP地址，数量: {}", ips.length);

        return ipRegistryApiClient.batchQueryIP(ips)
                .map(response -> {
                    Map<String, Map<String, Object>> results = new HashMap<>();
                    
                    if (response.isSuccess() && response.getData() != null) {
                        IPRegistryResult[] responses = response.getData();
                        for (IPRegistryResult ipResponse : responses) {
                            String ip = ipResponse.getIp();
                            results.put(ip, processDetectionResult(ip, ipResponse));
                        }
                    } else {
                        log.warn("批量IP检测失败: {}", response.getMessage());
                        // 为每个IP创建错误结果
                        for (String ip : ips) {
                            results.put(ip, createErrorResult(ip, response.getMessage()));
                        }
                    }
                    
                    return results;
                })
                .onErrorResume(Exception.class, e -> {
                    log.error("批量IP检测异常: {}", e.getMessage(), e);
                    Map<String, Map<String, Object>> errorResults = new HashMap<>();
                    for (String ip : ips) {
                        errorResults.put(ip, createErrorResult(ip, "检测异常: " + e.getMessage()));
                    }
                    return Mono.just(errorResults);
                });
    }

    /**
     * 检查IP是否应该被拦截
     *
     * @param ip IP地址
     * @return 拦截检查结果
     */
    public Mono<Map<String, Object>> checkIPBlock(String ip) {
        return detectIP(ip)
                .map(result -> {
                    Map<String, Object> blockResult = new HashMap<>();
                    blockResult.put("ip", ip);
                    blockResult.put("shouldBlock", result.get("shouldBlock"));
                    blockResult.put("blockReasons", result.get("blockReasons"));
                    blockResult.put("riskLevel", result.get("riskLevel"));
                    blockResult.put("redirectUrl", ipDetectionConfig.getRedirectUrl());
                    blockResult.put("checkTime", System.currentTimeMillis());
                    
                    return blockResult;
                });
    }

    /**
     * 处理检测结果
     *
     * @param ip IP地址
     * @param response IPRegistry响应
     * @return 处理后的结果
     */
    private Map<String, Object> processDetectionResult(String ip, IPRegistryResult response) {
        Map<String, Object> result = new HashMap<>();
        
        result.put("ip", ip);
        result.put("success", true);
        result.put("checkTime", System.currentTimeMillis());
        
        if (response != null && response.getSecurity() != null) {
            IPRegistryResult.Security security = response.getSecurity();
            
            // 基本检测信息
            result.put("isProxy", security.isProxy());
            result.put("isVpn", security.isVpn());
            result.put("isAnonymous", security.isAnonymous());
            result.put("isCloudProvider", security.isCloudProvider());
            result.put("isRelay", security.isRelay());
            result.put("isThreat", security.isThreat());
            result.put("isAbuser", security.isAbuser());
            result.put("isAttacker", security.isAttacker());
            result.put("isTor", security.isTor());
            result.put("isTorExit", security.isTorExit());
            result.put("isBogon", security.isBogon());
            
            // 风险评估 - 基于安全检测结果计算风险级别
            String riskLevel = calculateRiskLevel(security);
            result.put("riskLevel", riskLevel);
            result.put("detectedTypes", getDetectedTypes(security));
            result.put("isSuspicious", isSecuritySuspicious(security));

            // 拦截判断
            boolean shouldBlock = ipDetectionConfig.shouldBlock(response);
            result.put("shouldBlock", shouldBlock);
            result.put("blockReasons", ipDetectionConfig.getBlockReasons(response));

            log.info("IP检测完成: {} - 风险级别: {}, 是否拦截: {}",
                    ip, riskLevel, shouldBlock);
        } else {
            result.put("shouldBlock", false);
            result.put("riskLevel", "UNKNOWN");
            result.put("blockReasons", new String[0]);
            log.warn("IP检测响应数据不完整: {}", ip);
        }
        
        return result;
    }

    /**
     * 创建默认结果
     */
    private Map<String, Object> createDefaultResult(String ip, String message) {
        Map<String, Object> result = new HashMap<>();
        result.put("ip", ip);
        result.put("success", false);
        result.put("message", message);
        result.put("shouldBlock", false);
        result.put("riskLevel", "UNKNOWN");
        result.put("blockReasons", new String[0]);
        result.put("checkTime", System.currentTimeMillis());
        return result;
    }

    /**
     * 创建错误结果
     */
    private Map<String, Object> createErrorResult(String ip, String error) {
        Map<String, Object> result = new HashMap<>();
        result.put("ip", ip);
        result.put("success", false);
        result.put("error", error);
        result.put("shouldBlock", false);
        result.put("riskLevel", "UNKNOWN");
        result.put("blockReasons", new String[0]);
        result.put("checkTime", System.currentTimeMillis());
        return result;
    }



    /**
     * 检查服务是否可用
     */
    public boolean isServiceAvailable() {
        return ipDetectionConfig.isEnabled() && ipRegistryApiClient.isAvailable();
    }

    /**
     * 计算风险级别
     */
    private String calculateRiskLevel(IPRegistryResult.Security security) {
        if (security == null) {
            return "UNKNOWN";
        }

        int riskScore = 0;
        if (security.isThreat()) riskScore += 10;
        if (security.isAttacker()) riskScore += 10;
        if (security.isAbuser()) riskScore += 8;
        if (security.isTor()) riskScore += 7;
        if (security.isTorExit()) riskScore += 8;
        if (security.isProxy()) riskScore += 5;
        if (security.isVpn()) riskScore += 3;
        if (security.isAnonymous()) riskScore += 4;
        if (security.isRelay()) riskScore += 3;
        if (security.isCloudProvider()) riskScore += 2;

        if (riskScore >= 10) return "HIGH";
        if (riskScore >= 5) return "MEDIUM";
        if (riskScore >= 1) return "LOW";
        return "SAFE";
    }

    /**
     * 获取检测到的类型列表
     */
    private String[] getDetectedTypes(IPRegistryResult.Security security) {
        if (security == null) {
            return new String[0];
        }

        java.util.List<String> types = new java.util.ArrayList<>();
        if (security.isThreat()) types.add("THREAT");
        if (security.isAttacker()) types.add("ATTACKER");
        if (security.isAbuser()) types.add("ABUSER");
        if (security.isTor()) types.add("TOR");
        if (security.isTorExit()) types.add("TOR_EXIT");
        if (security.isProxy()) types.add("PROXY");
        if (security.isVpn()) types.add("VPN");
        if (security.isAnonymous()) types.add("ANONYMOUS");
        if (security.isRelay()) types.add("RELAY");
        if (security.isCloudProvider()) types.add("CLOUD_PROVIDER");
        if (security.isBogon()) types.add("BOGON");

        return types.toArray(new String[0]);
    }

    /**
     * 判断是否可疑
     */
    private boolean isSecuritySuspicious(IPRegistryResult.Security security) {
        if (security == null) {
            return false;
        }

        return security.isThreat() || security.isAttacker() || security.isAbuser() ||
               security.isTor() || security.isTorExit() || security.isProxy();
    }

    /**
     * 获取服务配置信息
     */
    public Map<String, Object> getServiceInfo() {
        Map<String, Object> info = new HashMap<>();
        info.put("enabled", ipDetectionConfig.isEnabled());
        info.put("hasApiKey", ipDetectionConfig.getApiKey() != null && !ipDetectionConfig.getApiKey().trim().isEmpty());
        info.put("baseUrl", ipDetectionConfig.getBaseUrl());
        info.put("timeout", ipDetectionConfig.getTimeout());
        info.put("cacheEnabled", ipDetectionConfig.isCacheEnabled());
        info.put("detectionTypes", ipDetectionConfig.getDetectionTypes());
        info.put("redirectUrl", ipDetectionConfig.getRedirectUrl());
        info.put("available", isServiceAvailable());
        return info;
    }
}
