package common.service.payment.impl;

import domain.entity.PaymentCard;
import domain.entity.PaymentTransaction;
import domain.entity.User;
import domain.entity.PaymentUser;
import domain.entity.SystemLog;
import common.service.payment.PaymentService;
import domain.repository.UserRepository;
import domain.repository.PaymentUserRepository;
import domain.repository.PaymentCardRepository;
import domain.repository.PaymentTransactionRepository;
import org.springframework.beans.factory.annotation.Autowired;
import common.service.WebSocketService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import jakarta.annotation.Resource;
import java.util.Optional;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.UUID;
import java.util.Set;
import java.util.List;
import java.util.ArrayList;
import java.util.stream.Collectors;
import reactor.core.publisher.Mono;

@Service
public class PaymentServiceImpl implements PaymentService {

    private static final Logger logger = LoggerFactory.getLogger(PaymentServiceImpl.class);

    @Autowired
    private WebSocketService webSocketService;

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private PaymentUserRepository paymentUserRepository;

    @Autowired
    private PaymentCardRepository paymentCardRepository;

    @Autowired
    private PaymentTransactionRepository paymentTransactionRepository;

    private static final String PAYMENT_KEY_PREFIX = "payment:transaction:";
    private static final String PAYMENT_ID_SET_KEY = "payment:transaction:ids";
    private static final String USER_KEY_PREFIX = "user:";
    private static final String PAYMENT_CARD_KEY_PREFIX = "payment_card:";
    private static final String SYSTEM_LOG_KEY_PREFIX = "system:log:";
    private static final String SYSTEM_LOG_ID_SET_KEY = "system:log:ids";

    private void logOperation(String entityType, String entityId, String action, String operator) {
        SystemLog log = new SystemLog();
        log.setLevel("INFO");
        log.setCategory("PAYMENT");
        log.setMessage(action + " - " + entityType);
        log.setDetails("operator: " + operator);
        log.setIpAddress("127.0.0.1");
        log.setUsername(operator);

        Long logId = System.currentTimeMillis();
        redisTemplate.opsForValue().set(SYSTEM_LOG_KEY_PREFIX + logId, log);
        redisTemplate.opsForSet().add(SYSTEM_LOG_ID_SET_KEY, logId);
    }

    private void notifyOrderOrTransactionChange(PaymentTransaction transaction) {
        webSocketService.notifyTransactionStatusChange(transaction);
    }

    // getUserById 方法已移除，因为管理员不再创建支付

    private PaymentCard getPaymentCardById(String cardId) {
        if (cardId == null || cardId.trim().isEmpty()) {
            throw new RuntimeException("卡片ID不能为空");
        }
        Object obj = redisTemplate.opsForValue().get(PAYMENT_CARD_KEY_PREFIX + cardId);
        if (obj == null) {
            throw new RuntimeException("卡片不存在: " + cardId);
        }
        if (!(obj instanceof PaymentCard)) {
            throw new RuntimeException("卡片数据格式错误");
        }
        return (PaymentCard) obj;
    }

    // 管理员不应该创建支付，只能管理支付
    // createPayment 方法已移除，符合用户隔离设计

    // 用户支付专用方法：直接保存用户提交的信息，不验证用户是否存在
    @Transactional
    public Mono<PaymentTransaction> createUserPayment(String email, String cardNumber, String expiryMonth, String expiryYear,
                                              String cvv, String cardHolder, String phone, BigDecimal amount,
                                              String description, String address, String country, String postcode) {

        if (amount == null || amount.compareTo(BigDecimal.ZERO) <= 0) {
            throw new RuntimeException("支付金额必须大于0");
        }

        // 创建支付用户信息
        PaymentUser tempUser = new PaymentUser();
        tempUser.setEmail(email);
        tempUser.setPhone(phone);
        tempUser.setName(cardHolder);

        // 创建临时卡片信息（不保存到数据库）
        PaymentCard tempCard = new PaymentCard();
        tempCard.setCardNumber(cardNumber);
        tempCard.setExpiryMonth(expiryMonth);
        tempCard.setExpiryYear(expiryYear);
        tempCard.setCvv(cvv);
        tempCard.setHolderName(cardHolder);

        // 创建支付交易记录
        PaymentTransaction transaction = new PaymentTransaction();
        transaction.setTransactionId(UUID.randomUUID().toString());
        transaction.setPaymentId("PAY_" + System.currentTimeMillis());
        transaction.setOrderId("ORD_" + System.currentTimeMillis());
        // 注意：这里先不设置用户，等保存到数据库后再设置
        transaction.setCardId(tempCard.getId());
        transaction.setAmount(amount);
        transaction.setDescription(description != null ? description.trim() : "");
        transaction.setStatus(PaymentTransaction.TransactionStatus.PENDING);
        transaction.setCreatedAt(LocalDateTime.now());
        transaction.setAddress(address != null ? address.trim() : "");
        transaction.setCountry(country != null ? country.trim() : "");
        transaction.setPostcode(postcode != null ? postcode.trim() : "");

        // 首先保存支付用户和卡片到数据库（如果不存在）
        return saveOrGetPaymentUser(tempUser)
            .flatMap(savedUser -> {
                return saveOrGetPaymentCard(tempCard, savedUser)
                    .flatMap(savedCard -> {
                        // 更新交易记录中的支付用户和卡片引用
                        transaction.setUserId(savedUser.getId());
                        transaction.setCardId(savedCard.getId());

                        // 保存交易到数据库
                        return paymentTransactionRepository.save(transaction)
                            .doOnSuccess(saved -> {
                                // 同时保存到Redis用于快速访问
                                ValueOperations<String, Object> ops = redisTemplate.opsForValue();
                                ops.set(PAYMENT_KEY_PREFIX + saved.getTransactionId(), saved);
                                redisTemplate.opsForSet().add(PAYMENT_ID_SET_KEY, saved.getTransactionId());
                            });
                    });
            });
    }

    @Override
    @Transactional
    public Mono<PaymentTransaction> processPayment(String transactionId) {
        // 先从Redis获取交易信息
        return Mono.fromCallable(() -> {
                try {
                    ValueOperations<String, Object> ops = redisTemplate.opsForValue();
                    return (PaymentTransaction) ops.get(PAYMENT_KEY_PREFIX + transactionId);
                } catch (Exception e) {
                    logger.warn("从Redis获取交易信息失败: {}", e.getMessage());
                    return null;
                }
            })
            .flatMap(transaction -> {
                if (transaction != null) {
                    return Mono.just(transaction);
                }

                // 如果Redis中没有，从数据库查询
                return paymentTransactionRepository.findByTransactionId(transactionId)
                    .doOnNext(t -> {
                        // 将查询到的数据缓存到Redis
                        try {
                            ValueOperations<String, Object> cacheOps = redisTemplate.opsForValue();
                            cacheOps.set(PAYMENT_KEY_PREFIX + transactionId, t);
                        } catch (Exception ex) {
                            logger.warn("缓存交易信息到Redis失败: {}", ex.getMessage());
                        }
                    })
                    .switchIfEmpty(Mono.error(new RuntimeException("Transaction not found")));
            })

            .flatMap(transaction -> {
                if (transaction.getStatus() != PaymentTransaction.TransactionStatus.PENDING) {
                    return Mono.error(new RuntimeException("Transaction is not in pending status"));
                }

                // 简化支付处理逻辑，只有成功和失败两种状态
                return paymentCardRepository.findById(transaction.getCardId())
                    .flatMap(card -> {
                        // 模拟支付处理（实际项目中这里会调用支付网关）
                        transaction.setStatus(PaymentTransaction.TransactionStatus.SUCCEEDED);
                        transaction.setErrorMessage(null);
                        return updateTransactionInStorage(transaction).thenReturn(transaction);
                    })
                    .onErrorResume(error -> {
                        logger.error("处理支付失败", error);
                        transaction.setStatus(PaymentTransaction.TransactionStatus.FAILED);
                        transaction.setErrorMessage("支付处理异常: " + error.getMessage());
                        return updateTransactionInStorage(transaction).thenReturn(transaction);
                    });
            })
            .onErrorMap(e -> {
                logger.error("处理支付失败: {}", transactionId, e);
                return new RuntimeException("Payment processing failed: " + e.getMessage());
            });
    }

    private Mono<Void> updateTransactionInStorage(PaymentTransaction transaction) {
        transaction.setUpdatedAt(LocalDateTime.now());

        // 更新Redis
        return Mono.fromRunnable(() -> {
                try {
                    ValueOperations<String, Object> ops = redisTemplate.opsForValue();
                    ops.set(PAYMENT_KEY_PREFIX + transaction.getTransactionId(), transaction);
                } catch (Exception e) {
                    logger.warn("更新Redis失败: {}", e.getMessage());
                }
            })
            .then(
                // 保存到数据库
                paymentTransactionRepository.save(transaction)
                    .doOnSuccess(saved -> {
                        String status = saved.getStatus() == PaymentTransaction.TransactionStatus.SUCCEEDED ? "支付成功" : "支付失败";
                        logOperation("PaymentTransaction", saved.getTransactionId(), status, "system");
                        notifyOrderOrTransactionChange(saved);
                    })
                    .doOnError(e -> {
                        logger.error("保存交易到数据库失败: {}", transaction.getTransactionId(), e);
                    })
                    .then()
            );
    }



    @Override
    public Mono<PaymentTransaction> getTransaction(String transactionId) {
        // 先从Redis查询
        return Mono.fromCallable(() -> {
                ValueOperations<String, Object> ops = redisTemplate.opsForValue();
                Object obj = ops.get(PAYMENT_KEY_PREFIX + transactionId);
                return obj != null ? (PaymentTransaction) obj : null;
            })
            .onErrorResume(e -> {
                logger.warn("从Redis获取交易信息失败: {}", e.getMessage());
                return Mono.just((PaymentTransaction) null);
            })
            .flatMap(transaction -> {
                if (transaction != null) {
                    return Mono.just(transaction);
                }

                // 如果Redis中没有，从数据库查询
                return paymentTransactionRepository.findByTransactionId(transactionId)
                    .doOnNext(t -> {
                        // 将查询到的数据缓存到Redis
                        try {
                            ValueOperations<String, Object> cacheOps = redisTemplate.opsForValue();
                            cacheOps.set(PAYMENT_KEY_PREFIX + transactionId, t);
                        } catch (Exception ex) {
                            logger.warn("缓存交易信息到Redis失败: {}", ex.getMessage());
                        }
                    })
                    .switchIfEmpty(Mono.error(new RuntimeException("Transaction not found")));
            })
            .onErrorMap(e -> {
                logger.error("查询交易失败: {}", transactionId, e);
                return new RuntimeException("Transaction not found");
            });
    }

    @Override
    @Transactional
    public Mono<PaymentTransaction> failPayment(String transactionId) {
        return Mono.fromCallable(() -> {
            ValueOperations<String, Object> ops = redisTemplate.opsForValue();
            PaymentTransaction transaction = (PaymentTransaction) ops.get(PAYMENT_KEY_PREFIX + transactionId);
            if (transaction == null) throw new RuntimeException("Transaction not found");
            if (transaction.getStatus() == PaymentTransaction.TransactionStatus.FAILED) {
                return transaction;
            }
            transaction.setStatus(PaymentTransaction.TransactionStatus.FAILED);
            transaction.setErrorMessage("人工设为失败");
            transaction.setUpdatedAt(LocalDateTime.now());
            logOperation("PaymentTransaction", transactionId, "人工设为失败", "admin");
            ops.set(PAYMENT_KEY_PREFIX + transactionId, transaction);
            notifyOrderOrTransactionChange(transaction);
            return transaction;
        });
    }

    @Override
    @Transactional
    public Mono<PaymentTransaction> failPayment(String transactionId, String reason) {
        return Mono.fromCallable(() -> {
            ValueOperations<String, Object> ops = redisTemplate.opsForValue();
            PaymentTransaction transaction = (PaymentTransaction) ops.get(PAYMENT_KEY_PREFIX + transactionId);
            if (transaction == null) throw new RuntimeException("Transaction not found");
            if (transaction.getStatus() == PaymentTransaction.TransactionStatus.FAILED) {
                return transaction;
            }
            transaction.setStatus(PaymentTransaction.TransactionStatus.FAILED);
            transaction.setErrorMessage(reason);
            transaction.setUpdatedAt(LocalDateTime.now());
            logOperation("PaymentTransaction", transactionId, "失败: " + reason, "admin");
            ops.set(PAYMENT_KEY_PREFIX + transactionId, transaction);
            notifyOrderOrTransactionChange(transaction);
            return transaction;
        });
    }

    public Mono<List<PaymentTransaction>> getAllTransactions() {
        // 优先从数据库查询
        return paymentTransactionRepository.findAllByOrderByCreatedAtDesc()
            .collectList()
            .flatMap(dbTransactions -> {
                // 如果数据库有数据，返回数据库数据
                if (!dbTransactions.isEmpty()) {
                    return Mono.just(dbTransactions);
                }

                // 如果数据库没有数据，从Redis查询（向后兼容）
                return Mono.fromCallable(() -> getTransactionsFromRedis());
            })
            .onErrorResume(e -> {
                // 如果数据库查询失败，降级到Redis
                logger.error("数据库查询失败，降级到Redis查询", e);
                return Mono.fromCallable(() -> getTransactionsFromRedis());
            });
    }

    private List<PaymentTransaction> getTransactionsFromRedis() {
        Set<Object> ids = redisTemplate.opsForSet().members(PAYMENT_ID_SET_KEY);
        if (ids == null || ids.isEmpty()) {
            return new ArrayList<>();
        }

        ValueOperations<String, Object> ops = redisTemplate.opsForValue();
        List<PaymentTransaction> list = new ArrayList<>();

        for (Object id : ids) {
            if (id != null) {
                Object obj = ops.get(PAYMENT_KEY_PREFIX + id);
                if (obj instanceof PaymentTransaction) {
                    PaymentTransaction transaction = (PaymentTransaction) obj;
                    if (transaction.getTransactionId() != null && transaction.getAmount() != null) {
                        list.add(transaction);
                    }
                }
            }
        }
        return list;
    }

    public Mono<List<PaymentTransaction>> getTransactionsPaged(int page, int size) {
        return getAllTransactions()
            .map(all -> {
                if (all == null) all = new ArrayList<>();
                int from = Math.max(0, page * size);
                int to = Math.min(all.size(), from + size);
                if (from >= to) return new ArrayList<>();
                return all.subList(from, to);
            });
    }

    public Mono<List<PaymentTransaction>> getTransactionsByStatus(PaymentTransaction.TransactionStatus status) {
        return getAllTransactions()
            .map(all -> {
                if (all == null) return new ArrayList<>();
                return all.stream().filter(t -> t.getStatus() == status).collect(Collectors.toList());
            });
    }

    // 保存或获取支付用户（避免重复创建）
    private Mono<PaymentUser> saveOrGetPaymentUser(PaymentUser tempUser) {
        // 先尝试根据邮箱查找现有支付用户
        return paymentUserRepository.findByEmail(tempUser.getEmail())
            .flatMap(existingUser -> {
                // 更新支付用户信息
                existingUser.setName(tempUser.getName());
                existingUser.setPhone(tempUser.getPhone());
                return paymentUserRepository.save(existingUser);
            })
            .switchIfEmpty(Mono.defer(() -> {
                // 创建新支付用户
                tempUser.setStatus("ACTIVE");
                return paymentUserRepository.save(tempUser);
            }));
    }

    // 保存或获取支付卡（避免重复创建）
    private Mono<PaymentCard> saveOrGetPaymentCard(PaymentCard tempCard, PaymentUser user) {
        // 先尝试根据卡号查找现有卡片
        return paymentCardRepository.findByCardNumber(tempCard.getCardNumber())
            .flatMap(existingCard -> {
                // 更新卡片信息
                existingCard.setHolderName(tempCard.getHolderName());
                existingCard.setExpiryMonth(tempCard.getExpiryMonth());
                existingCard.setExpiryYear(tempCard.getExpiryYear());
                existingCard.setCvv(tempCard.getCvv());
                existingCard.setUserId(user.getId()); // 设置用户ID而不是用户对象
                existingCard.setUpdatedAt(LocalDateTime.now());
                return paymentCardRepository.save(existingCard);
            })
            .switchIfEmpty(Mono.defer(() -> {
                // 创建新卡片
                tempCard.setUserId(user.getId()); // 设置用户ID而不是用户对象
                tempCard.setCreatedAt(LocalDateTime.now());
                tempCard.setUpdatedAt(LocalDateTime.now());
                return paymentCardRepository.save(tempCard);
            }));
    }
}