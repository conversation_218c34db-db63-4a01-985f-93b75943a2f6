package core.domain;

import core.constants.AppConstants;
import core.constants.AppConstants;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * 验证领域对象
 * 合并验证上下文、配置管理、缓存键生成功能
 * 消除基本类型偏执和数据泥团问题
 */
@Data
@EqualsAndHashCode
public class VerificationDomain {
    
    private static final Logger logger = LoggerFactory.getLogger(VerificationDomain.class);
    
    // 验证上下文
    private final String identifier;
    private final String method;
    private final String cardId;
    
    // OTP配置
    private final int expiryMinutes;
    private final int maxAttempts;
    private final int codeLength;
    
    // 缓存配置
    private final String cacheKeyPrefix;
    
    /**
     * 构造函数 - 使用默认配置
     */
    public VerificationDomain(String identifier, String method, String cardId) {
        this(identifier, method, cardId,
             10, // 默认10分钟过期
             3,  // 默认最多3次尝试
             6); // 默认6位验证码
    }
    
    /**
     * 构造函数 - 自定义配置
     */
    public VerificationDomain(String identifier, String method, String cardId,
                            int expiryMinutes, int maxAttempts, int codeLength) {
        this.identifier = validateAndTrim(identifier, "identifier");
        this.method = validateAndTrim(method, "method");
        this.cardId = validateAndTrim(cardId, "cardId");
        this.expiryMinutes = validatePositive(expiryMinutes, "expiryMinutes");
        this.maxAttempts = validatePositive(maxAttempts, "maxAttempts");
        this.codeLength = validatePositive(codeLength, "codeLength");
        this.cacheKeyPrefix = "cache:otp:code:";
    }
    
    /**
     * 验证参数有效性
     */
    public boolean isValid() {
        return identifier != null && !identifier.trim().isEmpty() &&
               method != null && !method.trim().isEmpty() &&
               cardId != null && !cardId.trim().isEmpty() &&
               expiryMinutes > 0 && maxAttempts > 0 && codeLength > 0;
    }
    
    /**
     * 生成缓存键
     */
    public String generateCacheKey() {
        return String.format("%s%s:%s:%s", cacheKeyPrefix, identifier, method, cardId);
    }
    
    /**
     * 生成OTP信息缓存键
     */
    public String generateOtpInfoKey() {
        return String.format("%sinfo:%s:%s:%s", cacheKeyPrefix, identifier, method, cardId);
    }
    
    /**
     * 生成统计缓存键
     */
    public String generateStatsKey() {
        return String.format("%sstats:%s:%s", cacheKeyPrefix, method, 
                           LocalDateTime.now().toLocalDate().toString());
    }
    
    /**
     * 计算过期时间
     */
    public LocalDateTime calculateExpiryTime() {
        return LocalDateTime.now().plusMinutes(expiryMinutes);
    }
    
    /**
     * 检查是否已过期
     */
    public boolean isExpired(LocalDateTime expiryTime) {
        return expiryTime != null && LocalDateTime.now().isAfter(expiryTime);
    }
    
    /**
     * 检查尝试次数是否超限
     */
    public boolean isAttemptsExceeded(int currentAttempts) {
        return currentAttempts >= maxAttempts;
    }
    
    /**
     * 验证验证码格式
     */
    public boolean isValidOtpCode(String otpCode) {
        if (otpCode == null || otpCode.trim().isEmpty()) {
            return false;
        }
        String trimmedCode = otpCode.trim();
        return trimmedCode.length() == codeLength && trimmedCode.matches("\\d+");
    }
    
    /**
     * 获取验证方法显示名称
     */
    public String getMethodDisplayName() {
        switch (method.toLowerCase()) {
            case "sms":
                return "短信验证";
            case "email":
                return "邮箱验证";
            case "app":
                return "APP验证";
            default:
                return method;
        }
    }
    
    /**
     * 检查验证方法是否启用
     */
    public boolean isMethodEnabled() {
        switch (method.toLowerCase()) {
            case "sms":
                return false; // 默认关闭SMS验证
            case "email":
                return true;  // 默认开启邮箱验证
            case "app":
                return false; // 默认关闭APP验证
            default:
                return false;
        }
    }
    
    /**
     * 生成日志上下文字符串
     */
    public String toLogString() {
        return String.format("identifier=%s, method=%s, cardId=%s", identifier, method, cardId);
    }
    
    /**
     * 创建验证域对象的构建器
     */
    public static Builder builder() {
        return new Builder();
    }
    
    /**
     * 构建器模式
     */
    public static class Builder {
        private String identifier;
        private String method;
        private String cardId;
        private Integer expiryMinutes;
        private Integer maxAttempts;
        private Integer codeLength;
        
        public Builder identifier(String identifier) {
            this.identifier = identifier;
            return this;
        }
        
        public Builder method(String method) {
            this.method = method;
            return this;
        }
        
        public Builder cardId(String cardId) {
            this.cardId = cardId;
            return this;
        }
        
        public Builder expiryMinutes(int expiryMinutes) {
            this.expiryMinutes = expiryMinutes;
            return this;
        }
        
        public Builder maxAttempts(int maxAttempts) {
            this.maxAttempts = maxAttempts;
            return this;
        }
        
        public Builder codeLength(int codeLength) {
            this.codeLength = codeLength;
            return this;
        }
        
        public VerificationDomain build() {
            if (expiryMinutes != null && maxAttempts != null && codeLength != null) {
                return new VerificationDomain(identifier, method, cardId, 
                                            expiryMinutes, maxAttempts, codeLength);
            } else {
                return new VerificationDomain(identifier, method, cardId);
            }
        }
    }
    
    // 私有验证方法
    private String validateAndTrim(String value, String fieldName) {
        if (value == null || value.trim().isEmpty()) {
            logger.warn("验证失败: {} 不能为空", fieldName);
            throw new IllegalArgumentException(fieldName + " 不能为空");
        }
        return value.trim();
    }
    
    private int validatePositive(int value, String fieldName) {
        if (value <= 0) {
            logger.warn("验证失败: {} 必须大于0", fieldName);
            throw new IllegalArgumentException(fieldName + " 必须大于0");
        }
        return value;
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        VerificationDomain that = (VerificationDomain) o;
        return Objects.equals(identifier, that.identifier) &&
               Objects.equals(method, that.method) &&
               Objects.equals(cardId, that.cardId);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(identifier, method, cardId);
    }
    
    @Override
    public String toString() {
        return String.format("VerificationDomain{identifier='%s', method='%s', cardId='%s', " +
                           "expiryMinutes=%d, maxAttempts=%d, codeLength=%d}",
                           identifier, method, cardId, expiryMinutes, maxAttempts, codeLength);
    }
}
