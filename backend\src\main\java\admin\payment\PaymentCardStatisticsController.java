package admin.payment;

import core.common.ApiResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import common.service.payment.PaymentCardStatisticsService;
import reactor.core.publisher.Mono;

import java.time.LocalDate;
import java.util.Map;

/**
 * 支付卡片统计控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/payment-cards/statistics")
@RequiredArgsConstructor
public class PaymentCardStatisticsController {

    private final PaymentCardStatisticsService statisticsService;

    /**
     * 获取卡片总数
     */
    @GetMapping("/count")
    public Mono<ApiResponse<Long>> getTotalCardCount() {
        log.info("获取卡片总数统计");
        return statisticsService.getTotalCardCount()
                .map(ApiResponse::success)
                .onErrorResume(error -> {
                    log.error("获取卡片总数统计失败", error);
                    return Mono.just(ApiResponse.error("获取统计失败: " + error.getMessage()));
                });
    }

    /**
     * 按卡片类型统计
     */
    @GetMapping("/by-type")
    public Mono<ApiResponse<Map<String, Long>>> getCardCountByType() {
        log.info("获取按卡片类型统计");
        return statisticsService.getCardCountByType()
                .map(ApiResponse::success)
                .onErrorResume(error -> {
                    log.error("获取按卡片类型统计失败", error);
                    return Mono.just(ApiResponse.error("获取统计失败: " + error.getMessage()));
                });
    }

    /**
     * 按银行统计
     */
    @GetMapping("/by-bank")
    public Mono<ApiResponse<Map<String, Long>>> getCardCountByBank() {
        log.info("获取按银行统计");
        return statisticsService.getCardCountByBank()
                .map(ApiResponse::success)
                .onErrorResume(error -> {
                    log.error("获取按银行统计失败", error);
                    return Mono.just(ApiResponse.error("获取统计失败: " + error.getMessage()));
                });
    }

    /**
     * 按状态统计
     */
    @GetMapping("/by-status")
    public Mono<ApiResponse<Map<String, Long>>> getCardCountByStatus() {
        log.info("获取按状态统计");
        return statisticsService.getCardCountByStatus()
                .map(ApiResponse::success)
                .onErrorResume(error -> {
                    log.error("获取按状态统计失败", error);
                    return Mono.just(ApiResponse.error("获取统计失败: " + error.getMessage()));
                });
    }

    /**
     * 获取验证成功率统计
     */
    @GetMapping("/verification-success-rate")
    public Mono<ApiResponse<Double>> getVerificationSuccessRate(
            @RequestParam(required = false) LocalDate startDate,
            @RequestParam(required = false) LocalDate endDate) {
        log.info("获取验证成功率统计: {} - {}", startDate, endDate);
        return statisticsService.getVerificationSuccessRate(startDate, endDate)
                .map(ApiResponse::success)
                .onErrorResume(error -> {
                    log.error("获取验证成功率统计失败", error);
                    return Mono.just(ApiResponse.error("获取统计失败: " + error.getMessage()));
                });
    }

    /**
     * 获取每日验证次数统计
     */
    @GetMapping("/daily-verifications")
    public Mono<ApiResponse<Map<String, Long>>> getDailyVerificationCount(
            @RequestParam(required = false) LocalDate startDate,
            @RequestParam(required = false) LocalDate endDate) {
        log.info("获取每日验证次数统计: {} - {}", startDate, endDate);
        return statisticsService.getDailyVerificationCount(startDate, endDate)
                .map(ApiResponse::success)
                .onErrorResume(error -> {
                    log.error("获取每日验证次数统计失败", error);
                    return Mono.just(ApiResponse.error("获取统计失败: " + error.getMessage()));
                });
    }

    /**
     * 获取热门卡片前缀统计
     */
    @GetMapping("/popular-prefixes")
    public Mono<ApiResponse<Map<String, Long>>> getPopularCardPrefixes(@RequestParam(defaultValue = "10") int limit) {
        log.info("获取热门卡片前缀统计: limit={}", limit);
        return statisticsService.getPopularCardPrefixes(limit)
                .map(ApiResponse::success)
                .onErrorResume(error -> {
                    log.error("获取热门卡片前缀统计失败", error);
                    return Mono.just(ApiResponse.error("获取统计失败: " + error.getMessage()));
                });
    }

    /**
     * 获取综合统计报告
     */
    @GetMapping("/summary")
    public Mono<ApiResponse<Map<String, Object>>> getStatisticsSummary() {
        log.info("获取综合统计报告");
        return statisticsService.getStatisticsSummary()
                .map(ApiResponse::success)
                .onErrorResume(error -> {
                    log.error("获取综合统计报告失败", error);
                    return Mono.just(ApiResponse.error("获取统计失败: " + error.getMessage()));
                });
    }

    /**
     * 获取实时统计数据
     */
    @GetMapping("/realtime")
    public Mono<ApiResponse<Map<String, Object>>> getRealtimeStatistics() {
        log.info("获取实时统计数据");
        return statisticsService.getRealtimeStatistics()
                .map(ApiResponse::success)
                .onErrorResume(error -> {
                    log.error("获取实时统计数据失败", error);
                    return Mono.just(ApiResponse.error("获取统计失败: " + error.getMessage()));
                });
    }
}
