package common.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

/**
 * 验证参数DTO
 * 封装验证相关的参数，提高类型安全性和代码可读性
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class VerificationParams {

    /**
     * 卡片ID
     */
    private String cardId;

    /**
     * 验证类型
     */
    private String verificationType;

    /**
     * 金额
     */
    private String amount;
    
    /**
     * 货币代码
     */
    private String currency;
    
    /**
     * 商户名称
     */
    private String merchantName;
    
    /**
     * 手机号码
     */
    private String phoneNumber;
    
    /**
     * 邮箱地址
     */
    private String emailAddress;
    
    /**
     * 银行名称
     */
    private String bankName;
    
    /**
     * 创建默认参数
     */
    public static VerificationParams createDefault() {
        return VerificationParams.builder()
            .merchantName("BakaOTP")
            .build();
    }
    
    /**
     * 创建带金额和货币的参数
     */
    public static VerificationParams createWithAmount(String amount, String currency) {
        return VerificationParams.builder()
            .amount(amount)
            .currency(currency)
            .merchantName("BakaOTP")
            .build();
    }
    
    /**
     * 创建完整参数
     */
    public static VerificationParams createFull(String amount, String currency, String merchantName,
                                               String phoneNumber, String emailAddress, String bankName) {
        return VerificationParams.builder()
            .amount(amount)
            .currency(currency)
            .merchantName(merchantName)
            .phoneNumber(phoneNumber)
            .emailAddress(emailAddress)
            .bankName(bankName)
            .build();
    }
    
    /**
     * 验证参数是否有效
     */
    public boolean isValid() {
        // 基本验证逻辑
        if (amount != null && !isValidAmount(amount)) {
            return false;
        }
        if (currency != null && !isValidCurrency(currency)) {
            return false;
        }
        if (emailAddress != null && !isValidEmail(emailAddress)) {
            return false;
        }
        return true;
    }
    
    /**
     * 验证金额格式
     */
    private boolean isValidAmount(String amount) {
        if (amount == null || amount.trim().isEmpty()) {
            return false;
        }
        try {
            double amountValue = Double.parseDouble(amount);
            return amountValue > 0;
        } catch (NumberFormatException e) {
            return false;
        }
    }
    
    /**
     * 验证货币代码格式
     */
    private boolean isValidCurrency(String currency) {
        if (currency == null || currency.trim().isEmpty()) {
            return false;
        }
        // 简单的货币代码验证（3位字母）
        return currency.matches("^[A-Z]{3}$");
    }
    
    /**
     * 验证邮箱格式
     */
    private boolean isValidEmail(String email) {
        if (email == null || email.trim().isEmpty()) {
            return false;
        }
        return email.matches("^[A-Za-z0-9+_.-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$");
    }
}
