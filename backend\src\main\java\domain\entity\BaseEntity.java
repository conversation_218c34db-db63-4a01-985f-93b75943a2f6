package domain.entity;

import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.relational.core.mapping.Column;

import java.time.LocalDateTime;

// 响应式基础实体类
public abstract class BaseEntity {

    /**
     * 创建时间
     */
    @CreatedDate
    @Column("created_at")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @LastModifiedDate
    @Column("updated_at")
    private LocalDateTime updatedAt;

    // Getter和Setter方法
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    /**
     * 获取创建时间（兼容旧字段名）
     */
    public LocalDateTime getCreateTime() {
        return createdAt;
    }

    /**
     * 获取更新时间（兼容旧字段名）
     */
    public LocalDateTime getUpdateTime() {
        return updatedAt;
    }

    /**
     * 设置创建时间（兼容旧字段名）
     */
    public void setCreateTime(LocalDateTime createTime) {
        this.createdAt = createTime;
    }

    /**
     * 设置更新时间（兼容旧字段名）
     */
    public void setUpdateTime(LocalDateTime updateTime) {
        this.updatedAt = updateTime;
    }

    /**
     * 检查实体是否为新创建的
     */
    public boolean isNew() {
        return createdAt == null;
    }

    /**
     * 获取实体存在时长（秒）
     */
    public long getExistenceDurationSeconds() {
        if (createdAt == null) {
            return 0;
        }
        return java.time.Duration.between(createdAt, LocalDateTime.now()).getSeconds();
    }

    /**
     * 获取最后更新时长（秒）
     */
    public long getLastUpdateDurationSeconds() {
        if (updatedAt == null) {
            return 0;
        }
        return java.time.Duration.between(updatedAt, LocalDateTime.now()).getSeconds();
    }

    @Override
    public String toString() {
        return getClass().getSimpleName() + "{" +
                "createdAt=" + createdAt +
                ", updatedAt=" + updatedAt +
                '}';
    }
}
