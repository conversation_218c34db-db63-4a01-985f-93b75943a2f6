import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.data.r2dbc.repository.config.EnableR2dbcRepositories;
import org.springframework.scheduling.annotation.EnableScheduling;

//启动类
@SpringBootApplication
@EnableConfigurationProperties
@ComponentScan(basePackages = {"admin", "auth", "core", "domain", "external", "security", "user", "common"})
@EnableR2dbcRepositories(basePackages = "domain.repository")
@EnableScheduling
public class BankPaymentApplication {

    public static void main(String[] args) {
        // Netty配置优化
        System.setProperty("reactor.netty.pool.leasingStrategy", "lifo");
        System.setProperty("reactor.netty.ioWorkerCount", String.valueOf(Runtime.getRuntime().availableProcessors() * 2));

        SpringApplication application = new SpringApplication(BankPaymentApplication.class);

        // 设置默认配置
        application.setDefaultProperties(java.util.Map.of(
            "server.port", "8080",
            "spring.main.web-application-type", "reactive"
        ));

        application.run(args);
    }
}