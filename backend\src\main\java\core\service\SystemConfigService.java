package core.service;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Service;
import domain.entity.SystemSetting;
import domain.repository.SystemSettingRepository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.Map;

/**
 * 系统配置服务
 */
@Slf4j
@Data
@Service
@ConfigurationProperties(prefix = "bakaotp")
public class SystemConfigService {

    @Autowired
    private SystemSettingRepository systemSettingRepository;

    private SystemConfig system = new SystemConfig();
    private SecurityConfig security = new SecurityConfig();
    private VerificationConfig verification = new VerificationConfig();
    private PaymentConfig payment = new PaymentConfig();
    private FeaturesConfig features = new FeaturesConfig();
    private PerformanceConfig performance = new PerformanceConfig();

    @Data
    public static class SystemConfig {
        private String name = "BakaOTP";
        private String version = "2.0.0";
        private boolean maintenanceMode = false;
        private boolean debugMode = false;
        private String logLevel = "INFO";
    }

    @Data
    public static class SecurityConfig {
        private AuthenticationConfig authentication = new AuthenticationConfig();
        private PaymentSecurityConfig paymentSecurity = new PaymentSecurityConfig();
    }

    @Data
    public static class AuthenticationConfig {
        private int maxLoginAttempts = 5;
        private int sessionTimeout = 3600;
        private int jwtExpiration = 86400;
        private boolean enable2fa = false;
        private int passwordMinLength = 8;
    }

    @Data
    public static class PaymentSecurityConfig {
        private boolean threeDSecureEnabled = true;
        private boolean threeD2Enabled = false;
        private boolean enableNetworkTypeCheck = false;
        private boolean filterCrawlerUserAgent = true;
    }

    @Data
    public static class VerificationConfig {
        private OtpConfig otp = new OtpConfig();
        private MethodsConfig methods = new MethodsConfig();
    }

    @Data
    public static class OtpConfig {
        private int expiryMinutes = 10;
        private int maxAttempts = 3;
        private int codeLength = 6;
        private boolean autoGenerate = true;
    }

    @Data
    public static class MethodsConfig {
        private boolean smsEnabled = false;
        private boolean emailEnabled = true;
        private boolean appEnabled = false;
        private String defaultMethod = "email";
    }

    @Data
    public static class PaymentConfig {
        private LimitsConfig limits = new LimitsConfig();
        private SettingsConfig settings = new SettingsConfig();
    }

    @Data
    public static class LimitsConfig {
        private int timeout = 300;
        private int maxAmount = 10000;
        private int minAmount = 1;
    }

    @Data
    public static class SettingsConfig {
        private String defaultCurrency = "USD";
        private String binBlacklist = "";
        private String countryRestrictions = "";
        private String cardHeaderFilter = "";
    }

    @Data
    public static class FeaturesConfig {
        private CoreConfig core = new CoreConfig();
        private NotificationsConfig notifications = new NotificationsConfig();
    }

    @Data
    public static class CoreConfig {
        private boolean unattendedMode = false;
        private boolean websocketEnabled = true;
        private boolean autoBackup = true;
    }

    @Data
    public static class NotificationsConfig {
        private boolean emailNotifications = true;
        private boolean smsNotifications = false;
        private boolean webhookEnabled = false;
    }

    @Data
    public static class PerformanceConfig {
        private ApiConfig api = new ApiConfig();
        private UiConfig ui = new UiConfig();
    }

    @Data
    public static class ApiConfig {
        private int rateLimitPerMinute = 1000;
        private int maxConcurrentRequests = 100;
        private int timeout = 30;
        private int retryAttempts = 3;
    }

    @Data
    public static class UiConfig {
        private String theme = "light";
        private String language = "zh-CN";
        private int itemsPerPage = 20;
        private int autoRefreshInterval = 30;
    }

    // 便捷方法
    public String getSystemName() {
        return system.getName();
    }

    public boolean isMaintenanceMode() {
        return system.isMaintenanceMode();
    }

    public int getOtpExpiryMinutes() {
        return verification.getOtp().getExpiryMinutes();
    }

    public int getMaxLoginAttempts() {
        return security.getAuthentication().getMaxLoginAttempts();
    }

    // 映射到新的配置结构
    public int getCodeExpiryMinutes() {
        return verification.getOtp().getExpiryMinutes();
    }

    public int getMaxAttempts() {
        return verification.getOtp().getMaxAttempts();
    }

    public int getCodeLength() {
        return verification.getOtp().getCodeLength();
    }

    public boolean isSmsVerificationEnabled() {
        return verification.getMethods().isSmsEnabled();
    }

    public boolean isEmailVerificationEnabled() {
        return verification.getMethods().isEmailEnabled();
    }

    public boolean isAppVerificationEnabled() {
        return verification.getMethods().isAppEnabled();
    }

    public String getDefaultVerificationMethod() {
        return verification.getMethods().getDefaultMethod();
    }

    public boolean isMaintenanceModeEnabled() {
        return system.isMaintenanceMode();
    }

    public boolean isDebugModeEnabled() {
        return system.isDebugMode();
    }

    public int getSessionTimeout() {
        return security.getAuthentication().getSessionTimeout();
    }

    public int getJwtExpiration() {
        return security.getAuthentication().getJwtExpiration();
    }

    public boolean is3dSecureEnabled() {
        return security.getPaymentSecurity().isThreeDSecureEnabled();
    }

    public boolean is3d2Enabled() {
        return security.getPaymentSecurity().isThreeD2Enabled();
    }

    public boolean isUnattendedModeEnabled() {
        return features.getCore().isUnattendedMode();
    }

    public boolean isWebSocketEnabled() {
        return features.getCore().isWebsocketEnabled();
    }

    public boolean isAutoBackupEnabled() {
        return features.getCore().isAutoBackup();
    }

    // 动态配置更新方法
    public Mono<Void> updateSecurityConfig(Map<String, Object> updates) {
        log.info("更新安全配置: {}", updates);

        return Mono.fromRunnable(() -> {
            // 更新内存中的配置
            if (updates.containsKey("3d_secure_enabled")) {
                security.getPaymentSecurity().setThreeDSecureEnabled((Boolean) updates.get("3d_secure_enabled"));
            }
            if (updates.containsKey("3d2_enabled")) {
                security.getPaymentSecurity().setThreeD2Enabled((Boolean) updates.get("3d2_enabled"));
            }
            if (updates.containsKey("enable_network_type_check")) {
                security.getPaymentSecurity().setEnableNetworkTypeCheck((Boolean) updates.get("enable_network_type_check"));
            }
            if (updates.containsKey("filter_crawler_user_agent")) {
                security.getPaymentSecurity().setFilterCrawlerUserAgent((Boolean) updates.get("filter_crawler_user_agent"));
            }
        }).then(
            // 持久化到数据库
            persistSecurityConfigToDatabase(updates)
        ).doOnSuccess(unused -> log.info("安全配置更新完成"));
    }

    /**
     * 将安全配置持久化到数据库
     */
    private Mono<Void> persistSecurityConfigToDatabase(Map<String, Object> updates) {
        return Flux.fromIterable(updates.entrySet())
            .flatMap(entry -> {
                String configKey = "security." + entry.getKey();
                String configValue = entry.getValue().toString();

                return systemSettingRepository.existsByConfigKey(configKey)
                    .flatMap(exists -> {
                        if (exists) {
                            // 更新现有配置
                            return systemSettingRepository.updateConfigValue(configKey, configValue)
                                .doOnSuccess(count -> log.debug("更新配置 {} = {}", configKey, configValue))
                                .then();
                        } else {
                            // 创建新配置
                            SystemSetting setting = new SystemSetting();
                            setting.setConfigKey(configKey);
                            setting.setConfigValue(configValue);
                            setting.setConfigType(SystemSetting.ConfigType.BOOLEAN);
                            setting.setDescription("安全配置项");
                            setting.setCategory("SECURITY");

                            return systemSettingRepository.save(setting)
                                .doOnSuccess(saved -> log.debug("创建配置 {} = {}", configKey, configValue))
                                .then();
                        }
                    });
            })
            .then();
    }

    /**
     * 从数据库加载安全配置
     */
    public Mono<Void> loadSecurityConfigFromDatabase() {
        return systemSettingRepository.findByCategory("SECURITY")
            .doOnNext(setting -> {
                String key = setting.getConfigKey();
                Boolean value = setting.getBooleanValue();

                if (value != null) {
                    switch (key) {
                        case "security.3d_secure_enabled":
                            security.getPaymentSecurity().setThreeDSecureEnabled(value);
                            break;
                        case "security.3d2_enabled":
                            security.getPaymentSecurity().setThreeD2Enabled(value);
                            break;
                        case "security.enable_network_type_check":
                            security.getPaymentSecurity().setEnableNetworkTypeCheck(value);
                            break;
                        case "security.filter_crawler_user_agent":
                            security.getPaymentSecurity().setFilterCrawlerUserAgent(value);
                            break;
                    }
                }
            })
            .doOnComplete(() -> log.info("从数据库加载安全配置完成"))
            .then();
    }
}

