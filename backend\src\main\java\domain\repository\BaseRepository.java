package domain.repository;

import org.springframework.data.r2dbc.repository.R2dbcRepository;
import org.springframework.data.repository.NoRepositoryBean;
import reactor.core.publisher.Flux;

import java.time.LocalDateTime;

// 基础Repository接口
@NoRepositoryBean
public interface BaseRepository<T, ID> extends R2dbcRepository<T, ID> {

    /**
     * 根据创建时间范围查询
     */
    Flux<T> findByCreatedAtBetween(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 根据更新时间范围查询
     */
    Flux<T> findByUpdatedAtBetween(LocalDateTime startTime, LocalDateTime endTime);
}
