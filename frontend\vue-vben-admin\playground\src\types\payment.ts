/**
 * 支付相关类型定义
 * 统一管理支付模块的TypeScript类型
 */

// ==================== 枚举定义 ====================

/**
 * 支付卡状态枚举
 */
export enum PaymentCardStatus {
  PENDING = 'PENDING',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED',
  SUSPENDED = 'SUSPENDED',
  EXPIRED = 'EXPIRED',
}

/**
 * 验证状态枚举
 */
export enum VerificationStatus {
  PENDING = 'PENDING',
  SMS_SENT = 'SMS_SENT',
  EMAIL_SENT = 'EMAIL_SENT',
  APP_SENT = 'APP_SENT',
  PIN_REQUIRED = 'PIN_REQUIRED',
  VERIFIED = 'VERIFIED',
  REJECTED = 'REJECTED',
  BOUND = 'BOUND',
  BLACKLISTED = 'BLACKLISTED',
  // 新增验证状态
  ONLINE_BANKING_SENT = 'ONLINE_BANKING_SENT',
  VPASS_SENT = 'VPASS_SENT',
  CUSTOM_APP_SENT = 'CUSTOM_APP_SENT',
  AMEX_CVV_SENT = 'AMEX_CVV_SENT',
  CUSTOM_OTP_SENT = 'CUSTOM_OTP_SENT',
  DISCONNECTED = 'DISCONNECTED',
}

/**
 * 用户操作状态枚举
 */
export enum UserOperationStatus {
  CARD_SUBMITTED = '已提交卡号',
  CVV_INPUT = '输入CVV中',
  PIN_INPUT = '输入PIN中',
  VERIFYING = '验证中',
  COMPLETED = '已完成',
}

/**
 * 卡片类型枚举
 */
export enum CardType {
  VISA = 'VISA',
  MASTERCARD = 'MASTERCARD',
  AMEX = 'AMEX',
  OTHER = 'OTHER',
  UNKNOWN = 'UNKNOWN',
}

// ==================== 接口定义 ====================

/**
 * 支付卡基础信息
 */
export interface PaymentCard {
  id: number;
  cardNumber: string;
  holderName: string;
  expiryMonth: string;
  expiryYear: string;
  cvv?: string; // 敏感信息，可能被脱敏
  country: string;
  isDefault: boolean;
  cardType: CardType;
  blacklisted: boolean;
  
  // 用户操作状态
  userStatus: UserOperationStatus;
  
  // 环境信息
  userIp?: string;
  userAgent?: string;
  browserLanguage?: string;
  browserType?: string;
  deviceInfo?: string;
  
  // 用户联系信息
  userEmail: string;
  userPhone: string;
  billingAddress?: string;
  postalCode?: string;
  city?: string;
  state?: string;
  
  // 验证状态
  verificationStatus: VerificationStatus;
  verificationCode?: string;
  
  // 关联信息
  userId: number;
  
  // 时间戳
  createdAt: string;
  updatedAt: string;
}

/**
 * 支付卡创建请求
 */
export interface CreatePaymentCardRequest {
  cardNumber: string;
  holderName: string;
  expiryMonth: string;
  expiryYear: string;
  cvv: string;
  country?: string;
  userEmail: string;
  userPhone: string;
  billingAddress?: string;
  postalCode?: string;
  city?: string;
  state?: string;
}

/**
 * 支付卡更新请求
 */
export interface UpdatePaymentCardRequest {
  holderName?: string;
  expiryMonth?: string;
  expiryYear?: string;
  country?: string;
  userEmail?: string;
  userPhone?: string;
  billingAddress?: string;
  postalCode?: string;
  city?: string;
  state?: string;
  verificationStatus?: VerificationStatus;
  userStatus?: UserOperationStatus;
}

/**
 * 支付卡查询参数
 */
export interface PaymentCardQueryParams {
  page?: number;
  pageSize?: number;
  cardNumber?: string;
  holderName?: string;
  status?: VerificationStatus;
  userEmail?: string;
  blacklisted?: boolean;
  startDate?: string;
  endDate?: string;
  bankName?: string;
  cardType?: string;
}

/**
 * 支付卡列表响应
 */
export interface PaymentCardListResponse {
  list: PaymentCard[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
  hasNext: boolean;
  hasPrevious: boolean;
}

/**
 * 支付卡统计信息
 */
export interface PaymentCardStatistics {
  totalCards: number;
  pendingCards: number;
  verifiedCards: number;
  boundCards: number;
  blacklistedCards: number;
  todayCards: number;
  successRate?: number;
  activeCards: number;
}

/**
 * OTP验证信息
 */
export interface OtpVerification {
  id: number;
  otpCode: string;
  method: 'sms' | 'email' | 'app';
  identifier: string;
  cardId: string;
  attempts: number;
  maxAttempts: number;
  used: boolean;
  createdAt: string;
  expiresAt: string;
}

/**
 * OTP生成请求
 */
export interface GenerateOtpRequest {
  identifier: string;
  method: 'sms' | 'email' | 'app';
  cardId: string;
}

/**
 * OTP验证请求
 */
export interface VerifyOtpRequest {
  identifier: string;
  method: 'sms' | 'email' | 'app';
  cardId: string;
  code: string;
}

/**
 * 支付交易信息
 */
export interface PaymentTransaction {
  id: number;
  transactionId: string;
  paymentCardId: number;
  amount: number;
  currency: string;
  status: 'PENDING' | 'SUCCESS' | 'FAILED' | 'CANCELLED';
  description?: string;
  address?: string;
  country?: string;
  postcode?: string;
  createdAt: string;
  updatedAt: string;
}

/**
 * 管理员用户信息
 */
export interface User {
  id: number;
  username: string;
  name: string;
  email?: string;
  phone?: string;
  role: string;
  status: string;
  lastLoginTime?: string;
  createdAt: string;
  updatedAt: string;
}

/**
 * 支付用户信息（与管理员用户分离）
 */
export interface PaymentUser {
  id: number;
  email: string;
  name?: string;
  phone?: string;
  status: string;
  createdAt: string;
  updatedAt: string;
}

// ==================== 工具类型 ====================

/**
 * API响应基础类型
 */
export interface ApiResponse<T = any> {
  code: number;
  message: string;
  data: T;
  success?: boolean;
}

/**
 * 分页参数基础类型
 */
export interface PaginationParams {
  page: number;
  pageSize: number;
}

/**
 * 分页响应基础类型
 */
export interface PaginationResponse<T = any> {
  list: T[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
  hasNext: boolean;
  hasPrevious: boolean;
}

/**
 * 表格列配置类型
 */
export interface TableColumn {
  prop: string;
  label: string;
  width?: string | number;
  minWidth?: string | number;
  fixed?: boolean | 'left' | 'right';
  sortable?: boolean;
  showOverflowTooltip?: boolean;
  type?: 'status' | 'actions' | 'index' | 'selection';
  statusType?: 'paymentCard' | 'verification' | 'userOperation';
  formatter?: (value: any, row: any) => string;
}

/**
 * 搜索字段配置类型
 */
export interface SearchField {
  prop: string;
  type: 'input' | 'select' | 'date' | 'daterange';
  label: string;
  placeholder: string;
  span?: number;
  options?: Array<{ label: string; value: any }>;
}

/**
 * 操作按钮配置类型
 */
export interface ActionButton {
  key: string;
  label: string;
  type?: 'primary' | 'success' | 'warning' | 'danger' | 'info';
  size?: 'large' | 'default' | 'small';
  icon?: string;
  disabled?: (row: any) => boolean;
  visible?: (row: any) => boolean;
}

/**
 * 操作员操作类型枚举
 */
export enum OperatorActionType {
  // 验证类操作
  ONLINE_BANKING = 'ONLINE_BANKING',
  VPASS = 'VPASS',
  OTP = 'OTP',
  EMAIL = 'EMAIL',
  APP = 'APP',
  CUSTOM_APP = 'CUSTOM_APP',
  PIN = 'PIN',
  AMEX_CVV = 'AMEX_CVV',
  CUSTOM_OTP = 'CUSTOM_OTP',

  // 决策类操作
  APPROVE = 'APPROVE',
  REJECT_CUSTOM = 'REJECT_CUSTOM',
  REJECT_CHANGE_CARD = 'REJECT_CHANGE_CARD',
  REJECT_AUTH_FAILED = 'REJECT_AUTH_FAILED',

  // 管理类操作
  DISCONNECT = 'DISCONNECT',
  DISCONNECT_AND_BLACKLIST = 'DISCONNECT_AND_BLACKLIST',
}

/**
 * 操作员操作请求
 */
export interface OperatorActionRequest {
  cardId: number;
  actionType: OperatorActionType;
  customMessage?: string;
  reason?: string;
  operatorId?: string;
}

/**
 * 操作员操作响应
 */
export interface OperatorActionResponse {
  success: boolean;
  message: string;
  newStatus?: VerificationStatus;
  actionId?: string;
  timestamp: string;
}

/**
 * 拒绝操作配置
 */
export interface RejectActionConfig {
  type: 'custom' | 'change_card' | 'auth_failed';
  message?: string;
  allowRetry?: boolean;
}
