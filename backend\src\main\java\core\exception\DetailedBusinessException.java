package core.exception;

/**
 * 详细业务异常类
 * 提供更精确的错误分类和处理
 */
public class DetailedBusinessException extends BusinessException {

    private final ErrorCategory category;
    private final String errorCode;
    private final Object[] args;

    public DetailedBusinessException(ErrorCategory category, String errorCode, String message, Object... args) {
        super(category.getHttpStatus(), message);
        this.category = category;
        this.errorCode = errorCode;
        this.args = args;
    }

    public DetailedBusinessException(ErrorCategory category, String errorCode, String message, Throwable cause, Object... args) {
        super(category.getHttpStatus(), message, cause);
        this.category = category;
        this.errorCode = errorCode;
        this.args = args;
    }

    public ErrorCategory getCategory() {
        return category;
    }

    public String getErrorCode() {
        return errorCode;
    }

    public Object[] getArgs() {
        return args;
    }

    /**
     * 错误分类枚举
     */
    public enum ErrorCategory {
        // 认证授权相关 (401, 403)
        AUTHENTICATION(401, "认证失败"),
        AUTHORIZATION(403, "权限不足"),
        
        // 请求参数相关 (400)
        VALIDATION(400, "参数验证失败"),
        INVALID_REQUEST(400, "无效请求"),
        
        // 业务逻辑相关 (422)
        BUSINESS_RULE(422, "业务规则违反"),
        PAYMENT_FAILED(422, "支付处理失败"),
        VERIFICATION_FAILED(422, "验证失败"),
        
        // 资源相关 (404, 409)
        RESOURCE_NOT_FOUND(404, "资源不存在"),
        RESOURCE_CONFLICT(409, "资源冲突"),
        
        // 外部服务相关 (502, 503, 504)
        EXTERNAL_SERVICE(502, "外部服务错误"),
        SERVICE_UNAVAILABLE(503, "服务不可用"),
        TIMEOUT(504, "服务超时"),
        
        // 系统内部相关 (500)
        SYSTEM_ERROR(500, "系统内部错误"),
        DATABASE_ERROR(500, "数据库错误"),
        CACHE_ERROR(500, "缓存错误");

        private final int httpStatus;
        private final String description;

        ErrorCategory(int httpStatus, String description) {
            this.httpStatus = httpStatus;
            this.description = description;
        }

        public int getHttpStatus() {
            return httpStatus;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 常用异常工厂方法
     */
    public static class Factory {
        
        public static DetailedBusinessException authenticationFailed(String message) {
            return new DetailedBusinessException(ErrorCategory.AUTHENTICATION, "AUTH_001", message);
        }
        
        public static DetailedBusinessException authorizationFailed(String message) {
            return new DetailedBusinessException(ErrorCategory.AUTHORIZATION, "AUTH_002", message);
        }
        
        public static DetailedBusinessException validationFailed(String field, String message) {
            return new DetailedBusinessException(ErrorCategory.VALIDATION, "VAL_001", 
                String.format("字段 '%s' 验证失败: %s", field, message), field);
        }
        
        public static DetailedBusinessException resourceNotFound(String resourceType, String id) {
            return new DetailedBusinessException(ErrorCategory.RESOURCE_NOT_FOUND, "RES_001", 
                String.format("%s (ID: %s) 不存在", resourceType, id), resourceType, id);
        }
        
        public static DetailedBusinessException resourceConflict(String resourceType, String message) {
            return new DetailedBusinessException(ErrorCategory.RESOURCE_CONFLICT, "RES_002", 
                String.format("%s 冲突: %s", resourceType, message), resourceType);
        }
        
        public static DetailedBusinessException paymentFailed(String reason) {
            return new DetailedBusinessException(ErrorCategory.PAYMENT_FAILED, "PAY_001", 
                "支付处理失败: " + reason);
        }
        
        public static DetailedBusinessException verificationFailed(String type, String reason) {
            return new DetailedBusinessException(ErrorCategory.VERIFICATION_FAILED, "VER_001", 
                String.format("%s验证失败: %s", type, reason), type);
        }
        
        public static DetailedBusinessException externalServiceError(String serviceName, String message) {
            return new DetailedBusinessException(ErrorCategory.EXTERNAL_SERVICE, "EXT_001", 
                String.format("外部服务 '%s' 错误: %s", serviceName, message), serviceName);
        }
        
        public static DetailedBusinessException serviceTimeout(String serviceName, long timeoutMs) {
            return new DetailedBusinessException(ErrorCategory.TIMEOUT, "EXT_002", 
                String.format("服务 '%s' 超时 (%dms)", serviceName, timeoutMs), serviceName, timeoutMs);
        }
        
        public static DetailedBusinessException databaseError(String operation, String message) {
            return new DetailedBusinessException(ErrorCategory.DATABASE_ERROR, "DB_001", 
                String.format("数据库操作 '%s' 失败: %s", operation, message), operation);
        }
        
        public static DetailedBusinessException cacheError(String operation, String message) {
            return new DetailedBusinessException(ErrorCategory.CACHE_ERROR, "CACHE_001", 
                String.format("缓存操作 '%s' 失败: %s", operation, message), operation);
        }
        
        public static DetailedBusinessException systemError(String component, String message) {
            return new DetailedBusinessException(ErrorCategory.SYSTEM_ERROR, "SYS_001", 
                String.format("系统组件 '%s' 错误: %s", component, message), component);
        }
    }
}
