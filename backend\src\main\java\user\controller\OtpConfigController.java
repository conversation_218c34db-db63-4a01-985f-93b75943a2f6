package user.controller;

import system.controller.BaseController;
import core.common.ApiResponse;
import core.service.CoreService;
import common.service.payment.OtpVerificationService;
import core.service.SystemConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

import java.util.HashMap;
import java.util.Map;

/**
 * OTP配置控制器
 */
@RestController
@RequestMapping("/api/otp")
@CrossOrigin(origins = "*")
public class OtpConfigController extends BaseController {

    @Autowired
    private OtpVerificationService otpVerificationService;

    @Autowired
    private CoreService coreService;

    @Autowired
    private SystemConfigService systemConfigService;

    @GetMapping("/config")
    public ResponseEntity<Map<String, Object>> getOtpConfig() {
        // 使用CoreService获取统一配置
        Map<String, Object> config = coreService.getOtpConfiguration();
        config.put("available_methods", otpVerificationService.getAvailableVerificationMethods());
        config.put("recommended_method", "email");

        return ResponseEntity.ok(config);
    }

    /**
     * 获取可用的验证方式
     */
    @GetMapping("/methods")
    public ResponseEntity<Map<String, Object>> getAvailableMethods() {
        Map<String, Object> response = new HashMap<>();
        
        Map<String, Boolean> methods = otpVerificationService.getAvailableVerificationMethods();
        response.put("methods", methods);
        response.put("default_method", "email"); // 默认推荐邮箱验证
        
        // 添加方式描述
        Map<String, String> descriptions = new HashMap<>();
        descriptions.put("sms", "短信验证码");
        descriptions.put("email", "邮箱验证码");
        descriptions.put("app", "APP验证码（如Google Authenticator）");
        response.put("descriptions", descriptions);
        
        return ResponseEntity.ok(response);
    }



    /**
     * 获取已生成的验证码信息（管理员查看）
     */
    @GetMapping("/info")
    public Mono<ResponseEntity<Map<String, Object>>> getOtpInfo(
            @RequestParam String identifier,
            @RequestParam String method,
            @RequestParam String cardId) {

        return otpVerificationService.getOtpInfo(identifier, method, cardId)
            .map(otpInfo -> {
                Map<String, Object> response = new HashMap<>();
                response.put("success", true);
                response.put("data", otpInfo);
                return ResponseEntity.ok(response);
            })
            .switchIfEmpty(Mono.fromCallable(() -> {
                Map<String, Object> response = new HashMap<>();
                response.put("success", false);
                response.put("message", "验证码不存在或已过期");
                return ResponseEntity.ok(response);
            }));
    }

    /**
     * 验证OTP验证码
     */
    @PostMapping("/verify")
    public ResponseEntity<Map<String, Object>> verifyOtp(@RequestBody Map<String, String> request) {
        String identifier = request.get("identifier");
        String method = request.get("method");
        String cardId = request.get("card_id");
        String code = request.get("code");
        
        Map<String, Object> response = new HashMap<>();
        
        if (identifier == null || method == null || cardId == null || code == null) {
            response.put("success", false);
            response.put("message", "缺少必要参数");
            return ResponseEntity.badRequest().body(response);
        }

        boolean verified = otpVerificationService.verifyOtpCode(identifier, method, cardId, code);
        
        if (verified) {
            response.put("success", true);
            response.put("message", "验证成功");
        } else {
            response.put("success", false);
            response.put("message", "验证码错误或已过期");
        }
        
        return ResponseEntity.ok(response);
    }

    /**
     * 获取验证码配置参数（用于前端显示）
     */
    @GetMapping("/parameters")
    public ResponseEntity<Map<String, Object>> getOtpParameters() {
        Map<String, Object> parameters = new HashMap<>();

        parameters.put("code_expiry_minutes", systemConfigService.getCodeExpiryMinutes());
        parameters.put("max_attempts", systemConfigService.getMaxAttempts());

        // 添加用户友好的描述
        Map<String, String> descriptions = new HashMap<>();
        descriptions.put("code_expiry", String.format("验证码%d分钟内有效", systemConfigService.getCodeExpiryMinutes()));
        descriptions.put("max_attempts", String.format("每个验证码最多可尝试%d次", systemConfigService.getMaxAttempts()));
        descriptions.put("manual_sending", "验证码需要人工发送给用户");

        parameters.put("descriptions", descriptions);

        return ResponseEntity.ok(parameters);
    }

    /**
     * 健康检查 - 检查OTP服务状态
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> healthCheck() {
        Map<String, Object> health = new HashMap<>();
        
        Map<String, Boolean> methods = otpVerificationService.getAvailableVerificationMethods();
        boolean hasAnyMethod = methods.values().stream().anyMatch(enabled -> enabled);
        
        health.put("status", hasAnyMethod ? "healthy" : "warning");
        health.put("message", hasAnyMethod ? "OTP服务正常" : "没有启用任何验证方式");
        health.put("available_methods", methods);
        health.put("timestamp", System.currentTimeMillis());
        
        return ResponseEntity.ok(health);
    }
}
