package core.exception;

import core.common.ApiResponse;
import core.constants.AppConstants;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.web.reactive.error.ErrorWebExceptionHandler;
import org.springframework.core.annotation.Order;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ResponseStatusException;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import com.fasterxml.jackson.databind.ObjectMapper;
import java.nio.charset.StandardCharsets;

/**
 * 响应式全局异常处理器
 * 处理WebFlux环境下的所有异常
 */
@Component
@Order(-2)
public class ReactiveGlobalExceptionHandler implements ErrorWebExceptionHandler {

    private static final Logger logger = LoggerFactory.getLogger(ReactiveGlobalExceptionHandler.class);
    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public Mono<Void> handle(ServerWebExchange exchange, Throwable ex) {
        ServerHttpResponse response = exchange.getResponse();
        
        // 设置响应头
        response.getHeaders().setContentType(MediaType.APPLICATION_JSON);
        
        ApiResponse<Object> apiResponse;
        HttpStatus status;
        
        // 根据异常类型进行分类处理
        if (ex instanceof BusinessException) {
            BusinessException businessEx = (BusinessException) ex;
            status = HttpStatus.valueOf(businessEx.getCode());
            apiResponse = new ApiResponse<>(businessEx.getCode(), businessEx.getMessage(), null, false);
            logger.warn("业务异常: {} - {}", exchange.getRequest().getURI().getPath(), ex.getMessage());
            
        } else if (ex instanceof ResponseStatusException) {
            ResponseStatusException statusEx = (ResponseStatusException) ex;
            status = HttpStatus.valueOf(statusEx.getStatusCode().value());
            apiResponse = new ApiResponse<>(status.value(), statusEx.getReason(), null, false);
            logger.warn("HTTP状态异常: {} - {}", exchange.getRequest().getURI().getPath(), ex.getMessage());
            
        } else if (ex instanceof IllegalArgumentException) {
            status = HttpStatus.BAD_REQUEST;
            apiResponse = new ApiResponse<>(400, "参数错误: " + ex.getMessage(), null, false);
            logger.warn("参数异常: {} - {}", exchange.getRequest().getURI().getPath(), ex.getMessage());
            
        } else if (ex instanceof SecurityException) {
            status = HttpStatus.FORBIDDEN;
            apiResponse = new ApiResponse<>(403, "权限不足", null, false);
            logger.warn("安全异常: {} - {}", exchange.getRequest().getURI().getPath(), ex.getMessage());
            
        } else if (ex instanceof org.springframework.dao.DataAccessException) {
            status = HttpStatus.INTERNAL_SERVER_ERROR;
            apiResponse = new ApiResponse<>(500, "数据访问异常", null, false);
            logger.error("数据访问异常: {} - {}", exchange.getRequest().getURI().getPath(), ex.getMessage(), ex);
            
        } else if (ex instanceof org.springframework.web.server.ServerWebInputException) {
            status = HttpStatus.BAD_REQUEST;
            apiResponse = new ApiResponse<>(400, "请求参数格式错误", null, false);
            logger.warn("请求参数异常: {} - {}", exchange.getRequest().getURI().getPath(), ex.getMessage());
            
        } else if (ex instanceof java.util.concurrent.TimeoutException) {
            status = HttpStatus.REQUEST_TIMEOUT;
            apiResponse = new ApiResponse<>(408, "请求超时", null, false);
            logger.warn("请求超时: {} - {}", exchange.getRequest().getURI().getPath(), ex.getMessage());
            
        } else if (ex.getClass().getName().contains("ReactiveException")) {
            status = HttpStatus.INTERNAL_SERVER_ERROR;
            apiResponse = new ApiResponse<>(500, "响应式处理异常", null, false);
            logger.error("响应式异常: {} - {}", exchange.getRequest().getURI().getPath(), ex.getMessage(), ex);
            
        } else {
            // 未知异常
            status = HttpStatus.INTERNAL_SERVER_ERROR;
            apiResponse = new ApiResponse<>(
                AppConstants.HttpStatus.INTERNAL_SERVER_ERROR,
                AppConstants.ErrorMessage.SYSTEM_INTERNAL_ERROR,
                null,
                false
            );
            logger.error("未处理的异常: {} - {}", exchange.getRequest().getURI().getPath(), ex.getMessage(), ex);
        }
        
        response.setStatusCode(status);
        
        try {
            String responseBody = objectMapper.writeValueAsString(apiResponse);
            DataBuffer buffer = response.bufferFactory().wrap(responseBody.getBytes(StandardCharsets.UTF_8));
            return response.writeWith(Mono.just(buffer));
        } catch (Exception e) {
            logger.error("序列化异常响应失败", e);
            return response.setComplete();
        }
    }

    /**
     * 构建错误响应
     */
    private ApiResponse<Object> buildErrorResponse(int code, String message, String path, Throwable ex) {
        ApiResponse<Object> response = new ApiResponse<>(code, message, null, false);
        
        // 在开发环境下添加更多调试信息
        if (isDebugMode()) {
            response.setTimestamp(System.currentTimeMillis());
            response.setPath(path);
            if (ex != null) {
                response.setException(ex.getClass().getSimpleName());
            }
        }
        
        return response;
    }

    /**
     * 判断是否为调试模式
     */
    private boolean isDebugMode() {
        String profile = System.getProperty("spring.profiles.active", "");
        return profile.contains("dev") || profile.contains("test");
    }
}
