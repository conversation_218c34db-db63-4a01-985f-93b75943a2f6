package security.controller;

import system.controller.BaseController;
import domain.entity.Blacklist;
import domain.repository.BlacklistRepository;
import core.common.ApiResponse;

import common.service.cache.UnifiedCacheManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 统一黑名单管理控制器
 * 支持IP、用户、卡号、邮箱等多种类型的黑名单管理
 */
@RestController
@RequestMapping("/api/blacklist")
@CrossOrigin(origins = "*")
public class BlacklistController extends BaseController {
    
    private static final String BLACKLIST_KEY_PREFIX = "blacklist:";
    private static final String BLACKLIST_ID_SET_KEY = "blacklist:ids";

    @Autowired
    private BlacklistRepository blacklistRepository;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    private UnifiedCacheManager unifiedCacheManager;

    /**
     * 获取黑名单列表（分页）
     */
    @GetMapping
    public Mono<ResponseEntity<ApiResponse<Object>>> getBlacklistItems(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int pageSize,
            @RequestParam(required = false) String type,
            @RequestParam(required = false) String value,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String createdBy,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {

        return Mono.fromCallable(() -> {
            Pageable pageable = PageRequest.of(page - 1, pageSize, Sort.by("createdAt").descending());
            Blacklist.BlacklistStatus statusEnum = parseStatus(status);

            return buildQuery(type, value, startDate, endDate, statusEnum, pageable);
        })
        .flatMap(blacklistFlux ->
            // 将Flux转换为List并创建Page对象
            blacklistFlux.collectList()
                .map(blacklistList -> {
                    Pageable pageable = PageRequest.of(page - 1, pageSize, Sort.by("createdAt").descending());
                    Page<Blacklist> blacklistPage = new PageImpl<>(blacklistList, pageable, blacklistList.size());
                    return pageSuccess(blacklistPage, "获取黑名单列表成功");
                })
        )
        .onErrorResume(Exception.class, e -> {
            logger.error("获取黑名单列表失败", e);
            if (e.getMessage().contains("日期格式")) {
                return Mono.just(error("日期格式错误，请使用 yyyy-MM-dd 格式"));
            }
            return Mono.just(handleException(e, "获取黑名单列表"));
        });
    }

    /**
     * 获取黑名单统计信息
     */
    @GetMapping("/statistics")
    public Mono<ResponseEntity<ApiResponse<Object>>> getBlacklistStatistics() {
        // 计算今日开始时间
        LocalDateTime todayStart = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0).withNano(0);

        // 并行统计各种数据
        return Mono.zip(
            // 统计各类型的数量
            Flux.fromArray(Blacklist.BlacklistType.values())
                .flatMap(type ->
                    blacklistRepository.countByTypeAndStatus(type, Blacklist.BlacklistStatus.ACTIVE)
                        .map(count -> Map.entry(type.name().toLowerCase(), count))
                )
                .collectMap(Map.Entry::getKey, Map.Entry::getValue),
            // 统计今日新增
            blacklistRepository.countTodayAddedByStatus(todayStart, Blacklist.BlacklistStatus.ACTIVE),
            // 统计总数
            blacklistRepository.count()
        ).map(tuple -> {
            Map<String, Long> typeCounts = tuple.getT1();
            long todayAdded = tuple.getT2();
            long totalItems = tuple.getT3();

            long totalActive = typeCounts.values().stream().mapToLong(Long::longValue).sum();
            long expiredItems = totalItems - totalActive;

            Map<String, Object> statistics = new HashMap<>();
            statistics.put("totalItems", totalItems);
            statistics.put("activeItems", totalActive);
            statistics.put("expiredItems", expiredItems);
            statistics.put("todayAdded", todayAdded);
            
            // 各类型统计
            statistics.put("userBlacklist", typeCounts.getOrDefault("user", 0L));
            statistics.put("cardBlacklist", typeCounts.getOrDefault("card", 0L));
            statistics.put("ipBlacklist", typeCounts.getOrDefault("ip", 0L));
            statistics.put("emailBlacklist", typeCounts.getOrDefault("email", 0L));

            return success((Object) statistics);
        }).onErrorResume(Exception.class, e -> {
            logger.error("获取黑名单统计信息失败", e);
            return Mono.just(handleException(e, "获取黑名单统计"));
        });
    }

    /**
     * 添加黑名单项
     */
    @PostMapping
    public Mono<ResponseEntity<ApiResponse<Object>>> addBlacklistItem(@RequestBody Map<String, Object> request) {
        String type = request.get("type").toString().toUpperCase();
        String value = request.get("value").toString().trim();
        String reason = request.get("reason").toString();
        String description = request.get("description") != null ? request.get("description").toString() : "";
        String createdBy = request.get("createdBy") != null ? request.get("createdBy").toString() : "admin";

        // 验证类型
        Blacklist.BlacklistType blacklistType;
        try {
            blacklistType = Blacklist.BlacklistType.valueOf(type);
        } catch (IllegalArgumentException e) {
            return Mono.just(error("无效的黑名单类型: " + type));
        }

            // 检查是否已存在 - 需要改为响应式
            return blacklistRepository.existsByTypeAndValueAndStatus(blacklistType, value, Blacklist.BlacklistStatus.ACTIVE)
                .flatMap(exists -> {
                    if (exists) {
                        return Mono.just(error("该项目已在黑名单中"));
                    }

                    // 创建黑名单项
                    Blacklist blacklist = new Blacklist();
                    blacklist.setType(blacklistType);
                    blacklist.setValue(value);
                    blacklist.setReason(reason);
                    blacklist.setDescription(description);
                    blacklist.setCreatedBy(createdBy);
                    blacklist.setStatus(Blacklist.BlacklistStatus.ACTIVE);

                    // 保存到数据库
                    return blacklistRepository.save(blacklist)
                        .map(savedBlacklist -> success((Object) savedBlacklist, "添加成功"));
                })
                .onErrorResume(Exception.class, e -> {
                    logger.error("添加黑名单项失败", e);
                    return Mono.just(handleException((Exception) e, "添加黑名单项"));
                });
    }

    /**
     * 删除黑名单项（软删除）
     */
    @DeleteMapping("/{id}")
    public Mono<ResponseEntity<ApiResponse<Object>>> removeBlacklistItem(@PathVariable Long id) {
        return blacklistRepository.findById(id)
            .flatMap(blacklist -> {
                blacklist.markAsRemoved();
                return blacklistRepository.save(blacklist)
                    .doOnSuccess(savedBlacklist -> {
                        // 更新Redis
                        redisTemplate.opsForValue().set(BLACKLIST_KEY_PREFIX + id, savedBlacklist);
                    })
                    .map(savedBlacklist -> success(null, "删除成功"));
            })
            .switchIfEmpty(Mono.just(error(404, "黑名单项不存在")))
            .onErrorResume(Exception.class, e -> {
                logger.error("删除黑名单项失败", e);
                return Mono.just(handleException(e, "删除黑名单项"));
            });
    }

    /**
     * 更新黑名单项
     */
    @PutMapping("/{id}")
    public Mono<ResponseEntity<ApiResponse<Object>>> updateBlacklistItem(@PathVariable Long id, @RequestBody Map<String, Object> request) {
        return blacklistRepository.findById(id)
            .flatMap(blacklist -> {
                if (request.containsKey("reason")) {
                    blacklist.setReason(request.get("reason").toString());
                }
                if (request.containsKey("description")) {
                    blacklist.setDescription(request.get("description").toString());
                }
                if (request.containsKey("status")) {
                    String status = request.get("status").toString().toUpperCase();
                    blacklist.setStatus(Blacklist.BlacklistStatus.valueOf(status));
                }

                return blacklistRepository.save(blacklist)
                    .doOnSuccess(savedBlacklist -> {
                        // 更新Redis
                        redisTemplate.opsForValue().set(BLACKLIST_KEY_PREFIX + id, savedBlacklist);
                    })
                    .map(savedBlacklist -> success((Object) savedBlacklist, "更新成功"));
            })
            .switchIfEmpty(Mono.just(error(404, "黑名单项不存在")))
            .onErrorResume(Exception.class, e -> {
                logger.error("更新黑名单项失败", e);
                return Mono.just(handleException(e, "更新黑名单项"));
            });
    }

    /**
     * 检查是否在黑名单中
     */
    @GetMapping("/check")
    public Mono<ResponseEntity<ApiResponse<Object>>> checkBlacklist(
            @RequestParam String type,
            @RequestParam String value) {
        try {
            Blacklist.BlacklistType blacklistType = Blacklist.BlacklistType.valueOf(type.toUpperCase());

            return blacklistRepository.findByTypeAndValueAndStatus(blacklistType, value, Blacklist.BlacklistStatus.ACTIVE)
                .map(blacklist -> {
                    Map<String, Object> result = new HashMap<>();
                    result.put("isBlacklisted", true);
                    result.put("reason", blacklist.getReason());
                    result.put("createdAt", blacklist.getCreatedAt());
                    return success((Object) result);
                })
                .switchIfEmpty(Mono.defer(() -> {
                    Map<String, Object> result = new HashMap<>();
                    result.put("isBlacklisted", false);
                    return Mono.just(success((Object) result));
                }))
                .onErrorResume(Exception.class, e -> {
                    logger.error("检查黑名单失败", e);
                    return Mono.just(handleException((Exception) e, "检查黑名单"));
                });
        } catch (Exception e) {
            return Mono.just(handleException(e, "检查黑名单"));
        }
    }

    /**
     * 批量删除黑名单项
     */
    @PostMapping("/batch-remove")
    public Mono<ResponseEntity<ApiResponse<Object>>> batchRemoveBlacklistItems(@RequestBody Map<String, Object> request) {
        @SuppressWarnings("unchecked")
        List<Long> ids = (List<Long>) request.get("ids");

        return Flux.fromIterable(ids)
            .flatMap(id ->
                blacklistRepository.findById(id)
                    .flatMap(blacklist -> {
                        blacklist.markAsRemoved();
                        return blacklistRepository.save(blacklist)
                            .doOnSuccess(savedBlacklist -> {
                                // 更新Redis
                                redisTemplate.opsForValue().set(BLACKLIST_KEY_PREFIX + id, savedBlacklist);
                            })
                            .map(savedBlacklist -> 1); // 返回1表示成功删除
                    })
                    .onErrorReturn(0) // 返回0表示删除失败
            )
            .reduce(0, Integer::sum) // 统计成功删除的数量
            .map(removedCount -> {
                Map<String, Object> result = new HashMap<>();
                result.put("removedCount", removedCount);
                result.put("totalRequested", ids.size());
                return success((Object) result, "批量删除完成");
            })
            .onErrorResume(Exception.class, e -> {
                logger.error("批量删除黑名单项失败", e);
                return Mono.just(handleException(e, "批量删除黑名单项"));
            });
    }

    // 辅助方法：解析状态
    private Blacklist.BlacklistStatus parseStatus(String status) {
        return status != null ?
            Blacklist.BlacklistStatus.valueOf(status.toUpperCase()) :
            Blacklist.BlacklistStatus.ACTIVE;
    }

    // 辅助方法：构建查询
    private Flux<Blacklist> buildQuery(String type, String value, String startDate, String endDate,
                                      Blacklist.BlacklistStatus statusEnum, Pageable pageable) {

        // 优先级：类型+值 > 类型 > 值 > 时间范围 > 全部
        if (hasTypeAndValue(type, value)) {
            return queryByTypeAndValue(type, value, statusEnum, pageable);
        }

        if (hasType(type)) {
            return queryByType(type, statusEnum, pageable);
        }

        if (hasValue(value)) {
            return queryByValue(value, statusEnum, pageable);
        }

        if (hasDateRange(startDate, endDate)) {
            return queryByDateRange(startDate, endDate, statusEnum, pageable);
        }

        return blacklistRepository.findByStatusOrderByCreatedAtDesc(statusEnum, pageable);
    }

    // 条件检查方法
    private boolean hasTypeAndValue(String type, String value) {
        return type != null && value != null && !value.trim().isEmpty();
    }

    private boolean hasType(String type) {
        return type != null;
    }

    private boolean hasValue(String value) {
        return value != null && !value.trim().isEmpty();
    }

    private boolean hasDateRange(String startDate, String endDate) {
        return startDate != null && !startDate.trim().isEmpty() &&
               endDate != null && !endDate.trim().isEmpty();
    }

    // 查询方法
    private Flux<Blacklist> queryByTypeAndValue(String type, String value,
                                               Blacklist.BlacklistStatus statusEnum, Pageable pageable) {
        Blacklist.BlacklistType typeEnum = Blacklist.BlacklistType.valueOf(type.toUpperCase());
        return blacklistRepository.findByTypeAndValueContainingAndStatus(typeEnum, value.trim(), statusEnum, pageable);
    }

    private Flux<Blacklist> queryByType(String type, Blacklist.BlacklistStatus statusEnum, Pageable pageable) {
        Blacklist.BlacklistType typeEnum = Blacklist.BlacklistType.valueOf(type.toUpperCase());
        return blacklistRepository.findByTypeAndStatusOrderByCreatedAtDesc(typeEnum, statusEnum, pageable);
    }

    private Flux<Blacklist> queryByValue(String value, Blacklist.BlacklistStatus statusEnum, Pageable pageable) {
        return blacklistRepository.findByValueContainingAndStatus(value.trim(), statusEnum, pageable);
    }

    private Flux<Blacklist> queryByDateRange(String startDate, String endDate,
                                            Blacklist.BlacklistStatus statusEnum, Pageable pageable) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime start = LocalDateTime.parse(startDate.trim() + " 00:00:00", formatter);
        LocalDateTime end = LocalDateTime.parse(endDate.trim() + " 23:59:59", formatter);
        return blacklistRepository.findByCreatedAtBetweenAndStatus(start, end, statusEnum, pageable);
    }
}
