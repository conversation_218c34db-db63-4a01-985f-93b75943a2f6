package domain.repository;

import domain.entity.User;
import org.springframework.data.r2dbc.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.List;
import java.util.Optional;

@Repository
public interface UserRepository extends BaseRepository<User, Long> {

    /**
     * 根据用户名查找用户
     */
    @Query("SELECT * FROM users WHERE username = ?")
    Mono<User> findByUsername(String username);

    /**
     * 根据邮箱查找用户
     */
    @Query("SELECT * FROM users WHERE email = ?")
    Mono<User> findByEmail(String email);

    /**
     * 根据状态查找用户
     */
    @Query("SELECT * FROM users WHERE status = ?")
    Flux<User> findByStatus(String status);

    /**
     * 统计指定状态的用户数量
     */
    @Query("SELECT COUNT(*) FROM users WHERE status = ?")
    Mono<Long> countByStatus(String status);

    /**
     * 根据角色查找用户
     */
    @Query("SELECT * FROM users WHERE role = ?")
    Flux<User> findByRole(String role);

    /**
     * 统计指定角色的用户数量
     */
    @Query("SELECT COUNT(*) FROM users WHERE role = ?")
    Mono<Long> countByRole(String role);

    /**
     * 根据用户名和邮箱查找用户（用于检查重复）
     */
    @Query("SELECT * FROM users WHERE username = ? OR email = ?")
    Flux<User> findByUsernameOrEmail(String username, String email);
}
