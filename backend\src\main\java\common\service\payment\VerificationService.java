package common.service.payment;

import domain.entity.VerificationSetting;
import org.springframework.data.domain.Page;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

//3D安全验证服务接口
public interface VerificationService {

    //创建或更新验证设置
    Mono<VerificationSetting> saveVerificationSetting(String transactionId,
                                               String method,
                                               String customPhone,
                                               String customEmail,
                                               String message,
                                               boolean requireAdminVerification);

    //获取交易的验证设置
    Mono<VerificationSetting> getVerificationSetting(String transactionId);

    //将验证设置转换为前端所需的Map格式
    Map<String, Object> convertToMap(VerificationSetting setting);

    //手动验证交易
    Mono<VerificationSetting> manualVerify(String transactionId, boolean success, String message);

    //获取所有待验证的交易
    Flux<VerificationSetting> getAllPendingVerifications();

    //获取验证统计信息
    Mono<Map<String, Object>> getVerificationStats();

    //分页查询验证设置
    Mono<Page<VerificationSetting>> findVerifications(
            int page,
            int size,
            String query,
            String status,
            LocalDateTime startTime,
            LocalDateTime endTime);
}
