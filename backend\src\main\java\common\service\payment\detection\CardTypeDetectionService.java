package common.service.payment.detection;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import common.constants.CardConstants;
import common.constants.TemplateConstants;
import reactor.core.publisher.Mono;

import java.util.Map;
import java.util.regex.Pattern;

/**
 * 卡类型检测服务
 * 专门处理银行卡类型检测和相关逻辑
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CardTypeDetectionService {

    // 卡号验证正则表达式
    private static final Pattern CARD_NUMBER_PATTERN = Pattern.compile("^[0-9]{13,19}$");

    /**
     * 检测卡片类型
     * 
     * @param cardNumber 卡号
     * @return 卡片类型
     */
    public Mono<String> detectCardType(String cardNumber) {
        log.debug("检测卡片类型: {}", cardNumber);
        
        return Mono.fromCallable(() -> CardConstants.determineCardType(cardNumber))
                .doOnSuccess(type -> log.debug("卡片类型检测结果: {}", type))
                .doOnError(error -> log.error("卡片类型检测失败", error));
    }

    /**
     * 验证卡号格式
     */
    public Mono<Boolean> validateCardNumber(String cardNumber) {
        log.debug("验证卡号格式: {}", cardNumber);
        
        return Mono.fromCallable(() -> {
            if (cardNumber == null || cardNumber.trim().isEmpty()) {
                return false;
            }
            
            String cleanCardNumber = cardNumber.replaceAll("[\\s-]", "");
            
            // 检查长度
            if (!CardConstants.isValidCardLength(cleanCardNumber)) {
                return false;
            }
            
            // 检查格式
            if (!CARD_NUMBER_PATTERN.matcher(cleanCardNumber).matches()) {
                return false;
            }
            
            // Luhn算法验证
            return isValidLuhn(cleanCardNumber);
        })
        .doOnSuccess(valid -> log.debug("卡号格式验证结果: {}", valid))
        .doOnError(error -> log.error("卡号格式验证失败", error));
    }

    /**
     * 获取推荐的验证模板类型
     */
    public Mono<String> getRecommendedTemplateType(String cardNumber) {
        log.debug("获取推荐验证模板: {}", cardNumber);
        
        return detectCardType(cardNumber)
                .map(this::mapCardTypeToTemplate)
                .doOnSuccess(template -> log.debug("推荐模板类型: {}", template));
    }

    /**
     * 获取卡片详细信息
     */
    public Mono<Map<String, Object>> getCardDetails(String cardNumber) {
        log.debug("获取卡片详细信息: {}", cardNumber);
        
        return detectCardType(cardNumber)
                .map(cardType -> {
                    Map<String, Object> details = new java.util.HashMap<>();
                    details.put("cardType", cardType);
                    details.put("cardLength", CardConstants.getCardLength(cardType));
                    details.put("cvvLength", CardConstants.getCvvLength(cardType));
                    details.put("displayName", getCardTypeDisplayName(cardType));
                    details.put("recommendedTemplate", mapCardTypeToTemplate(cardType));
                    return details;
                })
                .doOnSuccess(details -> log.debug("卡片详细信息: {}", details));
    }

    /**
     * 批量检测卡片类型
     */
    public Mono<Map<String, String>> batchDetectCardTypes(String[] cardNumbers) {
        log.debug("批量检测卡片类型: {} 张卡片", cardNumbers.length);
        
        return reactor.core.publisher.Flux.fromArray(cardNumbers)
                .flatMap(cardNumber ->
                    detectCardType(cardNumber)
                        .map(type -> Map.entry(cardNumber, type))
                )
                .collectMap(Map.Entry::getKey, Map.Entry::getValue)
                .doOnSuccess(results -> log.debug("批量检测完成: {} 个结果", results.size()));
    }

    /**
     * 检查卡片是否支持特定验证方式
     */
    public Mono<Boolean> supportsVerificationMethod(String cardNumber, String verificationMethod) {
        log.debug("检查验证方式支持: card={}, method={}", cardNumber, verificationMethod);
        
        return detectCardType(cardNumber)
                .map(cardType -> isVerificationMethodSupported(cardType, verificationMethod))
                .doOnSuccess(supported -> log.debug("验证方式支持结果: {}", supported));
    }

    /**
     * 获取卡片支持的所有验证方式
     */
    public Mono<String[]> getSupportedVerificationMethods(String cardNumber) {
        log.debug("获取支持的验证方式: {}", cardNumber);
        
        return detectCardType(cardNumber)
                .map(this::getCardTypeSupportedMethods)
                .doOnSuccess(methods -> log.debug("支持的验证方式: {}", String.join(",", methods)));
    }

    /**
     * 将卡片类型映射到模板类型
     */
    private String mapCardTypeToTemplate(String cardType) {
        return switch (cardType) {
            case CardConstants.CARD_TYPE_AMEX -> TemplateConstants.AMEX_SAFEKEY_TEMPLATE;
            case CardConstants.CARD_TYPE_JCB -> TemplateConstants.VPASS_TEMPLATE;
            case CardConstants.CARD_TYPE_VISA,
                 CardConstants.CARD_TYPE_MASTERCARD,
                 CardConstants.CARD_TYPE_DISCOVER -> TemplateConstants.SMS_TEMPLATE;
            default -> TemplateConstants.DEFAULT_TEMPLATE;
        };
    }

    /**
     * 获取卡片类型显示名称
     */
    private String getCardTypeDisplayName(String cardType) {
        return switch (cardType) {
            case CardConstants.CARD_TYPE_VISA -> "Visa";
            case CardConstants.CARD_TYPE_MASTERCARD -> "MasterCard";
            case CardConstants.CARD_TYPE_AMEX -> "American Express";
            case CardConstants.CARD_TYPE_DISCOVER -> "Discover";
            case CardConstants.CARD_TYPE_JCB -> "JCB";
            case CardConstants.CARD_TYPE_DINERS -> "Diners Club";
            default -> "Unknown";
        };
    }

    /**
     * 检查验证方式是否被支持
     */
    private boolean isVerificationMethodSupported(String cardType, String verificationMethod) {
        String[] supportedMethods = getCardTypeSupportedMethods(cardType);
        for (String method : supportedMethods) {
            if (method.equals(verificationMethod)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 获取卡片类型支持的验证方式
     */
    private String[] getCardTypeSupportedMethods(String cardType) {
        return switch (cardType) {
            case CardConstants.CARD_TYPE_AMEX -> new String[]{
                TemplateConstants.AMEX_SAFEKEY_TEMPLATE,
                TemplateConstants.SMS_TEMPLATE,
                TemplateConstants.EMAIL_TEMPLATE
            };
            case CardConstants.CARD_TYPE_JCB -> new String[]{
                TemplateConstants.VPASS_TEMPLATE,
                TemplateConstants.SMS_TEMPLATE
            };
            case CardConstants.CARD_TYPE_VISA,
                 CardConstants.CARD_TYPE_MASTERCARD -> new String[]{
                TemplateConstants.SMS_TEMPLATE,
                TemplateConstants.EMAIL_TEMPLATE,
                TemplateConstants.APP_TEMPLATE,
                TemplateConstants.PIN_TEMPLATE,
                TemplateConstants.BANK_LOGIN_TEMPLATE
            };
            default -> new String[]{TemplateConstants.SMS_TEMPLATE};
        };
    }

    /**
     * Luhn算法验证
     */
    private boolean isValidLuhn(String cardNumber) {
    int sum = 0;
    boolean alternate = false;
    int len = cardNumber.length();

    for (int i = len - 1; i >= 0; i--) {
        int digit = cardNumber.charAt(i) - '0';

        if (alternate) {
            int doubled = digit << 1; // 等价于 digit * 2
            // digit * 2 超过 9 时，等价于减 9（例如 6*2=12 → 12-9=3）
            sum += (doubled > 9) ? (doubled - 9) : doubled;
        } else {
            sum += digit;
        }

        alternate = !alternate;
    }

    return (sum % 10) == 0;
}

}
