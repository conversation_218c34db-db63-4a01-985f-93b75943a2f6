<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Transaction Cancelled - 3D Secure</title>
    <link rel="stylesheet" href="assets/css/common.css">
    <link rel="stylesheet" href="assets/css/payment.css">
    <style>
        .error-container {
            text-align: center;
            padding: 40px 20px;
        }
        
        .error-icon {
            width: 60px;
            height: 60px;
            background-color: #dc2626;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            color: white;
            font-size: 24px;
            font-weight: bold;
        }
        
        .error-title {
            color: #dc2626;
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 20px;
        }
        
        .error-message {
            color: #374151;
            font-size: 14px;
            line-height: 1.6;
            margin-bottom: 15px;
            max-width: 400px;
            margin-left: auto;
            margin-right: auto;
        }
        
        .continue-btn {
            background-color: #4b5563;
            color: white;
            padding: 12px 40px;
            border: none;
            border-radius: 4px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            margin-top: 20px;
            transition: background-color 0.3s ease;
        }
        
        .continue-btn:hover {
            background-color: #374151;
        }
        
        .bank-logo-cancelled {
            width: 60px;
            height: 30px;
            background-image: url('assets/img/bank.png');
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
        }
        
        .cancelled-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 40px;
            padding: 20px 0;
        }
        
        .visa-logo-cancelled {
            width: 80px;
            height: 32px;
            background-image: url('assets/img/visa.png');
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
        }
    </style>
</head>
<body class="bg-gray-100 min-h-screen">
    <div class="main-container">
        <!-- Main Content -->
        <div class="px-6 py-8 min-h-screen flex flex-col">
            <!-- Header with Bank and VISA logos -->
            <div class="cancelled-header">
                <div class="bank-logo-cancelled"></div>
                <div class="visa-logo-cancelled"></div>
            </div>
            
            <!-- Error Content -->
            <div class="error-container">
                <div class="error-icon">
                    !
                </div>
                
                <h1 class="error-title">
                    Transaction Cancelled
                </h1>
                
                <p class="error-message">
                    We can't authenticate you at this time.
                </p>

                <p class="error-message">
                    Please check that your bank have your correct mobile number, then please try your transaction again.
                </p>

                <p class="error-message">
                    If you're still having trouble authenticating you, please call your bank using the telephone number on the back of your card and quote "Online Secure".
                </p>
                
                <button class="continue-btn" onclick="continueTransaction()">
                    Continue
                </button>
            </div>
            
            <!-- Spacer -->
            <div class="flex-1"></div>
            
            <!-- Help Section -->
            <div class="help-section">
                <button 
                    class="help-button-new"
                    onclick="toggleCancelledExpand('help-section')"
                >
                    <span>Help</span>
                    <span class="help-icon" id="help-section-icon">+</span>
                </button>
                <div id="help-section" class="expandable">
                    <div class="py-3 text-sm text-gray-600 leading-relaxed">
                        Learn More
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Toggle expandable sections
        function toggleCancelledExpand(sectionId) {
            const section = document.getElementById(sectionId);
            const icon = document.getElementById(sectionId + '-icon');
            
            if (section.classList.contains('expanded')) {
                section.classList.remove('expanded');
                icon.textContent = '+';
                icon.classList.remove('rotated');
            } else {
                section.classList.add('expanded');
                icon.textContent = '+';
                icon.classList.add('rotated');
            }
        }
        
        // Continue button functionality
        function continueTransaction() {
            // Redirect back to navigation or close window
            if (confirm('Return to transaction selection?')) {
                window.location.href = 'navigation.html';
            }
        }
    </script>
</body>
</html>
