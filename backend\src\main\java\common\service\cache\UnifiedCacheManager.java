package common.service.cache;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;
import common.config.CacheKeyPrefixConfig;

/**
 * 统一缓存管理器
 */
@Service
public class UnifiedCacheManager {

    private static final Logger logger = LoggerFactory.getLogger(UnifiedCacheManager.class);

    @Autowired
    private ReactiveCacheService reactiveCacheService;

    @Autowired
    private TransactionalCacheManager transactionalCacheManager;

    @Autowired
    private CacheOperationTemplate cacheOperationTemplate;

    @Autowired
    private CacheKeyPrefixConfig cacheKeyPrefixConfig;

    /**
     * OTP缓存操作
     */
    public Mono<Boolean> setOtpCache(String otpKey, String code, Duration ttl) {
        String cacheKey = cacheKeyPrefixConfig.buildOtpCodeKey(otpKey);
        return reactiveCacheService.set(cacheKey, code, ttl)
                .doOnSuccess(result -> logger.debug("OTP缓存设置: key={}, success={}", otpKey, result));
    }

    public Mono<String> getOtpCache(String otpKey) {
        String cacheKey = cacheKeyPrefixConfig.buildOtpCodeKey(otpKey);
        return reactiveCacheService.get(cacheKey, String.class)
                .doOnSuccess(result -> logger.debug("OTP缓存获取: key={}, found={}", otpKey, result != null));
    }

    public Mono<Boolean> deleteOtpCache(String otpKey) {
        String cacheKey = cacheKeyPrefixConfig.buildOtpCodeKey(otpKey);
        return cacheOperationTemplate.evictCache(cacheKey);
    }

    /**
     * OTP信息缓存操作
     */
    public Mono<Boolean> setOtpInfoCache(String otpKey, Map<String, Object> otpInfo, Duration ttl) {
        String cacheKey = cacheKeyPrefixConfig.buildOtpInfoKey(otpKey);
        return reactiveCacheService.setHash(cacheKey, otpInfo, ttl)
                .doOnSuccess(result -> logger.debug("OTP信息缓存设置: key={}, success={}", otpKey, result));
    }

    public Mono<Map<String, Object>> getOtpInfoCache(String otpKey) {
        String cacheKey = cacheKeyPrefixConfig.buildOtpInfoKey(otpKey);
        return reactiveCacheService.getHash(cacheKey)
                .map(map -> {
                    Map<String, Object> result = new HashMap<>();
                    if (map != null) {
                        map.forEach((k, v) -> result.put(k.toString(), v));
                    }
                    return result;
                })
                .doOnSuccess(result -> logger.debug("OTP信息缓存获取: key={}, found={}", otpKey, result != null));
    }

    /**
     * 黑名单缓存操作
     */
    public Mono<Boolean> setBlacklistCache(String id, Object blacklist, Duration ttl) {
        String cacheKey = cacheKeyPrefixConfig.buildBlacklistKey("ip", id);
        return reactiveCacheService.set(cacheKey, blacklist, ttl)
                .doOnSuccess(result -> logger.debug("黑名单缓存设置: id={}, success={}", id, result));
    }

    public <T> Mono<T> getBlacklistCache(String id, Class<T> clazz) {
        String cacheKey = cacheKeyPrefixConfig.buildBlacklistKey("ip", id);
        return reactiveCacheService.get(cacheKey, clazz)
                .doOnSuccess(result -> logger.debug("黑名单缓存获取: id={}, found={}", id, result != null));
    }

    public Mono<Boolean> deleteBlacklistCache(String id) {
        String cacheKey = cacheKeyPrefixConfig.buildBlacklistKey("ip", id);
        return cacheOperationTemplate.evictCache(cacheKey);
    }

    /**
     * 域名缓存操作
     */
    public Mono<Boolean> setDomainCache(String domainName, Object domain, Duration ttl) {
        String cacheKey = cacheKeyPrefixConfig.buildDomainKey(domainName);
        return reactiveCacheService.set(cacheKey, domain, ttl)
                .doOnSuccess(result -> logger.debug("域名缓存设置: domain={}, success={}", domainName, result));
    }

    public <T> Mono<T> getDomainCache(String domainName, Class<T> clazz) {
        String cacheKey = cacheKeyPrefixConfig.buildDomainKey(domainName);
        return reactiveCacheService.get(cacheKey, clazz)
                .doOnSuccess(result -> logger.debug("域名缓存获取: domain={}, found={}", domainName, result != null));
    }

    /**
     * 用户缓存操作
     */
    public Mono<Boolean> setUserCache(String userId, Object user, Duration ttl) {
        String cacheKey = cacheKeyPrefixConfig.buildUserKey(userId);
        return reactiveCacheService.set(cacheKey, user, ttl)
                .doOnSuccess(result -> logger.debug("用户缓存设置: userId={}, success={}", userId, result));
    }

    public <T> Mono<T> getUserCache(String userId, Class<T> clazz) {
        String cacheKey = cacheKeyPrefixConfig.buildUserKey(userId);
        return reactiveCacheService.get(cacheKey, clazz)
                .doOnSuccess(result -> logger.debug("用户缓存获取: userId={}, found={}", userId, result != null));
    }

    /**
     * BIN查询缓存操作
     */
    public Mono<Boolean> setBinLookupCache(String bin, Object binInfo, Duration ttl) {
        String cacheKey = cacheKeyPrefixConfig.buildBinLookupKey(bin);
        return reactiveCacheService.set(cacheKey, binInfo, ttl)
                .doOnSuccess(result -> logger.debug("BIN查询缓存设置: bin={}, success={}", bin, result));
    }

    public <T> Mono<T> getBinLookupCache(String bin, Class<T> clazz) {
        String cacheKey = cacheKeyPrefixConfig.buildBinLookupKey(bin);
        return reactiveCacheService.get(cacheKey, clazz)
                .doOnSuccess(result -> logger.debug("BIN查询缓存获取: bin={}, found={}", bin, result != null));
    }

    /**
     * 通用缓存操作
     */
    public <T> Mono<Boolean> setCache(String key, T value, Duration ttl) {
        return reactiveCacheService.set(key, value, ttl);
    }

    public <T> Mono<T> getCache(String key, Class<T> clazz) {
        return reactiveCacheService.get(key, clazz);
    }

    public Mono<Boolean> deleteCache(String key) {
        return reactiveCacheService.delete(key);
    }

    /**
     * 事务性缓存操作
     */
    public <T> Mono<T> getWithFallback(String key, Class<T> type, 
                                       java.util.function.Supplier<Mono<T>> fallback, 
                                       Duration ttl) {
        return transactionalCacheManager.getWithFallback(key, type, fallback, ttl);
    }

    /**
     * 缓存预热
     */
    public <T> Mono<Void> warmupCache(String key, 
                                      java.util.function.Supplier<Mono<T>> dataLoader, 
                                      Duration ttl) {
        return transactionalCacheManager.warmupCache(key, dataLoader, ttl);
    }

    /**
     * 构建缓存键
     */
    public String buildCacheKey(String prefix, String... parts) {
        return prefix + String.join(":", parts);
    }
}
