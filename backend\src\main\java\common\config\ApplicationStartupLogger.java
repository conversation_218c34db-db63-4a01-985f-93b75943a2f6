package common.config;

import core.service.SystemConfigService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;

import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.Arrays;

@Configuration
public class ApplicationStartupLogger {

    private static final Logger log = LoggerFactory.getLogger(ApplicationStartupLogger.class);

    @Value("${server.port:8080}")
    private int serverPort;

    @Autowired
    private SystemConfigService systemConfigService;

    @Bean
    public CommandLineRunner logApplicationStartup(Environment env) {
        return args -> {
            // 初始化系统配置
            try {
                log.info("开始初始化系统配置...");
                systemConfigService.loadSecurityConfigFromDatabase()
                    .doOnSuccess(unused -> log.info("系统配置初始化完成"))
                    .doOnError(error -> log.error("系统配置初始化失败", error))
                    .subscribe();
            } catch (Exception e) {
                log.error("配置初始化过程中发生异常", e);
            }

            String protocol = "http";
            if (env.getProperty("server.ssl.key-store") != null) {
                protocol = "https";
            }

            String hostAddress = "localhost";
            try {
                hostAddress = InetAddress.getLocalHost().getHostAddress();
            } catch (UnknownHostException e) {
                log.warn("The host name could not be determined, using 'localhost' as fallback");
            }

            String activeProfiles = Arrays.toString(env.getActiveProfiles());
            String defaultProfiles = Arrays.toString(env.getDefaultProfiles());

            log.info(
                "\n----------------------------------------------------------\n\t" +
                "应用 '{}' 已启动! 访问地址:\n\t" +
                "本地: \t\t{}://localhost:{}\n\t" +
                "外部: \t{}://{}:{}\n\t" +
                "Profile(s): \t{} (默认: {})\n" +
                "----------------------------------------------------------",
                env.getProperty("spring.application.name", "BakaOTP"),
                protocol,
                serverPort,
                protocol,
                hostAddress,
                serverPort,
                activeProfiles.isEmpty() ? "default" : activeProfiles,
                defaultProfiles
            );
        };
    }
}
