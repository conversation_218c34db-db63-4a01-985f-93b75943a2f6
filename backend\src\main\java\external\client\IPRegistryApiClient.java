package external.client;

import core.common.ApiResponse;
import external.dto.IPRegistryResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.time.Duration;

/**
 * IPRegistry API客户端
 * 用于检测IP地址的类型和威胁信息
 * 
 * 支持检测的IP类型：
 * - 代理 (Proxy)
 * - VPN
 * - 匿名 (Anonymous)
 * - 云服务商 (Cloud Service Provider)
 * - 中继服务器 (Relay Server)
 * - 威胁 (Threat)
 * - 滥用者 (Abuser)
 * - 攻击者 (Attacker)
 * - Tor网络 (Tor Network)
 * - Tor出口节点 (Tor Exit Node)
 */
@Slf4j
@Component
public class IPRegistryApiClient {

    @Value("${external.api.ip-registry.enabled:false}")
    private boolean enabled;

    @Value("${external.api.ip-registry.api-key:}")
    private String apiKey;

    @Value("${external.api.ip-registry.base-url:https://api.ipregistry.co}")
    private String baseUrl;

    @Value("${external.api.ip-registry.timeout:5000}")
    private int timeout;

    @Value("${external.api.ip-registry.retry-count:2}")
    private int retryCount;

    private final WebClient webClient;

    public IPRegistryApiClient() {
        this.webClient = WebClient.builder()
                .codecs(configurer -> configurer.defaultCodecs().maxInMemorySize(1024 * 1024))
                .build();
    }

    /**
     * 查询IP地址信息
     *
     * @param ip IP地址
     * @return IP信息查询结果
     */
    public Mono<ApiResponse<IPRegistryResult>> queryIP(String ip) {
        if (!enabled) {
            log.debug("IPRegistry API未启用");
            return Mono.just(ApiResponse.error(503, "IPRegistry API未启用", null));
        }

        if (apiKey == null || apiKey.trim().isEmpty()) {
            log.warn("IPRegistry API Key未配置");
            return Mono.just(ApiResponse.error(401, "IPRegistry API Key未配置", null));
        }

        if (!isValidIP(ip)) {
            log.warn("无效的IP地址: {}", ip);
            return Mono.just(ApiResponse.error(400, "无效的IP地址", null));
        }

        log.debug("查询IP地址信息: {}", ip);

        String url = String.format("%s/%s?key=%s&fields=security", baseUrl, ip, apiKey);

        return webClient.get()
                .uri(url)
                .retrieve()
                .bodyToMono(IPRegistryResult.class)
                .timeout(Duration.ofMillis(timeout))
                .map(response -> {
                    log.debug("IPRegistry查询成功: {}", ip);
                    return ApiResponse.success(response);
                })
                .retry(retryCount)
                .onErrorResume(Exception.class, e -> {
                    log.error("IPRegistry查询失败: {} - {}", ip, e.getMessage(), e);
                    return Mono.just(ApiResponse.error(500, "IPRegistry查询失败: " + e.getMessage(), null));
                });
    }

    /**
     * 批量查询IP地址信息
     *
     * @param ips IP地址列表
     * @return 批量查询结果
     */
    public Mono<ApiResponse<IPRegistryResult[]>> batchQueryIP(String[] ips) {
        if (!enabled) {
            log.debug("IPRegistry API未启用");
            return Mono.just(ApiResponse.error(503, "IPRegistry API未启用", null));
        }

        if (apiKey == null || apiKey.trim().isEmpty()) {
            log.warn("IPRegistry API Key未配置");
            return Mono.just(ApiResponse.error(401, "IPRegistry API Key未配置", null));
        }

        if (ips == null || ips.length == 0) {
            log.warn("IP地址列表为空");
            return Mono.just(ApiResponse.error(400, "IP地址列表为空", null));
        }

        log.debug("批量查询IP地址信息，数量: {}", ips.length);

        String ipList = String.join(",", ips);
        String url = String.format("%s/%s?key=%s&fields=security", baseUrl, ipList, apiKey);

        return webClient.get()
                .uri(url)
                .retrieve()
                .bodyToMono(IPRegistryResult[].class)
                .timeout(Duration.ofMillis(timeout * 2)) // 批量查询超时时间加倍
                .map(responses -> {
                    log.debug("IPRegistry批量查询成功，数量: {}", responses.length);
                    return ApiResponse.success(responses);
                })
                .retry(retryCount)
                .onErrorResume(Exception.class, e -> {
                    log.error("IPRegistry批量查询失败: {}", e.getMessage(), e);
                    return Mono.just(ApiResponse.error(500, "IPRegistry批量查询失败: " + e.getMessage(), null));
                });
    }

    /**
     * 验证IP地址格式
     *
     * @param ip IP地址
     * @return 是否有效
     */
    private boolean isValidIP(String ip) {
        if (ip == null || ip.trim().isEmpty()) {
            return false;
        }

        // 简单的IP地址格式验证
        String[] parts = ip.split("\\.");
        if (parts.length != 4) {
            return false;
        }

        try {
            for (String part : parts) {
                int num = Integer.parseInt(part);
                if (num < 0 || num > 255) {
                    return false;
                }
            }
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    /**
     * 检查API是否可用
     *
     * @return 是否可用
     */
    public boolean isAvailable() {
        return enabled && apiKey != null && !apiKey.trim().isEmpty();
    }
}
