package auth.controller;

import system.controller.BaseController;
import core.common.ApiResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.cors.reactive.CorsConfigurationSource;
import security.service.DomainService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.stream.Collectors;

/**
 * CORS配置管理控制器
 * 提供动态CORS配置管理功能
 */
@RestController
@RequestMapping("/api/config/cors")
@CrossOrigin(origins = "*")
public class CorsConfigController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(CorsConfigController.class);

    @Autowired
    private auth.config.DynamicCorsConfigurationSource dynamicCorsConfigurationSource;

    /**
     * 获取当前CORS配置
     */
    @GetMapping
    public ResponseEntity<ApiResponse<Map<String, Object>>> getCorsConfig() {
        try {
            var corsConfig = dynamicCorsConfigurationSource.getCurrentConfiguration();

            Map<String, Object> config = new HashMap<>();
            config.put("allowedOrigins", corsConfig.getAllowedOriginPatterns());
            config.put("allowedMethods", corsConfig.getAllowedMethods());
            config.put("allowedHeaders", corsConfig.getAllowedHeaders());
            config.put("exposedHeaders", corsConfig.getExposedHeaders());
            config.put("allowCredentials", corsConfig.getAllowCredentials());
            config.put("maxAge", corsConfig.getMaxAge());

            return success(config, "获取CORS配置成功");
        } catch (Exception e) {
            logger.error("获取CORS配置失败", e);
            return handleException(e, "获取CORS配置");
        }
    }

    /**
     * 刷新CORS配置
     * 重新从域名管理中加载允许的域名
     */
    @PostMapping("/refresh")
    public ResponseEntity<ApiResponse<Map<String, Object>>> refreshCorsConfig() {
        try {
            // 刷新动态CORS配置
            dynamicCorsConfigurationSource.refreshConfiguration();

            // 获取刷新后的配置
            var corsConfig = dynamicCorsConfigurationSource.getCurrentConfiguration();

            Map<String, Object> result = new HashMap<>();
            result.put("allowedOrigins", corsConfig.getAllowedOriginPatterns());
            result.put("refreshTime", System.currentTimeMillis());
            result.put("message", "CORS配置已刷新，新配置将在下次请求时生效");

            logger.info("CORS配置已刷新，当前允许的域名: {}", corsConfig.getAllowedOriginPatterns());

            return success(result, "CORS配置刷新成功");
        } catch (Exception e) {
            logger.error("刷新CORS配置失败", e);
            return handleException(e, "刷新CORS配置");
        }
    }

    /**
     * 测试域名是否在CORS允许列表中
     */
    @PostMapping("/test")
    public ResponseEntity<ApiResponse<Map<String, Object>>> testOrigin(@RequestBody Map<String, String> request) {
        try {
            String origin = request.get("origin");
            if (origin == null || origin.trim().isEmpty()) {
                return error("域名不能为空");
            }

            var corsConfig = dynamicCorsConfigurationSource.getCurrentConfiguration();
            List<String> allowedOrigins = corsConfig.getAllowedOriginPatterns();
            boolean isAllowed = isOriginAllowed(origin, allowedOrigins);

            Map<String, Object> result = new HashMap<>();
            result.put("origin", origin);
            result.put("allowed", isAllowed);
            result.put("allowedOrigins", allowedOrigins);

            return success(result, isAllowed ? "域名在允许列表中" : "域名不在允许列表中");
        } catch (Exception e) {
            logger.error("测试域名失败", e);
            return handleException(e, "测试域名");
        }
    }



    /**
     * 检查域名是否被允许
     */
    private boolean isOriginAllowed(String origin, List<String> allowedOrigins) {
        for (String allowedOrigin : allowedOrigins) {
            if (allowedOrigin.equals("*")) {
                return true;
            }
            if (allowedOrigin.endsWith("*")) {
                String prefix = allowedOrigin.substring(0, allowedOrigin.length() - 1);
                if (origin.startsWith(prefix)) {
                    return true;
                }
            } else if (allowedOrigin.equals(origin)) {
                return true;
            }
        }
        return false;
    }
}
