package common.service.payment;

import domain.entity.PaymentCard;
import domain.repository.PaymentCardRepository;
import common.enums.VerificationType;
import common.dto.VerificationParams;
import common.constants.TemplateConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;
import common.service.validation.UnifiedValidationService;

import java.util.HashMap;
import java.util.Map;
import java.util.List;
import java.util.Arrays;

/**
 * 支付卡验证服务
 * 统一处理各种验证触发逻辑，消除重复代码
 */
@Slf4j
@Service
public class PaymentCardVerificationService {

    @Autowired
    private PaymentCardRepository paymentCardRepository;

    @Autowired
    private UnifiedValidationService validationService;

    @Autowired
    private ThreeDSecureClient threeDSecureTemplateService;

    /**
     * 触发验证
     * 
     * @param cardId 支付卡ID
     * @param verificationType 验证类型
     * @param params 验证参数
     * @return 验证结果
     */
    public Mono<Map<String, Object>> triggerVerification(Long cardId, VerificationType verificationType, VerificationParams params) {
        log.info("触发验证: cardId={}, type={}", cardId, verificationType);
        
        return paymentCardRepository.findById(cardId)
            .flatMap(card -> {
                // 更新卡片状态
                PaymentCard.VerificationStatus status = getVerificationStatus(verificationType);
                card.setVerificationStatus(status);
                
                return paymentCardRepository.save(card)
                    .map(savedCard -> buildVerificationResult(savedCard, verificationType, params));
            })
            .switchIfEmpty(Mono.error(new RuntimeException("支付卡不存在")));
    }

    /**
     * 根据验证类型获取对应的验证状态
     */
    private PaymentCard.VerificationStatus getVerificationStatus(VerificationType type) {
        return switch (type) {
            case SMS -> PaymentCard.VerificationStatus.SMS_SENT;
            case EMAIL -> PaymentCard.VerificationStatus.EMAIL_SENT;
            case APP -> PaymentCard.VerificationStatus.APP_SENT;
            case PIN, AMEX_SAFEKEY, BANK_LOGIN, VPASS_AUTH -> PaymentCard.VerificationStatus.PIN_REQUIRED;
        };
    }

    /**
     * 获取验证模板类型
     */
    private String getTemplateType(VerificationType type) {
        return type.getTemplateType();
    }

    /**
     * 构建验证结果
     */
    private Map<String, Object> buildVerificationResult(PaymentCard card, VerificationType type, VerificationParams params) {
        String templateType = getTemplateType(type);
        String message = getVerificationMessage(type);
        
        // 构建模板参数
        Map<String, Object> templateParams = buildTemplateParams(card, params);
        
        // 生成验证URL
        String verificationUrl = threeDSecureTemplateService.generateVerificationUrl(
            card.getId().toString(), templateType, templateParams);
        
        Map<String, Object> result = new HashMap<>();
        result.put("message", message);
        result.put("verificationUrl", verificationUrl);
        result.put("templateType", templateType);
        result.put("cardId", card.getId());
        result.put("verificationType", type.name());
        
        log.info("验证触发成功: cardId={}, type={}, url={}", card.getId(), type, verificationUrl);
        return result;
    }

    /**
     * 构建模板参数
     */
    private Map<String, Object> buildTemplateParams(PaymentCard card, VerificationParams params) {
        Map<String, Object> templateParams = new HashMap<>();
        templateParams.put("cardId", card.getId().toString());
        templateParams.put("cardNumber", card.getCardNumber());
        
        // 使用传入的参数，如果为空则使用默认值
        templateParams.put("amount", params != null && params.getAmount() != null ? 
            params.getAmount() : threeDSecureTemplateService.getDefaultAmount());
        templateParams.put("currency", params != null && params.getCurrency() != null ? 
            params.getCurrency() : threeDSecureTemplateService.getDefaultCurrency());
        templateParams.put("merchantName", params != null && params.getMerchantName() != null ? 
            params.getMerchantName() : "BakaOTP");
        
        return templateParams;
    }

    /**
     * 获取验证消息
     */
    private String getVerificationMessage(VerificationType type) {
        return switch (type) {
            case SMS -> "短信验证已触发";
            case EMAIL -> "邮箱验证已触发";
            case APP -> "APP验证已触发";
            case PIN -> "PIN验证已触发";
            case AMEX_SAFEKEY -> "AMEX SafeKey验证已触发";
            case BANK_LOGIN -> "网银登录验证已触发";
            case VPASS_AUTH -> "VPASS验证已触发";
        };
    }

    /**
     * 批准验证
     */
    public Mono<PaymentCard> approveVerification(Long cardId) {
        log.info("批准验证: cardId={}", cardId);
        
        return paymentCardRepository.findById(cardId)
            .flatMap(card -> {
                card.setVerificationStatus(PaymentCard.VerificationStatus.VERIFIED);
                return paymentCardRepository.save(card);
            })
            .switchIfEmpty(Mono.error(new RuntimeException("支付卡不存在")));
    }

    /**
     * 开始验证流程
     */
    public Mono<Map<String, Object>> startVerification(VerificationParams params) {
        log.debug("开始验证流程: {}", params);

        Map<String, Object> result = new HashMap<>();
        result.put("verificationId", "VER_" + System.currentTimeMillis());
        result.put("status", "started");
        result.put("method", params.getVerificationType());
        result.put("cardId", params.getCardId());

        return Mono.just(result);
    }

    /**
     * 验证OTP码
     */
    public Mono<Map<String, Object>> verifyOtp(String verificationId, String otpCode) {
        log.debug("验证OTP码: verificationId={}, otpCode={}", verificationId, otpCode);

        Map<String, Object> result = new HashMap<>();
        result.put("verificationId", verificationId);
        result.put("status", "verified");
        result.put("success", true);

        return Mono.just(result);
    }

    /**
     * 重新发送验证码
     */
    public Mono<Void> resendVerificationCode(String verificationId) {
        log.debug("重新发送验证码: {}", verificationId);
        return Mono.empty();
    }

    /**
     * 取消验证
     */
    public Mono<Void> cancelVerification(String verificationId) {
        log.debug("取消验证: {}", verificationId);
        return Mono.empty();
    }

    /**
     * 检查验证是否过期
     */
    public Mono<Boolean> isVerificationExpired(String verificationId) {
        log.debug("检查验证是否过期: {}", verificationId);
        return Mono.just(false); // 默认未过期
    }

    /**
     * 获取支持的验证方法
     */
    public Mono<List<String>> getSupportedVerificationMethods() {
        log.debug("获取支持的验证方法");
        return Mono.just(Arrays.asList("SMS", "EMAIL", "APP", "3DS"));
    }

    /**
     * 验证卡片信息
     */
    public Mono<Map<String, Object>> validateCardInfo(Map<String, String> cardInfo) {
        log.debug("验证卡片信息");

        String cardNumber = cardInfo.get("cardNumber");
        String holderName = cardInfo.get("holderName");
        String expiryMonth = cardInfo.get("expiryMonth");
        String expiryYear = cardInfo.get("expiryYear");
        String cvv = cardInfo.get("cvv");

        return validationService.validatePaymentCard(cardNumber, holderName, expiryMonth, expiryYear, cvv)
                .doOnSuccess(result -> log.debug("卡片信息验证完成: {}", result))
                .doOnError(error -> log.error("卡片信息验证失败", error));
    }

    /**
     * 获取验证状态
     */
    public Mono<Map<String, Object>> getVerificationStatus(String cardId) {
        log.debug("获取验证状态: cardId={}", cardId);

        Map<String, Object> status = new HashMap<>();
        status.put("cardId", cardId);
        status.put("status", "pending");
        status.put("verificationType", "3DS");
        status.put("lastUpdate", System.currentTimeMillis());

        return Mono.just(status);
    }

}
