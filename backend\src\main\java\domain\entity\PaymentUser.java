package domain.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import org.springframework.data.annotation.Id;
import org.springframework.data.relational.core.mapping.Column;
import org.springframework.data.relational.core.mapping.Table;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 支付用户实体（与管理员用户分离）- 响应式版本
 */
@Table("payment_users")
public class PaymentUser extends BaseEntity {

    @Id
    private Long id;

    @Column("email")
    private String email;

    @Column("name")
    private String name;

    @Column("phone")
    private String phone;

    @Column("status")
    private String status = "ACTIVE";

    // 构造函数
    public PaymentUser() {
        if (status == null) {
            status = "ACTIVE";
        }
    }

    // Getter和Setter方法
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }



    public boolean isEnabled() {
        return "ACTIVE".equals(status);
    }

    public void setEnabled(boolean enabled) {
        this.status = enabled ? "ACTIVE" : "INACTIVE";
    }
}
