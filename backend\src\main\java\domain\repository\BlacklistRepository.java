package domain.repository;

import domain.entity.Blacklist;
import org.springframework.data.domain.Pageable;
import org.springframework.data.r2dbc.repository.Query;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.LocalDateTime;

// 统一黑名单数据访问层
@Repository
public interface BlacklistRepository extends BaseRepository<Blacklist, Long> {

    // 根据类型和值查找活跃的黑名单项
    Mono<Blacklist> findByTypeAndValueAndStatus(Blacklist.BlacklistType type, String value, Blacklist.BlacklistStatus status);

    // 检查是否存在活跃的黑名单项
    Mono<Boolean> existsByTypeAndValueAndStatus(Blacklist.BlacklistType type, String value, Blacklist.BlacklistStatus status);

    // 根据类型查找所有活跃的黑名单项
    Flux<Blacklist> findByTypeAndStatusOrderByCreatedAtDesc(Blacklist.BlacklistType type, Blacklist.BlacklistStatus status);
    Flux<Blacklist> findByTypeAndStatusOrderByCreatedAtDesc(Blacklist.BlacklistType type, Blacklist.BlacklistStatus status, Pageable pageable);

    // 查找所有活跃的黑名单项
    Flux<Blacklist> findByStatusOrderByCreatedAtDesc(Blacklist.BlacklistStatus status);
    Flux<Blacklist> findByStatusOrderByCreatedAtDesc(Blacklist.BlacklistStatus status, Pageable pageable);

    // 根据值模糊查询
    @Query("SELECT * FROM blacklist_items WHERE value LIKE CONCAT('%', :value, '%') AND status = :status ORDER BY created_at DESC")
    Flux<Blacklist> findByValueContainingAndStatus(String value, Blacklist.BlacklistStatus status);
    @Query("SELECT * FROM blacklist_items WHERE value LIKE CONCAT('%', :value, '%') AND status = :status ORDER BY created_at DESC")
    Flux<Blacklist> findByValueContainingAndStatus(String value, Blacklist.BlacklistStatus status, Pageable pageable);

    // 根据类型和值模糊查询
    @Query("SELECT * FROM blacklist_items WHERE type = :type AND value LIKE CONCAT('%', :value, '%') AND status = :status ORDER BY created_at DESC")
    Flux<Blacklist> findByTypeAndValueContainingAndStatus(Blacklist.BlacklistType type, String value, Blacklist.BlacklistStatus status);
    @Query("SELECT * FROM blacklist_items WHERE type = :type AND value LIKE CONCAT('%', :value, '%') AND status = :status ORDER BY created_at DESC")
    Flux<Blacklist> findByTypeAndValueContainingAndStatus(Blacklist.BlacklistType type, String value, Blacklist.BlacklistStatus status, Pageable pageable);

    // 根据创建时间范围查询
    @Query("SELECT * FROM blacklist_items WHERE created_at BETWEEN :startDate AND :endDate AND status = :status ORDER BY created_at DESC")
    Flux<Blacklist> findByCreatedAtBetweenAndStatus(LocalDateTime startDate, LocalDateTime endDate, Blacklist.BlacklistStatus status);
    @Query("SELECT * FROM blacklist_items WHERE created_at BETWEEN :startDate AND :endDate AND status = :status ORDER BY created_at DESC")
    Flux<Blacklist> findByCreatedAtBetweenAndStatus(LocalDateTime startDate, LocalDateTime endDate, Blacklist.BlacklistStatus status, Pageable pageable);

    // 统计今日新增的黑名单数量
    @Query("SELECT COUNT(*) FROM blacklist_items WHERE created_at >= :startOfDay AND status = :status")
    Mono<Long> countTodayAddedByStatus(LocalDateTime startOfDay, Blacklist.BlacklistStatus status);

    // 统计指定状态的黑名单项数量
    Mono<Long> countByStatus(Blacklist.BlacklistStatus status);

    // 统计指定类型和状态的黑名单项数量
    Mono<Long> countByTypeAndStatus(Blacklist.BlacklistType type, Blacklist.BlacklistStatus status);
}