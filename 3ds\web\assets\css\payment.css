/* Payment page specific styles */

/* Payment details box */
.payment-details {
    background-color: #f9fafb;
    border-radius: 0.5rem;
    padding: 1rem;
    margin-bottom: 2rem;
}

/* Transaction cancelled page styles */
.cancelled-container {
    max-width: 400px;
    margin: 0 auto;
    background: white;
    min-height: 100vh;
    padding: 20px;
    text-align: center;
}

.cancelled-icon {
    font-size: 48px;
    color: #dc2626;
    margin-bottom: 20px;
}

.cancelled-title {
    font-size: 24px;
    font-weight: bold;
    color: #1f2937;
    margin-bottom: 10px;
}

.cancelled-message {
    color: #6b7280;
    font-size: 16px;
    line-height: 1.5;
    margin-bottom: 15px;
    max-width: 400px;
    margin-left: auto;
    margin-right: auto;
}

.continue-btn {
    background-color: #4b5563;
    color: white;
    padding: 12px 40px;
    border: none;
    border-radius: 4px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    margin-top: 20px;
    transition: background-color 0.3s ease;
}

.continue-btn:hover {
    background-color: #374151;
}

.bank-logo-cancelled {
    width: 60px;
    height: 30px;
    background-image: url('../icon/bank.png');
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
}

.cancelled-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 40px;
    padding: 20px 0;
}

.visa-logo-cancelled {
    width: 80px;
    height: 32px;
    background-image: url('../icon/visa.png');
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
}
