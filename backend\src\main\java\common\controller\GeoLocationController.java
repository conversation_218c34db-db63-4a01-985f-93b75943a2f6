package common.controller;

import external.service.GeoLocationService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

import java.util.Map;

/**
 * IP地理位置查询控制器
 * 提供IP地理位置查询和服务状态检查的API接口
 */
@RestController
@RequestMapping("/api/system/geo-location")
public class GeoLocationController {

    private static final Logger logger = LoggerFactory.getLogger(GeoLocationController.class);

    @Autowired
    private GeoLocationService geoLocationService;

    /**
     * 查询IP地理位置信息
     * 
     * @param ip IP地址
     * @return 地理位置信息
     */
    @GetMapping("/query")
    public Mono<ResponseEntity<Map<String, Object>>> queryLocation(@RequestParam String ip) {
        logger.info("查询IP地理位置: {}", ip);
        
        return geoLocationService.getLocationInfo(ip)
            .map(locationInfo -> {
                logger.info("IP地理位置查询结果: ip={}, country={}, city={}", 
                           ip, locationInfo.get("country"), locationInfo.get("city"));
                return ResponseEntity.ok(locationInfo);
            })
            .onErrorResume(Exception.class, e -> {
                logger.error("IP地理位置查询失败: ip={}, error={}", ip, e.getMessage());
                return Mono.just(ResponseEntity.internalServerError().build());
            });
    }

    /**
     * 测试IP查询功能
     * 
     * @param ip 可选的IP地址，默认使用*******
     * @return 测试结果
     */
    @GetMapping("/test")
    public Mono<ResponseEntity<Map<String, Object>>> testLocation(@RequestParam(defaultValue = "*******") String ip) {
        logger.info("测试IP地理位置查询: {}", ip);
        
        return geoLocationService.getLocationInfo(ip)
            .map(locationInfo -> {
                // 添加测试标识
                locationInfo.put("testMode", true);
                locationInfo.put("testIp", ip);
                locationInfo.put("timestamp", System.currentTimeMillis());
                
                logger.info("IP地理位置测试完成: ip={}, provider={}", ip, locationInfo.get("provider"));
                return ResponseEntity.ok(locationInfo);
            })
            .onErrorResume(Exception.class, e -> {
                logger.error("IP地理位置测试失败: ip={}, error={}", ip, e.getMessage());
                return Mono.just(ResponseEntity.internalServerError().build());
            });
    }

    /**
     * 获取地理位置服务状态
     * 
     * @return 服务状态信息
     */
    @GetMapping("/status")
    public ResponseEntity<Map<String, Object>> getServiceStatus() {
        try {
            Map<String, Object> serviceInfo = geoLocationService.getServiceInfo();
            serviceInfo.put("available", geoLocationService.isServiceAvailable());
            serviceInfo.put("timestamp", System.currentTimeMillis());
            
            logger.debug("地理位置服务状态: {}", serviceInfo);
            return ResponseEntity.ok(serviceInfo);
        } catch (Exception e) {
            logger.error("获取地理位置服务状态失败: {}", e.getMessage());
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 批量查询IP地理位置信息
     * 
     * @param ips IP地址列表
     * @return 批量查询结果
     */
    @PostMapping("/batch-query")
    public Mono<ResponseEntity<Map<String, Object>>> batchQueryLocation(@RequestBody Map<String, Object> request) {
        @SuppressWarnings("unchecked")
        java.util.List<String> ips = (java.util.List<String>) request.get("ips");
        
        if (ips == null || ips.isEmpty()) {
            return Mono.just(ResponseEntity.badRequest().build());
        }
        
        logger.info("批量查询IP地理位置: count={}", ips.size());
        
        // 限制批量查询数量
        if (ips.size() > 10) {
            logger.warn("批量查询IP数量超限: {}", ips.size());
            return Mono.just(ResponseEntity.badRequest().build());
        }
        
        return reactor.core.publisher.Flux.fromIterable(ips)
            .flatMap(ip -> geoLocationService.getLocationInfo(ip)
                .map(info -> Map.of(ip, info))
                .onErrorReturn(Map.of(ip, Map.of("error", "查询失败"))))
            .collectList()
            .map(results -> {
                Map<String, Object> response = new java.util.HashMap<>();
                response.put("results", results);
                response.put("total", ips.size());
                response.put("timestamp", System.currentTimeMillis());
                
                logger.info("批量IP地理位置查询完成: total={}", ips.size());
                return ResponseEntity.ok(response);
            })
            .onErrorResume(Exception.class, e -> {
                logger.error("批量IP地理位置查询失败: {}", e.getMessage());
                return Mono.just(ResponseEntity.internalServerError().build());
            });
    }

    /**
     * 健康检查接口
     * 
     * @return 健康状态
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> healthCheck() {
        Map<String, Object> health = new java.util.HashMap<>();
        
        try {
            boolean available = geoLocationService.isServiceAvailable();
            health.put("status", available ? "UP" : "DOWN");
            health.put("service", "GeoLocationService");
            health.put("timestamp", System.currentTimeMillis());
            
            return ResponseEntity.ok(health);
        } catch (Exception e) {
            logger.error("地理位置服务健康检查失败: {}", e.getMessage());
            health.put("status", "DOWN");
            health.put("error", e.getMessage());
            health.put("timestamp", System.currentTimeMillis());
            
            return ResponseEntity.status(503).body(health);
        }
    }
}
