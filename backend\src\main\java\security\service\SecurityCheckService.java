package security.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import core.constants.AppConstants;

import domain.entity.Domain;
import domain.entity.DomainSecurityHistory;
import domain.repository.DomainRepository;
import domain.repository.DomainSecurityHistoryRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;
import reactor.core.publisher.Flux;
import common.service.WebSocketService;
import java.time.Duration;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

//安全检查服务
@Slf4j
@Service
public class SecurityCheckService {



    private static final String GOOGLE_TRANSPARENCY_API_URL =
        "https://transparencyreport.google.com/transparencyreport/api/v3/safebrowsing/status?site=";

    @Autowired
    @Qualifier("externalApiWebClient")
    private WebClient webClient;
    private final ObjectMapper objectMapper;

    @Autowired
    private WebSocketService webSocketService;

    @Autowired
    private DomainRepository domainRepository;

    @Autowired
    private DomainSecurityHistoryRepository securityHistoryRepository;

    public SecurityCheckService() {
        this.objectMapper = new ObjectMapper();
    }
    
    //检查域名安全状态
    public Mono<Map<String, Object>> checkDomainSecurity(String domainName) {
        log.info("开始检查域名安全状态: {}", domainName);

        // 调用Google Transparency Report API
        return checkGoogleTransparencyReport(domainName)
            .map(googleResult -> {
                Map<String, Object> result = new HashMap<>();

                // 合并结果
                result.putAll(googleResult);
                result.put("domain", domainName);
                result.put("checkTime", System.currentTimeMillis());
                result.put("source", "Google Transparency Report");

                // 发送WebSocket通知
                notifySecurityStatusChange(domainName, result);

                log.info("域名安全检查完成: {} - 状态: {}", domainName, result.get("status"));
                return result;
            })
            .onErrorResume(Exception.class, e -> {
                log.error("域名安全检查失败: {}", domainName, e);
                Map<String, Object> errorResult = new HashMap<>();
                errorResult.put("domain", domainName);
                errorResult.put("safe", false);
                errorResult.put("status", "error");
                errorResult.put("error", e.getMessage());
                errorResult.put("checkTime", System.currentTimeMillis());
                return Mono.just(errorResult);
            });
    }
    
    //调用Google Transparency Report API检查域名安全状态
    private Mono<Map<String, Object>> checkGoogleTransparencyReport(String domainName) {
        try {
            // 编码域名
            String encodedDomain = URLEncoder.encode(domainName, StandardCharsets.UTF_8);
            String apiUrl = GOOGLE_TRANSPARENCY_API_URL + encodedDomain;

            log.debug("调用Google Transparency Report API: {}", apiUrl);

            // 调用API
            return webClient
                .get()
                .uri(apiUrl)
                .retrieve()
                .bodyToMono(String.class)
                .map(response -> {
                    if (response != null && !response.trim().isEmpty()) {
                        // 解析响应
                        return parseGoogleTransparencyResponse(response, domainName);
                    } else {
                        log.warn("Google Transparency Report API返回空响应: {}", domainName);
                        return createFallbackResult(domainName, "empty_response");
                    }
                })
                .onErrorResume(Exception.class, e -> {
                    log.error("调用Google Transparency Report API失败: {}", domainName, e);
                    return Mono.just(createFallbackResult(domainName, e.getMessage()));
                })
                .timeout(Duration.ofSeconds(10));

        } catch (Exception e) {
            log.error("构建Google Transparency Report API请求失败: {}", domainName, e);
            return Mono.just(createFallbackResult(domainName, e.getMessage()));
        }
    }

    //解析Google Transparency Report API响应
    private Map<String, Object> parseGoogleTransparencyResponse(String response, String domainName) {
        Map<String, Object> result = new HashMap<>();

        try {
            // 移除可能的前缀字符
            String cleanResponse = response.trim();
            if (cleanResponse.startsWith(")]}'\n")) {
                cleanResponse = cleanResponse.substring(5);
            }

            // 解析JSON数组
            JsonNode rootNode = objectMapper.readTree(cleanResponse);

            if (rootNode.isArray() && rootNode.size() > 0) {
                JsonNode firstArray = rootNode.get(0);

                if (firstArray.isArray() && firstArray.size() >= 8) {
                    // 解析响应数组
                    String apiId = firstArray.get(0).asText(); // "sb.ssr"
                    int statusCode = firstArray.get(1).asInt(); // X: 整体状态码
                    boolean redirectsToHarmful = firstArray.get(2).asInt() == 1; // A
                    boolean installsMalware = firstArray.get(3).asInt() == 1; // B
                    boolean socialEngineering = firstArray.get(4).asInt() == 1; // C
                    boolean unwantedContent = firstArray.get(5).asInt() == 1; // D
                    boolean uncommonDownloads = firstArray.get(6).asInt() == 1; // E
                    long timestamp = firstArray.get(7).asLong(); // TIMESTAMP
                    String checkedDomain = firstArray.size() > 8 ? firstArray.get(8).asText() : domainName;

                    // 构建结果
                    result.put("apiId", apiId);
                    result.put("statusCode", statusCode);
                    result.put("status", getStatusFromCode(statusCode));
                    result.put("safe", statusCode == 1);
                    result.put("googleTimestamp", timestamp);
                    result.put("checkedDomain", checkedDomain);

                    // 威胁类型
                    Map<String, Boolean> threats = new HashMap<>();
                    threats.put("redirectsToHarmful", redirectsToHarmful);
                    threats.put("installsMalware", installsMalware);
                    threats.put("socialEngineering", socialEngineering);
                    threats.put("unwantedContent", unwantedContent);
                    threats.put("uncommonDownloads", uncommonDownloads);
                    result.put("threats", threats);

                    // 风险等级
                    result.put("riskLevel", calculateRiskLevel(statusCode, threats));

                    // 状态描述
                    result.put("statusDescription", getStatusDescription(statusCode));

                    log.info("Google Transparency Report解析成功: {} - 状态码: {}", domainName, statusCode);

                } else {
                    log.warn("Google Transparency Report响应格式不正确: {}", response);
                    result = createFallbackResult(domainName, "invalid_response_format");
                }
            } else {
                log.warn("Google Transparency Report响应为空数组: {}", response);
                result = createFallbackResult(domainName, "empty_array_response");
            }

        } catch (Exception e) {
            log.error("解析Google Transparency Report响应失败: {}", domainName, e);
            result = createFallbackResult(domainName, "parse_error: " + e.getMessage());
        }

        return result;
    }
    
    //根据状态码获取状态字符串
    private String getStatusFromCode(int statusCode) {
        switch (statusCode) {
            case AppConstants.SecurityStatusCode.SAFE:
                return AppConstants.SecurityStatus.SAFE;
            case AppConstants.SecurityStatusCode.UNSAFE:
                return AppConstants.SecurityStatus.UNSAFE;
            case AppConstants.SecurityStatusCode.PARTIALLY_UNSAFE:
                return AppConstants.SecurityStatus.PARTIALLY_UNSAFE;
            case AppConstants.SecurityStatusCode.UNCOMMON_FILES:
                return AppConstants.SecurityStatus.UNCOMMON_FILES;
            case AppConstants.SecurityStatusCode.NO_DATA:
                return AppConstants.SecurityStatus.NO_DATA;
            default:
                return AppConstants.SecurityStatus.UNKNOWN;
        }
    }

    //获取状态描述
    private String getStatusDescription(int statusCode) {
        switch (statusCode) {
            case AppConstants.SecurityStatusCode.SAFE:
                return AppConstants.SecurityStatusDescription.SAFE;
            case AppConstants.SecurityStatusCode.UNSAFE:
                return AppConstants.SecurityStatusDescription.UNSAFE;
            case AppConstants.SecurityStatusCode.PARTIALLY_UNSAFE:
                return AppConstants.SecurityStatusDescription.PARTIALLY_UNSAFE;
            case AppConstants.SecurityStatusCode.UNCOMMON_FILES:
                return AppConstants.SecurityStatusDescription.UNCOMMON_FILES;
            case AppConstants.SecurityStatusCode.NO_DATA:
                return AppConstants.SecurityStatusDescription.NO_DATA;
            default:
                return AppConstants.SecurityStatusDescription.UNKNOWN;
        }
    }

    //计算风险等级
    private String calculateRiskLevel(int statusCode, Map<String, Boolean> threats) {
        if (statusCode == 2) {
            return "high";
        }

        if (statusCode == 3) {
            return "medium";
        }

        // 检查威胁类型
        long threatCount = threats.values().stream().mapToLong(b -> b ? 1 : 0).sum();

        if (threatCount >= 3) {
            return "high";
        } else if (threatCount >= 1) {
            return "medium";
        } else if (statusCode == 1) {
            return "low";
        } else {
            return "unknown";
        }
    }

    //创建备用结果
    private Map<String, Object> createFallbackResult(String domainName, String error) {
        Map<String, Object> result = new HashMap<>();
        result.put("statusCode", AppConstants.SecurityStatusCode.NO_DATA);
        result.put("status", AppConstants.SecurityStatus.NO_DATA);
        result.put("safe", null); // 未知状态
        result.put("error", error);
        result.put("fallback", true);
        result.put("statusDescription", AppConstants.SecurityStatusDescription.FALLBACK);
        result.put("riskLevel", AppConstants.RiskLevel.UNKNOWN);

        // 空威胁映射
        Map<String, Boolean> threats = new HashMap<>();
        threats.put(AppConstants.ThreatType.REDIRECTS_TO_HARMFUL, false);
        threats.put(AppConstants.ThreatType.INSTALLS_MALWARE, false);
        threats.put(AppConstants.ThreatType.SOCIAL_ENGINEERING, false);
        threats.put(AppConstants.ThreatType.UNWANTED_CONTENT, false);
        threats.put(AppConstants.ThreatType.UNCOMMON_DOWNLOADS, false);
        result.put("threats", threats);

        return result;
    }

    //发送安全状态变化通知
    private void notifySecurityStatusChange(String domainName, Map<String, Object> securityResult) {
        try {
            Map<String, Object> notification = new HashMap<>();
            notification.put("type", "security_status_update");
            notification.put("domain", domainName);
            notification.put("status", securityResult.get("status"));
            notification.put("safe", securityResult.get("safe"));
            notification.put("riskLevel", securityResult.get("riskLevel"));
            notification.put("timestamp", System.currentTimeMillis());

            if (webSocketService != null) {
                webSocketService.broadcastMessage(notification);
            }

        } catch (Exception e) {
            log.error("发送安全状态变化通知失败: {}", domainName, e);
        }
    }
    
    //验证域名格式
    private boolean isValidDomainFormat(String domainName) {
        if (domainName == null || domainName.trim().isEmpty()) {
            return false;
        }
        
        // 简单的域名格式验证
        String domainPattern = "^[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9]?\\.[a-zA-Z]{2,}$";
        return domainName.matches(domainPattern) || 
               domainName.matches("^[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9]?\\.[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9]?\\.[a-zA-Z]{2,}$");
    }
    
    //获取安全检查历史
    public Map<String, Object> getSecurityHistory(String domainName) {
        Map<String, Object> history = new HashMap<>();

        try {
            //历史数据查询逻辑
            history.put("domain", domainName);
            history.put("totalChecks", 0);
            history.put("safeChecks", 0);
            history.put("unsafeChecks", 0);
            history.put("lastCheckTime", null);
            history.put("firstCheckTime", null);

        } catch (Exception e) {
            log.error("获取安全检查历史失败: {}", domainName, e);
            history.put("error", e.getMessage());
        }

        return history;
    }
    
    //异步检查域名安全状态
    public CompletableFuture<Map<String, Object>> checkDomainSecurityAsync(String domainName) {
        return checkDomainSecurity(domainName)
            .map(result -> result)
            .onErrorResume(e -> {
                log.error("异步安全检查失败: {}", domainName, e);
                Map<String, Object> errorResult = new HashMap<>();
                errorResult.put("domain", domainName);
                errorResult.put("safe", false);
                errorResult.put("status", "error");
                errorResult.put("error", e.getMessage());
                return Mono.just(errorResult);
            })
            .toFuture();
    }

    //批量安全检查
    public Mono<Map<String, Map<String, Object>>> batchSecurityCheck(String[] domainNames) {
        if (domainNames == null || domainNames.length == 0) {
            return Mono.just(new HashMap<>());
        }

        log.info("开始批量安全检查，域名数量: {}", domainNames.length);
        return processBatchSecurityCheck(domainNames);
    }

    // 处理批量安全检查
    private Mono<Map<String, Map<String, Object>>> processBatchSecurityCheck(String[] domainNames) {
        return Flux.fromArray(domainNames)
            .delayElements(Duration.ofMillis(200)) // 添加延迟以避免API限制
            .flatMap(this::processSingleDomainCheck)
            .collectMap(Map.Entry::getKey, Map.Entry::getValue)
            .doOnSuccess(results -> log.info("批量安全检查完成，成功: {}, 总数: {}",
                                           results.size(), domainNames.length));
    }

    // 处理单个域名检查
    private Mono<Map.Entry<String, Map<String, Object>>> processSingleDomainCheck(String domainName) {
        return checkDomainSecurity(domainName)
            .map(result -> Map.entry(domainName, result))
            .onErrorReturn(Map.entry(domainName, createFallbackResult(domainName, "检查失败")));
    }



    //检查域名是否需要重新检查
    public boolean shouldRecheckDomain(String domainName, LocalDateTime lastCheckTime) {
        if (lastCheckTime == null) {
            return true;
        }

        // 15分钟检查间隔
        LocalDateTime nextCheckTime = lastCheckTime.plusMinutes(AppConstants.Time.DOMAIN_RECHECK_INTERVAL_MINUTES);
        return LocalDateTime.now().isAfter(nextCheckTime);
    }

    //获取域名安全状态的颜色代码
    public String getStatusColor(Map<String, Object> securityResult) {
        String status = (String) securityResult.get("status");
        Boolean safe = (Boolean) securityResult.get("safe");

        if (status == null) {
            return AppConstants.StatusColor.PURPLE; // 新添加/未知状态
        }

        switch (status) {
            case AppConstants.SecurityStatus.SAFE:
                return AppConstants.StatusColor.GREEN;
            case AppConstants.SecurityStatus.UNSAFE:
                return AppConstants.StatusColor.RED;
            case AppConstants.SecurityStatus.PARTIALLY_UNSAFE:
                return AppConstants.StatusColor.YELLOW;
            case AppConstants.SecurityStatus.UNCOMMON_FILES:
                return AppConstants.StatusColor.YELLOW;
            case AppConstants.SecurityStatus.NO_DATA:
            case AppConstants.SecurityStatus.ERROR:
                return AppConstants.StatusColor.PURPLE;
            default:
                return AppConstants.StatusColor.PURPLE;
        }
    }

    // ==================== 调度功能（从SecurityCheckScheduler合并） ====================

    /**
     * 定时安全检查任务
     * 每15分钟执行一次
     */
    @Scheduled(fixedRate = 900000) // 15分钟 = 900000毫秒
    public void scheduledSecurityCheck() {
        log.info("开始执行定时安全检查任务");

        try {
            // 获取所有启用的域名
            domainRepository.findByStatusOrderByUpdatedAtDesc(Domain.DomainStatus.NORMAL)
                .collectList()
                .subscribe(activeDomains -> {
                    if (activeDomains.isEmpty()) {
                        log.info("没有找到需要检查的活跃域名");
                        return;
                    }

                    log.info("找到 {} 个活跃域名需要检查", activeDomains.size());

                    // 异步检查所有域名
                    CompletableFuture<Void> allChecks = CompletableFuture.allOf(
                        activeDomains.stream()
                            .map(this::checkDomainSecurityAsync)
                            .toArray(CompletableFuture[]::new)
                    );

                    // 异步等待所有检查完成
                    allChecks.whenComplete((result, throwable) -> {
                        if (throwable != null) {
                            log.error("定时安全检查任务执行失败", throwable);
                        } else {
                            log.info("定时安全检查任务完成");
                        }
                    });
                }, error -> {
                    log.error("获取活跃域名失败", error);
                });

        } catch (Exception e) {
            log.error("执行定时安全检查任务失败", e);
        }
    }

    /**
     * 异步检查单个域名的安全状态
     */
    private CompletableFuture<Void> checkDomainSecurityAsync(Domain domain) {
        return CompletableFuture.runAsync(() -> {
            try {
                // 检查是否需要重新检查
                if (!shouldRecheckDomain(domain.getDomainName(), domain.getLastSecurityCheck())) {
                    log.debug("域名 {} 不需要重新检查", domain.getDomainName());
                    return;
                }

                log.debug("开始检查域名安全状态: {}", domain.getDomainName());

                // 执行安全检查
                checkDomainSecurity(domain.getDomainName())
                    .subscribe(
                        securityResult -> {
                            // 保存检查结果到历史记录
                            saveSecurityCheckResult(domain, securityResult);

                            // 更新域名的安全状态
                            updateDomainSecurityStatus(domain, securityResult);

                            // 发送WebSocket通知
                            notifySecurityCheckComplete(domain, securityResult);

                            log.debug("域名 {} 安全检查完成，状态: {}", domain.getDomainName(), securityResult.get("status"));
                        },
                        error -> {
                            log.error("检查域名 {} 安全状态失败", domain.getDomainName(), error);
                            // 记录错误到历史记录
                            saveErrorResult(domain, error.getMessage());
                        }
                    );
            } catch (Exception e) {
                log.error("检查域名 {} 安全状态时发生异常", domain.getDomainName(), e);
                saveErrorResult(domain, e.getMessage());
            }
        });
    }

    /**
     * 保存安全检查结果
     */
    private void saveSecurityCheckResult(Domain domain, Map<String, Object> securityResult) {
        try {
            log.debug("保存域名 {} 的安全检查结果", domain.getDomainName());
            // 这里可以实现保存到数据库的逻辑
            // 例如保存到 security_check_history 表
        } catch (Exception e) {
            log.error("保存安全检查结果失败: {}", domain.getDomainName(), e);
        }
    }

    /**
     * 更新域名安全状态
     */
    private void updateDomainSecurityStatus(Domain domain, Map<String, Object> securityResult) {
        try {
            log.debug("更新域名 {} 的安全状态", domain.getDomainName());
            // 这里可以实现更新域名状态的逻辑
            // 例如更新 domains 表中的 security_status 字段
        } catch (Exception e) {
            log.error("更新域名安全状态失败: {}", domain.getDomainName(), e);
        }
    }

    /**
     * 发送安全检查完成通知
     */
    private void notifySecurityCheckComplete(Domain domain, Map<String, Object> securityResult) {
        try {
            log.debug("发送域名 {} 安全检查完成通知", domain.getDomainName());
            // 这里可以实现WebSocket通知逻辑
            // 例如通知前端更新域名状态
        } catch (Exception e) {
            log.error("发送安全检查通知失败: {}", domain.getDomainName(), e);
        }
    }

    /**
     * 保存错误结果
     */
    private void saveErrorResult(Domain domain, String errorMessage) {
        try {
            log.debug("保存域名 {} 的错误结果: {}", domain.getDomainName(), errorMessage);
            // 这里可以实现保存错误信息的逻辑
            // 例如保存到 security_check_errors 表
        } catch (Exception e) {
            log.error("保存错误结果失败: {}", domain.getDomainName(), e);
        }
    }

}
