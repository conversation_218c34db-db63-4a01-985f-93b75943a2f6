.sendcodeSMS {

    word-break: normal;
    tab-size: 4;
    font-family: Roboto, sans-serif;
    line-height: 1.5;
    font-size: 1rem;
    text-rendering: optimizeLegibility;
    direction: ltr;
    color-scheme: normal;
    background-repeat: no-repeat;
    box-sizing: inherit;
    margin: 0;
    background-color: #f2f2f2;
    padding: 16px 20px;
    color: #141414;
    border-left: 4px solid #007ebb;
    margin-bottom: 32px;
}

.sendcodeCont {
    word-break: normal;
    tab-size: 4;
    font-family: Roboto, sans-serif;
    line-height: 1.5;
    font-size: 1rem;
    text-rendering: optimizeLegibility;
    color: rgba(var(--v-theme-on-background), var(--v-high-emphasis-opacity));
    direction: ltr;
    color-scheme: normal;
    background-repeat: no-repeat;
    box-sizing: inherit;
    padding: 0;
    margin: 0;
    margin-bottom: 16px !important;
}

.sendcode1 {
    -webkit-text-size-adjust: 100%;
    word-break: normal;
    tab-size: 4;
    font-family: <PERSON><PERSON>, sans-serif;
    line-height: 1.5;
    font-size: 1rem;
    text-rendering: optimizeLegibility;


    color: rgba(var(--v-theme-on-background), var(--v-high-emphasis-opacity));
    direction: ltr;
    color-scheme: normal;

    background-repeat: no-repeat;
    box-sizing: inherit;
    padding: 0;
    margin: 0;
    margin-top: 24px !important;
}

.sendcode2 {
    -webkit-text-size-adjust: 100%;
    word-break: normal;
    tab-size: 4;
    font-family: Roboto, sans-serif;
    line-height: 1.5;
    font-size: 1rem;
    text-rendering: optimizeLegibility;

    color: rgba(var(--v-theme-on-background), var(--v-high-emphasis-opacity));
    direction: ltr;
    color-scheme: normal;

    background-repeat: no-repeat;
    box-sizing: inherit;
    padding: 0;
    margin: 0;
    margin-top: 40px !important;
}

.sendcodeBti {
    word-break: normal;
    tab-size: 4;
    font-family: Roboto, sans-serif;
    text-rendering: optimizeLegibility;
    direction: ltr;
    font-size: 16px;
    font-weight: 600;
    background-repeat: no-repeat;
    box-sizing: inherit;
    padding: 0;
    margin: 0;
    --v-icon-size-multiplier: 1;
    align-items: center;
    display: inline-flex;
    font-feature-settings: "liga";
    height: 1em;
    justify-content: center;
    letter-spacing: normal;
    line-height: 1;
    position: relative;
    text-indent: 0;
    user-select: none;
    vertical-align: middle;
    width: 1em;
    cursor: pointer;
    color: inherit;
    margin-left: 4px !important;
    color-scheme: normal;
}

.sendcodeInput1 {
    -webkit-text-size-adjust: 100%;
    word-break: normal;
    tab-size: 4;
    -webkit-font-smoothing: antialiased;
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
    --v-theme-overlay-multiplier: 1;
    --v-scrollbar-offset: 0px;
    direction: ltr;
    color-scheme: normal;

    background-repeat: no-repeat;
    box-sizing: inherit;
    padding: 0;
    margin: 0;
    font: inherit;
    background-color: transparent;
    margin-top: 8px !important;
    height: 48px;
    border: 1px solid #9b9b9b;
    padding-left: 16px;
    padding-right: 16px;
    caret-color: #008452;
    border-radius: 4px;
    width: 90%;
    display: block;
}

.sendbtncode {
    -webkit-text-size-adjust: 100%;
    word-break: normal;
    tab-size: 4;
    -webkit-font-smoothing: antialiased;
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
    --v-theme-overlay-multiplier: 1;
    --v-scrollbar-offset: 0px;
    direction: ltr;
    color-scheme: normal;

    background-repeat: no-repeat;
    box-sizing: inherit;
    padding: 0;
    margin: 0;
    font: inherit;
    overflow: visible;
    text-transform: none;
    -webkit-appearance: button;
    display: inline-block;
    background-color: transparent;
    color: #495e7d;
    font-size: 16px;
    font-weight: 700;
    text-align: center;
    text-decoration: none;
    cursor: pointer;
    border: none;
    outline: none;
    transition: all .3s ease;
}
.submitBtn {
    -webkit-text-size-adjust: 100%;
    box-sizing: border-box;
    -webkit-font-smoothing: antialiased;
    border-radius: 4px;
    cursor: pointer;
    font-family: Ciutadella, Helvetica, Arial, sans-serif;
    font-size: 16px;
    font-style: normal;
    font-weight: 700;
    height: 50px;
    letter-spacing: .1em;
    line-height: 1.56;
    padding: 14px 20px 15px;
    position: relative;
    text-transform: uppercase;
    width: 100%;
    background-color: #e40000;
    border-width: 0;
    color: #fff;
}

.sendcodebtn1 {
    letter-spacing: 0;
    font-weight: 400;
    font-size: 16px;
    line-height: 1.4;
    font-family: Arial, Helvetica, sans-serif;
    -webkit-tap-highlight-color: transparent;
    -webkit-font-smoothing: antialiased;
    color: #212129;
    all: unset;
    box-sizing: inherit;
    display: flex;
    flex-direction: column;
    margin-top: 10px;
    margin-bottom: 8px;
}

.sendcodebtn2 {
    -webkit-tap-highlight-color: transparent;
    all: unset;
    font-style: normal;
    display: flex;
    align-items: center;
    justify-items: center;
    justify-content: center;
    text-align: center;
    text-decoration: none;
    border-width: 2px;
    border-style: solid;
    border-radius: 6px;
    border-color: #31313d33;
    box-sizing: border-box;
    transition: background-color .15s ease, border-color .15s ease, color .15s ease;
    min-height: 40px;
    min-width: 100%;
    padding-left: 32px;
    padding-right: 32px;
    font-family: ap_text, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica, Arial, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol;
    -webkit-font-smoothing: antialiased;
    margin: 0;
    line-height: 20px;
    letter-spacing: 0px;
    font-size: 14px;
    font-weight: 500;
    margin-top: 8px;
    background-color: rgb(220, 25, 40);
    color: rgb(255, 255, 255);
}