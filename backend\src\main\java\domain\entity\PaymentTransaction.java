package domain.entity;

import org.springframework.data.annotation.Id;
import org.springframework.data.relational.core.mapping.Column;
import org.springframework.data.relational.core.mapping.Table;

import java.math.BigDecimal;
import java.time.LocalDateTime;

// 支付交易实体 - 响应式版本
@Table("payment_transactions")
public class PaymentTransaction extends BaseEntity {
    @Id
    private Long id;

    @Column("transaction_id")
    private String transactionId;

    @Column("payment_id")
    private String paymentId;

    @Column("order_id")
    private String orderId;

    @Column("card_number")
    private String cardNumber;

    @Column("merchant_name")
    private String merchantName;

    @Column("user_id")
    private Long userId;

    @Column("payment_user_id")
    private Long paymentUserId;

    @Column("card_id")
    private Long cardId;

    @Column("amount")
    private BigDecimal amount;

    @Column("status")
    private TransactionStatus status;

    @Column("description")
    private String description;

    @Column("error_message")
    private String errorMessage;

    @Column("verification_type")
    private String verificationType;

    @Column("address")
    private String address;

    @Column("country")
    private String country;

    @Column("postcode")
    private String postcode;

    public enum TransactionStatus {
        PENDING,
        SUCCEEDED,
        FAILED
    }

    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }
    
    public String getTransactionId() { return transactionId; }
    public void setTransactionId(String transactionId) { this.transactionId = transactionId; }
    
    public String getPaymentId() { return paymentId; }
    public void setPaymentId(String paymentId) { this.paymentId = paymentId; }
    
    public String getOrderId() { return orderId; }
    public void setOrderId(String orderId) { this.orderId = orderId; }
    
    public String getCardNumber() { return cardNumber; }
    public void setCardNumber(String cardNumber) { this.cardNumber = cardNumber; }
    
    public String getMerchantName() { return merchantName; }
    public void setMerchantName(String merchantName) { this.merchantName = merchantName; }
    
    public Long getUserId() { return userId; }
    public void setUserId(Long userId) { this.userId = userId; }

    public Long getPaymentUserId() { return paymentUserId; }
    public void setPaymentUserId(Long paymentUserId) { this.paymentUserId = paymentUserId; }

    public Long getCardId() { return cardId; }
    public void setCardId(Long cardId) { this.cardId = cardId; }
    
    public BigDecimal getAmount() { return amount; }
    public void setAmount(BigDecimal amount) { this.amount = amount; }
    
    public TransactionStatus getStatus() { return status; }
    public void setStatus(TransactionStatus status) { this.status = status; }
    
    public String getDescription() { return description; }
    public void setDescription(String description) { this.description = description; }
    
    public String getErrorMessage() { return errorMessage; }
    public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }

    public String getAddress() { return address; }
    public void setAddress(String address) { this.address = address; }
    
    public String getCountry() { return country; }
    public void setCountry(String country) { this.country = country; }
    
    public String getPostcode() { return postcode; }
    public void setPostcode(String postcode) { this.postcode = postcode; }

    public String getVerificationType() { return verificationType; }
    public void setVerificationType(String verificationType) { this.verificationType = verificationType; }
}