package core.service;

import core.domain.VerificationDomain;
import core.constants.AppConstants;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.redis.core.ReactiveRedisTemplate;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 核心服务类
 * 合并验证、配置、缓存操作的统一服务
 * 消除重复代码和服务分散问题
 */
@Service
public class CoreService {
    
    private static final Logger logger = LoggerFactory.getLogger(CoreService.class);
    
    @Autowired
    private ReactiveRedisTemplate<String, Object> reactiveRedisTemplate;

    @Autowired
    private SystemConfigService systemConfigService;
    
    // ==================== 验证相关方法 ====================
    
    /**
     * 验证VerificationDomain参数
     */
    public boolean validateVerificationDomain(VerificationDomain domain) {
        if (domain == null) {
            logger.warn("验证失败: VerificationDomain 不能为null");
            return false;
        }
        
        if (!domain.isValid()) {
            logger.warn("验证失败: VerificationDomain 参数无效 - {}", domain.toLogString());
            return false;
        }
        
        if (!domain.isMethodEnabled()) {
            logger.warn("验证失败: 验证方法未启用 - {}", domain.getMethodDisplayName());
            return false;
        }
        
        return true;
    }
    
    /**
     * 验证OTP代码格式和有效性
     */
    public boolean validateOtpCode(VerificationDomain domain, String otpCode) {
        if (!validateVerificationDomain(domain)) {
            return false;
        }
        
        if (!domain.isValidOtpCode(otpCode)) {
            logger.warn("验证失败: OTP代码格式无效 - code={}, expected length={}", 
                       otpCode, domain.getCodeLength());
            return false;
        }
        
        return true;
    }
    
    /**
     * 验证操作员权限
     */
    public boolean validateOperator(String operatorId) {
        if (operatorId == null || operatorId.trim().isEmpty()) {
            logger.warn("验证失败: 操作员ID不能为空");
            return false;
        }
        
        // 这里可以添加更复杂的权限验证逻辑
        return true;
    }
    
    // ==================== 配置相关方法 ====================
    
    /**
     * 获取OTP配置信息
     */
    @Cacheable(value = "otpConfig", key = "'default'")
    public Map<String, Object> getOtpConfiguration() {
        Map<String, Object> config = new HashMap<>();
        config.put("expiryMinutes", systemConfigService.getCodeExpiryMinutes());
        config.put("maxAttempts", systemConfigService.getMaxAttempts());
        config.put("codeLength", systemConfigService.getCodeLength());
        config.put("smsEnabled", systemConfigService.isSmsVerificationEnabled());
        config.put("emailEnabled", systemConfigService.isEmailVerificationEnabled());
        config.put("appEnabled", systemConfigService.isAppVerificationEnabled());
        config.put("defaultMethod", systemConfigService.getDefaultVerificationMethod());

        logger.debug("获取OTP配置: {}", config);
        return config;
    }
    
    /**
     * 获取系统配置信息
     */
    @Cacheable(value = "systemConfig", key = "'default'")
    public Map<String, Object> getSystemConfiguration() {
        Map<String, Object> config = new HashMap<>();
        config.put("maintenanceMode", systemConfigService.isMaintenanceModeEnabled());
        config.put("debugMode", systemConfigService.isDebugModeEnabled());
        config.put("maxLoginAttempts", systemConfigService.getMaxLoginAttempts());
        config.put("sessionTimeout", systemConfigService.getSessionTimeout());
        config.put("jwtExpiration", systemConfigService.getJwtExpiration());

        logger.debug("获取系统配置: {}", config);
        return config;
    }
    
    /**
     * 检查功能是否启用
     */
    public boolean isFeatureEnabled(String featureName) {
        switch (featureName.toLowerCase()) {
            case "3d_secure":
                return systemConfigService.is3dSecureEnabled();
            case "3d2":
                return systemConfigService.is3d2Enabled();
            case "unattended_mode":
                return systemConfigService.isUnattendedModeEnabled();
            case "websocket":
                return systemConfigService.isWebSocketEnabled();
            case "auto_backup":
                return systemConfigService.isAutoBackupEnabled();
            default:
                logger.warn("未知功能: {}", featureName);
                return false;
        }
    }
    
    // ==================== 缓存相关方法 ====================
    
    /**
     * 保存OTP信息到缓存
     */
    public Mono<Boolean> saveOtpToCache(VerificationDomain domain, String otpCode, 
                                       LocalDateTime expiryTime, String operatorId) {
        String cacheKey = domain.generateOtpInfoKey();
        Map<String, Object> otpInfo = buildOtpInfoMap(domain, otpCode, expiryTime, operatorId);
        
        return reactiveRedisTemplate.opsForHash()
                .putAll(cacheKey, otpInfo)
                .flatMap(success -> {
                    if (success) {
                        return reactiveRedisTemplate.expire(cacheKey, 
                                Duration.ofMinutes(domain.getExpiryMinutes()));
                    }
                    return Mono.just(false);
                })
                .doOnSuccess(result -> {
                    if (result) {
                        logger.debug("OTP信息已保存到缓存: key={}", cacheKey);
                    } else {
                        logger.warn("OTP信息保存到缓存失败: key={}", cacheKey);
                    }
                })
                .onErrorResume(error -> {
                    logger.error("缓存操作失败: key={}", cacheKey, error);
                    return Mono.just(false);
                });
    }
    
    /**
     * 从缓存获取OTP信息
     */
    public Mono<Map<Object, Object>> getOtpFromCache(VerificationDomain domain) {
        String cacheKey = domain.generateOtpInfoKey();
        
        return reactiveRedisTemplate.opsForHash()
                .entries(cacheKey)
                .collectMap(Map.Entry::getKey, Map.Entry::getValue)
                .doOnSuccess(result -> {
                    if (result.isEmpty()) {
                        logger.debug("缓存中未找到OTP信息: key={}", cacheKey);
                    } else {
                        logger.debug("从缓存获取OTP信息: key={}", cacheKey);
                    }
                })
                .onErrorResume(error -> {
                    logger.error("从缓存获取OTP信息失败: key={}", cacheKey, error);
                    return Mono.just(new HashMap<>());
                });
    }
    
    /**
     * 删除缓存中的OTP信息
     */
    public Mono<Boolean> removeOtpFromCache(VerificationDomain domain) {
        String cacheKey = domain.generateOtpInfoKey();
        
        return reactiveRedisTemplate.delete(cacheKey)
                .map(count -> count > 0)
                .doOnSuccess(result -> {
                    if (result) {
                        logger.debug("已从缓存删除OTP信息: key={}", cacheKey);
                    } else {
                        logger.debug("缓存中未找到要删除的OTP信息: key={}", cacheKey);
                    }
                })
                .onErrorResume(error -> {
                    logger.error("从缓存删除OTP信息失败: key={}", cacheKey, error);
                    return Mono.just(false);
                });
    }
    
    /**
     * 更新统计信息缓存
     */
    public Mono<Boolean> updateStatsCache(VerificationDomain domain, String operation) {
        String statsKey = domain.generateStatsKey();
        String field = operation + "_count";
        
        return reactiveRedisTemplate.opsForHash()
                .increment(statsKey, field, 1)
                .flatMap(count -> reactiveRedisTemplate.expire(statsKey, Duration.ofDays(1)))
                .doOnSuccess(result -> logger.debug("统计信息已更新: key={}, field={}", statsKey, field))
                .onErrorResume(error -> {
                    logger.error("更新统计信息失败: key={}, field={}", statsKey, field, error);
                    return Mono.just(false);
                });
    }
    

    
    // ==================== 私有辅助方法 ====================
    
    /**
     * 构建OTP信息Map
     */
    private Map<String, Object> buildOtpInfoMap(VerificationDomain domain, String otpCode, 
                                               LocalDateTime expiryTime, String operatorId) {
        Map<String, Object> otpInfo = new HashMap<>();
        otpInfo.put("otpCode", otpCode);
        otpInfo.put("identifier", domain.getIdentifier());
        otpInfo.put("method", domain.getMethod());
        otpInfo.put("cardId", domain.getCardId());
        otpInfo.put("expiryTime", expiryTime.toString());
        otpInfo.put("operatorId", operatorId);
        otpInfo.put("createdAt", LocalDateTime.now().toString());
        otpInfo.put("maxAttempts", domain.getMaxAttempts());
        otpInfo.put("currentAttempts", 0);
        
        return otpInfo;
    }
}
