<template>
  <span
    :class="[
      'inline-flex items-center px-2 py-1 rounded-full text-xs font-medium',
      statusConfig.type === 'success' ? 'bg-green-100 text-green-800' :
      statusConfig.type === 'warning' ? 'bg-yellow-100 text-yellow-800' :
      statusConfig.type === 'danger' ? 'bg-red-100 text-red-800' :
      statusConfig.type === 'primary' ? 'bg-blue-100 text-blue-800' :
      'bg-gray-100 text-gray-800'
    ]"
    :style="showCustomColor ? { backgroundColor: statusConfig.color + '20', color: statusConfig.color } : undefined"
  >
    <span
      v-if="showIcon && statusConfig.icon"
      class="mr-1 text-xs"
    >
      {{ getIconEmoji(statusConfig.icon) }}
    </span>
    {{ statusConfig.text }}
    <button
      v-if="closable"
      @click="$emit('close')"
      class="ml-1 text-xs hover:text-gray-600"
    >
      ×
    </button>
  </span>
</template>

<script lang="ts" setup>
import { computed } from 'vue';
import { useStatusMapper } from '../../utils/status-mapper';

interface Props {
  /** 状态值 */
  status: string;
  /** 状态类型 */
  statusType: 'paymentCard' | 'verification' | 'userOperation';
  /** 标签大小 */
  size?: 'large' | 'default' | 'small';
  /** 标签效果 */
  effect?: 'dark' | 'light' | 'plain';
  /** 是否圆角 */
  round?: boolean;
  /** 是否可关闭 */
  closable?: boolean;
  /** 是否显示图标 */
  showIcon?: boolean;
  /** 图标大小 */
  iconSize?: number;
  /** 是否使用自定义颜色 */
  showCustomColor?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  size: 'default',
  effect: 'light',
  round: false,
  closable: false,
  showIcon: true,
  iconSize: 14,
  showCustomColor: false,
});

defineEmits<{
  close: [];
}>();

const { getPaymentCardStatus, getVerificationStatus, getUserOperationStatus } = useStatusMapper();

const statusConfig = computed(() => {
  switch (props.statusType) {
    case 'paymentCard':
      return getPaymentCardStatus(props.status);
    case 'verification':
      return getVerificationStatus(props.status);
    case 'userOperation':
      return getUserOperationStatus(props.status);
    default:
      return {
        text: props.status,
        type: 'info' as const,
        color: '#909399',
      };
  }
});

// 简单的图标映射到emoji
const getIconEmoji = (iconName: string) => {
  const iconMap: Record<string, string> = {
    'svg:cake': '⏰',
    'svg:download': '✅',
    'svg:card': '⏸️',
    'svg:antdv-logo': '📅',
    'svg:smartphone': '📱',
    'svg:mail': '📧',
    'svg:bell': '🔔',
    'svg:key': '🔑',
    'svg:link': '🔗',
    'svg:ban': '🚫',
    'svg:credit-card': '💳',
    'svg:lock': '🔒',
    'svg:loader': '⏳',
  };
  return iconMap[iconName] || '•';
};
</script>
