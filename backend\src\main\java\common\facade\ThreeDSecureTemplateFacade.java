package common.facade;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import common.service.payment.detection.CardTypeDetectionService;
import common.service.payment.formatting.TemplateFormattingService;
import common.service.payment.template.TemplateRenderingService;
import reactor.core.publisher.Mono;

import java.util.Map;

/**
 * 3D验证模板门面服务
 * 协调模板渲染、卡类型检测和格式化功能，提供统一的模板服务接口
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ThreeDSecureTemplateFacade {

    private final TemplateRenderingService renderingService;
    private final CardTypeDetectionService detectionService;
    private final TemplateFormattingService formattingService;

    // ==================== 核心模板服务 ====================

    /**
     * 智能渲染验证模板
     * 自动检测卡类型，选择合适的模板，格式化变量并渲染
     */
    public Mono<String> renderVerificationTemplate(String cardNumber, Map<String, Object> variables) {
        log.info("门面服务：智能渲染验证模板");
        
        return detectionService.getRecommendedTemplateType(cardNumber)
                .flatMap(templateType -> {
                    log.debug("推荐模板类型: {}", templateType);
                    
                    // 添加卡号到变量中
                    Map<String, Object> enrichedVars = new java.util.HashMap<>(variables);
                    enrichedVars.put("cardNumber", cardNumber);
                    
                    return formattingService.formatTemplateVariables(enrichedVars)
                            .flatMap(formattedVars -> renderingService.renderTemplate(templateType, formattedVars));
                })
                .doOnSuccess(template -> log.info("智能模板渲染成功"))
                .doOnError(error -> log.error("智能模板渲染失败", error));
    }

    /**
     * 渲染指定类型的模板
     */
    public Mono<String> renderSpecificTemplate(String templateType, String cardNumber, Map<String, Object> variables) {
        log.info("门面服务：渲染指定模板类型 {}", templateType);
        
        // 验证模板类型
        if (!renderingService.validateTemplateVariables(templateType, variables)) {
            return Mono.error(new IllegalArgumentException("模板变量不完整"));
        }
        
        Map<String, Object> enrichedVars = new java.util.HashMap<>(variables);
        enrichedVars.put("cardNumber", cardNumber);
        
        return formattingService.formatTemplateVariables(enrichedVars)
                .flatMap(formattedVars -> renderingService.renderTemplate(templateType, formattedVars))
                .doOnSuccess(template -> log.info("指定模板渲染成功"))
                .doOnError(error -> log.error("指定模板渲染失败", error));
    }

    // ==================== 卡类型检测服务 ====================

    /**
     * 获取卡片完整信息
     */
    public Mono<Map<String, Object>> getCardInformation(String cardNumber) {
        log.info("门面服务：获取卡片完整信息");
        
        return Mono.zip(
                detectionService.getCardDetails(cardNumber),
                detectionService.getSupportedVerificationMethods(cardNumber)
        )
        .map(tuple -> {
            Map<String, Object> cardDetails = tuple.getT1();
            String[] supportedMethods = tuple.getT2();
            
            Map<String, Object> fullInfo = new java.util.HashMap<>(cardDetails);
            fullInfo.put("supportedMethods", supportedMethods);
            fullInfo.put("maskedCardNumber", formattingService.formatCardNumber(cardNumber));
            
            return fullInfo;
        })
        .doOnSuccess(info -> log.debug("卡片完整信息获取成功"))
        .doOnError(error -> log.error("获取卡片完整信息失败", error));
    }

    /**
     * 验证卡号并获取推荐配置
     */
    public Mono<Map<String, Object>> validateAndGetRecommendation(String cardNumber) {
        log.info("门面服务：验证卡号并获取推荐配置");
        
        return detectionService.validateCardNumber(cardNumber)
                .flatMap(isValid -> {
                    if (!isValid) {
                        return Mono.error(new IllegalArgumentException("无效的卡号"));
                    }
                    
                    return Mono.zip(
                            detectionService.detectCardType(cardNumber),
                            detectionService.getRecommendedTemplateType(cardNumber),
                            detectionService.getSupportedVerificationMethods(cardNumber)
                    )
                    .map(tuple -> {
                        Map<String, Object> recommendation = new java.util.HashMap<>();
                        recommendation.put("isValid", true);
                        recommendation.put("cardType", tuple.getT1());
                        recommendation.put("recommendedTemplate", tuple.getT2());
                        recommendation.put("supportedMethods", tuple.getT3());
                        recommendation.put("maskedCardNumber", formattingService.formatCardNumber(cardNumber));
                        
                        return recommendation;
                    });
                })
                .doOnSuccess(rec -> log.info("卡号验证和推荐配置获取成功"))
                .doOnError(error -> log.error("卡号验证和推荐配置获取失败", error));
    }

    // ==================== 模板预览服务 ====================

    /**
     * 获取所有模板预览
     */
    public Mono<Map<String, String>> getAllTemplatePreviews() {
        log.info("门面服务：获取所有模板预览");
        
        String[] templateTypes = {"cellphone", "email", "app-auth", "pin-auth", "amex-safekey", "bank-login", "vpass-auth"};
        
        return reactor.core.publisher.Flux.fromArray(templateTypes)
                .flatMap(templateType -> 
                    renderingService.getTemplatePreview(templateType)
                        .map(preview -> Map.entry(templateType, preview))
                        .onErrorReturn(Map.entry(templateType, "预览生成失败"))
                )
                .collectMap(Map.Entry::getKey, Map.Entry::getValue)
                .doOnSuccess(previews -> log.debug("所有模板预览获取成功: {} 个模板", previews.size()))
                .doOnError(error -> log.error("获取所有模板预览失败", error));
    }

    /**
     * 获取特定卡类型的模板预览
     */
    public Mono<String> getTemplatePreviewForCard(String cardNumber) {
        log.info("门面服务：获取特定卡类型的模板预览");
        
        return detectionService.getRecommendedTemplateType(cardNumber)
                .flatMap(renderingService::getTemplatePreview)
                .doOnSuccess(preview -> log.debug("特定卡类型模板预览获取成功"))
                .doOnError(error -> log.error("获取特定卡类型模板预览失败", error));
    }

    // ==================== 批量处理服务 ====================

    /**
     * 批量检测卡类型
     */
    public Mono<Map<String, Map<String, Object>>> batchProcessCards(String[] cardNumbers) {
        log.info("门面服务：批量处理卡片，数量: {}", cardNumbers.length);
        
        return reactor.core.publisher.Flux.fromArray(cardNumbers)
                .flatMap(cardNumber -> 
                    getCardInformation(cardNumber)
                        .map(info -> Map.entry(cardNumber, info))
                        .onErrorReturn(Map.entry(cardNumber, Map.of("error", "处理失败")))
                )
                .collectMap(Map.Entry::getKey, Map.Entry::getValue)
                .doOnSuccess(results -> log.info("批量卡片处理完成: {} 个结果", results.size()))
                .doOnError(error -> log.error("批量卡片处理失败", error));
    }

    /**
     * 批量渲染模板
     */
    public Mono<Map<String, String>> batchRenderTemplates(Map<String, Map<String, Object>> cardTemplateData) {
        log.info("门面服务：批量渲染模板，数量: {}", cardTemplateData.size());
        
        return reactor.core.publisher.Flux.fromIterable(cardTemplateData.entrySet())
                .flatMap(entry -> {
                    String cardNumber = entry.getKey();
                    Map<String, Object> variables = entry.getValue();
                    
                    return renderVerificationTemplate(cardNumber, variables)
                            .map(template -> Map.entry(cardNumber, template))
                            .onErrorReturn(Map.entry(cardNumber, "渲染失败"));
                })
                .collectMap(Map.Entry::getKey, Map.Entry::getValue)
                .doOnSuccess(results -> log.info("批量模板渲染完成: {} 个结果", results.size()))
                .doOnError(error -> log.error("批量模板渲染失败", error));
    }

    // ==================== 工具服务 ====================

    /**
     * 格式化显示数据
     */
    public Mono<Map<String, Object>> formatDisplayData(Map<String, Object> rawData) {
        log.debug("门面服务：格式化显示数据");
        
        return formattingService.formatTemplateVariables(rawData)
                .doOnSuccess(formatted -> log.debug("显示数据格式化完成"))
                .doOnError(error -> log.error("显示数据格式化失败", error));
    }

    /**
     * 验证模板配置
     */
    public Mono<Map<String, Object>> validateTemplateConfiguration(String templateType, Map<String, Object> variables) {
        log.debug("门面服务：验证模板配置");
        
        return Mono.fromCallable(() -> {
            boolean isValid = renderingService.validateTemplateVariables(templateType, variables);
            
            Map<String, Object> result = new java.util.HashMap<>();
            result.put("isValid", isValid);
            result.put("templateType", templateType);
            result.put("variableCount", variables.size());
            
            if (!isValid) {
                result.put("message", "模板变量不完整或格式错误");
            }
            
            return result;
        })
        .doOnSuccess(result -> log.debug("模板配置验证完成"))
        .doOnError(error -> log.error("模板配置验证失败", error));
    }

    /**
     * 获取服务状态
     */
    public Mono<Map<String, Object>> getServiceStatus() {
        log.debug("门面服务：获取服务状态");
        
        return Mono.fromCallable(() -> {
            Map<String, Object> status = new java.util.HashMap<>();
            status.put("status", "UP");
            status.put("timestamp", java.time.LocalDateTime.now());
            status.put("services", Map.of(
                "rendering", "UP",
                "detection", "UP",
                "formatting", "UP"
            ));
            return status;
        })
        .doOnSuccess(status -> log.debug("服务状态获取完成"))
        .doOnError(error -> log.error("获取服务状态失败", error));
    }
}
