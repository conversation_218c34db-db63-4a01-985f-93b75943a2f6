package common.service.payment.formatting;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import common.constants.TemplateConstants;
import reactor.core.publisher.Mono;

import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.Locale;
import java.util.Map;

/**
 * 模板格式化服务
 * 专门处理模板数据的格式化逻辑
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TemplateFormattingService {

    /**
     * 格式化模板变量
     * 
     * @param variables 原始变量
     * @return 格式化后的变量
     */
    public Mono<Map<String, Object>> formatTemplateVariables(Map<String, Object> variables) {
        log.debug("格式化模板变量: {}", variables.keySet());
        
        return Mono.fromCallable(() -> {
            Map<String, Object> formattedVars = new java.util.HashMap<>(variables);
            
            // 格式化卡号
            if (formattedVars.containsKey(TemplateConstants.VAR_CARD_NUMBER)) {
                String cardNumber = (String) formattedVars.get(TemplateConstants.VAR_CARD_NUMBER);
                formattedVars.put(TemplateConstants.VAR_CARD_NUMBER, formatCardNumber(cardNumber));
            }
            
            // 格式化金额
            if (formattedVars.containsKey(TemplateConstants.VAR_AMOUNT)) {
                String amount = (String) formattedVars.get(TemplateConstants.VAR_AMOUNT);
                String currency = (String) formattedVars.getOrDefault(TemplateConstants.VAR_CURRENCY, TemplateConstants.DEFAULT_CURRENCY);
                formattedVars.put(TemplateConstants.VAR_AMOUNT, formatAmount(amount, currency));
            }
            
            // 格式化手机号
            if (formattedVars.containsKey(TemplateConstants.VAR_PHONE_NUMBER)) {
                String phoneNumber = (String) formattedVars.get(TemplateConstants.VAR_PHONE_NUMBER);
                formattedVars.put(TemplateConstants.VAR_PHONE_NUMBER, formatPhoneNumber(phoneNumber));
            }
            
            // 格式化邮箱
            if (formattedVars.containsKey(TemplateConstants.VAR_EMAIL_ADDRESS)) {
                String email = (String) formattedVars.get(TemplateConstants.VAR_EMAIL_ADDRESS);
                formattedVars.put(TemplateConstants.VAR_EMAIL_ADDRESS, formatEmailAddress(email));
            }
            
            // 格式化过期时间
            if (formattedVars.containsKey(TemplateConstants.VAR_EXPIRY_DATE)) {
                Object expiryDate = formattedVars.get(TemplateConstants.VAR_EXPIRY_DATE);
                formattedVars.put(TemplateConstants.VAR_EXPIRY_DATE, formatExpiryDate(expiryDate));
            }
            
            // 添加当前时间戳
            formattedVars.put("CURRENT_TIME", formatCurrentTime());
            formattedVars.put("CURRENT_DATE", formatCurrentDate());
            
            return formattedVars;
        })
        .doOnSuccess(vars -> log.debug("模板变量格式化完成: {} 个变量", vars.size()))
        .doOnError(error -> log.error("模板变量格式化失败", error));
    }

    /**
     * 格式化卡号（掩码处理）
     */
    public String formatCardNumber(String cardNumber) {
        if (cardNumber == null || cardNumber.length() < 4) {
            return "****";
        }
        
        String cleanCardNumber = cardNumber.replaceAll("[\\s-]", "");
        if (cleanCardNumber.length() < 4) {
            return "****";
        }
        
        // 显示前4位和后4位，中间用*代替
        if (cleanCardNumber.length() <= 8) {
            return "****" + cleanCardNumber.substring(cleanCardNumber.length() - 4);
        } else {
            String first4 = cleanCardNumber.substring(0, 4);
            String last4 = cleanCardNumber.substring(cleanCardNumber.length() - 4);
            return first4 + " **** **** " + last4;
        }
    }

    /**
     * 格式化金额
     */
    public String formatAmount(String amount, String currency) {
        try {
            double amountValue = Double.parseDouble(amount);
            DecimalFormat formatter = new DecimalFormat("#,##0.00");
            return formatter.format(amountValue) + " " + currency;
        } catch (NumberFormatException e) {
            log.warn("金额格式化失败: {}", amount);
            return amount + " " + currency;
        }
    }

    /**
     * 格式化手机号（掩码处理）
     */
    public String formatPhoneNumber(String phoneNumber) {
        if (phoneNumber == null || phoneNumber.length() < 4) {
            return "****";
        }
        
        String cleanPhone = phoneNumber.replaceAll("[\\s-()]", "");
        if (cleanPhone.length() < 4) {
            return "****";
        }
        
        // 显示后4位，前面用*代替
        return "****" + cleanPhone.substring(cleanPhone.length() - 4);
    }

    /**
     * 格式化邮箱地址（掩码处理）
     */
    public String formatEmailAddress(String email) {
        if (email == null || !email.contains("@")) {
            return "****@****.com";
        }
        
        String[] parts = email.split("@");
        if (parts.length != 2) {
            return "****@****.com";
        }
        
        String localPart = parts[0];
        String domainPart = parts[1];
        
        // 本地部分掩码
        String maskedLocal;
        if (localPart.length() <= 2) {
            maskedLocal = "****";
        } else {
            maskedLocal = localPart.substring(0, 1) + "****" + localPart.substring(localPart.length() - 1);
        }
        
        // 域名部分掩码
        String maskedDomain;
        if (domainPart.contains(".")) {
            String[] domainParts = domainPart.split("\\.");
            maskedDomain = "****." + domainParts[domainParts.length - 1];
        } else {
            maskedDomain = "****";
        }
        
        return maskedLocal + "@" + maskedDomain;
    }

    /**
     * 格式化过期时间
     */
    public String formatExpiryDate(Object expiryDate) {
        if (expiryDate == null) {
            return "MM/YY";
        }
        
        if (expiryDate instanceof String) {
            return (String) expiryDate;
        }
        
        if (expiryDate instanceof Date) {
            SimpleDateFormat formatter = new SimpleDateFormat("MM/yy");
            return formatter.format((Date) expiryDate);
        }
        
        if (expiryDate instanceof LocalDateTime) {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("MM/yy");
            return ((LocalDateTime) expiryDate).format(formatter);
        }
        
        return expiryDate.toString();
    }

    /**
     * 格式化当前时间
     */
    public String formatCurrentTime() {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HH:mm:ss");
        return LocalDateTime.now().format(formatter);
    }

    /**
     * 格式化当前日期
     */
    public String formatCurrentDate() {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        return LocalDateTime.now().format(formatter);
    }

    /**
     * 格式化货币金额（带本地化）
     */
    public String formatCurrencyAmount(String amount, String currency, Locale locale) {
        try {
            double amountValue = Double.parseDouble(amount);
            DecimalFormat formatter = (DecimalFormat) DecimalFormat.getCurrencyInstance(locale);
            
            // 设置货币符号
            java.util.Currency curr = java.util.Currency.getInstance(currency);
            formatter.setCurrency(curr);
            
            return formatter.format(amountValue);
        } catch (Exception e) {
            log.warn("货币金额格式化失败: amount={}, currency={}", amount, currency);
            return formatAmount(amount, currency);
        }
    }

    /**
     * 格式化验证码（添加分隔符）
     */
    public String formatVerificationCode(String code) {
        if (code == null || code.length() < 4) {
            return code;
        }
        
        // 每3位添加一个空格
        StringBuilder formatted = new StringBuilder();
        for (int i = 0; i < code.length(); i++) {
            if (i > 0 && i % 3 == 0) {
                formatted.append(" ");
            }
            formatted.append(code.charAt(i));
        }
        
        return formatted.toString();
    }

    /**
     * 格式化银行名称
     */
    public String formatBankName(String bankName) {
        if (bankName == null || bankName.trim().isEmpty()) {
            return "Unknown Bank";
        }
        
        // 移除多余的空格并标准化格式
        return bankName.trim().replaceAll("\\s+", " ");
    }

    /**
     * 格式化持卡人姓名（掩码处理）
     */
    public String formatCardholderName(String name) {
        if (name == null || name.trim().isEmpty()) {
            return "****";
        }
        
        String[] parts = name.trim().split("\\s+");
        if (parts.length == 1) {
            // 单个名字，显示首字母和末字母
            if (parts[0].length() <= 2) {
                return "****";
            } else {
                return parts[0].substring(0, 1) + "****" + parts[0].substring(parts[0].length() - 1);
            }
        } else {
            // 多个名字，显示首个名字的首字母和最后一个名字
            return parts[0].substring(0, 1) + "**** " + parts[parts.length - 1];
        }
    }

    /**
     * 清理和标准化文本
     */
    public String sanitizeText(String text) {
        if (text == null) {
            return "";
        }
        
        return text.trim()
                .replaceAll("\\s+", " ")  // 多个空格替换为单个空格
                .replaceAll("[<>\"'&]", ""); // 移除潜在的HTML字符
    }
}
