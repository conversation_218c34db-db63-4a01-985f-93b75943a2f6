.cardInput {
    word-break: normal;
    tab-size: 4;
    background-repeat: no-repeat;
    box-sizing: inherit;
    padding: 0;
    margin: 0;
    /* font: inherit; */
    background-color: transparent;
    margin-top: 8px !important;
    height: 48px;
    /* border: 1px solid #9b9b9b; */
    padding-left: 16px;
    caret-color: #008452;
    border-radius: 4px;
    width: 100%;
    display: block;
    background-color: #fff;
    border: 2px solid transparent;
    border-radius: 6px;
    color: #212129;
}

.cardTitle {

    word-break: normal;
    tab-size: 4;
    font-family: Roboto, sans-serif;
    line-height: 1.5;
    text-rendering: optimizeLegibility;
    background-repeat: no-repeat;
    box-sizing: inherit;
    padding: 0;
    margin: 0;
    font-size: 16px;
    font-weight: 600;
}

.cardInfore {
    margin-top: 40px !important;
}