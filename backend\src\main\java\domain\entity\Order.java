package domain.entity;

import org.springframework.data.annotation.Id;
import org.springframework.data.relational.core.mapping.Column;
import org.springframework.data.relational.core.mapping.Table;

import java.math.BigDecimal;

// 订单实体 - 响应式版本
@Table("orders")
public class Order extends BaseEntity {
    @Id
    private Long id;

    @Column("order_number")
    private String orderNumber;

    @Column("user_id")
    private Long userId;

    @Column("amount")
    private BigDecimal amount;

    @Column("status")
    private OrderStatus status;

    @Column("description")
    private String description;

    @Column("payment_transaction_id")
    private Long paymentTransactionId;

    public enum OrderStatus {
        PENDING,    // 待处理
        BOUND,      // 已绑定
        COMPLETED,  // 已完成
        DELETED     // 已删除
    }

    // Getter和Setter方法
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }
    
    public String getOrderNumber() { return orderNumber; }
    public void setOrderNumber(String orderNumber) { this.orderNumber = orderNumber; }
    
    public Long getUserId() { return userId; }
    public void setUserId(Long userId) { this.userId = userId; }

    public BigDecimal getAmount() { return amount; }
    public void setAmount(BigDecimal amount) { this.amount = amount; }

    public OrderStatus getStatus() { return status; }
    public void setStatus(OrderStatus status) { this.status = status; }

    public String getDescription() { return description; }
    public void setDescription(String description) { this.description = description; }

    public Long getPaymentTransactionId() { return paymentTransactionId; }
    public void setPaymentTransactionId(Long paymentTransactionId) { this.paymentTransactionId = paymentTransactionId; }
}