package security.service;

import core.constants.AppConstants;
import domain.entity.Domain;
import domain.entity.DomainSecurityHistory;
import domain.repository.DomainRepository;
import domain.repository.DomainSecurityHistoryRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import common.service.WebSocketService;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import reactor.core.publisher.Mono;
import reactor.core.publisher.Flux;

/**
 * 域名管理服务
 */
@Slf4j
@Service
@Transactional
public class DomainService {

    @Autowired
    private DomainRepository domainRepository;

    @Autowired
    private SecurityCheckService securityCheckService;

    @Autowired
    private DomainSecurityHistoryRepository securityHistoryRepository;

    @Autowired
    private WebSocketService webSocketService;

    /**
     * 获取所有域名
     */
    public Mono<List<Domain>> getAllDomains() {
        return domainRepository.findAllByOrderByCreatedAtDesc().collectList();
    }

    /**
     * 根据ID获取域名
     */
    public Mono<Domain> getDomainById(Long id) {
        return domainRepository.findById(id);
    }

    /**
     * 检查域名是否存在
     */
    public Mono<Boolean> existsByDomainName(String domainName) {
        return domainRepository.existsByDomainName(domainName);
    }

    /**
     * 创建域名
     */
    public Mono<Domain> createDomain(Domain domain) {
        domain.setStatus(Domain.DomainStatus.NORMAL);
        domain.setCreatedAt(LocalDateTime.now());
        domain.setUpdatedAt(LocalDateTime.now());

        return domainRepository.save(domain)
            .doOnSuccess(savedDomain -> {
                // 异步执行初始检测
                CompletableFuture.runAsync(() -> {
                    performInitialChecks(savedDomain.getId());
                });

                // 通过WebSocket通知前端
                webSocketService.broadcastDomainUpdate(savedDomain);
            });
    }

    /**
     * 更新域名
     */
    public Mono<Domain> updateDomain(Domain domain) {
        return domainRepository.findById(domain.getId())
            .flatMap(existing -> {
                existing.setDomainName(domain.getDomainName());
                existing.setDomainPath(domain.getDomainPath());
                existing.setDescription(domain.getDescription());
                existing.setAutoCheckEnabled(domain.getAutoCheckEnabled());
                existing.setCheckInterval(domain.getCheckInterval());
                existing.setTemplateType(domain.getTemplateType());
                existing.setUpdatedAt(LocalDateTime.now());

                return domainRepository.save(existing)
                    .doOnSuccess(savedDomain -> webSocketService.broadcastDomainUpdate(savedDomain));
            });
    }

    /**
     * 删除域名
     */
    public Mono<Boolean> deleteDomain(Long id) {
        return domainRepository.existsById(id)
            .flatMap(exists -> {
                if (exists) {
                    return domainRepository.deleteById(id)
                        .doOnSuccess(v -> webSocketService.broadcastDomainDelete(id))
                        .then(Mono.just(true));
                }
                return Mono.just(false);
            });
    }

    /**
     * 执行安全检测
     */
    public Mono<Map<String, Object>> performSecurityCheck(Long id) {
        return domainRepository.findById(id)
            .switchIfEmpty(Mono.error(new RuntimeException("域名不存在")))
            .flatMap(domain -> {
                // 调用Google透明度报告API（响应式）
                return securityCheckService.checkDomainSecurity(domain.getDomainName())
                    .flatMap(securityResult -> {
                        // 更新域名状态
                        String status = (String) securityResult.get("status");
                        domain.setStatus(Domain.DomainStatus.valueOf(status.toUpperCase()));
                        domain.setSecurityScore((Integer) securityResult.get("score"));
                        domain.setLastSecurityCheck(LocalDateTime.now());
                        domain.setLastError(null);

                        return domainRepository.save(domain)
                            .doOnSuccess(savedDomain -> webSocketService.broadcastDomainUpdate(savedDomain))
                            .map(savedDomain -> {
                                Map<String, Object> result = new HashMap<>();
                                result.put("success", true);
                                result.put("status", status);
                                result.put("score", securityResult.get("score"));
                                result.put("details", securityResult.get("details"));
                                return result;
                            });
                    })
                    .onErrorResume(e -> {
                        log.error("安全检测失败: {}", e.getMessage(), e);
                        domain.setLastError(e.getMessage());
                        domain.setLastSecurityCheck(LocalDateTime.now());

                        return domainRepository.save(domain)
                            .map(savedDomain -> {
                                Map<String, Object> result = new HashMap<>();
                                result.put("success", false);
                                result.put("error", e.getMessage());
                                return result;
                            });
                    });
            });
    }

    /**
     * 批量安全检测
     */
    public Mono<Map<String, Object>> batchSecurityCheck() {
        return domainRepository.findByAutoCheckEnabledTrue()
            .collectList()
            .flatMap(domains -> {
                // 过滤需要检测的域名
                List<Domain> domainsToCheck = domains.stream()
                    .filter(Domain::needsSecurityCheck)
                    .collect(java.util.stream.Collectors.toList());

                if (domainsToCheck.isEmpty()) {
                    Map<String, Object> result = new HashMap<>();
                    result.put("total", domains.size());
                    result.put("success", 0);
                    result.put("failure", 0);
                    result.put("timestamp", LocalDateTime.now());
                    return Mono.just(result);
                }

                // 并行执行安全检测
                return Flux.fromIterable(domainsToCheck)
                    .flatMap(domain ->
                        performSecurityCheck(domain.getId())
                            .map(checkResult -> (Boolean) checkResult.get("success"))
                            .onErrorReturn(false)
                    )
                    .collectList()
                    .map(results -> {
                        long successCount = results.stream().mapToLong(success -> success ? 1 : 0).sum();
                        long failureCount = results.size() - successCount;

                        Map<String, Object> result = new HashMap<>();
                        result.put("total", domains.size());
                        result.put("success", successCount);
                        result.put("failure", failureCount);
                        result.put("timestamp", LocalDateTime.now());
                        return result;
                    });
            });
    }

    /**
     * 获取域名统计信息
     */
    public Mono<Map<String, Object>> getDomainStatistics() {
        return Mono.zip(
            domainRepository.count(),
            domainRepository.countByStatus(Domain.DomainStatus.NORMAL),
            domainRepository.countByStatus(Domain.DomainStatus.UNSAFE),
            domainRepository.countByStatus(Domain.DomainStatus.PARTIAL_UNSAFE),
            domainRepository.countByStatus(Domain.DomainStatus.ERROR)
        ).map(tuple -> {
            Map<String, Object> statistics = new HashMap<>();
            statistics.put("total", tuple.getT1());
            statistics.put("normal", tuple.getT2());
            statistics.put("unsafe", tuple.getT3());
            statistics.put("partialUnsafe", tuple.getT4());
            statistics.put("error", tuple.getT5());

            return statistics;
        });
    }

    /**
     * 获取安全检测历史
     */
    public Mono<List<Map<String, Object>>> getSecurityHistory(Long id) {
        log.info("查询域名 {} 的安全检测历史", id);

        return domainRepository.findById(id)
            .flatMap(domain ->
                // 查询安全检测历史记录
                securityHistoryRepository.findByDomainIdOrderByCheckTimeDesc(id)
                    .collectList()
                    .map(historyList -> {
                        // 转换为前端需要的格式
                        List<Map<String, Object>> result = new ArrayList<>();
                        for (DomainSecurityHistory history : historyList) {
                            Map<String, Object> historyMap = convertSecurityHistoryToMap(history, domain);
                            result.add(historyMap);
                        }

                        log.info("查询到域名 {} 的 {} 条安全检测历史记录", domain.getDomainName(), result.size());
                        return result;
                    })
            )
            .switchIfEmpty(Mono.fromCallable(() -> {
                log.warn("域名不存在: {}", id);
                return new ArrayList<Map<String, Object>>();
            }))
            .onErrorResume(e -> {
                log.error("查询域名安全检测历史失败: domainId={}", id, e);
                return Mono.just(new ArrayList<>());
            });
    }

    /**
     * 获取安全检测历史（分页）
     */
    public Mono<Map<String, Object>> getSecurityHistoryPaged(Long id, int page, int size) {
        log.info("分页查询域名 {} 的安全检测历史: page={}, size={}", id, page, size);

        return domainRepository.findById(id)
                .flatMap(domain -> {
                    // 创建分页参数
                    org.springframework.data.domain.Pageable pageable =
                        org.springframework.data.domain.PageRequest.of(page - 1, size);

                    // 分页查询安全检测历史
                    return securityHistoryRepository.findByDomainIdOrderByCheckTimeDesc(id)
                        .collectList()
                        .map(allHistory -> {
                            Map<String, Object> result = new HashMap<>();

                            try {
                                // 手动分页
                                int totalElements = allHistory.size();
                                int totalPages = (int) Math.ceil((double) totalElements / size);
                                int startIndex = (page - 1) * size;
                                int endIndex = Math.min(startIndex + size, totalElements);

                                List<DomainSecurityHistory> pageContent = allHistory.subList(startIndex, endIndex);

                                // 转换数据
                                List<Map<String, Object>> historyList = new ArrayList<>();
                                for (DomainSecurityHistory history : pageContent) {
                                    Map<String, Object> historyMap = convertSecurityHistoryToMap(history, domain);
                                    historyList.add(historyMap);
                                }

                                // 构建分页结果
                                result.put("success", true);
                                result.put("data", historyList);
                                result.put("total", totalElements);
                                result.put("totalPages", totalPages);
                                result.put("currentPage", page);
                                result.put("pageSize", size);
                                result.put("hasNext", page < totalPages);
                                result.put("hasPrevious", page > 1);

                                log.info("分页查询完成: 域名={}, 总记录数={}, 当前页记录数={}",
                                    domain.getDomainName(), totalElements, historyList.size());

                            } catch (Exception e) {
                                log.error("分页查询安全检测历史失败: domainId={}, page={}, size={}", id, page, size, e);
                                result.put("success", false);
                                result.put("error", e.getMessage());
                            }

                            return result;
                        });
                })
                .switchIfEmpty(Mono.fromCallable(() -> {
                    Map<String, Object> errorResult = new HashMap<>();
                    errorResult.put("success", false);
                    errorResult.put("error", AppConstants.ErrorMessage.DOMAIN_NOT_FOUND);
                    return errorResult;
                }));
    }

    /**
     * 将安全检测历史转换为Map格式
     */
    private Map<String, Object> convertSecurityHistoryToMap(DomainSecurityHistory history, Domain domain) {
        Map<String, Object> map = new HashMap<>();

        // 基本信息
        map.put("id", history.getId());
        map.put("domainId", history.getDomainId());
        map.put("domainName", domain.getDomainName());
        map.put("checkTime", history.getCheckTime());
        map.put("checkSource", history.getCheckSource());

        // 安全状态
        map.put("securityStatus", history.getSecurityStatus());
        map.put("isSafe", history.getIsSafe());
        map.put("riskLevel", history.getRiskLevel());

        // 威胁信息
        Map<String, Boolean> threats = new HashMap<>();
        threats.put("malware", Boolean.TRUE.equals(history.getHasMalware()));
        threats.put("phishing", Boolean.TRUE.equals(history.getHasPhishing()));
        threats.put("unwantedSoftware", Boolean.TRUE.equals(history.getHasUnwantedSoftware()));
        threats.put("suspiciousActivity", Boolean.TRUE.equals(history.getHasSuspiciousActivity()));
        map.put("threats", threats);

        // 威胁统计
        map.put("hasAnyThreat", history.hasAnyThreat());
        map.put("threatCount", history.getThreatCount());
        map.put("threatDescription", history.getThreatDescription());

        // 错误信息
        if (history.getErrorMessage() != null && !history.getErrorMessage().trim().isEmpty()) {
            map.put("errorMessage", history.getErrorMessage());
        }

        // 状态颜色（用于前端显示）
        map.put("statusColor", getSecurityStatusColor(history.getSecurityStatus()));
        map.put("riskColor", getRiskLevelColor(history.getRiskLevel()));

        return map;
    }

    /**
     * 获取安全状态对应的颜色
     */
    private String getSecurityStatusColor(String securityStatus) {
        if (securityStatus == null) return AppConstants.StatusColor.GRAY;

        switch (securityStatus.toLowerCase()) {
            case AppConstants.SecurityStatus.SAFE:
                return AppConstants.StatusColor.GREEN;
            case AppConstants.SecurityStatus.UNSAFE:
                return AppConstants.StatusColor.RED;
            case AppConstants.SecurityStatus.PARTIALLY_UNSAFE:
                return AppConstants.StatusColor.YELLOW; // 使用黄色代替橙色
            case AppConstants.SecurityStatus.UNCOMMON_FILES:
                return AppConstants.StatusColor.YELLOW;
            case AppConstants.SecurityStatus.NO_DATA:
            case AppConstants.SecurityStatus.ERROR:
                return AppConstants.StatusColor.GRAY;
            default:
                return AppConstants.StatusColor.GRAY;
        }
    }

    /**
     * 获取风险等级对应的颜色
     */
    private String getRiskLevelColor(String riskLevel) {
        if (riskLevel == null) return AppConstants.StatusColor.GRAY;

        switch (riskLevel.toLowerCase()) {
            case AppConstants.RiskLevel.LOW:
                return AppConstants.StatusColor.GREEN;
            case AppConstants.RiskLevel.MEDIUM:
                return AppConstants.StatusColor.YELLOW;
            case AppConstants.RiskLevel.HIGH:
                return AppConstants.StatusColor.RED;
            case AppConstants.RiskLevel.UNKNOWN:
            default:
                return AppConstants.StatusColor.GRAY;
        }
    }

    /**
     * 获取安全检测统计信息
     */
    public Mono<Map<String, Object>> getSecurityHistoryStatistics(Long domainId) {
        return domainRepository.findById(domainId)
            .switchIfEmpty(Mono.error(new RuntimeException(AppConstants.ErrorMessage.DOMAIN_NOT_FOUND)))
            .flatMap(domain ->
                securityHistoryRepository.findByDomainIdOrderByCheckTimeDesc(domainId)
                    .collectList()
                    .map(historyList -> {
                        Map<String, Object> stats = new HashMap<>();

                        // 基本统计
                        stats.put("totalChecks", historyList.size());
                        stats.put("domainName", domain.getDomainName());

                        // 按状态统计
                        Map<String, Long> statusCounts = new HashMap<>();
                        Map<String, Long> riskCounts = new HashMap<>();
                        int threatCount = 0;

                        for (DomainSecurityHistory history : historyList) {
                            // 状态统计
                            String status = history.getSecurityStatus();
                            statusCounts.put(status, statusCounts.getOrDefault(status, 0L) + 1);

                            // 风险等级统计
                            String risk = history.getRiskLevel();
                            if (risk != null) {
                                riskCounts.put(risk, riskCounts.getOrDefault(risk, 0L) + 1);
                            }

                            // 威胁统计
                            if (history.hasAnyThreat()) {
                                threatCount++;
                            }
                        }

                        stats.put("statusCounts", statusCounts);
                        stats.put("riskCounts", riskCounts);
                        stats.put("threatCount", threatCount);

                        // 最近检测信息
                        if (!historyList.isEmpty()) {
                            DomainSecurityHistory latest = historyList.get(0);
                            Map<String, Object> latestCheck = new HashMap<>();
                            latestCheck.put("checkTime", latest.getCheckTime());
                            latestCheck.put("securityStatus", latest.getSecurityStatus());
                            latestCheck.put("isSafe", latest.getIsSafe());
                            latestCheck.put("riskLevel", latest.getRiskLevel());
                            latestCheck.put("hasAnyThreat", latest.hasAnyThreat());
                            stats.put("latestCheck", latestCheck);
                        }

                        stats.put("success", true);
                        return stats;
                    })
            )
            .onErrorResume(e -> {
                log.error("获取安全检测统计信息失败: domainId={}", domainId, e);
                Map<String, Object> errorStats = new HashMap<>();
                errorStats.put("success", false);
                errorStats.put("error", e.getMessage());
                return Mono.just(errorStats);
            });
    }

    /**
     * 执行初始检测
     */
    private void performInitialChecks(Long id) {
        try {
            // 延迟一下，确保事务提交
            Thread.sleep(1000);

            // 执行安全检测
            performSecurityCheck(id);

        } catch (Exception e) {
            log.error("初始检测失败: {}", e.getMessage(), e);
        }
    }


}
