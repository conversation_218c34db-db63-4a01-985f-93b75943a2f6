package external.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * IPRegistry API响应数据结构
 */
@Data
public class IPRegistryResult {

    /**
     * IP地址
     */
    private String ip;

    /**
     * 安全信息
     */
    private Security security;

    /**
     * 安全信息
     */
    @Data
    public static class Security {

        /**
         * 是否为代理
         */
        @JsonProperty("is_proxy")
        private boolean isProxy;

        /**
         * 是否为VPN
         */
        @JsonProperty("is_vpn")
        private boolean isVpn;

        /**
         * 是否为匿名IP
         */
        @JsonProperty("is_anonymous")
        private boolean isAnonymous;

        /**
         * 是否为云服务商
         */
        @JsonProperty("is_cloud_provider")
        private boolean isCloudProvider;

        /**
         * 是否为中继服务器
         */
        @JsonProperty("is_relay")
        private boolean isRelay;

        /**
         * 是否为威胁IP
         */
        @JsonProperty("is_threat")
        private boolean isThreat;

        /**
         * 是否为滥用者
         */
        @JsonProperty("is_abuser")
        private boolean isAbuser;

        /**
         * 是否为攻击者
         */
        @JsonProperty("is_attacker")
        private boolean isAttacker;

        /**
         * 是否为Tor网络
         */
        @JsonProperty("is_tor")
        private boolean isTor;

        /**
         * 是否为Tor出口节点
         */
        @JsonProperty("is_tor_exit")
        private boolean isTorExit;

        /**
         * 是否为虚假IP/保留IP
         */
        @JsonProperty("is_bogon")
        private boolean isBogon;
    }

    /**
     * 检查是否为可疑IP
     * 根据配置的检测类型判断
     */
    public boolean isSuspicious() {
        if (security == null) {
            return false;
        }

        return security.isProxy || 
               security.isVpn || 
               security.isAnonymous || 
               security.isRelay || 
               security.isThreat || 
               security.isAbuser || 
               security.isAttacker || 
               security.isTor || 
               security.isTorExit;
    }

    /**
     * 获取风险级别
     */
    public String getRiskLevel() {
        if (security == null) {
            return "UNKNOWN";
        }

        // 高风险：威胁、攻击者、滥用者
        if (security.isThreat || security.isAttacker || security.isAbuser) {
            return "HIGH";
        }

        // 中风险：代理、VPN、匿名、Tor
        if (security.isProxy || security.isVpn || security.isAnonymous || 
            security.isTor || security.isTorExit) {
            return "MEDIUM";
        }

        // 低风险：云服务商、中继服务器
        if (security.isCloudProvider || security.isRelay) {
            return "LOW";
        }

        return "SAFE";
    }

    /**
     * 获取检测到的类型列表
     */
    public String[] getDetectedTypes() {
        if (security == null) {
            return new String[0];
        }

        java.util.List<String> types = new java.util.ArrayList<>();
        
        if (security.isProxy) types.add("PROXY");
        if (security.isVpn) types.add("VPN");
        if (security.isAnonymous) types.add("ANONYMOUS");
        if (security.isCloudProvider) types.add("CLOUD_PROVIDER");
        if (security.isRelay) types.add("RELAY");
        if (security.isThreat) types.add("THREAT");
        if (security.isAbuser) types.add("ABUSER");
        if (security.isAttacker) types.add("ATTACKER");
        if (security.isTor) types.add("TOR");
        if (security.isTorExit) types.add("TOR_EXIT");
        if (security.isBogon) types.add("BOGON");

        return types.toArray(new String[0]);
    }
}
