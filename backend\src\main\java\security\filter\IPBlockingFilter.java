package security.filter;

import core.util.ValidationUtil;
import external.service.IPDetectionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpStatus;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;
import org.springframework.web.server.WebFilter;
import org.springframework.web.server.WebFilterChain;
import reactor.core.publisher.Mono;

import java.net.URI;
import java.util.Map;

/**
 * IP拦截过滤器
 * 在前台启用IP检测时，检测用户IP类型并拦截可疑IP
 */
@Slf4j
@Component
@Order(-100) // 高优先级，在其他过滤器之前执行
public class IPBlockingFilter implements WebFilter {

    @Autowired
    private IPDetectionService ipDetectionService;

    /**
     * 需要进行IP检测的路径前缀
     */
    private static final String[] PROTECTED_PATHS = {
        "/payment/",
        "/api/payment/"
    };

    /**
     * 排除的路径（不进行IP检测）
     */
    private static final String[] EXCLUDED_PATHS = {
        "/api/admin/",
        "/api/system/",
        "/api/auth/admin",
        "/actuator/",
        "/static/",
        "/css/",
        "/js/",
        "/images/"
    };

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, WebFilterChain chain) {
        ServerHttpRequest request = exchange.getRequest();
        String path = request.getURI().getPath();

        // 检查是否需要进行IP检测
        if (!shouldCheckIP(path)) {
            return chain.filter(exchange);
        }

        // 获取客户端IP地址
        String clientIP = getClientIP(request);
        if (clientIP == null || clientIP.isEmpty()) {
            log.warn("无法获取客户端IP地址，跳过IP检测");
            return chain.filter(exchange);
        }

        // 检查IP是否应该被拦截
        return ipDetectionService.checkIPBlock(clientIP)
                .flatMap(result -> {
                    Boolean shouldBlock = (Boolean) result.get("shouldBlock");
                    if (Boolean.TRUE.equals(shouldBlock)) {
                        return handleBlockedIP(exchange, clientIP, result);
                    } else {
                        // IP安全，继续处理请求
                        log.debug("IP检测通过: {}", clientIP);
                        return chain.filter(exchange);
                    }
                })
                .onErrorResume(Exception.class, e -> {
                    // IP检测失败时，记录错误但不阻止请求
                    log.error("IP检测失败: {} - {}", clientIP, e.getMessage());
                    return chain.filter(exchange);
                });
    }

    /**
     * 判断是否需要进行IP检测
     *
     * @param path 请求路径
     * @return 是否需要检测
     */
    private boolean shouldCheckIP(String path) {
        // 检查是否在排除列表中
        for (String excludedPath : EXCLUDED_PATHS) {
            if (path.startsWith(excludedPath)) {
                return false;
            }
        }

        // 检查是否在保护列表中
        for (String protectedPath : PROTECTED_PATHS) {
            if (path.startsWith(protectedPath)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 获取客户端真实IP地址
     *
     * @param request HTTP请求
     * @return 客户端IP地址
     */
    private String getClientIP(ServerHttpRequest request) {
        // 尝试从各种头部获取真实IP
        String[] headers = {
            "X-Forwarded-For",
            "X-Real-IP",
            "Proxy-Client-IP",
            "WL-Proxy-Client-IP",
            "HTTP_CLIENT_IP",
            "HTTP_X_FORWARDED_FOR"
        };

        for (String header : headers) {
            String ip = request.getHeaders().getFirst(header);
            if (ip != null && !ip.isEmpty() && !"unknown".equalsIgnoreCase(ip)) {
                // X-Forwarded-For可能包含多个IP，取第一个
                if (ip.contains(",")) {
                    ip = ip.split(",")[0].trim();
                }
                if (ValidationUtil.isValidIP(ip)) {
                    return ip;
                }
            }
        }

        // 如果没有找到，使用远程地址
        String remoteAddress = request.getRemoteAddress() != null ? 
                request.getRemoteAddress().getAddress().getHostAddress() : null;
        
        return remoteAddress;
    }

    /**
     * 处理被拦截的IP
     *
     * @param exchange Web交换对象
     * @param clientIP 客户端IP
     * @param result 检测结果
     * @return 响应
     */
    private Mono<Void> handleBlockedIP(ServerWebExchange exchange, String clientIP, Map<String, Object> result) {
        ServerHttpResponse response = exchange.getResponse();
        
        String[] blockReasons = (String[]) result.get("blockReasons");
        String riskLevel = (String) result.get("riskLevel");
        
        log.warn("IP被拦截: {} - 风险级别: {}, 拦截原因: {}", 
                clientIP, riskLevel, String.join(", ", blockReasons));

        // 设置响应状态码
        response.setStatusCode(HttpStatus.FORBIDDEN);
        
        // 设置响应头
        response.getHeaders().add("Content-Type", "application/json;charset=UTF-8");
        response.getHeaders().add("X-Blocked-IP", clientIP);
        response.getHeaders().add("X-Block-Reason", String.join(",", blockReasons));
        response.getHeaders().add("X-Risk-Level", riskLevel);

        // 构建响应内容
        String responseBody = String.format(
            "{\"success\":false,\"code\":403,\"message\":\"访问被拒绝\",\"data\":{" +
            "\"ip\":\"%s\"," +
            "\"riskLevel\":\"%s\"," +
            "\"blockReasons\":%s," +
            "\"redirectUrl\":\"%s\"," +
            "\"timestamp\":%d" +
            "}}",
            clientIP,
            riskLevel,
            arrayToJsonString(blockReasons),
            result.get("redirectUrl"),
            System.currentTimeMillis()
        );

        // 写入响应
        return response.writeWith(Mono.just(response.bufferFactory().wrap(responseBody.getBytes())));
    }



    /**
     * 将字符串数组转换为JSON字符串
     *
     * @param array 字符串数组
     * @return JSON字符串
     */
    private String arrayToJsonString(String[] array) {
        if (array == null || array.length == 0) {
            return "[]";
        }

        StringBuilder sb = new StringBuilder("[");
        for (int i = 0; i < array.length; i++) {
            if (i > 0) {
                sb.append(",");
            }
            sb.append("\"").append(array[i]).append("\"");
        }
        sb.append("]");
        return sb.toString();
    }
}
