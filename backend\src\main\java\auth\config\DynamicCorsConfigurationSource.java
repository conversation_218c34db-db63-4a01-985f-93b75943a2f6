package auth.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.reactive.CorsConfigurationSource;
import org.springframework.web.server.ServerWebExchange;
import security.service.DomainService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;
import reactor.core.publisher.Flux;

/**
 * 动态CORS配置源
 * 根据域名管理中的配置动态生成CORS规则
 */
@Component
public class DynamicCorsConfigurationSource implements CorsConfigurationSource {

    private static final Logger logger = LoggerFactory.getLogger(DynamicCorsConfigurationSource.class);

    @Autowired
    private DomainService domainService;

    // 缓存配置，避免频繁查询数据库
    private volatile CorsConfiguration cachedConfiguration;
    private final AtomicLong lastUpdateTime = new AtomicLong(0);
    private static final long CACHE_DURATION = 60000; // 1分钟缓存

    @Override
    public CorsConfiguration getCorsConfiguration(ServerWebExchange exchange) {
        long currentTime = System.currentTimeMillis();
        
        // 检查缓存是否过期
        if (cachedConfiguration == null || 
            (currentTime - lastUpdateTime.get()) > CACHE_DURATION) {
            
            synchronized (this) {
                // 双重检查锁定
                if (cachedConfiguration == null || 
                    (currentTime - lastUpdateTime.get()) > CACHE_DURATION) {
                    
                    cachedConfiguration = createCorsConfiguration();
                    lastUpdateTime.set(currentTime);
                    
                    logger.debug("CORS配置已更新，允许的域名: {}", 
                        cachedConfiguration.getAllowedOriginPatterns());
                }
            }
        }
        
        return cachedConfiguration;
    }

    /**
     * 创建CORS配置
     */
    private CorsConfiguration createCorsConfiguration() {
        CorsConfiguration configuration = new CorsConfiguration();

        try {
            // 从数据库获取域名配置并构建CORS源列表
            List<String> allowedOrigins = domainService.getAllDomains()
                .map(domains -> {
                    logger.debug("获取到域名列表，数量: {}", domains.size());
                    return domains.stream()
                        .filter(domain -> "NORMAL".equals(domain.getStatus().name()))
                        .flatMap(domain -> {
                            String domainName = domain.getDomainName();
                            String domainPath = domain.getDomainPath();
                            logger.debug("处理域名: {} 路径: {}", domainName, domainPath);

                            // 构建完整的域名URL
                            if (domainPath != null && !domainPath.isEmpty() && !"/".equals(domainPath)) {
                                return Arrays.asList(
                                    "https://" + domainName + domainPath + "*",
                                    "http://" + domainName + domainPath + "*"
                                ).stream();
                            } else {
                                return Arrays.asList(
                                    "https://" + domainName + "*",
                                    "http://" + domainName + "*"
                                ).stream();
                            }
                        })
                        .collect(Collectors.toList());
                })
                .block(); // 这里需要阻塞调用来获取结果

            // 设置允许的源 (使用AllowedOriginPatterns以支持allowCredentials=true)
            if (allowedOrigins != null && !allowedOrigins.isEmpty()) {
                configuration.setAllowedOriginPatterns(allowedOrigins);
                logger.debug("设置CORS允许的源模式: {}", allowedOrigins);
            } else {
                // 如果没有配置域名，使用默认配置
                configuration.setAllowedOriginPatterns(Arrays.asList("*"));
                logger.warn("未找到有效域名配置，使用通配符允许所有源");
            }

        } catch (Exception e) {
            logger.error("获取域名配置失败，使用默认CORS配置", e);
            // 发生异常时使用默认配置
            configuration.setAllowedOriginPatterns(Arrays.asList("*"));
        }

        // 设置允许的HTTP方法
        configuration.setAllowedMethods(Arrays.asList(
            "GET", "POST", "PUT", "DELETE", "OPTIONS"
        ));

        // 设置允许的请求头
        configuration.setAllowedHeaders(Arrays.asList(
            "Authorization",
            "Content-Type",
            "X-Requested-With",
            "Accept",
            "Origin",
            "Access-Control-Request-Method",
            "Access-Control-Request-Headers"
        ));

        // 设置暴露的响应头
        configuration.setExposedHeaders(Arrays.asList(
            "Access-Control-Allow-Origin",
            "Access-Control-Allow-Credentials"
        ));

        // 允许携带凭证
        configuration.setAllowCredentials(true);

        // 设置预检请求的缓存时间
        configuration.setMaxAge(3600L);

        return configuration;
    }



    /**
     * 手动刷新配置缓存
     */
    public void refreshConfiguration() {
        synchronized (this) {
            cachedConfiguration = null;
            lastUpdateTime.set(0);
            logger.info("CORS配置缓存已清除，将在下次请求时重新加载");
        }
    }

    /**
     * 获取当前缓存的配置信息
     */
    public CorsConfiguration getCurrentConfiguration() {
        if (cachedConfiguration == null) {
            return createCorsConfiguration();
        }
        return cachedConfiguration;
    }
}
