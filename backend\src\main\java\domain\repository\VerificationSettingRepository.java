package domain.repository;

import domain.entity.VerificationSetting;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.List;
import java.util.Optional;

// 3D安全验证设置数据访问层
@Repository
public interface VerificationSettingRepository extends BaseRepository<VerificationSetting, Long> {

    /**
     * 根据交易ID查找验证设置
     */
    Mono<VerificationSetting> findByTransactionId(String transactionId);

    /**
     * 查询所有指定状态的验证设置
     */
    Flux<VerificationSetting> findAllByStatus(VerificationSetting.VerificationStatus status);

    /**
     * 根据验证方法统计数量
     */
    long countByMethod(String method);

    /**
     * 统计指定状态的验证设置数量
     */
    long countByStatus(VerificationSetting.VerificationStatus status);

    /**
     * 分页查询指定状态的验证设置
     */
    Flux<VerificationSetting> findAllByStatus(VerificationSetting.VerificationStatus status, Pageable pageable);
}
