<template>
  <div v-if="visible" class="fixed inset-0 z-50 flex items-center justify-center">
    <!-- 遮罩层 -->
    <div 
      class="absolute inset-0 bg-black bg-opacity-50" 
      @click="handleCancel"
    ></div>
    
    <!-- 对话框 -->
    <div class="relative bg-white rounded-lg shadow-lg max-w-md w-full mx-4 p-6">
      <!-- 标题 -->
      <div class="flex items-center mb-4">
        <span class="text-2xl mr-2">{{ icon }}</span>
        <h3 class="text-lg font-medium text-gray-900">{{ title }}</h3>
      </div>
      
      <!-- 内容 -->
      <div class="mb-6">
        <p class="text-gray-600">{{ message }}</p>
        
        <!-- 自定义输入框 -->
        <div v-if="showInput" class="mt-4">
          <label class="block text-sm font-medium text-gray-700 mb-2">
            {{ inputLabel }}
          </label>
          <textarea
            v-model="inputValue"
            :placeholder="inputPlaceholder"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            rows="3"
          ></textarea>
        </div>
      </div>
      
      <!-- 按钮 -->
      <div class="flex justify-end space-x-3">
        <VbenButton
          variant="outline"
          @click="handleCancel"
          :disabled="loading"
        >
          {{ cancelText }}
        </VbenButton>
        <VbenButton
          :variant="confirmButtonType"
          @click="handleConfirm"
          :disabled="loading || (showInput && !inputValue.trim())"
        >
          <span v-if="loading" class="mr-2">⏳</span>
          {{ confirmText }}
        </VbenButton>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue';
import { VbenButton } from '@vben/common-ui';

interface Props {
  /** 是否显示对话框 */
  visible: boolean;
  /** 对话框标题 */
  title: string;
  /** 对话框消息 */
  message: string;
  /** 图标 */
  icon?: string;
  /** 确认按钮文本 */
  confirmText?: string;
  /** 取消按钮文本 */
  cancelText?: string;
  /** 确认按钮类型 */
  confirmButtonType?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost';
  /** 是否显示输入框 */
  showInput?: boolean;
  /** 输入框标签 */
  inputLabel?: string;
  /** 输入框占位符 */
  inputPlaceholder?: string;
  /** 加载状态 */
  loading?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  icon: '❓',
  confirmText: '确认',
  cancelText: '取消',
  confirmButtonType: 'default',
  showInput: false,
  inputLabel: '请输入',
  inputPlaceholder: '请输入内容...',
  loading: false,
});

const emit = defineEmits<{
  confirm: [value?: string];
  cancel: [];
  'update:visible': [visible: boolean];
}>();

const inputValue = ref('');

// 监听visible变化，重置输入值
watch(() => props.visible, (newVisible) => {
  if (newVisible) {
    inputValue.value = '';
  }
});

const handleConfirm = () => {
  if (props.showInput) {
    emit('confirm', inputValue.value.trim());
  } else {
    emit('confirm');
  }
};

const handleCancel = () => {
  emit('cancel');
  emit('update:visible', false);
};
</script>

<style scoped>
/* 确保对话框在最顶层 */
.fixed {
  z-index: 9999;
}

/* 动画效果 */
.fixed {
  animation: fadeIn 0.2s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.relative {
  animation: slideIn 0.2s ease-out;
}

@keyframes slideIn {
  from {
    transform: translateY(-20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}
</style>
