package external.client;

import core.common.ApiResponse;
import external.dto.BinLookupResult;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;
import reactor.core.publisher.Mono;

/**
 * BIN码查询API客户端
 * 支持多个BIN查询服务提供商
 *
 * 支持的提供商：
 * - binlist.net (免费)
 * - bindb.com (需要API密钥)
 */
@Component
public class BinLookupApiClient extends BaseApiClient {

    @Value("${external.api.bin-lookup.enabled:true}")
    private boolean enabled;

    @Value("${external.api.bin-lookup.provider:binlist}")
    private String provider;

    @Value("${external.api.bin-lookup.api-key:}")
    private String apiKey;

    @Value("${external.api.bin-lookup.base-url:https://lookup.binlist.net}")
    private String baseUrl;

    @Value("${external.api.bin-lookup.timeout:5000}")
    private int timeoutMs;

    @Value("${external.api.bin-lookup.retry-count:3}")
    private int retryCount;

    /**
     * 查询BIN码信息（响应式）
     *
     * @param bin BIN码（通常是卡号前6-8位）
     * @return BIN查询结果的Mono
     */
    public Mono<ApiResponse<BinLookupResult>> lookupBin(String bin) {
        if (!enabled) {
            logger.debug("BIN查询服务已禁用");
            return Mono.just(ApiResponse.error(503, "BIN查询服务已禁用", null));
        }

        if (bin == null || bin.trim().isEmpty()) {
            return Mono.just(ApiResponse.error(400, "BIN码不能为空", null));
        }

        // 清理BIN码，只保留数字
        String cleanBin = bin.replaceAll("[^0-9]", "");
        if (cleanBin.length() < 6) {
            return Mono.just(ApiResponse.error(400, "BIN码长度不能少于6位", null));
        }

        // 只取前6-8位
        String binToQuery = cleanBin.substring(0, Math.min(8, cleanBin.length()));

        logger.info("查询BIN码: {} (提供商: {})", binToQuery, provider);

        switch (provider.toLowerCase()) {
            case "binlist":
                return queryBinList(binToQuery);
            case "bindb":
                return queryBinDb(binToQuery);
            default:
                logger.warn("不支持的BIN查询提供商: {}", provider);
                return Mono.just(ApiResponse.error(500, "不支持的BIN查询提供商: " + provider, null));
        }
    }

    /**
     * 使用BinList.net查询BIN信息（响应式）
     *
     * @param bin BIN码
     * @return 查询结果的Mono
     */
    private Mono<ApiResponse<BinLookupResult>> queryBinList(String bin) {
        String url = baseUrl + "/" + bin;
        HttpHeaders headers = createDefaultHeaders();

        logger.debug("查询BinList: {}", url);

        return doGet(url, headers, Map.class)
            .map(response -> {
                if (response.isSuccess() && response.getData() != null) {
                    BinLookupResult binInfo = convertBinListResponse(response.getData());
                    return ApiResponse.success(binInfo);
                }
                return ApiResponse.error(response.getStatusCode(), response.getMessage(), null);
            });
    }

    /**
     * 使用BinDB查询BIN信息（响应式）
     *
     * @param bin BIN码
     * @return 查询结果的Mono
     */
    private Mono<ApiResponse<BinLookupResult>> queryBinDb(String bin) {
        if (apiKey == null || apiKey.trim().isEmpty()) {
            return Mono.just(ApiResponse.error(500, "BinDB API密钥未配置", null));
        }

        Map<String, String> params = new HashMap<>();
        params.put("bin", bin);
        params.put("api_key", apiKey);

        String url = buildUrlWithParams(baseUrl, params);
        HttpHeaders headers = createDefaultHeaders();

        logger.debug("查询BinDB: {}", url);

        return doGet(url, headers, Map.class)
            .map(response -> {
                if (response.isSuccess() && response.getData() != null) {
                    BinLookupResult binInfo = convertBinDbResponse(response.getData());
                    return ApiResponse.success(binInfo);
                }
                return ApiResponse.error(response.getStatusCode(), response.getMessage(), null);
            });
    }



    /**
     * 转换BinList.net响应格式
     *
     * @param data 原始响应数据
     * @return 标准化的BIN信息
     */
    private BinLookupResult convertBinListResponse(Map<String, Object> data) {
        BinLookupResult response = new BinLookupResult();

        response.setScheme(String.valueOf(data.get("scheme")));
        response.setType(String.valueOf(data.get("type")));
        response.setBrand(String.valueOf(data.get("brand")));

        // 处理国家信息
        Map<String, Object> country = (Map<String, Object>) data.get("country");
        if (country != null) {
            BinLookupResult.CountryInfo countryInfo = new BinLookupResult.CountryInfo();
            countryInfo.setName(String.valueOf(country.get("name")));
            countryInfo.setAlpha2(String.valueOf(country.get("alpha2")));
            response.setCountry(countryInfo);
        }

        // 处理银行信息
        Map<String, Object> bank = (Map<String, Object>) data.get("bank");
        if (bank != null) {
            BinLookupResult.BankInfo bankInfo = new BinLookupResult.BankInfo();
            bankInfo.setName(String.valueOf(bank.get("name")));
            bankInfo.setPhone(String.valueOf(bank.get("phone")));
            bankInfo.setUrl(String.valueOf(bank.get("url")));
            response.setBank(bankInfo);
        }

        return response;
    }

    /**
     * 转换BinDB响应格式
     *
     * @param data 原始响应数据
     * @return 标准化的BIN信息
     */
    private BinLookupResult convertBinDbResponse(Map<String, Object> data) {
        BinLookupResult response = new BinLookupResult();

        // BinDB响应格式转换
        response.setScheme(String.valueOf(data.get("scheme")));
        response.setBrand(String.valueOf(data.get("brand")));
        response.setType(String.valueOf(data.get("type")));

        return response;
    }

    @Override
    protected int getMaxRetries() {
        return retryCount;
    }

    @Override
    protected Duration getTimeout() {
        return Duration.ofMillis(timeoutMs);
    }

    @Override
    protected long getRetryDelay(int retryCount) {
        // BIN查询使用较短的重试间隔
        return retryCount * 500; // 500ms, 1000ms, 1500ms...
    }

    /**
     * 检查BIN查询服务是否可用
     * 
     * @return 服务状态
     */
    public Mono<Boolean> isServiceAvailable() {
        if (!enabled) {
            return Mono.just(false);
        }

        // 简单返回true，表示服务可用
        return Mono.just(true);
    }

    /**
     * 获取服务配置信息
     *
     * @return 配置信息
     */
    public Mono<Map<String, Object>> getServiceInfo() {
        return isServiceAvailable()
            .map(available -> {
                Map<String, Object> info = new HashMap<>();
                info.put("enabled", enabled);
                info.put("provider", provider);
                info.put("baseUrl", baseUrl);
                info.put("timeout", timeoutMs);
                info.put("retryCount", retryCount);
                info.put("hasApiKey", apiKey != null && !apiKey.trim().isEmpty());
                info.put("available", available);
                return info;
            });
    }
}
