FROM openresty/openresty:alpine

# 设置时区
ENV TZ=Asia/Shanghai
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# 复制web文件到nginx目录
COPY web/ /usr/share/nginx/html/

# 复制openresty配置
COPY openresty.conf /etc/nginx/conf.d/default.conf

# 生成自签名SSL证书（下次改
RUN apk add --no-cache openssl && \
    mkdir -p /etc/ssl/certs /etc/ssl/private && \
    openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
        -keyout /etc/ssl/private/nginx-selfsigned.key \
        -out /etc/ssl/certs/nginx-selfsigned.crt \
        -subj "/C=US/ST=State/L=City/O=Organization/CN=localhost"

# 设置权限
RUN chown -R nobody:nobody /usr/share/nginx/html && \
    chmod -R 755 /usr/share/nginx/html && \
    chmod 600 /etc/ssl/private/nginx-selfsigned.key && \
    chmod 644 /etc/ssl/certs/nginx-selfsigned.crt

# 暴露端口
EXPOSE 80 443

# 健康检查
HEALTHCHECK --interval=30s --timeout=5s --start-period=20s --retries=3 \
  CMD wget --spider -q http://localhost/ || exit 1

# 启动nginx
CMD ["nginx", "-g", "daemon off;"]
