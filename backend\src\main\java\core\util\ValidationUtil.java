package core.util;

import core.constants.AppConstants;
import domain.entity.Blacklist;
import org.springframework.util.StringUtils;

import java.util.regex.Pattern;
import common.constants.TemplateConstants;

//统一验证工具类
public class ValidationUtil {

    // 常用正则表达式
    private static final Pattern EMAIL_PATTERN = Pattern.compile(
        "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$"
    );

    private static final Pattern PHONE_PATTERN = Pattern.compile(
        "^[+]?[1-9]\\d{1,14}$"
    );

    private static final Pattern CARD_NUMBER_PATTERN = Pattern.compile(
        "^[0-9]{13,19}$"
    );

    private static final Pattern BIN_PATTERN = Pattern.compile(
        "^[0-9]{6,8}$"
    );

    private static final Pattern CVV_PATTERN = Pattern.compile(
        "^[0-9]{3,4}$"
    );

    // 基础参数验证

    //验证字符串参数不为空
    public static void validateNotEmpty(String value, String fieldName) {
        if (!StringUtils.hasText(value)) {
            throw new IllegalArgumentException(fieldName + "不能为空");
        }
    }

    //验证多个字符串参数不为空
    public static void validateNotEmpty(String... params) {
        if (params.length % 2 != 0) {
            throw new IllegalArgumentException("参数数量必须是偶数");
        }
        
        for (int i = 0; i < params.length; i += 2) {
            String value = params[i];
            String fieldName = params[i + 1];
            validateNotEmpty(value, fieldName);
        }
    }

    //验证对象不为空
    public static void validateNotNull(Object value, String fieldName) {
        if (value == null) {
            throw new IllegalArgumentException(fieldName + "不能为空");
        }
    }

    //验证数字范围
    public static void validateRange(long value, long min, long max, String fieldName) {
        if (value < min || value > max) {
            throw new IllegalArgumentException(
                String.format("%s必须在%d到%d之间", fieldName, min, max)
            );
        }
    }

    //验证字符串长度
    public static void validateLength(String value, int minLength, int maxLength, String fieldName) {
        validateNotEmpty(value, fieldName);

        int length = value.trim().length();
        if (length < minLength || length > maxLength) {
            throw new IllegalArgumentException(
                String.format("%s长度必须在%d到%d个字符之间", fieldName, minLength, maxLength)
            );
        }
    }

    // 格式验证

    //验证邮箱格式
    public static void validateEmail(String email) {
        validateNotEmpty(email, "邮箱");

        if (!EMAIL_PATTERN.matcher(email.trim()).matches()) {
            throw new IllegalArgumentException("邮箱格式无效");
        }
    }

    //验证手机号格式
    public static void validatePhone(String phone) {
        validateNotEmpty(phone, "手机号");

        if (!PHONE_PATTERN.matcher(phone.trim()).matches()) {
            throw new IllegalArgumentException("手机号格式无效");
        }
    }

    // 银行卡号验证已移至 UnifiedValidationService

    //验证BIN码格式
    public static void validateBin(String bin) {
        validateNotEmpty(bin, "BIN码");

        if (!BIN_PATTERN.matcher(bin.trim()).matches()) {
            throw new IllegalArgumentException("BIN码格式无效，必须是6-8位数字");
        }
    }

    //验证IP地址格式
    public static boolean isValidIP(String ip) {
        if (ip == null || ip.trim().isEmpty()) {
            return false;
        }

        String[] parts = ip.trim().split("\\.");
        if (parts.length != 4) {
            return false;
        }

        try {
            for (String part : parts) {
                int num = Integer.parseInt(part);
                if (num < 0 || num > 255) {
                    return false;
                }
            }
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    //验证IP地址格式（抛出异常版本）
    public static void validateIP(String ip) {
        if (!isValidIP(ip)) {
            throw new IllegalArgumentException("IP地址格式无效");
        }
    }

    //验证CVV格式
    public static void validateCvv(String cvv) {
        validateNotEmpty(cvv, "CVV");

        if (!CVV_PATTERN.matcher(cvv.trim()).matches()) {
            throw new IllegalArgumentException("CVV格式无效，必须是3-4位数字");
        }
    }

    // 业务验证

    //验证OTP参数
    public static void validateOtpParameters(String identifier, String method, String cardId) {
        validateNotEmpty(identifier, "标识符");
        validateNotEmpty(method, "验证方式");
        validateNotEmpty(cardId, "卡片ID");

        // 验证标识符格式（邮箱或手机号）
        if (identifier.contains("@")) {
            validateEmail(identifier);
        } else {
            validatePhone(identifier);
        }

        // 验证验证方式
        validateVerificationMethod(method);
    }

    //验证验证方式
    public static void validateVerificationMethod(String method) {
        validateNotEmpty(method, "验证方式");

        String[] validMethods = {
            TemplateConstants.SMS_TEMPLATE,
            TemplateConstants.EMAIL_TEMPLATE,
            TemplateConstants.APP_TEMPLATE,
            TemplateConstants.PIN_TEMPLATE,
            TemplateConstants.AMEX_SAFEKEY_TEMPLATE
        };
        boolean isValid = false;
        for (String validMethod : validMethods) {
            if (validMethod.equalsIgnoreCase(method.trim())) {
                isValid = true;
                break;
            }
        }

        if (!isValid) {
            throw new IllegalArgumentException("不支持的验证方式: " + method);
        }
    }

    //验证黑名单类型
    public static Blacklist.BlacklistType validateBlacklistType(String type) {
        validateNotEmpty(type, "黑名单类型");

        try {
            return Blacklist.BlacklistType.valueOf(type.toUpperCase());
        } catch (IllegalArgumentException e) {
            throw new IllegalArgumentException("无效的黑名单类型: " + type);
        }
    }

    //验证分页参数
    public static void validatePaginationParameters(int page, int size) {
        if (page < 0) {
            throw new IllegalArgumentException("页码不能小于0");
        }

        if (size <= 0) {
            throw new IllegalArgumentException("页大小必须大于0");
        }

        if (size > 1000) {
            throw new IllegalArgumentException("页大小不能超过1000");
        }
    }

    // 组合验证

    // 支付卡信息验证已移至 UnifiedValidationService

    //验证用户注册信息
    public static void validateUserRegistration(String username, String email, String password) {
        validateNotEmpty(username, "用户名");
        validateLength(username, 3, 20, "用户名");

        validateEmail(email);

        validateNotEmpty(password, "密码");
        validateLength(password, 6, 50, "密码");
    }

    //检查字符串是否为有效的数字
    public static boolean isNumeric(String str) {
        if (!StringUtils.hasText(str)) {
            return false;
        }

        try {
            Long.parseLong(str.trim());
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    //清理和标准化字符串
    public static String cleanString(String str) {
        if (!StringUtils.hasText(str)) {
            return "";
        }

        return str.trim().replaceAll("\\s+", " ");
    }

    //验证并清理字符串
    public static String validateAndClean(String value, String fieldName) {
        validateNotEmpty(value, fieldName);
        return cleanString(value);
    }
}
