<template>
  <Page
    content-class="flex flex-col gap-4"
    description="安全配置"
    title="🔒 安全配置"
  >
    <!-- IPRegistry配置 -->
    <Card title="🌐 IPRegistry配置">
      <template #extra>
        <div class="space-x-2">
          <Button type="default" :loading="testing" @click="testIPDetection">
            测试连接
          </Button>
          <Button type="primary" :loading="saving" @click="saveConfig">
            保存配置
          </Button>
        </div>
      </template>
      <p class="text-gray-600 mb-6">IP风险检测和威胁分析</p>

      <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        <div>
          <label class="block font-medium mb-2">IPRegistry API Key</label>
          <input
            type="password"
            v-model="securityConfig.ipRegistry.apiKey"
            class="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="输入您的IPRegistry API密钥"
          />
        </div>

        <div>
          <label class="block font-medium mb-2">拦截后跳转地址</label>
          <input
            type="url"
            v-model="securityConfig.ipRegistry.redirectUrl"
            class="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="例如：https://example.com"
          />
        </div>
      </div>

      <div class="mb-6">
        <div class="flex items-center justify-between mb-4">
          <div>
            <label class="font-medium">启用IP纯净度检测</label>
            <p class="text-sm text-gray-600">检测并拦截可疑IP地址</p>
          </div>
          <label class="relative inline-flex items-center cursor-pointer">
            <input type="checkbox" v-model="securityConfig.ipRegistry.enabled" class="sr-only peer">
            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
          </label>
        </div>
      </div>

      <div class="border-t pt-6">
        <h4 class="font-medium mb-4">检测以下类型IP</h4>
        <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
          <div class="flex items-center space-x-2">
            <input type="checkbox" v-model="securityConfig.ipRegistry.detectionTypes.proxy" id="proxy" class="rounded">
            <label for="proxy" class="text-sm">代理</label>
          </div>
          <div class="flex items-center space-x-2">
            <input type="checkbox" v-model="securityConfig.ipRegistry.detectionTypes.vpn" id="vpn" class="rounded">
            <label for="vpn" class="text-sm">VPN</label>
          </div>
          <div class="flex items-center space-x-2">
            <input type="checkbox" v-model="securityConfig.ipRegistry.detectionTypes.anonymous" id="anonymous" class="rounded">
            <label for="anonymous" class="text-sm">匿名</label>
          </div>
          <div class="flex items-center space-x-2">
            <input type="checkbox" v-model="securityConfig.ipRegistry.detectionTypes.cloudProvider" id="cloudProvider" class="rounded">
            <label for="cloudProvider" class="text-sm">云服务商</label>
          </div>
          <div class="flex items-center space-x-2">
            <input type="checkbox" v-model="securityConfig.ipRegistry.detectionTypes.relay" id="relay" class="rounded">
            <label for="relay" class="text-sm">中继服务器</label>
          </div>
          <div class="flex items-center space-x-2">
            <input type="checkbox" v-model="securityConfig.ipRegistry.detectionTypes.threat" id="threat" class="rounded">
            <label for="threat" class="text-sm">威胁</label>
          </div>
          <div class="flex items-center space-x-2">
            <input type="checkbox" v-model="securityConfig.ipRegistry.detectionTypes.abuser" id="abuser" class="rounded">
            <label for="abuser" class="text-sm">滥用者</label>
          </div>
          <div class="flex items-center space-x-2">
            <input type="checkbox" v-model="securityConfig.ipRegistry.detectionTypes.attacker" id="attacker" class="rounded">
            <label for="attacker" class="text-sm">攻击者</label>
          </div>
          <div class="flex items-center space-x-2">
            <input type="checkbox" v-model="securityConfig.ipRegistry.detectionTypes.fakeIp" id="fakeIp" class="rounded">
            <label for="fakeIp" class="text-sm">虚假IP</label>
          </div>
          <div class="flex items-center space-x-2">
            <input type="checkbox" v-model="securityConfig.ipRegistry.detectionTypes.tor" id="tor" class="rounded">
            <label for="tor" class="text-sm">Tor网络</label>
          </div>
          <div class="flex items-center space-x-2">
            <input type="checkbox" v-model="securityConfig.ipRegistry.detectionTypes.torExit" id="torExit" class="rounded">
            <label for="torExit" class="text-sm">Tor出口节点</label>
          </div>
        </div>
      </div>
    </Card>

    <!-- 用户行为检测 -->
    <Card title="👤 用户行为检测">
      <template #extra>
        <span class="px-2 py-1 bg-purple-100 text-purple-800 rounded text-xs">行为分析</span>
      </template>
        <p class="text-gray-600 mb-4">行为异常检测</p>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div class="space-y-4">
            <div class="flex items-center justify-between">
              <div>
                <label class="font-medium">用户支付UA异常检测</label>
                <p class="text-sm text-gray-600">UA异常检测</p>
              </div>
              <label class="relative inline-flex items-center cursor-pointer">
                <input type="checkbox" v-model="securityConfig.userBehavior.enableUADetection" class="sr-only peer">
                <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
              </label>
            </div>


          </div>


        </div>
    </Card>


  </Page>
</template>

<script setup lang="ts">
import { reactive, ref, onMounted } from 'vue'
import { Page } from '@vben/common-ui'
import { Card, Button, message } from 'ant-design-vue'
import { requestClient } from '#/api/request'


const saving = ref(false)
const loading = ref(false)
const testing = ref(false)

// 安全配置数据
const securityConfig = reactive({
  // IPRegistry配置
  ipRegistry: {
    enabled: false,
    apiKey: '',
    redirectUrl: 'https://google.com',
    detectionTypes: {
      proxy: true,
      vpn: false,
      anonymous: true,
      cloudProvider: false,
      relay: true,
      threat: true,
      abuser: true,
      attacker: true,
      fakeIp: true,
      tor: true,
      torExit: true
    }
  },

  // 用户行为检测
  userBehavior: {
    enableUADetection: true
  },

  // 其他安全配置
  enableNetworkTypeCheck: false,
  filterCrawlerUserAgent: true,
  enable3DSecure: true,
  enable3D2: false
})

// 加载配置
const loadConfig = async () => {
  loading.value = true
  try {
    // 加载基础安全配置
    const securityResponse = await requestClient.get('/security/config')
    // 响应拦截器已经处理了数据，直接使用response作为数据
    const data = securityResponse || {}
    securityConfig.enableNetworkTypeCheck = data.enable_network_type_check || false
    securityConfig.filterCrawlerUserAgent = data.filter_crawler_user_agent || true
    securityConfig.enable3DSecure = data['3d_secure_enabled'] || true
    securityConfig.enable3D2 = data['3d2_enabled'] || false

    // 加载IPRegistry配置
    const ipRegistryResponse = await requestClient.get('/ip-detection/status')
    // 响应拦截器已经处理了数据，直接使用response作为数据
    const ipData = ipRegistryResponse || {}
    securityConfig.ipRegistry.enabled = ipData.enabled || false
    securityConfig.ipRegistry.redirectUrl = ipData.redirectUrl || 'https://example.com'

    if (ipData.detectionTypes) {
      Object.assign(securityConfig.ipRegistry.detectionTypes, ipData.detectionTypes)
    }
  } catch (error) {
    console.error('加载安全配置失败:', error)
    message.error('加载安全配置失败')
  } finally {
    loading.value = false
  }
}

// 保存配置
const saveConfig = async () => {
  saving.value = true
  try {
    // 保存基础安全配置
    const configData = {
      enable_network_type_check: securityConfig.enableNetworkTypeCheck,
      filter_crawler_user_agent: securityConfig.filterCrawlerUserAgent,
      '3d_secure_enabled': securityConfig.enable3DSecure,
      '3d2_enabled': securityConfig.enable3D2
    }

    const securityResponse = await requestClient.post('/security/config', configData)

    // 保存IPRegistry配置
    const ipRegistryData = {
      enabled: securityConfig.ipRegistry.enabled,
      apiKey: securityConfig.ipRegistry.apiKey,
      redirectUrl: securityConfig.ipRegistry.redirectUrl,
      detectionTypes: securityConfig.ipRegistry.detectionTypes
    }

    const ipRegistryResponse = await requestClient.post('/ip-detection/config', ipRegistryData)

    // 响应拦截器已经处理了数据，成功的请求会直接返回数据
    message.success('安全配置保存成功')
  } catch (error) {
    console.error('保存安全配置失败:', error)
    message.error('保存安全配置失败')
  } finally {
    saving.value = false
  }
}

// 测试IP检测服务
const testIPDetection = async () => {
  testing.value = true
  try {
    const response = await requestClient.get('/api/ip-detection/test')
    if (response.data.success) {
      const data = response.data.data
      if (data.success) {
        message.success(`连接测试成功！测试IP: ${data.testIP}, 风险级别: ${data.riskLevel}`)
      } else {
        message.warning(`连接测试失败: ${data.error || '未知错误'}`)
      }
    } else {
      message.error('测试失败: ' + (response.data.message || '未知错误'))
    }
  } catch (error) {
    console.error('测试IP检测服务失败:', error)
    message.error('测试连接失败')
  } finally {
    testing.value = false
  }
}

// 页面加载时获取配置
onMounted(() => {
  loadConfig()
})
</script>
